<svg width="350" height="247" viewBox="0 0 350 247" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="projectGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f8fafc;stop-opacity:0.8" />
      <stop offset="50%" style="stop-color:#f1f5f9;stop-opacity:0.6" />
      <stop offset="100%" style="stop-color:#e2e8f0;stop-opacity:0.5" />
    </linearGradient>
    <pattern id="projectPattern" width="20" height="20" patternUnits="userSpaceOnUse">
      <path d="M 20 0 L 0 0 0 20" fill="none" stroke="rgba(148,163,184,0.1)" stroke-width="0.5"/>
    </pattern>
    <filter id="blur" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur in="SourceGraphic" stdDeviation="1"/>
    </filter>
  </defs>
  
  <!-- Background -->
  <rect width="350" height="247" fill="url(#projectGradient)"/>
  <rect width="350" height="247" fill="url(#projectPattern)"/>
  
  <!-- Building Structure -->
  <rect x="50" y="80" width="60" height="120" fill="rgba(148,163,184,0.15)" rx="4" opacity="0.6" filter="url(#blur)"/>
  <rect x="120" y="60" width="60" height="140" fill="rgba(148,163,184,0.15)" rx="4" opacity="0.6" filter="url(#blur)"/>
  <rect x="190" y="100" width="60" height="100" fill="rgba(148,163,184,0.15)" rx="4" opacity="0.6" filter="url(#blur)"/>
  <rect x="260" y="70" width="60" height="130" fill="rgba(148,163,184,0.15)" rx="4" opacity="0.6" filter="url(#blur)"/>
  
  <!-- Windows -->
  <rect x="55" y="90" width="8" height="8" fill="rgba(148,163,184,0.2)" rx="1" opacity="0.4"/>
  <rect x="67" y="90" width="8" height="8" fill="rgba(148,163,184,0.2)" rx="1" opacity="0.4"/>
  <rect x="79" y="90" width="8" height="8" fill="rgba(148,163,184,0.2)" rx="1" opacity="0.4"/>
  <rect x="91" y="90" width="8" height="8" fill="rgba(148,163,184,0.2)" rx="1" opacity="0.4"/>
  
  <rect x="55" y="105" width="8" height="8" fill="rgba(148,163,184,0.2)" rx="1" opacity="0.4"/>
  <rect x="67" y="105" width="8" height="8" fill="rgba(148,163,184,0.2)" rx="1" opacity="0.4"/>
  <rect x="79" y="105" width="8" height="8" fill="rgba(148,163,184,0.2)" rx="1" opacity="0.4"/>
  <rect x="91" y="105" width="8" height="8" fill="rgba(148,163,184,0.2)" rx="1" opacity="0.4"/>
  
  <!-- More windows for other buildings -->
  <rect x="125" y="70" width="8" height="8" fill="rgba(148,163,184,0.2)" rx="1" opacity="0.4"/>
  <rect x="137" y="70" width="8" height="8" fill="rgba(148,163,184,0.2)" rx="1" opacity="0.4"/>
  <rect x="149" y="70" width="8" height="8" fill="rgba(148,163,184,0.2)" rx="1" opacity="0.4"/>
  <rect x="161" y="70" width="8" height="8" fill="rgba(148,163,184,0.2)" rx="1" opacity="0.4"/>
  
  <!-- Construction Elements -->
  <circle cx="100" cy="50" r="15" fill="rgba(148,163,184,0.2)" opacity="0.4" filter="url(#blur)"/>
  <rect x="95" y="45" width="10" height="10" fill="rgba(148,163,184,0.3)" rx="2" opacity="0.5"/>
  
  <!-- Crane -->
  <line x1="200" y1="200" x2="200" y2="50" stroke="rgba(148,163,184,0.3)" stroke-width="2" opacity="0.5" filter="url(#blur)"/>
  <line x1="200" y1="50" x2="250" y2="50" stroke="rgba(148,163,184,0.3)" stroke-width="2" opacity="0.5" filter="url(#blur)"/>
  <line x1="250" y1="50" x2="250" y2="80" stroke="rgba(148,163,184,0.3)" stroke-width="1.5" opacity="0.5" filter="url(#blur)"/>
</svg>
