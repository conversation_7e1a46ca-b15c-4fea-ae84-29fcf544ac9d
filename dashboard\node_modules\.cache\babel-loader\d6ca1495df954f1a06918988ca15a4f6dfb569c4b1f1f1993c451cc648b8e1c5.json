{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Akshay\\\\LOOP_PROJECTS\\\\shade_cms\\\\dashboard\\\\src\\\\features\\\\Resources\\\\components\\\\contentmanager\\\\CareerManager.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from 'react';\nimport ContentSection from \"../breakUI/ContentSections\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CareerManager = ({\n  content,\n  indexes,\n  language,\n  currentPath,\n  outOfEditing\n}) => {\n  _s();\n  var _content$5, _content$5$content, _content$5$content$ti, _content$6, _content$6$content, _content$6$content$de, _content$7, _content$7$content, _content$7$content$de, _content$8, _content$8$content, _content$8$content$de;\n  const [validationErrors, setValidationErrors] = useState({});\n\n  // Function to clear specific validation error\n  const clearValidationError = errorKey => {\n    setValidationErrors(prev => {\n      const newErrors = {\n        ...prev\n      };\n      delete newErrors[errorKey];\n      return newErrors;\n    });\n  };\n\n  // Validation function\n  const validateField = (value, fieldName) => {\n    if (!value || typeof value === 'string' && value.trim() === '') {\n      return `${fieldName} is required`;\n    }\n    if (typeof value === 'string' && value.trim().length < 10) {\n      return `${fieldName} must be at least 10 characters`;\n    }\n    return null;\n  };\n  const validateAllFields = useCallback(() => {\n    var _content$, _content$$content, _content$$content$tit, _content$2, _content$2$content, _content$2$content$de, _content$3, _content$3$content, _content$3$content$de, _content$4, _content$4$content, _content$4$content$de;\n    const errors = {};\n    let hasErrors = false;\n\n    // Banner Section validation\n    const bannerTitle = content === null || content === void 0 ? void 0 : (_content$ = content[1]) === null || _content$ === void 0 ? void 0 : (_content$$content = _content$.content) === null || _content$$content === void 0 ? void 0 : (_content$$content$tit = _content$$content.title) === null || _content$$content$tit === void 0 ? void 0 : _content$$content$tit[language];\n    const bannerDescription = content === null || content === void 0 ? void 0 : (_content$2 = content[1]) === null || _content$2 === void 0 ? void 0 : (_content$2$content = _content$2.content) === null || _content$2$content === void 0 ? void 0 : (_content$2$content$de = _content$2$content.description) === null || _content$2$content$de === void 0 ? void 0 : _content$2$content$de[language];\n    const bannerDescription2 = content === null || content === void 0 ? void 0 : (_content$3 = content[1]) === null || _content$3 === void 0 ? void 0 : (_content$3$content = _content$3.content) === null || _content$3$content === void 0 ? void 0 : (_content$3$content$de = _content$3$content.description2) === null || _content$3$content$de === void 0 ? void 0 : _content$3$content$de[language];\n    const bannerDescription3 = content === null || content === void 0 ? void 0 : (_content$4 = content[1]) === null || _content$4 === void 0 ? void 0 : (_content$4$content = _content$4.content) === null || _content$4$content === void 0 ? void 0 : (_content$4$content$de = _content$4$content.description3) === null || _content$4$content$de === void 0 ? void 0 : _content$4$content$de[language];\n    if (validateField(bannerTitle, \"Title\")) {\n      errors[\"banner_title\"] = validateField(bannerTitle, \"Title\");\n      hasErrors = true;\n    }\n    if (validateField(bannerDescription, \"First Paragraph\")) {\n      errors[\"banner_description\"] = validateField(bannerDescription, \"First Paragraph\");\n      hasErrors = true;\n    }\n    if (validateField(bannerDescription2, \"Second Paragraph\")) {\n      errors[\"banner_description2\"] = validateField(bannerDescription2, \"Second Paragraph\");\n      hasErrors = true;\n    }\n    if (validateField(bannerDescription3, \"Third Paragraph\")) {\n      errors[\"banner_description3\"] = validateField(bannerDescription3, \"Third Paragraph\");\n      hasErrors = true;\n    }\n    setValidationErrors(errors);\n    return !hasErrors;\n  }, [content, language]);\n\n  // Validate on content or language change\n  useEffect(() => {\n    if (!outOfEditing) {\n      validateAllFields();\n    }\n  }, [content, language, outOfEditing, validateAllFields]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      padding: '20px',\n      backgroundColor: '#f8fafc',\n      minHeight: '100vh'\n    },\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        maxWidth: '900px',\n        margin: '0 auto',\n        backgroundColor: '#ffffff',\n        borderRadius: '12px',\n        padding: '24px',\n        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: '32px',\n          paddingBottom: '20px',\n          borderBottom: '2px solid #e2e8f0'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          style: {\n            fontSize: '28px',\n            fontWeight: '700',\n            color: '#1e293b',\n            marginBottom: '8px'\n          },\n          children: \"Career Page Editor\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 91,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            fontSize: '15px',\n            color: '#64748b',\n            lineHeight: '1.6'\n          },\n          children: \"Edit the hero section content for the Career page. Job listings are managed separately.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 99,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 86,\n        columnNumber: 9\n      }, this), Object.keys(validationErrors).length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          backgroundColor: '#fef2f2',\n          border: '1px solid #fecaca',\n          borderRadius: '8px',\n          padding: '16px',\n          marginBottom: '24px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          style: {\n            fontSize: '16px',\n            fontWeight: '600',\n            color: '#dc2626',\n            marginBottom: '8px'\n          },\n          children: \"\\u26A0\\uFE0F Validation Errors\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 117,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n          style: {\n            margin: '0',\n            paddingLeft: '20px',\n            color: '#991b1b',\n            fontSize: '14px'\n          },\n          children: Object.entries(validationErrors).map(([key, error]) => /*#__PURE__*/_jsxDEV(\"li\", {\n            style: {\n              marginBottom: '4px'\n            },\n            children: error\n          }, key, false, {\n            fileName: _jsxFileName,\n            lineNumber: 132,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 125,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 110,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(ContentSection, {\n        currentPath: currentPath,\n        Heading: \"Hero Section\",\n        subHeading: \"Main banner section with title and three paragraphs about company culture\",\n        section: \"1\",\n        sectionIndex: indexes === null || indexes === void 0 ? void 0 : indexes[0],\n        language: language,\n        outOfEditing: outOfEditing,\n        currentContent: content,\n        clearValidationError: clearValidationError,\n        inputs: [{\n          input: \"input\",\n          label: \"Title\",\n          updateType: \"title\",\n          value: content === null || content === void 0 ? void 0 : (_content$5 = content[1]) === null || _content$5 === void 0 ? void 0 : (_content$5$content = _content$5.content) === null || _content$5$content === void 0 ? void 0 : (_content$5$content$ti = _content$5$content.title) === null || _content$5$content$ti === void 0 ? void 0 : _content$5$content$ti[language],\n          maxLength: 100,\n          errorMessage: validationErrors[\"banner_title\"],\n          errorKey: \"banner_title\"\n        }, {\n          input: \"textarea\",\n          label: \"First Paragraph (Purpose & Mission)\",\n          updateType: \"description\",\n          value: content === null || content === void 0 ? void 0 : (_content$6 = content[1]) === null || _content$6 === void 0 ? void 0 : (_content$6$content = _content$6.content) === null || _content$6$content === void 0 ? void 0 : (_content$6$content$de = _content$6$content.description) === null || _content$6$content$de === void 0 ? void 0 : _content$6$content$de[language],\n          maxLength: 1000,\n          rows: 4,\n          errorMessage: validationErrors[\"banner_description\"],\n          errorKey: \"banner_description\"\n        }, {\n          input: \"textarea\",\n          label: \"Second Paragraph (Team Philosophy)\",\n          updateType: \"description2\",\n          value: content === null || content === void 0 ? void 0 : (_content$7 = content[1]) === null || _content$7 === void 0 ? void 0 : (_content$7$content = _content$7.content) === null || _content$7$content === void 0 ? void 0 : (_content$7$content$de = _content$7$content.description2) === null || _content$7$content$de === void 0 ? void 0 : _content$7$content$de[language],\n          maxLength: 1000,\n          rows: 5,\n          errorMessage: validationErrors[\"banner_description2\"],\n          errorKey: \"banner_description2\"\n        }, {\n          input: \"textarea\",\n          label: \"Third Paragraph (Culture & Opportunities)\",\n          updateType: \"description3\",\n          value: content === null || content === void 0 ? void 0 : (_content$8 = content[1]) === null || _content$8 === void 0 ? void 0 : (_content$8$content = _content$8.content) === null || _content$8$content === void 0 ? void 0 : (_content$8$content$de = _content$8$content.description3) === null || _content$8$content$de === void 0 ? void 0 : _content$8$content$de[language],\n          maxLength: 1000,\n          rows: 5,\n          errorMessage: validationErrors[\"banner_description3\"],\n          errorKey: \"banner_description3\"\n        }]\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 139,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginTop: '32px',\n          padding: '20px',\n          backgroundColor: '#f0f9ff',\n          border: '1px solid #bae6fd',\n          borderRadius: '8px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          style: {\n            fontSize: '16px',\n            fontWeight: '600',\n            color: '#0369a1',\n            marginBottom: '12px'\n          },\n          children: \"\\uD83D\\uDCA1 Editing Guidelines\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 200,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n          style: {\n            margin: '0',\n            paddingLeft: '20px',\n            color: '#075985',\n            fontSize: '14px',\n            lineHeight: '1.8'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"li\", {\n            children: [\"The \", /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Title\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 215,\n              columnNumber: 21\n            }, this), \" appears as a large heading at the top (e.g., \\\"CAREERS\\\")\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 215,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: [\"The \", /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"First Paragraph\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 216,\n              columnNumber: 21\n            }, this), \" describes your company's purpose and mission\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 216,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: [\"The \", /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Second Paragraph\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 217,\n              columnNumber: 21\n            }, this), \" explains your team philosophy and growth values\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 217,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: [\"The \", /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Third Paragraph\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 218,\n              columnNumber: 21\n            }, this), \" highlights your company culture and opportunities\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"All fields must be at least 10 characters long\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 219,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"Remember to edit content in both English and Arabic\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 220,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"Job listings are managed through the Job Management section\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 221,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 208,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 193,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginTop: '20px',\n          padding: '16px',\n          backgroundColor: '#fefce8',\n          border: '1px solid #fde047',\n          borderRadius: '8px',\n          textAlign: 'center'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            margin: '0',\n            fontSize: '14px',\n            color: '#854d0e',\n            fontWeight: '500'\n          },\n          children: [\"\\uD83D\\uDCDD Currently editing: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: language === 'en' ? 'English' : 'Arabic'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 240,\n            columnNumber: 35\n          }, this), \" content. Switch languages using the toggle above to edit both versions.\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 234,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 226,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 77,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 72,\n    columnNumber: 5\n  }, this);\n};\n_s(CareerManager, \"V8j0aSlIPZOFFQO/wNur0bcJmdM=\");\n_c = CareerManager;\nexport default CareerManager;\nvar _c;\n$RefreshReg$(_c, \"CareerManager\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "ContentSection", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON><PERSON>", "content", "indexes", "language", "currentPath", "outOfEditing", "_s", "_content$5", "_content$5$content", "_content$5$content$ti", "_content$6", "_content$6$content", "_content$6$content$de", "_content$7", "_content$7$content", "_content$7$content$de", "_content$8", "_content$8$content", "_content$8$content$de", "validationErrors", "setValidationErrors", "clearValidationError", "<PERSON><PERSON><PERSON>", "prev", "newErrors", "validateField", "value", "fieldName", "trim", "length", "validate<PERSON>ll<PERSON>ields", "_content$", "_content$$content", "_content$$content$tit", "_content$2", "_content$2$content", "_content$2$content$de", "_content$3", "_content$3$content", "_content$3$content$de", "_content$4", "_content$4$content", "_content$4$content$de", "errors", "hasErrors", "bannerTitle", "title", "bannerDescription", "description", "bannerDescription2", "description2", "bannerDescription3", "description3", "style", "padding", "backgroundColor", "minHeight", "children", "max<PERSON><PERSON><PERSON>", "margin", "borderRadius", "boxShadow", "marginBottom", "paddingBottom", "borderBottom", "fontSize", "fontWeight", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "lineHeight", "Object", "keys", "border", "paddingLeft", "entries", "map", "key", "error", "Heading", "subHeading", "section", "sectionIndex", "currentC<PERSON>nt", "inputs", "input", "label", "updateType", "max<PERSON><PERSON><PERSON>", "errorMessage", "rows", "marginTop", "textAlign", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Akshay/LOOP_PROJECTS/shade_cms/dashboard/src/features/Resources/components/contentmanager/CareerManager.jsx"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from 'react';\nimport ContentSection from \"../breakUI/ContentSections\";\n\nconst CareerManager = ({ \n  content, \n  indexes, \n  language, \n  currentPath, \n  outOfEditing \n}) => {\n  const [validationErrors, setValidationErrors] = useState({});\n\n  // Function to clear specific validation error\n  const clearValidationError = (errorKey) => {\n    setValidationErrors(prev => {\n      const newErrors = { ...prev };\n      delete newErrors[errorKey];\n      return newErrors;\n    });\n  };\n\n  // Validation function\n  const validateField = (value, fieldName) => {\n    if (!value || (typeof value === 'string' && value.trim() === '')) {\n      return `${fieldName} is required`;\n    }\n    if (typeof value === 'string' && value.trim().length < 10) {\n      return `${fieldName} must be at least 10 characters`;\n    }\n    return null;\n  };\n\n  const validateAllFields = useCallback(() => {\n    const errors = {};\n    let hasErrors = false;\n\n    // Banner Section validation\n    const bannerTitle = content?.[1]?.content?.title?.[language];\n    const bannerDescription = content?.[1]?.content?.description?.[language];\n    const bannerDescription2 = content?.[1]?.content?.description2?.[language];\n    const bannerDescription3 = content?.[1]?.content?.description3?.[language];\n\n    if (validateField(bannerTitle, \"Title\")) {\n      errors[\"banner_title\"] = validateField(bannerTitle, \"Title\");\n      hasErrors = true;\n    }\n    if (validateField(bannerDescription, \"First Paragraph\")) {\n      errors[\"banner_description\"] = validateField(bannerDescription, \"First Paragraph\");\n      hasErrors = true;\n    }\n    if (validateField(bannerDescription2, \"Second Paragraph\")) {\n      errors[\"banner_description2\"] = validateField(bannerDescription2, \"Second Paragraph\");\n      hasErrors = true;\n    }\n    if (validateField(bannerDescription3, \"Third Paragraph\")) {\n      errors[\"banner_description3\"] = validateField(bannerDescription3, \"Third Paragraph\");\n      hasErrors = true;\n    }\n\n    setValidationErrors(errors);\n    return !hasErrors;\n  }, [content, language]);\n\n  // Validate on content or language change\n  useEffect(() => {\n    if (!outOfEditing) {\n      validateAllFields();\n    }\n  }, [content, language, outOfEditing, validateAllFields]);\n\n  return (\n    <div style={{\n      padding: '20px',\n      backgroundColor: '#f8fafc',\n      minHeight: '100vh'\n    }}>\n      <div style={{\n        maxWidth: '900px',\n        margin: '0 auto',\n        backgroundColor: '#ffffff',\n        borderRadius: '12px',\n        padding: '24px',\n        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'\n      }}>\n        {/* Header */}\n        <div style={{\n          marginBottom: '32px',\n          paddingBottom: '20px',\n          borderBottom: '2px solid #e2e8f0'\n        }}>\n          <h2 style={{\n            fontSize: '28px',\n            fontWeight: '700',\n            color: '#1e293b',\n            marginBottom: '8px'\n          }}>\n            Career Page Editor\n          </h2>\n          <p style={{\n            fontSize: '15px',\n            color: '#64748b',\n            lineHeight: '1.6'\n          }}>\n            Edit the hero section content for the Career page. Job listings are managed separately.\n          </p>\n        </div>\n\n        {/* Validation Summary */}\n        {Object.keys(validationErrors).length > 0 && (\n          <div style={{\n            backgroundColor: '#fef2f2',\n            border: '1px solid #fecaca',\n            borderRadius: '8px',\n            padding: '16px',\n            marginBottom: '24px'\n          }}>\n            <h4 style={{\n              fontSize: '16px',\n              fontWeight: '600',\n              color: '#dc2626',\n              marginBottom: '8px'\n            }}>\n              ⚠️ Validation Errors\n            </h4>\n            <ul style={{\n              margin: '0',\n              paddingLeft: '20px',\n              color: '#991b1b',\n              fontSize: '14px'\n            }}>\n              {Object.entries(validationErrors).map(([key, error]) => (\n                <li key={key} style={{ marginBottom: '4px' }}>{error}</li>\n              ))}\n            </ul>\n          </div>\n        )}\n\n        {/* Banner Section */}\n        <ContentSection\n          currentPath={currentPath}\n          Heading=\"Hero Section\"\n          subHeading=\"Main banner section with title and three paragraphs about company culture\"\n          section={\"1\"}\n          sectionIndex={indexes?.[0]}\n          language={language}\n          outOfEditing={outOfEditing}\n          currentContent={content}\n          clearValidationError={clearValidationError}\n          inputs={[\n            { \n              input: \"input\", \n              label: \"Title\", \n              updateType: \"title\", \n              value: content?.[1]?.content?.title?.[language],\n              maxLength: 100,\n              errorMessage: validationErrors[\"banner_title\"],\n              errorKey: \"banner_title\"\n            },\n            { \n              input: \"textarea\", \n              label: \"First Paragraph (Purpose & Mission)\", \n              updateType: \"description\", \n              value: content?.[1]?.content?.description?.[language],\n              maxLength: 1000,\n              rows: 4,\n              errorMessage: validationErrors[\"banner_description\"],\n              errorKey: \"banner_description\"\n            },\n            { \n              input: \"textarea\", \n              label: \"Second Paragraph (Team Philosophy)\", \n              updateType: \"description2\", \n              value: content?.[1]?.content?.description2?.[language],\n              maxLength: 1000,\n              rows: 5,\n              errorMessage: validationErrors[\"banner_description2\"],\n              errorKey: \"banner_description2\"\n            },\n            { \n              input: \"textarea\", \n              label: \"Third Paragraph (Culture & Opportunities)\", \n              updateType: \"description3\", \n              value: content?.[1]?.content?.description3?.[language],\n              maxLength: 1000,\n              rows: 5,\n              errorMessage: validationErrors[\"banner_description3\"],\n              errorKey: \"banner_description3\"\n            }\n          ]}\n        />\n\n        {/* Help Text */}\n        <div style={{\n          marginTop: '32px',\n          padding: '20px',\n          backgroundColor: '#f0f9ff',\n          border: '1px solid #bae6fd',\n          borderRadius: '8px'\n        }}>\n          <h4 style={{\n            fontSize: '16px',\n            fontWeight: '600',\n            color: '#0369a1',\n            marginBottom: '12px'\n          }}>\n            💡 Editing Guidelines\n          </h4>\n          <ul style={{\n            margin: '0',\n            paddingLeft: '20px',\n            color: '#075985',\n            fontSize: '14px',\n            lineHeight: '1.8'\n          }}>\n            <li>The <strong>Title</strong> appears as a large heading at the top (e.g., \"CAREERS\")</li>\n            <li>The <strong>First Paragraph</strong> describes your company's purpose and mission</li>\n            <li>The <strong>Second Paragraph</strong> explains your team philosophy and growth values</li>\n            <li>The <strong>Third Paragraph</strong> highlights your company culture and opportunities</li>\n            <li>All fields must be at least 10 characters long</li>\n            <li>Remember to edit content in both English and Arabic</li>\n            <li>Job listings are managed through the Job Management section</li>\n          </ul>\n        </div>\n\n        {/* Language Reminder */}\n        <div style={{\n          marginTop: '20px',\n          padding: '16px',\n          backgroundColor: '#fefce8',\n          border: '1px solid #fde047',\n          borderRadius: '8px',\n          textAlign: 'center'\n        }}>\n          <p style={{\n            margin: '0',\n            fontSize: '14px',\n            color: '#854d0e',\n            fontWeight: '500'\n          }}>\n            📝 Currently editing: <strong>{language === 'en' ? 'English' : 'Arabic'}</strong> content. \n            Switch languages using the toggle above to edit both versions.\n          </p>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default CareerManager;\n\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,OAAOC,cAAc,MAAM,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExD,MAAMC,aAAa,GAAGA,CAAC;EACrBC,OAAO;EACPC,OAAO;EACPC,QAAQ;EACRC,WAAW;EACXC;AACF,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,UAAA,EAAAC,kBAAA,EAAAC,qBAAA,EAAAC,UAAA,EAAAC,kBAAA,EAAAC,qBAAA,EAAAC,UAAA,EAAAC,kBAAA,EAAAC,qBAAA,EAAAC,UAAA,EAAAC,kBAAA,EAAAC,qBAAA;EACJ,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG1B,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAE5D;EACA,MAAM2B,oBAAoB,GAAIC,QAAQ,IAAK;IACzCF,mBAAmB,CAACG,IAAI,IAAI;MAC1B,MAAMC,SAAS,GAAG;QAAE,GAAGD;MAAK,CAAC;MAC7B,OAAOC,SAAS,CAACF,QAAQ,CAAC;MAC1B,OAAOE,SAAS;IAClB,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMC,aAAa,GAAGA,CAACC,KAAK,EAAEC,SAAS,KAAK;IAC1C,IAAI,CAACD,KAAK,IAAK,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACE,IAAI,CAAC,CAAC,KAAK,EAAG,EAAE;MAChE,OAAO,GAAGD,SAAS,cAAc;IACnC;IACA,IAAI,OAAOD,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACE,IAAI,CAAC,CAAC,CAACC,MAAM,GAAG,EAAE,EAAE;MACzD,OAAO,GAAGF,SAAS,iCAAiC;IACtD;IACA,OAAO,IAAI;EACb,CAAC;EAED,MAAMG,iBAAiB,GAAGlC,WAAW,CAAC,MAAM;IAAA,IAAAmC,SAAA,EAAAC,iBAAA,EAAAC,qBAAA,EAAAC,UAAA,EAAAC,kBAAA,EAAAC,qBAAA,EAAAC,UAAA,EAAAC,kBAAA,EAAAC,qBAAA,EAAAC,UAAA,EAAAC,kBAAA,EAAAC,qBAAA;IAC1C,MAAMC,MAAM,GAAG,CAAC,CAAC;IACjB,IAAIC,SAAS,GAAG,KAAK;;IAErB;IACA,MAAMC,WAAW,GAAG5C,OAAO,aAAPA,OAAO,wBAAA8B,SAAA,GAAP9B,OAAO,CAAG,CAAC,CAAC,cAAA8B,SAAA,wBAAAC,iBAAA,GAAZD,SAAA,CAAc9B,OAAO,cAAA+B,iBAAA,wBAAAC,qBAAA,GAArBD,iBAAA,CAAuBc,KAAK,cAAAb,qBAAA,uBAA5BA,qBAAA,CAA+B9B,QAAQ,CAAC;IAC5D,MAAM4C,iBAAiB,GAAG9C,OAAO,aAAPA,OAAO,wBAAAiC,UAAA,GAAPjC,OAAO,CAAG,CAAC,CAAC,cAAAiC,UAAA,wBAAAC,kBAAA,GAAZD,UAAA,CAAcjC,OAAO,cAAAkC,kBAAA,wBAAAC,qBAAA,GAArBD,kBAAA,CAAuBa,WAAW,cAAAZ,qBAAA,uBAAlCA,qBAAA,CAAqCjC,QAAQ,CAAC;IACxE,MAAM8C,kBAAkB,GAAGhD,OAAO,aAAPA,OAAO,wBAAAoC,UAAA,GAAPpC,OAAO,CAAG,CAAC,CAAC,cAAAoC,UAAA,wBAAAC,kBAAA,GAAZD,UAAA,CAAcpC,OAAO,cAAAqC,kBAAA,wBAAAC,qBAAA,GAArBD,kBAAA,CAAuBY,YAAY,cAAAX,qBAAA,uBAAnCA,qBAAA,CAAsCpC,QAAQ,CAAC;IAC1E,MAAMgD,kBAAkB,GAAGlD,OAAO,aAAPA,OAAO,wBAAAuC,UAAA,GAAPvC,OAAO,CAAG,CAAC,CAAC,cAAAuC,UAAA,wBAAAC,kBAAA,GAAZD,UAAA,CAAcvC,OAAO,cAAAwC,kBAAA,wBAAAC,qBAAA,GAArBD,kBAAA,CAAuBW,YAAY,cAAAV,qBAAA,uBAAnCA,qBAAA,CAAsCvC,QAAQ,CAAC;IAE1E,IAAIsB,aAAa,CAACoB,WAAW,EAAE,OAAO,CAAC,EAAE;MACvCF,MAAM,CAAC,cAAc,CAAC,GAAGlB,aAAa,CAACoB,WAAW,EAAE,OAAO,CAAC;MAC5DD,SAAS,GAAG,IAAI;IAClB;IACA,IAAInB,aAAa,CAACsB,iBAAiB,EAAE,iBAAiB,CAAC,EAAE;MACvDJ,MAAM,CAAC,oBAAoB,CAAC,GAAGlB,aAAa,CAACsB,iBAAiB,EAAE,iBAAiB,CAAC;MAClFH,SAAS,GAAG,IAAI;IAClB;IACA,IAAInB,aAAa,CAACwB,kBAAkB,EAAE,kBAAkB,CAAC,EAAE;MACzDN,MAAM,CAAC,qBAAqB,CAAC,GAAGlB,aAAa,CAACwB,kBAAkB,EAAE,kBAAkB,CAAC;MACrFL,SAAS,GAAG,IAAI;IAClB;IACA,IAAInB,aAAa,CAAC0B,kBAAkB,EAAE,iBAAiB,CAAC,EAAE;MACxDR,MAAM,CAAC,qBAAqB,CAAC,GAAGlB,aAAa,CAAC0B,kBAAkB,EAAE,iBAAiB,CAAC;MACpFP,SAAS,GAAG,IAAI;IAClB;IAEAxB,mBAAmB,CAACuB,MAAM,CAAC;IAC3B,OAAO,CAACC,SAAS;EACnB,CAAC,EAAE,CAAC3C,OAAO,EAAEE,QAAQ,CAAC,CAAC;;EAEvB;EACAR,SAAS,CAAC,MAAM;IACd,IAAI,CAACU,YAAY,EAAE;MACjByB,iBAAiB,CAAC,CAAC;IACrB;EACF,CAAC,EAAE,CAAC7B,OAAO,EAAEE,QAAQ,EAAEE,YAAY,EAAEyB,iBAAiB,CAAC,CAAC;EAExD,oBACE/B,OAAA;IAAKsD,KAAK,EAAE;MACVC,OAAO,EAAE,MAAM;MACfC,eAAe,EAAE,SAAS;MAC1BC,SAAS,EAAE;IACb,CAAE;IAAAC,QAAA,eACA1D,OAAA;MAAKsD,KAAK,EAAE;QACVK,QAAQ,EAAE,OAAO;QACjBC,MAAM,EAAE,QAAQ;QAChBJ,eAAe,EAAE,SAAS;QAC1BK,YAAY,EAAE,MAAM;QACpBN,OAAO,EAAE,MAAM;QACfO,SAAS,EAAE;MACb,CAAE;MAAAJ,QAAA,gBAEA1D,OAAA;QAAKsD,KAAK,EAAE;UACVS,YAAY,EAAE,MAAM;UACpBC,aAAa,EAAE,MAAM;UACrBC,YAAY,EAAE;QAChB,CAAE;QAAAP,QAAA,gBACA1D,OAAA;UAAIsD,KAAK,EAAE;YACTY,QAAQ,EAAE,MAAM;YAChBC,UAAU,EAAE,KAAK;YACjBC,KAAK,EAAE,SAAS;YAChBL,YAAY,EAAE;UAChB,CAAE;UAAAL,QAAA,EAAC;QAEH;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLxE,OAAA;UAAGsD,KAAK,EAAE;YACRY,QAAQ,EAAE,MAAM;YAChBE,KAAK,EAAE,SAAS;YAChBK,UAAU,EAAE;UACd,CAAE;UAAAf,QAAA,EAAC;QAEH;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,EAGLE,MAAM,CAACC,IAAI,CAACvD,gBAAgB,CAAC,CAACU,MAAM,GAAG,CAAC,iBACvC9B,OAAA;QAAKsD,KAAK,EAAE;UACVE,eAAe,EAAE,SAAS;UAC1BoB,MAAM,EAAE,mBAAmB;UAC3Bf,YAAY,EAAE,KAAK;UACnBN,OAAO,EAAE,MAAM;UACfQ,YAAY,EAAE;QAChB,CAAE;QAAAL,QAAA,gBACA1D,OAAA;UAAIsD,KAAK,EAAE;YACTY,QAAQ,EAAE,MAAM;YAChBC,UAAU,EAAE,KAAK;YACjBC,KAAK,EAAE,SAAS;YAChBL,YAAY,EAAE;UAChB,CAAE;UAAAL,QAAA,EAAC;QAEH;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLxE,OAAA;UAAIsD,KAAK,EAAE;YACTM,MAAM,EAAE,GAAG;YACXiB,WAAW,EAAE,MAAM;YACnBT,KAAK,EAAE,SAAS;YAChBF,QAAQ,EAAE;UACZ,CAAE;UAAAR,QAAA,EACCgB,MAAM,CAACI,OAAO,CAAC1D,gBAAgB,CAAC,CAAC2D,GAAG,CAAC,CAAC,CAACC,GAAG,EAAEC,KAAK,CAAC,kBACjDjF,OAAA;YAAcsD,KAAK,EAAE;cAAES,YAAY,EAAE;YAAM,CAAE;YAAAL,QAAA,EAAEuB;UAAK,GAA3CD,GAAG;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAA6C,CAC1D;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CACN,eAGDxE,OAAA,CAACF,cAAc;QACbO,WAAW,EAAEA,WAAY;QACzB6E,OAAO,EAAC,cAAc;QACtBC,UAAU,EAAC,2EAA2E;QACtFC,OAAO,EAAE,GAAI;QACbC,YAAY,EAAElF,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAG,CAAC,CAAE;QAC3BC,QAAQ,EAAEA,QAAS;QACnBE,YAAY,EAAEA,YAAa;QAC3BgF,cAAc,EAAEpF,OAAQ;QACxBoB,oBAAoB,EAAEA,oBAAqB;QAC3CiE,MAAM,EAAE,CACN;UACEC,KAAK,EAAE,OAAO;UACdC,KAAK,EAAE,OAAO;UACdC,UAAU,EAAE,OAAO;UACnB/D,KAAK,EAAEzB,OAAO,aAAPA,OAAO,wBAAAM,UAAA,GAAPN,OAAO,CAAG,CAAC,CAAC,cAAAM,UAAA,wBAAAC,kBAAA,GAAZD,UAAA,CAAcN,OAAO,cAAAO,kBAAA,wBAAAC,qBAAA,GAArBD,kBAAA,CAAuBsC,KAAK,cAAArC,qBAAA,uBAA5BA,qBAAA,CAA+BN,QAAQ,CAAC;UAC/CuF,SAAS,EAAE,GAAG;UACdC,YAAY,EAAExE,gBAAgB,CAAC,cAAc,CAAC;UAC9CG,QAAQ,EAAE;QACZ,CAAC,EACD;UACEiE,KAAK,EAAE,UAAU;UACjBC,KAAK,EAAE,qCAAqC;UAC5CC,UAAU,EAAE,aAAa;UACzB/D,KAAK,EAAEzB,OAAO,aAAPA,OAAO,wBAAAS,UAAA,GAAPT,OAAO,CAAG,CAAC,CAAC,cAAAS,UAAA,wBAAAC,kBAAA,GAAZD,UAAA,CAAcT,OAAO,cAAAU,kBAAA,wBAAAC,qBAAA,GAArBD,kBAAA,CAAuBqC,WAAW,cAAApC,qBAAA,uBAAlCA,qBAAA,CAAqCT,QAAQ,CAAC;UACrDuF,SAAS,EAAE,IAAI;UACfE,IAAI,EAAE,CAAC;UACPD,YAAY,EAAExE,gBAAgB,CAAC,oBAAoB,CAAC;UACpDG,QAAQ,EAAE;QACZ,CAAC,EACD;UACEiE,KAAK,EAAE,UAAU;UACjBC,KAAK,EAAE,oCAAoC;UAC3CC,UAAU,EAAE,cAAc;UAC1B/D,KAAK,EAAEzB,OAAO,aAAPA,OAAO,wBAAAY,UAAA,GAAPZ,OAAO,CAAG,CAAC,CAAC,cAAAY,UAAA,wBAAAC,kBAAA,GAAZD,UAAA,CAAcZ,OAAO,cAAAa,kBAAA,wBAAAC,qBAAA,GAArBD,kBAAA,CAAuBoC,YAAY,cAAAnC,qBAAA,uBAAnCA,qBAAA,CAAsCZ,QAAQ,CAAC;UACtDuF,SAAS,EAAE,IAAI;UACfE,IAAI,EAAE,CAAC;UACPD,YAAY,EAAExE,gBAAgB,CAAC,qBAAqB,CAAC;UACrDG,QAAQ,EAAE;QACZ,CAAC,EACD;UACEiE,KAAK,EAAE,UAAU;UACjBC,KAAK,EAAE,2CAA2C;UAClDC,UAAU,EAAE,cAAc;UAC1B/D,KAAK,EAAEzB,OAAO,aAAPA,OAAO,wBAAAe,UAAA,GAAPf,OAAO,CAAG,CAAC,CAAC,cAAAe,UAAA,wBAAAC,kBAAA,GAAZD,UAAA,CAAcf,OAAO,cAAAgB,kBAAA,wBAAAC,qBAAA,GAArBD,kBAAA,CAAuBmC,YAAY,cAAAlC,qBAAA,uBAAnCA,qBAAA,CAAsCf,QAAQ,CAAC;UACtDuF,SAAS,EAAE,IAAI;UACfE,IAAI,EAAE,CAAC;UACPD,YAAY,EAAExE,gBAAgB,CAAC,qBAAqB,CAAC;UACrDG,QAAQ,EAAE;QACZ,CAAC;MACD;QAAA8C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGFxE,OAAA;QAAKsD,KAAK,EAAE;UACVwC,SAAS,EAAE,MAAM;UACjBvC,OAAO,EAAE,MAAM;UACfC,eAAe,EAAE,SAAS;UAC1BoB,MAAM,EAAE,mBAAmB;UAC3Bf,YAAY,EAAE;QAChB,CAAE;QAAAH,QAAA,gBACA1D,OAAA;UAAIsD,KAAK,EAAE;YACTY,QAAQ,EAAE,MAAM;YAChBC,UAAU,EAAE,KAAK;YACjBC,KAAK,EAAE,SAAS;YAChBL,YAAY,EAAE;UAChB,CAAE;UAAAL,QAAA,EAAC;QAEH;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLxE,OAAA;UAAIsD,KAAK,EAAE;YACTM,MAAM,EAAE,GAAG;YACXiB,WAAW,EAAE,MAAM;YACnBT,KAAK,EAAE,SAAS;YAChBF,QAAQ,EAAE,MAAM;YAChBO,UAAU,EAAE;UACd,CAAE;UAAAf,QAAA,gBACA1D,OAAA;YAAA0D,QAAA,GAAI,MAAI,eAAA1D,OAAA;cAAA0D,QAAA,EAAQ;YAAK;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,8DAAwD;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC3FxE,OAAA;YAAA0D,QAAA,GAAI,MAAI,eAAA1D,OAAA;cAAA0D,QAAA,EAAQ;YAAe;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,iDAA6C;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC1FxE,OAAA;YAAA0D,QAAA,GAAI,MAAI,eAAA1D,OAAA;cAAA0D,QAAA,EAAQ;YAAgB;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,oDAAgD;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC9FxE,OAAA;YAAA0D,QAAA,GAAI,MAAI,eAAA1D,OAAA;cAAA0D,QAAA,EAAQ;YAAe;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,sDAAkD;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC/FxE,OAAA;YAAA0D,QAAA,EAAI;UAA8C;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACvDxE,OAAA;YAAA0D,QAAA,EAAI;UAAmD;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC5DxE,OAAA;YAAA0D,QAAA,EAAI;UAA2D;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAGNxE,OAAA;QAAKsD,KAAK,EAAE;UACVwC,SAAS,EAAE,MAAM;UACjBvC,OAAO,EAAE,MAAM;UACfC,eAAe,EAAE,SAAS;UAC1BoB,MAAM,EAAE,mBAAmB;UAC3Bf,YAAY,EAAE,KAAK;UACnBkC,SAAS,EAAE;QACb,CAAE;QAAArC,QAAA,eACA1D,OAAA;UAAGsD,KAAK,EAAE;YACRM,MAAM,EAAE,GAAG;YACXM,QAAQ,EAAE,MAAM;YAChBE,KAAK,EAAE,SAAS;YAChBD,UAAU,EAAE;UACd,CAAE;UAAAT,QAAA,GAAC,kCACqB,eAAA1D,OAAA;YAAA0D,QAAA,EAAStD,QAAQ,KAAK,IAAI,GAAG,SAAS,GAAG;UAAQ;YAAAiE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS,CAAC,4EAEnF;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACjE,EAAA,CAnPIN,aAAa;AAAA+F,EAAA,GAAb/F,aAAa;AAqPnB,eAAeA,aAAa;AAAC,IAAA+F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}