import React, { useState, useEffect, useCallback } from 'react';
import FileUploader from "../../../../components/Input/InputFileUploader";
import ContentSection from "../breakUI/ContentSections";
import createContent from "../../defineContent";
import { useSelector } from "react-redux";

const CareerManager = ({ 
  language, 
  currentPath, 
  outOfEditing 
}) => {
  const [validationErrors, setValidationErrors] = useState({});

  const currentContent = useSelector((state) => state.homeContent.present);
  const { content, indexes } = createContent(currentContent, "edit");

  // Function to clear specific validation error
  const clearValidationError = (errorKey) => {
    setValidationErrors(prev => {
      const newErrors = { ...prev };
      delete newErrors[errorKey];
      return newErrors;
    });
  };

  // Validation functions
  const validateField = (value, fieldName) => {
    if (!value || (typeof value === 'string' && value.trim() === '')) {
      return "Required";
    }
    return null;
  };

  const validateAllFields = useCallback(() => {
    const errors = {};
    let hasErrors = false;

    // Banner Section validation
    const bannerTitle = content?.[1]?.content?.title?.[language];
    const bannerDescription = content?.[1]?.content?.description?.[language];
    const bannerDescription2 = content?.[1]?.content?.description2?.[language];
    const bannerDescription3 = content?.[1]?.content?.description3?.[language];

    if (validateField(bannerTitle, "Title")) {
      errors["banner_title"] = validateField(bannerTitle, "Title");
      hasErrors = true;
    }
    if (validateField(bannerDescription, "First Paragraph")) {
      errors["banner_description"] = validateField(bannerDescription, "First Paragraph");
      hasErrors = true;
    }
    if (validateField(bannerDescription2, "Second Paragraph")) {
      errors["banner_description2"] = validateField(bannerDescription2, "Second Paragraph");
      hasErrors = true;
    }
    if (validateField(bannerDescription3, "Third Paragraph")) {
      errors["banner_description3"] = validateField(bannerDescription3, "Third Paragraph");
      hasErrors = true;
    }

    setValidationErrors(errors);
    return !hasErrors;
  }, [content, language]);

  // Validate on content or language change
  useEffect(() => {
    if (!outOfEditing) {
      validateAllFields();
    }
  }, [content, language, outOfEditing, validateAllFields]);

  // Expose validation function to parent via window object for submit validation
  useEffect(() => {
    window.validateCareerContent = validateAllFields;
    return () => {
      delete window.validateCareerContent;
    };
  }, [validateAllFields]);

  return (
    <div className="w-full">
      {/* Reference doc */}
      <FileUploader 
        id={"careerReference"} 
        label={"Rerference doc"} 
        fileName={"Upload your file..."} 
        outOfEditing={outOfEditing} 
      />
      
      {/* Banner Section */}
      <ContentSection
        currentPath={currentPath}
        Heading={"Hero Banner"}
        inputs={[
          { 
            input: "input", 
            label: "Title", 
            updateType: "title", 
            value: content?.[1]?.content?.title?.[language], 
            maxLength: 100, 
            errorMessage: validationErrors["banner_title"], 
            errorKey: "banner_title" 
          },
          { 
            input: "textarea", 
            label: "First Paragraph", 
            updateType: "description", 
            maxLength: 1000, 
            value: content?.[1]?.content?.description?.[language], 
            errorMessage: validationErrors["banner_description"], 
            errorKey: "banner_description" 
          },
          { 
            input: "textarea", 
            label: "Second Paragraph", 
            updateType: "description2", 
            maxLength: 1000, 
            value: content?.[1]?.content?.description2?.[language], 
            errorMessage: validationErrors["banner_description2"], 
            errorKey: "banner_description2" 
          },
          { 
            input: "textarea", 
            label: "Third Paragraph", 
            updateType: "description3", 
            maxLength: 1000, 
            value: content?.[1]?.content?.description3?.[language], 
            errorMessage: validationErrors["banner_description3"], 
            errorKey: "banner_description3" 
          }
        ]}
        section={"bannerSection"}
        language={language}
        currentContent={content}
        sectionIndex={indexes?.[1]}
        outOfEditing={outOfEditing}
        clearValidationError={clearValidationError}
      />
    </div>
  );
};

export default CareerManager;
