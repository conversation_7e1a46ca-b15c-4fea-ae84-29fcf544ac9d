import Joi from "joi";
import validate from "./validator.js";

// Job validation schemas
const jobCreationSchema = Joi.object({
  title: Joi.string().min(3).max(200).required().messages({
    'string.empty': 'Job title is required',
    'string.min': 'Job title must be at least 3 characters long',
    'string.max': 'Job title cannot exceed 200 characters'
  }),
  
  category: Joi.string().valid(
    'ENGINEERING', 'MANAGEMENT', 'DESIGN', 'MARKETING', 'SALES', 
    'HR', 'FINANCE', 'OPERATIONS', 'IT', 'CONSTRUCTION', 
    'PROJECT_MANAGEMENT', 'QUALITY_ASSURANCE', 'SAFETY', 'OTHER'
  ).required().messages({
    'any.only': 'Invalid job category',
    'any.required': 'Job category is required'
  }),
  
  department: Joi.string().max(100).optional(),
  
  description: Joi.string().min(50).required().messages({
    'string.empty': 'Job description is required',
    'string.min': 'Job description must be at least 50 characters long'
  }),
  
  shortDescription: Joi.string().max(500).optional(),
  
  responsibilities: Joi.array().items(Joi.string()).optional(),
  requirements: Joi.array().items(Joi.string()).optional(),
  qualifications: Joi.array().items(Joi.string()).optional(),
  benefits: Joi.array().items(Joi.string()).optional(),
  
  jobType: Joi.string().valid(
    'FULL_TIME', 'PART_TIME', 'CONTRACT', 'INTERNSHIP', 'REMOTE', 'HYBRID'
  ).default('FULL_TIME').messages({
    'any.only': 'Invalid job type'
  }),
  
  experienceLevel: Joi.string().valid(
    'ENTRY_LEVEL', 'MID_LEVEL', 'SENIOR_LEVEL', 'EXECUTIVE', 'INTERNSHIP'
  ).default('ENTRY_LEVEL').messages({
    'any.only': 'Invalid experience level'
  }),
  
  minExperience: Joi.number().integer().min(0).max(50).optional(),
  maxExperience: Joi.number().integer().min(0).max(50).optional(),
  
  location: Joi.string().min(2).max(100).required().messages({
    'string.empty': 'Job location is required',
    'string.min': 'Location must be at least 2 characters long',
    'string.max': 'Location cannot exceed 100 characters'
  }),
  
  isRemote: Joi.boolean().default(false),
  isHybrid: Joi.boolean().default(false),
  
  minSalary: Joi.number().positive().optional(),
  maxSalary: Joi.number().positive().optional(),
  currency: Joi.string().length(3).default('SAR').optional(),
  salaryPeriod: Joi.string().valid('HOURLY', 'DAILY', 'WEEKLY', 'MONTHLY', 'YEARLY').default('MONTHLY').optional(),
  
  applicationDeadline: Joi.date().greater('now').optional().messages({
    'date.greater': 'Application deadline must be in the future'
  }),
  
  applicationEmail: Joi.string().email().optional().messages({
    'string.email': 'Invalid email format'
  }),
  
  applicationUrl: Joi.string().uri().optional().messages({
    'string.uri': 'Invalid URL format'
  }),
  
  applicationInstructions: Joi.string().max(1000).optional(),
  
  metaTitle: Joi.string().max(60).optional(),
  metaDescription: Joi.string().max(160).optional(),
  keywords: Joi.array().items(Joi.string()).optional(),
  
  status: Joi.string().valid('ACTIVE', 'INACTIVE', 'CLOSED', 'DRAFT').default('DRAFT').messages({
    'any.only': 'Invalid job status'
  }),
  
  isPublished: Joi.boolean().default(false),
  isFeatured: Joi.boolean().default(false)
}).custom((value, helpers) => {
  // Custom validation: maxExperience should be greater than minExperience
  if (value.minExperience && value.maxExperience && value.maxExperience < value.minExperience) {
    return helpers.error('custom.maxExperienceInvalid');
  }
  
  // Custom validation: maxSalary should be greater than minSalary
  if (value.minSalary && value.maxSalary && value.maxSalary < value.minSalary) {
    return helpers.error('custom.maxSalaryInvalid');
  }
  
  return value;
}).messages({
  'custom.maxExperienceInvalid': 'Maximum experience must be greater than minimum experience',
  'custom.maxSalaryInvalid': 'Maximum salary must be greater than minimum salary'
});

const jobUpdateSchema = Joi.object({
  title: Joi.string().min(3).max(200).optional(),
  category: Joi.string().valid(
    'ENGINEERING', 'MANAGEMENT', 'DESIGN', 'MARKETING', 'SALES', 
    'HR', 'FINANCE', 'OPERATIONS', 'IT', 'CONSTRUCTION', 
    'PROJECT_MANAGEMENT', 'QUALITY_ASSURANCE', 'SAFETY', 'OTHER'
  ).optional(),
  department: Joi.string().max(100).optional(),
  description: Joi.string().min(50).optional(),
  shortDescription: Joi.string().max(500).optional(),
  responsibilities: Joi.array().items(Joi.string()).optional(),
  requirements: Joi.array().items(Joi.string()).optional(),
  qualifications: Joi.array().items(Joi.string()).optional(),
  benefits: Joi.array().items(Joi.string()).optional(),
  jobType: Joi.string().valid(
    'FULL_TIME', 'PART_TIME', 'CONTRACT', 'INTERNSHIP', 'REMOTE', 'HYBRID'
  ).optional(),
  experienceLevel: Joi.string().valid(
    'ENTRY_LEVEL', 'MID_LEVEL', 'SENIOR_LEVEL', 'EXECUTIVE', 'INTERNSHIP'
  ).optional(),
  minExperience: Joi.number().integer().min(0).max(50).optional(),
  maxExperience: Joi.number().integer().min(0).max(50).optional(),
  location: Joi.string().min(2).max(100).optional(),
  isRemote: Joi.boolean().optional(),
  isHybrid: Joi.boolean().optional(),
  minSalary: Joi.number().positive().optional(),
  maxSalary: Joi.number().positive().optional(),
  currency: Joi.string().length(3).optional(),
  salaryPeriod: Joi.string().valid('HOURLY', 'DAILY', 'WEEKLY', 'MONTHLY', 'YEARLY').optional(),
  applicationDeadline: Joi.date().greater('now').optional(),
  applicationEmail: Joi.string().email().optional(),
  applicationUrl: Joi.string().uri().optional(),
  applicationInstructions: Joi.string().max(1000).optional(),
  metaTitle: Joi.string().max(60).optional(),
  metaDescription: Joi.string().max(160).optional(),
  keywords: Joi.array().items(Joi.string()).optional(),
  status: Joi.string().valid('ACTIVE', 'INACTIVE', 'CLOSED', 'DRAFT').optional(),
  isPublished: Joi.boolean().optional(),
  isFeatured: Joi.boolean().optional()
}).custom((value, helpers) => {
  // Custom validation: maxExperience should be greater than minExperience
  if (value.minExperience && value.maxExperience && value.maxExperience < value.minExperience) {
    return helpers.error('custom.maxExperienceInvalid');
  }
  
  // Custom validation: maxSalary should be greater than minSalary
  if (value.minSalary && value.maxSalary && value.maxSalary < value.minSalary) {
    return helpers.error('custom.maxSalaryInvalid');
  }
  
  return value;
}).messages({
  'custom.maxExperienceInvalid': 'Maximum experience must be greater than minimum experience',
  'custom.maxSalaryInvalid': 'Maximum salary must be greater than minimum salary'
});

// Validation middleware functions
export const validateJobCreation = validate(jobCreationSchema);

export const validateJobUpdate = validate(jobUpdateSchema);
