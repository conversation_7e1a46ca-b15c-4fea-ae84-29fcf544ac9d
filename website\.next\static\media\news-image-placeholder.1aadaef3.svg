<svg width="280" height="154" viewBox="0 0 280 154" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="newsGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#fefce8;stop-opacity:0.8" />
      <stop offset="100%" style="stop-color:#fef3c7;stop-opacity:0.6" />
    </linearGradient>
    <filter id="blur" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur in="SourceGraphic" stdDeviation="1"/>
    </filter>
  </defs>
  
  <!-- Background -->
  <rect width="280" height="154" fill="url(#newsGradient)"/>
  
  <!-- Newspaper Icon -->
  <rect x="40" y="30" width="200" height="120" fill="rgba(148,163,184,0.1)" rx="4" opacity="0.6" filter="url(#blur)"/>
  <rect x="50" y="40" width="180" height="100" fill="rgba(148,163,184,0.08)" rx="2" opacity="0.5" filter="url(#blur)"/>
  
  <!-- Headlines -->
  <rect x="60" y="50" width="120" height="4" fill="rgba(148,163,184,0.3)" rx="2" opacity="0.4" filter="url(#blur)"/>
  <rect x="60" y="60" width="100" height="3" fill="rgba(148,163,184,0.25)" rx="1.5" opacity="0.4" filter="url(#blur)"/>
  <rect x="60" y="68" width="80" height="3" fill="rgba(148,163,184,0.25)" rx="1.5" opacity="0.4" filter="url(#blur)"/>
  
  <!-- Article Text Lines -->
  <rect x="60" y="80" width="160" height="2" fill="rgba(148,163,184,0.2)" rx="1" opacity="0.3"/>
  <rect x="60" y="85" width="150" height="2" fill="rgba(148,163,184,0.2)" rx="1" opacity="0.3"/>
  <rect x="60" y="90" width="140" height="2" fill="rgba(148,163,184,0.2)" rx="1" opacity="0.3"/>
  <rect x="60" y="95" width="130" height="2" fill="rgba(148,163,184,0.2)" rx="1" opacity="0.3"/>
  
  <!-- Date -->
  <rect x="60" y="110" width="60" height="2" fill="rgba(148,163,184,0.15)" rx="1" opacity="0.3"/>
  
  <!-- News Icon -->
  <circle cx="220" cy="70" r="15" fill="rgba(148,163,184,0.1)" opacity="0.4" filter="url(#blur)"/>
  <rect x="215" y="65" width="10" height="10" fill="rgba(148,163,184,0.2)" rx="1" opacity="0.4" filter="url(#blur)"/>
</svg>
