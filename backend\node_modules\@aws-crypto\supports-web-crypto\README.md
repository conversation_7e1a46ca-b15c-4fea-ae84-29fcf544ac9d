# @aws-crypto/supports-web-crypto

Functions to check web crypto support for browsers.

## Usage

```
import {supportsWebCrypto} from '@aws-crypto/supports-web-crypto';

if (supportsWebCrypto(window)) {
  // window.crypto.subtle.encrypt will exist
}

```

## supportsWebCrypto

Used to make sure `window.crypto.subtle` exists and implements crypto functions
as well as a cryptographic secure random source exists.

## supportsSecureRandom

Used to make sure that a cryptographic secure random source exists.
Does not check for `window.crypto.subtle`.

## supportsSubtleCrypto

## supportsZeroByteGCM

## Test

`npm test`
