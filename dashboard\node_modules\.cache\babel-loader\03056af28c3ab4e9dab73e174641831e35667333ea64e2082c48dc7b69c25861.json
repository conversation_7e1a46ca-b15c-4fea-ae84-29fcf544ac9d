{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Akshay\\\\LOOP_PROJECTS\\\\shade_cms\\\\dashboard\\\\src\\\\features\\\\Resources\\\\components\\\\AllForOne.jsx\",\n  _s = $RefreshSig$();\nimport HomePage from \"./websiteComponent/Home\";\nimport SolutionPage from \"./websiteComponent/Solutions\";\nimport AboutUs from \"./websiteComponent/About\";\nimport MarketPage from \"./websiteComponent/Market\";\nimport ProjectPage from \"./websiteComponent/Projects\";\nimport CareerPage from \"./websiteComponent/CareersPage\";\nimport NewsPage from \"./websiteComponent/NewsPage\";\nimport Footer from \"./websiteComponent/subparts/Footerweb\";\nimport Header from \"./websiteComponent/subparts/Headerweb\";\nimport ProjectDetailPage from \"./websiteComponent/detailspages/ProjectDetails\";\nimport CareerDetailPage from \"./websiteComponent/detailspages/CareersDetails\";\nimport NewsBlogDetailPage from \"./websiteComponent/detailspages/NewsDetails\";\nimport Testimonials from \"./websiteComponent/subparts/Testimonials\";\nimport ContactUsModal from \"./websiteComponent/subparts/ContactUsModal\";\nimport Services from \"./websiteComponent/Service\";\nimport ServiceDetails from \"./websiteComponent/detailspages/ServiceDetails\";\nimport SubServiceDetails from \"./websiteComponent/subDetailsPages/SubServiceDetails\";\nimport tempContent from \"./websiteComponent/content.json\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useEffect, useRef, useState } from \"react\";\nimport { updateMainContent } from \"../../common/homeContentSlice\";\nimport SnR from \"./websiteComponent/SafetyAndResponsibility\";\nimport SnRPolicies from \"./websiteComponent/detailspages/SnRPolicies\";\nimport History from \"./websiteComponent/HistoryPage\";\nimport VisionNMission from \"./websiteComponent/VisionPage\";\nimport HSnE from \"./websiteComponent/HSE\";\nimport MarketDetails from \"./websiteComponent/detailspages/MarketDetails\";\nimport AffiliatesPage from \"./websiteComponent/Affiliates\";\nimport Organization from \"./websiteComponent/Organizational\";\nimport TemplateOne from \"./websiteComponent/TemplateOne\";\nimport TemplateTwo from \"./websiteComponent/TemplateTwo\";\nimport TemplateThree from \"./websiteComponent/TempThree\";\nimport TemplateFour from \"./websiteComponent/TemplateFour\";\nimport ContactUsPage from \"./websiteComponent/ContactUsPage\";\n\n// import { useDispatch, useSelector } from \"react-redux\";\n// import { useEffect, useRef, useState } from \"react\";\n// import { updateMainContent } from \"../../common/homeContentSlice\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AllForOne = ({\n  language,\n  screen,\n  content,\n  setLanguage,\n  fullScreen,\n  currentPath,\n  subPath,\n  deepPath,\n  showDifference = false,\n  live,\n  hideScroll,\n  purpose\n}) => {\n  _s();\n  // console.log(currentPath, subPath, deepPath)\n  const isComputer = screen > 1100;\n  const isTablet = 1100 > screen && screen > 767;\n  const isPhone = screen < 767;\n  const dispatch = useDispatch();\n  const fontRegular = useSelector(state => state.fontStyle.regular);\n  const divRef = useRef(null);\n  const [width, setWidth] = useState(0);\n  useEffect(() => {\n    const observer = new ResizeObserver(entries => {\n      for (let entry of entries) {\n        setWidth(entry.contentRect.width);\n      }\n    });\n    if (divRef.current) observer.observe(divRef.current);\n    return () => {\n      if (divRef.current) observer.unobserve(divRef.current);\n    };\n  }, []);\n  const baseProps = {\n    width,\n    language,\n    screen,\n    highlight: showDifference,\n    liveContent: live,\n    fullScreen,\n    purpose\n  };\n  const renderPage = () => {\n    switch (currentPath) {\n      case \"home\":\n        return /*#__PURE__*/_jsxDEV(HomePage, {\n          ...baseProps,\n          content: content\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 72,\n          columnNumber: 24\n        }, this);\n      case \"solution\":\n        return /*#__PURE__*/_jsxDEV(SolutionPage, {\n          ...baseProps,\n          currentContent: content\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 74,\n          columnNumber: 24\n        }, this);\n      case \"about-us\":\n        return /*#__PURE__*/_jsxDEV(AboutUs, {\n          ...baseProps,\n          currentContent: content\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 76,\n          columnNumber: 24\n        }, this);\n      case \"service\":\n        if (subPath) {\n          return deepPath ? /*#__PURE__*/_jsxDEV(SubServiceDetails, {\n            ...baseProps,\n            serviceId: subPath,\n            content: content,\n            deepPath: deepPath\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 80,\n            columnNumber: 27\n          }, this) : /*#__PURE__*/_jsxDEV(ServiceDetails, {\n            ...baseProps,\n            serviceId: subPath,\n            content: content\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 81,\n            columnNumber: 27\n          }, this);\n        }\n        return /*#__PURE__*/_jsxDEV(Services, {\n          ...baseProps,\n          currentContent: content\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 83,\n          columnNumber: 24\n        }, this);\n      case \"market\":\n        return subPath ? /*#__PURE__*/_jsxDEV(MarketDetails, {\n          ...baseProps,\n          content: content\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 86,\n          columnNumber: 21\n        }, this) : /*#__PURE__*/_jsxDEV(MarketPage, {\n          ...baseProps,\n          currentContent: content\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 87,\n          columnNumber: 23\n        }, this);\n      case \"projects\":\n      case \"project\":\n        return subPath ? /*#__PURE__*/_jsxDEV(ProjectDetailPage, {\n          ...baseProps,\n          projectId: subPath,\n          content: content\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 92,\n          columnNumber: 23\n        }, this) : /*#__PURE__*/_jsxDEV(ProjectPage, {\n          ...baseProps,\n          currentContent: content\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 93,\n          columnNumber: 23\n        }, this);\n      case \"careers\":\n        return /*#__PURE__*/_jsxDEV(Footer, {\n          ...baseProps,\n          currentContent: content\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 95,\n          columnNumber: 24\n        }, this);\n      case \"news-blogs\":\n        return subPath ? /*#__PURE__*/_jsxDEV(NewsBlogDetailPage, {\n          ...baseProps,\n          newsId: subPath,\n          contentOn: content\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 99,\n          columnNumber: 23\n        }, this) : /*#__PURE__*/_jsxDEV(NewsPage, {\n          ...baseProps,\n          content: content\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 100,\n          columnNumber: 23\n        }, this);\n      case \"news\":\n        return /*#__PURE__*/_jsxDEV(NewsBlogDetailPage, {\n          ...baseProps,\n          newsId: subPath,\n          content: content\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 102,\n          columnNumber: 24\n        }, this);\n      case \"footer\":\n        return /*#__PURE__*/_jsxDEV(Footer, {\n          ...baseProps,\n          currentContent: content\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 105,\n          columnNumber: 24\n        }, this);\n      case \"header\":\n        return /*#__PURE__*/_jsxDEV(Header, {\n          ...baseProps,\n          currentContent: content,\n          setLanguage: setLanguage\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 107,\n          columnNumber: 24\n        }, this);\n      case \"testimonials\":\n      case \"testimonial\":\n        return /*#__PURE__*/_jsxDEV(Testimonials, {\n          ...baseProps,\n          testimonyId: subPath,\n          currentContent: content\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 110,\n          columnNumber: 24\n        }, this);\n      case \"contactus-modal\":\n        return /*#__PURE__*/_jsxDEV(ContactUsModal, {\n          ...baseProps,\n          currentContent: content\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 112,\n          columnNumber: 24\n        }, this);\n      case \"safety\":\n      case \"safety_responsibility\":\n        return subPath ? /*#__PURE__*/_jsxDEV(SnRPolicies, {\n          ...baseProps,\n          currentContent: content\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 116,\n          columnNumber: 23\n        }, this) : /*#__PURE__*/_jsxDEV(SnR, {\n          ...baseProps,\n          currentContent: content\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 117,\n          columnNumber: 23\n        }, this);\n      case \"history\":\n        return /*#__PURE__*/_jsxDEV(History, {\n          ...baseProps,\n          currentContent: content\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 120,\n          columnNumber: 24\n        }, this);\n      case \"vision\":\n        return /*#__PURE__*/_jsxDEV(VisionNMission, {\n          ...baseProps,\n          currentContent: content\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 123,\n          columnNumber: 24\n        }, this);\n      case \"hse\":\n        return /*#__PURE__*/_jsxDEV(HSnE, {\n          ...baseProps,\n          currentContent: content\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 126,\n          columnNumber: 24\n        }, this);\n      case \"affiliates\":\n        return /*#__PURE__*/_jsxDEV(AffiliatesPage, {\n          ...baseProps,\n          content: content\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 129,\n          columnNumber: 24\n        }, this);\n      case \"organization\":\n        return /*#__PURE__*/_jsxDEV(Organization, {\n          ...baseProps,\n          content: content\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 132,\n          columnNumber: 24\n        }, this);\n      case \"temp-1\":\n        return /*#__PURE__*/_jsxDEV(TemplateOne, {\n          ...baseProps,\n          content: content\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 135,\n          columnNumber: 24\n        }, this);\n      case \"temp-2\":\n        return /*#__PURE__*/_jsxDEV(TemplateTwo, {\n          ...baseProps,\n          content: content\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 138,\n          columnNumber: 24\n        }, this);\n      case \"temp-3\":\n        return /*#__PURE__*/_jsxDEV(TemplateThree, {\n          ...baseProps,\n          content: content\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 141,\n          columnNumber: 24\n        }, this);\n      case \"temp-4\":\n        return /*#__PURE__*/_jsxDEV(TemplateFour, {\n          ...baseProps,\n          content: content\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 144,\n          columnNumber: 24\n        }, this);\n      case \"contact-us\":\n        return /*#__PURE__*/_jsxDEV(ContactUsPage, {\n          ...baseProps,\n          content: content\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 24\n        }, this);\n      default:\n        return null;\n    }\n  };\n\n  // console.log(fullScreen)\n\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    ref: divRef,\n    className: `dark:text-[#2A303C] mt-0 transition-custom border-stone-200 border mx-auto w-full bankgothic-medium-dt ${fontRegular} bg-[white] \n            ${fullScreen ? \"overflow-y-hidden\" : \"overflow-y-scroll\"} \n            ${hideScroll ? \"rm-scroll\" : \"customscroller\"}`,\n    style: {\n      width: fullScreen ? isComputer ? \"100%\" : screen : screen > 950 ? \"100%\" : screen,\n      wordBreak: \"break-word\"\n    },\n    children: renderPage()\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 157,\n    columnNumber: 9\n  }, this);\n};\n_s(AllForOne, \"T5YnMwzC/VYX8yIY53dS90fXh2o=\", false, function () {\n  return [useDispatch, useSelector];\n});\n_c = AllForOne;\nexport default AllForOne;\nvar _c;\n$RefreshReg$(_c, \"AllForOne\");", "map": {"version": 3, "names": ["HomePage", "SolutionPage", "AboutUs", "MarketPage", "ProjectPage", "CareerPage", "NewsPage", "Footer", "Header", "ProjectDetailPage", "CareerDetailPage", "NewsBlogDetailPage", "Testimonials", "ContactUsModal", "Services", "ServiceDetails", "SubServiceDetails", "temp<PERSON><PERSON><PERSON>", "useDispatch", "useSelector", "useEffect", "useRef", "useState", "update<PERSON>ain<PERSON><PERSON>nt", "SnR", "SnRPolicies", "History", "VisionNMission", "HSnE", "MarketDetails", "AffiliatesPage", "Organization", "TemplateOne", "TemplateTwo", "Template<PERSON><PERSON><PERSON>", "TemplateFour", "ContactUsPage", "jsxDEV", "_jsxDEV", "AllForOne", "language", "screen", "content", "setLanguage", "fullScreen", "currentPath", "subPath", "<PERSON><PERSON><PERSON>", "showDifference", "live", "hideScroll", "purpose", "_s", "isComputer", "isTablet", "isPhone", "dispatch", "fontRegular", "state", "fontStyle", "regular", "divRef", "width", "<PERSON><PERSON><PERSON><PERSON>", "observer", "ResizeObserver", "entries", "entry", "contentRect", "current", "observe", "unobserve", "baseProps", "highlight", "liveContent", "renderPage", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "currentC<PERSON>nt", "serviceId", "projectId", "newsId", "contentOn", "testimonyId", "ref", "className", "style", "wordBreak", "children", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Akshay/LOOP_PROJECTS/shade_cms/dashboard/src/features/Resources/components/AllForOne.jsx"], "sourcesContent": ["import HomePage from \"./websiteComponent/Home\";\nimport SolutionPage from \"./websiteComponent/Solutions\";\nimport AboutUs from \"./websiteComponent/About\";\nimport MarketPage from \"./websiteComponent/Market\";\nimport ProjectPage from \"./websiteComponent/Projects\";\nimport CareerPage from \"./websiteComponent/CareersPage\";\nimport NewsPage from \"./websiteComponent/NewsPage\";\nimport Footer from \"./websiteComponent/subparts/Footerweb\";\nimport Header from \"./websiteComponent/subparts/Headerweb\";\nimport ProjectDetailPage from \"./websiteComponent/detailspages/ProjectDetails\";\nimport CareerDetailPage from \"./websiteComponent/detailspages/CareersDetails\";\nimport NewsBlogDetailPage from \"./websiteComponent/detailspages/NewsDetails\";\nimport Testimonials from \"./websiteComponent/subparts/Testimonials\";\nimport ContactUsModal from \"./websiteComponent/subparts/ContactUsModal\";\nimport Services from \"./websiteComponent/Service\";\nimport ServiceDetails from \"./websiteComponent/detailspages/ServiceDetails\";\nimport SubServiceDetails from \"./websiteComponent/subDetailsPages/SubServiceDetails\";\nimport tempContent from \"./websiteComponent/content.json\"\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useEffect, useRef, useState } from \"react\";\nimport { updateMainContent } from \"../../common/homeContentSlice\";\nimport SnR from \"./websiteComponent/SafetyAndResponsibility\";\nimport SnRPolicies from \"./websiteComponent/detailspages/SnRPolicies\";\nimport History from \"./websiteComponent/HistoryPage\";\nimport VisionNMission from \"./websiteComponent/VisionPage\";\nimport HSnE from \"./websiteComponent/HSE\";\nimport MarketDetails from \"./websiteComponent/detailspages/MarketDetails\";\nimport AffiliatesPage from \"./websiteComponent/Affiliates\";\nimport Organization from \"./websiteComponent/Organizational\";\nimport TemplateOne from \"./websiteComponent/TemplateOne\";\nimport TemplateTwo from \"./websiteComponent/TemplateTwo\";\nimport TemplateThree from \"./websiteComponent/TempThree\";\nimport TemplateFour from \"./websiteComponent/TemplateFour\";\nimport ContactUsPage from \"./websiteComponent/ContactUsPage\";\n\n// import { useDispatch, useSelector } from \"react-redux\";\n// import { useEffect, useRef, useState } from \"react\";\n// import { updateMainContent } from \"../../common/homeContentSlice\";\n\nconst AllForOne = ({\n    language, screen, content, setLanguage, fullScreen,\n    currentPath, subPath, deepPath, showDifference = false, live, hideScroll,\n    purpose\n}) => {\n    // console.log(currentPath, subPath, deepPath)\n    const isComputer = screen > 1100;\n    const isTablet = 1100 > screen && screen > 767;\n    const isPhone = screen < 767;\n    const dispatch = useDispatch();\n    const fontRegular = useSelector(state => state.fontStyle.regular);\n    const divRef = useRef(null);\n    const [width, setWidth] = useState(0);\n\n    useEffect(() => {\n        const observer = new ResizeObserver(entries => {\n            for (let entry of entries) {\n                setWidth(entry.contentRect.width);\n            }\n        });\n\n        if (divRef.current) observer.observe(divRef.current);\n        return () => {\n            if (divRef.current) observer.unobserve(divRef.current);\n        };\n    }, []);\n\n    const baseProps = { width, language, screen, highlight: showDifference, liveContent: live, fullScreen, purpose };\n\n    const renderPage = () => {\n        switch (currentPath) {\n            case \"home\":\n                return <HomePage {...baseProps} content={content} />;\n            case \"solution\":\n                return <SolutionPage {...baseProps} currentContent={content} />;\n            case \"about-us\":\n                return <AboutUs {...baseProps} currentContent={content} />;\n            case \"service\":\n                if (subPath) {\n                    return deepPath\n                        ? <SubServiceDetails {...baseProps} serviceId={subPath} content={content} deepPath={deepPath} />\n                        : <ServiceDetails {...baseProps} serviceId={subPath} content={content} />;\n                }\n                return <Services {...baseProps} currentContent={content} />;\n            case \"market\":\n                return subPath ?\n                    <MarketDetails {...baseProps} content={content} />\n                    : <MarketPage {...baseProps} currentContent={content} />;\n\n            case \"projects\":\n            case \"project\":\n                return subPath\n                    ? <ProjectDetailPage {...baseProps} projectId={subPath} content={content} />\n                    : <ProjectPage {...baseProps} currentContent={content} />;\n            case \"careers\":\n                return <Footer {...baseProps} currentContent={content} />;\n\n            case \"news-blogs\":\n                return subPath\n                    ? <NewsBlogDetailPage {...baseProps} newsId={subPath} contentOn={content} />\n                    : <NewsPage {...baseProps} content={content} />;\n            case \"news\":\n                return <NewsBlogDetailPage {...baseProps} newsId={subPath} content={content} />;\n\n            case \"footer\":\n                return <Footer {...baseProps} currentContent={content} />;\n            case \"header\":\n                return <Header {...baseProps} currentContent={content} setLanguage={setLanguage} />;\n            case \"testimonials\":\n            case \"testimonial\":\n                return <Testimonials {...baseProps} testimonyId={subPath} currentContent={content} />;\n            case \"contactus-modal\":\n                return <ContactUsModal {...baseProps} currentContent={content} />;\n            case \"safety\":\n            case \"safety_responsibility\":\n                return subPath\n                    ? <SnRPolicies {...baseProps} currentContent={content} />\n                    : <SnR {...baseProps} currentContent={content} />;\n\n            case \"history\":\n                return <History {...baseProps} currentContent={content} />;\n\n            case \"vision\":\n                return <VisionNMission {...baseProps} currentContent={content} />\n\n            case \"hse\":\n                return <HSnE {...baseProps} currentContent={content} />\n\n            case \"affiliates\":\n                return <AffiliatesPage {...baseProps} content={content} />\n\n            case \"organization\":\n                return <Organization {...baseProps} content={content} />\n\n            case \"temp-1\":\n                return <TemplateOne {...baseProps} content={content} />\n\n            case \"temp-2\":\n                return <TemplateTwo {...baseProps} content={content} />\n\n            case \"temp-3\":\n                return <TemplateThree {...baseProps} content={content} />\n\n            case \"temp-4\":\n                return <TemplateFour {...baseProps} content={content} />\n\n            case \"contact-us\":\n                return <ContactUsPage {...baseProps} content={content} />\n\n            default:\n                return null;\n        }\n    };\n\n    // console.log(fullScreen)\n\n    return (\n        <div\n            ref={divRef}\n            className={`dark:text-[#2A303C] mt-0 transition-custom border-stone-200 border mx-auto w-full bankgothic-medium-dt ${fontRegular} bg-[white] \n            ${fullScreen ? \"overflow-y-hidden\" : \"overflow-y-scroll\"} \n            ${hideScroll ? \"rm-scroll\" : \"customscroller\"}`}\n            style={{\n                width: (fullScreen) ? isComputer ? \"100%\" : screen : screen > 950 ? \"100%\" : screen,\n                wordBreak: \"break-word\",\n            }}\n        >\n            {renderPage()}\n        </div>\n    );\n};\n\nexport default AllForOne;\n"], "mappings": ";;AAAA,OAAOA,QAAQ,MAAM,yBAAyB;AAC9C,OAAOC,YAAY,MAAM,8BAA8B;AACvD,OAAOC,OAAO,MAAM,0BAA0B;AAC9C,OAAOC,UAAU,MAAM,2BAA2B;AAClD,OAAOC,WAAW,MAAM,6BAA6B;AACrD,OAAOC,UAAU,MAAM,gCAAgC;AACvD,OAAOC,QAAQ,MAAM,6BAA6B;AAClD,OAAOC,MAAM,MAAM,uCAAuC;AAC1D,OAAOC,MAAM,MAAM,uCAAuC;AAC1D,OAAOC,iBAAiB,MAAM,gDAAgD;AAC9E,OAAOC,gBAAgB,MAAM,gDAAgD;AAC7E,OAAOC,kBAAkB,MAAM,6CAA6C;AAC5E,OAAOC,YAAY,MAAM,0CAA0C;AACnE,OAAOC,cAAc,MAAM,4CAA4C;AACvE,OAAOC,QAAQ,MAAM,4BAA4B;AACjD,OAAOC,cAAc,MAAM,gDAAgD;AAC3E,OAAOC,iBAAiB,MAAM,sDAAsD;AACpF,OAAOC,WAAW,MAAM,iCAAiC;AACzD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AACnD,SAASC,iBAAiB,QAAQ,+BAA+B;AACjE,OAAOC,GAAG,MAAM,4CAA4C;AAC5D,OAAOC,WAAW,MAAM,6CAA6C;AACrE,OAAOC,OAAO,MAAM,gCAAgC;AACpD,OAAOC,cAAc,MAAM,+BAA+B;AAC1D,OAAOC,IAAI,MAAM,wBAAwB;AACzC,OAAOC,aAAa,MAAM,+CAA+C;AACzE,OAAOC,cAAc,MAAM,+BAA+B;AAC1D,OAAOC,YAAY,MAAM,mCAAmC;AAC5D,OAAOC,WAAW,MAAM,gCAAgC;AACxD,OAAOC,WAAW,MAAM,gCAAgC;AACxD,OAAOC,aAAa,MAAM,8BAA8B;AACxD,OAAOC,YAAY,MAAM,iCAAiC;AAC1D,OAAOC,aAAa,MAAM,kCAAkC;;AAE5D;AACA;AACA;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAEA,MAAMC,SAAS,GAAGA,CAAC;EACfC,QAAQ;EAAEC,MAAM;EAAEC,OAAO;EAAEC,WAAW;EAAEC,UAAU;EAClDC,WAAW;EAAEC,OAAO;EAAEC,QAAQ;EAAEC,cAAc,GAAG,KAAK;EAAEC,IAAI;EAAEC,UAAU;EACxEC;AACJ,CAAC,KAAK;EAAAC,EAAA;EACF;EACA,MAAMC,UAAU,GAAGZ,MAAM,GAAG,IAAI;EAChC,MAAMa,QAAQ,GAAG,IAAI,GAAGb,MAAM,IAAIA,MAAM,GAAG,GAAG;EAC9C,MAAMc,OAAO,GAAGd,MAAM,GAAG,GAAG;EAC5B,MAAMe,QAAQ,GAAGtC,WAAW,CAAC,CAAC;EAC9B,MAAMuC,WAAW,GAAGtC,WAAW,CAACuC,KAAK,IAAIA,KAAK,CAACC,SAAS,CAACC,OAAO,CAAC;EACjE,MAAMC,MAAM,GAAGxC,MAAM,CAAC,IAAI,CAAC;EAC3B,MAAM,CAACyC,KAAK,EAAEC,QAAQ,CAAC,GAAGzC,QAAQ,CAAC,CAAC,CAAC;EAErCF,SAAS,CAAC,MAAM;IACZ,MAAM4C,QAAQ,GAAG,IAAIC,cAAc,CAACC,OAAO,IAAI;MAC3C,KAAK,IAAIC,KAAK,IAAID,OAAO,EAAE;QACvBH,QAAQ,CAACI,KAAK,CAACC,WAAW,CAACN,KAAK,CAAC;MACrC;IACJ,CAAC,CAAC;IAEF,IAAID,MAAM,CAACQ,OAAO,EAAEL,QAAQ,CAACM,OAAO,CAACT,MAAM,CAACQ,OAAO,CAAC;IACpD,OAAO,MAAM;MACT,IAAIR,MAAM,CAACQ,OAAO,EAAEL,QAAQ,CAACO,SAAS,CAACV,MAAM,CAACQ,OAAO,CAAC;IAC1D,CAAC;EACL,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMG,SAAS,GAAG;IAAEV,KAAK;IAAEtB,QAAQ;IAAEC,MAAM;IAAEgC,SAAS,EAAEzB,cAAc;IAAE0B,WAAW,EAAEzB,IAAI;IAAEL,UAAU;IAAEO;EAAQ,CAAC;EAEhH,MAAMwB,UAAU,GAAGA,CAAA,KAAM;IACrB,QAAQ9B,WAAW;MACf,KAAK,MAAM;QACP,oBAAOP,OAAA,CAACtC,QAAQ;UAAA,GAAKwE,SAAS;UAAE9B,OAAO,EAAEA;QAAQ;UAAAkC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACxD,KAAK,UAAU;QACX,oBAAOzC,OAAA,CAACrC,YAAY;UAAA,GAAKuE,SAAS;UAAEQ,cAAc,EAAEtC;QAAQ;UAAAkC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACnE,KAAK,UAAU;QACX,oBAAOzC,OAAA,CAACpC,OAAO;UAAA,GAAKsE,SAAS;UAAEQ,cAAc,EAAEtC;QAAQ;UAAAkC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC9D,KAAK,SAAS;QACV,IAAIjC,OAAO,EAAE;UACT,OAAOC,QAAQ,gBACTT,OAAA,CAACtB,iBAAiB;YAAA,GAAKwD,SAAS;YAAES,SAAS,EAAEnC,OAAQ;YAACJ,OAAO,EAAEA,OAAQ;YAACK,QAAQ,EAAEA;UAAS;YAAA6B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAC9FzC,OAAA,CAACvB,cAAc;YAAA,GAAKyD,SAAS;YAAES,SAAS,EAAEnC,OAAQ;YAACJ,OAAO,EAAEA;UAAQ;YAAAkC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QACjF;QACA,oBAAOzC,OAAA,CAACxB,QAAQ;UAAA,GAAK0D,SAAS;UAAEQ,cAAc,EAAEtC;QAAQ;UAAAkC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC/D,KAAK,QAAQ;QACT,OAAOjC,OAAO,gBACVR,OAAA,CAACT,aAAa;UAAA,GAAK2C,SAAS;UAAE9B,OAAO,EAAEA;QAAQ;UAAAkC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAChDzC,OAAA,CAACnC,UAAU;UAAA,GAAKqE,SAAS;UAAEQ,cAAc,EAAEtC;QAAQ;UAAAkC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAEhE,KAAK,UAAU;MACf,KAAK,SAAS;QACV,OAAOjC,OAAO,gBACRR,OAAA,CAAC7B,iBAAiB;UAAA,GAAK+D,SAAS;UAAEU,SAAS,EAAEpC,OAAQ;UAACJ,OAAO,EAAEA;QAAQ;UAAAkC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAC1EzC,OAAA,CAAClC,WAAW;UAAA,GAAKoE,SAAS;UAAEQ,cAAc,EAAEtC;QAAQ;UAAAkC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACjE,KAAK,SAAS;QACV,oBAAOzC,OAAA,CAAC/B,MAAM;UAAA,GAAKiE,SAAS;UAAEQ,cAAc,EAAEtC;QAAQ;UAAAkC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAE7D,KAAK,YAAY;QACb,OAAOjC,OAAO,gBACRR,OAAA,CAAC3B,kBAAkB;UAAA,GAAK6D,SAAS;UAAEW,MAAM,EAAErC,OAAQ;UAACsC,SAAS,EAAE1C;QAAQ;UAAAkC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAC1EzC,OAAA,CAAChC,QAAQ;UAAA,GAAKkE,SAAS;UAAE9B,OAAO,EAAEA;QAAQ;UAAAkC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACvD,KAAK,MAAM;QACP,oBAAOzC,OAAA,CAAC3B,kBAAkB;UAAA,GAAK6D,SAAS;UAAEW,MAAM,EAAErC,OAAQ;UAACJ,OAAO,EAAEA;QAAQ;UAAAkC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAEnF,KAAK,QAAQ;QACT,oBAAOzC,OAAA,CAAC/B,MAAM;UAAA,GAAKiE,SAAS;UAAEQ,cAAc,EAAEtC;QAAQ;UAAAkC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC7D,KAAK,QAAQ;QACT,oBAAOzC,OAAA,CAAC9B,MAAM;UAAA,GAAKgE,SAAS;UAAEQ,cAAc,EAAEtC,OAAQ;UAACC,WAAW,EAAEA;QAAY;UAAAiC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACvF,KAAK,cAAc;MACnB,KAAK,aAAa;QACd,oBAAOzC,OAAA,CAAC1B,YAAY;UAAA,GAAK4D,SAAS;UAAEa,WAAW,EAAEvC,OAAQ;UAACkC,cAAc,EAAEtC;QAAQ;UAAAkC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACzF,KAAK,iBAAiB;QAClB,oBAAOzC,OAAA,CAACzB,cAAc;UAAA,GAAK2D,SAAS;UAAEQ,cAAc,EAAEtC;QAAQ;UAAAkC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACrE,KAAK,QAAQ;MACb,KAAK,uBAAuB;QACxB,OAAOjC,OAAO,gBACRR,OAAA,CAACb,WAAW;UAAA,GAAK+C,SAAS;UAAEQ,cAAc,EAAEtC;QAAQ;UAAAkC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBACvDzC,OAAA,CAACd,GAAG;UAAA,GAAKgD,SAAS;UAAEQ,cAAc,EAAEtC;QAAQ;UAAAkC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAEzD,KAAK,SAAS;QACV,oBAAOzC,OAAA,CAACZ,OAAO;UAAA,GAAK8C,SAAS;UAAEQ,cAAc,EAAEtC;QAAQ;UAAAkC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAE9D,KAAK,QAAQ;QACT,oBAAOzC,OAAA,CAACX,cAAc;UAAA,GAAK6C,SAAS;UAAEQ,cAAc,EAAEtC;QAAQ;UAAAkC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAErE,KAAK,KAAK;QACN,oBAAOzC,OAAA,CAACV,IAAI;UAAA,GAAK4C,SAAS;UAAEQ,cAAc,EAAEtC;QAAQ;UAAAkC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAE3D,KAAK,YAAY;QACb,oBAAOzC,OAAA,CAACR,cAAc;UAAA,GAAK0C,SAAS;UAAE9B,OAAO,EAAEA;QAAQ;UAAAkC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAE9D,KAAK,cAAc;QACf,oBAAOzC,OAAA,CAACP,YAAY;UAAA,GAAKyC,SAAS;UAAE9B,OAAO,EAAEA;QAAQ;UAAAkC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAE5D,KAAK,QAAQ;QACT,oBAAOzC,OAAA,CAACN,WAAW;UAAA,GAAKwC,SAAS;UAAE9B,OAAO,EAAEA;QAAQ;UAAAkC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAE3D,KAAK,QAAQ;QACT,oBAAOzC,OAAA,CAACL,WAAW;UAAA,GAAKuC,SAAS;UAAE9B,OAAO,EAAEA;QAAQ;UAAAkC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAE3D,KAAK,QAAQ;QACT,oBAAOzC,OAAA,CAACJ,aAAa;UAAA,GAAKsC,SAAS;UAAE9B,OAAO,EAAEA;QAAQ;UAAAkC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAE7D,KAAK,QAAQ;QACT,oBAAOzC,OAAA,CAACH,YAAY;UAAA,GAAKqC,SAAS;UAAE9B,OAAO,EAAEA;QAAQ;UAAAkC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAE5D,KAAK,YAAY;QACb,oBAAOzC,OAAA,CAACF,aAAa;UAAA,GAAKoC,SAAS;UAAE9B,OAAO,EAAEA;QAAQ;UAAAkC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAE7D;QACI,OAAO,IAAI;IACnB;EACJ,CAAC;;EAED;;EAEA,oBACIzC,OAAA;IACIgD,GAAG,EAAEzB,MAAO;IACZ0B,SAAS,EAAE,0GAA0G9B,WAAW;AAC5I,cAAcb,UAAU,GAAG,mBAAmB,GAAG,mBAAmB;AACpE,cAAcM,UAAU,GAAG,WAAW,GAAG,gBAAgB,EAAG;IAChDsC,KAAK,EAAE;MACH1B,KAAK,EAAGlB,UAAU,GAAIS,UAAU,GAAG,MAAM,GAAGZ,MAAM,GAAGA,MAAM,GAAG,GAAG,GAAG,MAAM,GAAGA,MAAM;MACnFgD,SAAS,EAAE;IACf,CAAE;IAAAC,QAAA,EAEDf,UAAU,CAAC;EAAC;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACZ,CAAC;AAEd,CAAC;AAAC3B,EAAA,CAlIIb,SAAS;EAAA,QASMrB,WAAW,EACRC,WAAW;AAAA;AAAAwE,EAAA,GAV7BpD,SAAS;AAoIf,eAAeA,SAAS;AAAC,IAAAoD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}