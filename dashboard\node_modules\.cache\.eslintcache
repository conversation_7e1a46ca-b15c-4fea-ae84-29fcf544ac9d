[{"C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\index.js": "1", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\App.js": "2", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\reportWebVitals.js": "3", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\app\\store.js": "4", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\containers\\SuspenseContent.jsx": "5", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Context\\Context.js": "6", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\app\\auth.js": "7", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\app\\init.js": "8", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\containers\\Layout.jsx": "9", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\pages\\ForgotPassword.js": "10", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\pages\\Documentation.js": "11", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\pages\\Register.js": "12", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\pages\\Login.js": "13", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\common\\modalSlice.js": "14", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\common\\headerSlice.js": "15", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\common\\userSlice.js": "16", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\common\\debounceSlice.js": "17", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\common\\SbStateSlice.js": "18", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\common\\resourceSlice.js": "19", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\common\\rightDrawerSlice.js": "20", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\common\\routeLists.js": "21", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\common\\InitialContentSlice.js": "22", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\common\\homeContentSlice.js": "23", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\common\\platformSlice.js": "24", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\common\\navbarSlice.js": "25", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\common\\fontStyle.js": "26", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\common\\ImagesSlice.js": "27", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\leadSlice.js": "28", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\components\\fallbackLoader\\FallbackLoader.jsx": "29", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\Socket\\socket.js": "30", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\app\\fetch.js": "31", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\containers\\LeftSidebar.jsx": "32", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\containers\\RightSidebar.jsx": "33", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\containers\\ModalLayout.jsx": "34", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\containers\\PageContent.jsx": "35", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\utils\\globalConstantUtil.js": "36", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\app\\checkUser.js": "37", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\user\\Register.js": "38", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\components\\Typography\\Title.js": "39", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\user\\Login.jsx": "40", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\user\\ForgotPassword.jsx": "41", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\documentation\\components\\FeaturesNav.js": "42", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\documentation\\components\\GettingStartedNav.js": "43", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\documentation\\components\\GettingStartedContent.js": "44", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\documentation\\components\\DocComponentsContent.js": "45", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\documentation\\components\\DocComponentsNav.js": "46", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\documentation\\components\\FeaturesContent.js": "47", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\VersionDetails.jsx": "48", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Component\\ToastPlacer.jsx": "49", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Requests\\RequestDetails.jsx": "50", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\app\\emailregex.js": "51", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\app\\toastify.js": "52", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\app\\deviceId.js": "53", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\app\\valid.js": "54", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\user\\ResetPasswordModalBody.jsx": "55", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\containers\\Header.jsx": "56", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\routes\\backend.js": "57", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\routes\\sidebar.js": "58", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\common\\components\\NotificationBodyRightDrawer.js": "59", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\common\\components\\ConfirmationModalBody.js": "60", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\pages\\protected\\404.js": "61", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\calendar\\CalendarEventsBodyRightDrawer.js": "62", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\user\\LandingIntro.jsx": "63", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\components\\Typography\\ErrorText.jsx": "64", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\components\\Typography\\Subtitle.js": "65", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\components\\Typography\\HelperText.js": "66", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\components\\Input\\InputText.jsx": "67", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\user\\components\\OTP.jsx": "68", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\user\\components\\BackroundImg.jsx": "69", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\user\\components\\PasswordValidation.jsx": "70", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\user\\components\\UpdatePassword.jsx": "71", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\components\\Button\\Button.jsx": "72", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\routes\\index.jsx": "73", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\components\\Cards\\TitleCard.jsx": "74", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\app\\TimeFormat.js": "75", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\app\\capitalizeword.js": "76", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Context\\CustomContext.js": "77", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\components\\Input\\SearchBar.jsx": "78", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Requests\\Comments.jsx": "79", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Component\\Toaster.jsx": "80", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Component\\Paginations.jsx": "81", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\components\\CalendarView\\util.js": "82", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\app\\OTP.jsx": "83", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\pages\\DocFeatures.js": "84", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\pages\\DocComponents.js": "85", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\pages\\protected\\ProfileSettings.js": "86", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\pages\\protected\\Reminders.js": "87", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\pages\\protected\\Roles.js": "88", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\pages\\protected\\Calendar.js": "89", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\pages\\protected\\Integration.js": "90", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\pages\\protected\\Bills.js": "91", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\pages\\protected\\Requests.js": "92", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\pages\\protected\\Logs.js": "93", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\pages\\protected\\Resources.js": "94", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\pages\\protected\\Team.js": "95", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\pages\\protected\\Blank.js": "96", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\pages\\GettingStarted.js": "97", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\pages\\protected\\Users.js": "98", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\pages\\protected\\Dashboard.js": "99", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\pages\\protected\\Welcome.js": "100", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\documentation\\DocComponents.js": "101", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\documentation\\DocFeatures.js": "102", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\documentation\\DocGettingStarted.js": "103", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\index.jsx": "104", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\calendar\\index.js": "105", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\user\\components\\TemplatePointers.js": "106", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Requests\\index.jsx": "107", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\dashboard\\index.js": "108", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\logstable\\index.jsx": "109", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Reminders\\index.jsx": "110", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Roles\\index.jsx": "111", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\charts\\index.jsx": "112", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\integration\\index.js": "113", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\settings\\team\\index.js": "114", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\settings\\billing\\index.js": "115", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\settings\\profilesettings\\index.jsx": "116", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Requests\\Showdifference.jsx": "117", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\utils\\dummyData.js": "118", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Requests\\ShowVerifierTooltip.jsx": "119", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Requests\\ShowPDF.jsx": "120", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\Resources.jsx": "121", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\EditPage.jsx": "122", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\VersionTable.jsx": "123", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\RequestTable.jsx": "124", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\components\\Input\\ToogleInput.js": "125", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\components\\CalendarView\\index.js": "126", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\components\\Toggle\\Toggle.jsx": "127", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\components\\Input\\Select.jsx": "128", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\components\\Input\\TextAreaInput.jsx": "129", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\logstable\\ShowLog.jsx": "130", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\dashboard\\components\\DashboardStats.js": "131", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\dashboard\\components\\AmountStats.js": "132", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\dashboard\\components\\PageStats.js": "133", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\dashboard\\components\\LineChart.js": "134", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\dashboard\\components\\DashboardTopBar.js": "135", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\dashboard\\components\\BarChart.js": "136", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\dashboard\\components\\UserChannels.js": "137", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\dashboard\\components\\DoughnutChart.js": "138", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Roles\\AddRole.jsx": "139", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Roles\\ShowRole.jsx": "140", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\charts\\ShowRole.jsx": "141", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\charts\\AddUser.jsx": "142", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\components\\Input\\DirectInputFile.jsx": "143", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\defineContent.js": "144", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Requests\\RejectPopup.jsx": "145", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Requests\\DateTime.jsx": "146", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\containers\\Navbar.jsx": "147", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\ResourceCard.jsx": "148", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\components\\Button\\CloseButton.jsx": "149", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\NewProjectDialog.jsx": "150", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\breakUI\\Popups.jsx": "151", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\AllForOne.jsx": "152", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\breakUI\\SwitchLang.jsx": "153", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\breakUI\\ConfigBar.jsx": "154", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\AllForOneManager.jsx": "155", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\breakUI\\PageDetails.jsx": "156", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\breakUI\\ContentTopBar.jsx": "157", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\components\\Input\\InputFileForm.jsx": "158", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\structures\\PageStructure.jsx": "159", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\components\\Loader\\SkeletonLoader.jsx": "160", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\resourcedata.js": "161", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\breakUI\\DropDownMenu.jsx": "162", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\Home.jsx": "163", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\About.jsx": "164", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\Projects.jsx": "165", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\Solutions.jsx": "166", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\Market.jsx": "167", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\CareersPage.jsx": "168", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\NewsPage.jsx": "169", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\Service.jsx": "170", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\HistoryPage.jsx": "171", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\SafetyAndResponsibility.jsx": "172", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\VisionPage.jsx": "173", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\HSE.jsx": "174", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\Affiliates.jsx": "175", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\subparts\\Footerweb.jsx": "176", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\subparts\\Testimonials.jsx": "177", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\subparts\\ContactUsModal.jsx": "178", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\subparts\\Headerweb.jsx": "179", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\detailspages\\ProjectDetails.jsx": "180", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\detailspages\\ServiceDetails.jsx": "181", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\detailspages\\CareersDetails.jsx": "182", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\detailspages\\SnRPolicies.jsx": "183", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\detailspages\\NewsDetails.jsx": "184", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\detailspages\\MarketDetails.jsx": "185", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\Organizational.jsx": "186", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\TemplateOne.jsx": "187", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\TemplateTwo.jsx": "188", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\app\\convertContent.js": "189", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\TempThree.jsx": "190", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\breakUI\\SelectorAccordion.jsx": "191", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\breakUI\\Statusbar.jsx": "192", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\TemplateFour.jsx": "193", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\components\\Input\\ImageSelector.jsx": "194", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\subDetailsPages\\SubServiceDetails.jsx": "195", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\contentmanager\\SolutionManager.jsx": "196", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\contentmanager\\HomeManager.jsx": "197", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\contentmanager\\MarketManager.jsx": "198", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\contentmanager\\AboutManager.jsx": "199", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\contentmanager\\ProjectContentManager.jsx": "200", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\contentmanager\\NewsManager.jsx": "201", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\contentmanager\\HistoryManager.jsx": "202", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\contentmanager\\ServiceManager.jsx": "203", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\contentmanager\\SnRPolicyManager.jsx": "204", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\contentmanager\\CareersManager.jsx": "205", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\contentmanager\\SnRManager.jsx": "206", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\contentmanager\\AffiliatesManager.jsx": "207", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\contentmanager\\VisionManager.jsx": "208", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\contentmanager\\OrganizationManager.jsx": "209", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\contentmanager\\TemplateOneManager.jsx": "210", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\contentmanager\\HSnEManager.jsx": "211", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\contentmanager\\TemplateThreeManager.jsx": "212", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\contentmanager\\TemplateFourManager.jsx": "213", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\contentmanager\\CMforDetails\\CareerDetailManager.jsx": "214", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\contentmanager\\CMforDetails\\ProjectDetailManager.jsx": "215", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\contentmanager\\CMforDetails\\MarketDetailsManager.jsx": "216", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\contentmanager\\CMforDetails\\NewsDetailsManager.jsx": "217", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\contentmanager\\CMforDetails\\ServiceDetailsManager.jsx": "218", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\contentmanager\\CMforSubParts\\HeaderManager.jsx": "219", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\contentmanager\\CMforSubParts\\TestimonyManager.jsx": "220", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\contentmanager\\CMforSubParts\\FooterManager.jsx": "221", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\contentmanager\\subDetailsManagement\\SubServiceDetailManagement.jsx": "222", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\contentmanager\\TemplateTwoManager.jsx": "223", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\app\\fontSizes.js": "224", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\assets\\index.js": "225", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\subparts\\Pagination.jsx": "226", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\subparts\\ModalPortal.jsx": "227", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\components\\Input\\useImageUpload.js": "228", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\breakUI\\ContentSections.jsx": "229", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\breakUI\\MultiSelect.jsx": "230", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\components\\Input\\InputFileUploader.jsx": "231", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\breakUI\\MultiSelectPro.jsx": "232", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\breakUI\\DynamicContentSection.jsx": "233", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\breakUI\\MultiSelectSM.jsx": "234", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\components\\Input\\InputFile.jsx": "235", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\common\\thunk\\smsThunk.js": "236", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\pages\\protected\\ContactQueries.js": "237", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\ContactQueries\\index.jsx": "238", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\components\\TableStates\\TableSkeleton.jsx": "239", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\components\\TableStates\\ErrorState.jsx": "240", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\components\\TableStates\\EmptyState.jsx": "241", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\breakUI\\ScrollableParagraph.jsx": "242", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\breakUI\\ScrollableHtml.jsx": "243", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\components\\Input\\UploadProgress.jsx": "244", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\components\\Input\\useBatchUpload.js": "245", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\ContactUsPage.jsx": "246", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\contentmanager\\ContactUsManager.jsx": "247", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\pages\\protected\\Jobs.js": "248", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\jobs\\index.js": "249", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\jobs\\components\\JobsTable.js": "250", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\jobs\\components\\JobModal.js": "251"}, {"size": 672, "mtime": 1758275343652, "results": "252", "hashOfConfig": "253"}, {"size": 1536, "mtime": 1758275343257, "results": "254", "hashOfConfig": "253"}, {"size": 375, "mtime": 1744971878440, "results": "255", "hashOfConfig": "253"}, {"size": 1498, "mtime": 1758275343264, "results": "256", "hashOfConfig": "253"}, {"size": 298, "mtime": 1758275343542, "results": "257", "hashOfConfig": "253"}, {"size": 611, "mtime": 1758275343544, "results": "258", "hashOfConfig": "253"}, {"size": 1373, "mtime": 1745213846544, "results": "259", "hashOfConfig": "253"}, {"size": 491, "mtime": 1745213862545, "results": "260", "hashOfConfig": "253"}, {"size": 4582, "mtime": 1758275365452, "results": "261", "hashOfConfig": "253"}, {"size": 349, "mtime": 1745213847378, "results": "262", "hashOfConfig": "253"}, {"size": 1838, "mtime": 1745213847378, "results": "263", "hashOfConfig": "253"}, {"size": 287, "mtime": 1745213847379, "results": "264", "hashOfConfig": "253"}, {"size": 278, "mtime": 1745213847379, "results": "265", "hashOfConfig": "253"}, {"size": 1037, "mtime": 1745213847349, "results": "266", "hashOfConfig": "253"}, {"size": 1402, "mtime": 1758275343638, "results": "267", "hashOfConfig": "253"}, {"size": 3064, "mtime": 1760621504819, "results": "268", "hashOfConfig": "253"}, {"size": 441, "mtime": 1758275343637, "results": "269", "hashOfConfig": "253"}, {"size": 571, "mtime": 1745213847344, "results": "270", "hashOfConfig": "253"}, {"size": 684, "mtime": 1758275343641, "results": "271", "hashOfConfig": "253"}, {"size": 1009, "mtime": 1745213847349, "results": "272", "hashOfConfig": "253"}, {"size": 727, "mtime": 1758275343641, "results": "273", "hashOfConfig": "253"}, {"size": 558, "mtime": 1758275343636, "results": "274", "hashOfConfig": "253"}, {"size": 35289, "mtime": 1762359156425, "results": "275", "hashOfConfig": "253"}, {"size": 522, "mtime": 1758275343640, "results": "276", "hashOfConfig": "253"}, {"size": 751, "mtime": 1758275343640, "results": "277", "hashOfConfig": "253"}, {"size": 516, "mtime": 1758275343638, "results": "278", "hashOfConfig": "253"}, {"size": 632, "mtime": 1745213847344, "results": "279", "hashOfConfig": "253"}, {"size": 1344, "mtime": 1758275343629, "results": "280", "hashOfConfig": "253"}, {"size": 338, "mtime": 1758972567734, "results": "281", "hashOfConfig": "253"}, {"size": 209, "mtime": 1758778372370, "results": "282", "hashOfConfig": "253"}, {"size": 17050, "mtime": 1760520728719, "results": "283", "hashOfConfig": "253"}, {"size": 4699, "mtime": 1760621504749, "results": "284", "hashOfConfig": "253"}, {"size": 7102, "mtime": 1758275353542, "results": "285", "hashOfConfig": "253"}, {"size": 2123, "mtime": 1745213847270, "results": "286", "hashOfConfig": "253"}, {"size": 3849, "mtime": 1760520728733, "results": "287", "hashOfConfig": "253"}, {"size": 490, "mtime": 1758275343660, "results": "288", "hashOfConfig": "253"}, {"size": 638, "mtime": 1758275343260, "results": "289", "hashOfConfig": "253"}, {"size": 3209, "mtime": 1745213847371, "results": "290", "hashOfConfig": "253"}, {"size": 150, "mtime": 1745213847262, "results": "291", "hashOfConfig": "253"}, {"size": 8911, "mtime": 1762249092597, "results": "292", "hashOfConfig": "253"}, {"size": 7941, "mtime": 1759989927780, "results": "293", "hashOfConfig": "253"}, {"size": 1349, "mtime": 1745213847361, "results": "294", "hashOfConfig": "253"}, {"size": 1432, "mtime": 1745213847363, "results": "295", "hashOfConfig": "253"}, {"size": 10535, "mtime": 1745213847362, "results": "296", "hashOfConfig": "253"}, {"size": 5850, "mtime": 1745213847360, "results": "297", "hashOfConfig": "253"}, {"size": 1159, "mtime": 1745213847360, "results": "298", "hashOfConfig": "253"}, {"size": 10102, "mtime": 1745213847361, "results": "299", "hashOfConfig": "253"}, {"size": 14928, "mtime": 1762232154486, "results": "300", "hashOfConfig": "253"}, {"size": 1463, "mtime": 1760698780581, "results": "301", "hashOfConfig": "253"}, {"size": 10165, "mtime": 1758275353550, "results": "302", "hashOfConfig": "253"}, {"size": 510, "mtime": 1745213846545, "results": "303", "hashOfConfig": "253"}, {"size": 1120, "mtime": 1758275343265, "results": "304", "hashOfConfig": "253"}, {"size": 401, "mtime": 1758275343262, "results": "305", "hashOfConfig": "253"}, {"size": 1533, "mtime": 1758275343266, "results": "306", "hashOfConfig": "253"}, {"size": 5754, "mtime": 1760520728832, "results": "307", "hashOfConfig": "253"}, {"size": 12837, "mtime": 1758275375451, "results": "308", "hashOfConfig": "253"}, {"size": 3865, "mtime": 1760520728836, "results": "309", "hashOfConfig": "253"}, {"size": 7038, "mtime": 1762402764725, "results": "310", "hashOfConfig": "253"}, {"size": 6445, "mtime": 1761635587107, "results": "311", "hashOfConfig": "253"}, {"size": 1263, "mtime": 1745213847345, "results": "312", "hashOfConfig": "253"}, {"size": 1182, "mtime": 1758275343652, "results": "313", "hashOfConfig": "253"}, {"size": 557, "mtime": 1745213847337, "results": "314", "hashOfConfig": "253"}, {"size": 829, "mtime": 1745213847369, "results": "315", "hashOfConfig": "253"}, {"size": 202, "mtime": 1760621504748, "results": "316", "hashOfConfig": "253"}, {"size": 232, "mtime": 1745213847252, "results": "317", "hashOfConfig": "253"}, {"size": 168, "mtime": 1745213847251, "results": "318", "hashOfConfig": "253"}, {"size": 7263, "mtime": 1762249092570, "results": "319", "hashOfConfig": "253"}, {"size": 9156, "mtime": 1758275375522, "results": "320", "hashOfConfig": "253"}, {"size": 1640, "mtime": 1745213847372, "results": "321", "hashOfConfig": "253"}, {"size": 2201, "mtime": 1745213847373, "results": "322", "hashOfConfig": "253"}, {"size": 5242, "mtime": 1758275375526, "results": "323", "hashOfConfig": "253"}, {"size": 433, "mtime": 1758275343528, "results": "324", "hashOfConfig": "253"}, {"size": 3118, "mtime": 1762402738644, "results": "325", "hashOfConfig": "253"}, {"size": 1603, "mtime": 1760520728724, "results": "326", "hashOfConfig": "253"}, {"size": 1031, "mtime": 1761635587106, "results": "327", "hashOfConfig": "253"}, {"size": 1859, "mtime": 1758275343260, "results": "328", "hashOfConfig": "253"}, {"size": 156, "mtime": 1758275343545, "results": "329", "hashOfConfig": "253"}, {"size": 898, "mtime": 1745213847248, "results": "330", "hashOfConfig": "253"}, {"size": 1899, "mtime": 1758275343546, "results": "331", "hashOfConfig": "253"}, {"size": 1110, "mtime": 1758275365454, "results": "332", "hashOfConfig": "253"}, {"size": 3271, "mtime": 1758275353543, "results": "333", "hashOfConfig": "253"}, {"size": 551, "mtime": 1745213847240, "results": "334", "hashOfConfig": "253"}, {"size": 1132, "mtime": 1745213846543, "results": "335", "hashOfConfig": "253"}, {"size": 301, "mtime": 1745213847377, "results": "336", "hashOfConfig": "253"}, {"size": 307, "mtime": 1745213847376, "results": "337", "hashOfConfig": "253"}, {"size": 458, "mtime": 1745213847388, "results": "338", "hashOfConfig": "253"}, {"size": 433, "mtime": 1758275343653, "results": "339", "hashOfConfig": "253"}, {"size": 423, "mtime": 1745213847392, "results": "340", "hashOfConfig": "253"}, {"size": 428, "mtime": 1745213847381, "results": "341", "hashOfConfig": "253"}, {"size": 447, "mtime": 1745213847388, "results": "342", "hashOfConfig": "253"}, {"size": 431, "mtime": 1745213847380, "results": "343", "hashOfConfig": "253"}, {"size": 479, "mtime": 1758275343654, "results": "344", "hashOfConfig": "253"}, {"size": 417, "mtime": 1745213847388, "results": "345", "hashOfConfig": "253"}, {"size": 480, "mtime": 1745213847389, "results": "346", "hashOfConfig": "253"}, {"size": 428, "mtime": 1745213847392, "results": "347", "hashOfConfig": "253"}, {"size": 799, "mtime": 1745213847381, "results": "348", "hashOfConfig": "253"}, {"size": 319, "mtime": 1745213847379, "results": "349", "hashOfConfig": "253"}, {"size": 421, "mtime": 1745213847398, "results": "350", "hashOfConfig": "253"}, {"size": 438, "mtime": 1745213847386, "results": "351", "hashOfConfig": "253"}, {"size": 602, "mtime": 1758275343654, "results": "352", "hashOfConfig": "253"}, {"size": 1204, "mtime": 1745213847357, "results": "353", "hashOfConfig": "253"}, {"size": 1188, "mtime": 1745213847358, "results": "354", "hashOfConfig": "253"}, {"size": 1100, "mtime": 1745213847358, "results": "355", "hashOfConfig": "253"}, {"size": 1647, "mtime": 1758275343629, "results": "356", "hashOfConfig": "253"}, {"size": 1687, "mtime": 1745213847337, "results": "357", "hashOfConfig": "253"}, {"size": 7478, "mtime": 1758275343651, "results": "358", "hashOfConfig": "253"}, {"size": 30301, "mtime": 1760621504772, "results": "359", "hashOfConfig": "253"}, {"size": 5654, "mtime": 1760520728827, "results": "360", "hashOfConfig": "253"}, {"size": 29497, "mtime": 1760520728829, "results": "361", "hashOfConfig": "253"}, {"size": 20606, "mtime": 1758275373455, "results": "362", "hashOfConfig": "253"}, {"size": 16781, "mtime": 1760681220273, "results": "363", "hashOfConfig": "253"}, {"size": 20147, "mtime": 1760520728823, "results": "364", "hashOfConfig": "253"}, {"size": 3473, "mtime": 1745213847363, "results": "365", "hashOfConfig": "253"}, {"size": 4723, "mtime": 1745213847368, "results": "366", "hashOfConfig": "253"}, {"size": 4033, "mtime": 1745213847366, "results": "367", "hashOfConfig": "253"}, {"size": 12572, "mtime": 1760520728831, "results": "368", "hashOfConfig": "253"}, {"size": 18063, "mtime": 1762232154489, "results": "369", "hashOfConfig": "253"}, {"size": 5742, "mtime": 1745213847405, "results": "370", "hashOfConfig": "253"}, {"size": 1103, "mtime": 1758275343551, "results": "371", "hashOfConfig": "253"}, {"size": 1644, "mtime": 1758275353550, "results": "372", "hashOfConfig": "253"}, {"size": 13874, "mtime": 1760621527143, "results": "373", "hashOfConfig": "253"}, {"size": 12529, "mtime": 1762242324186, "results": "374", "hashOfConfig": "253"}, {"size": 20780, "mtime": 1760621527145, "results": "375", "hashOfConfig": "253"}, {"size": 32246, "mtime": 1760621504776, "results": "376", "hashOfConfig": "253"}, {"size": 775, "mtime": 1758275343534, "results": "377", "hashOfConfig": "253"}, {"size": 5834, "mtime": 1745213847239, "results": "378", "hashOfConfig": "253"}, {"size": 1952, "mtime": 1758275343536, "results": "379", "hashOfConfig": "253"}, {"size": 3076, "mtime": 1762232702715, "results": "380", "hashOfConfig": "253"}, {"size": 3119, "mtime": 1762249092572, "results": "381", "hashOfConfig": "253"}, {"size": 17062, "mtime": 1758275353645, "results": "382", "hashOfConfig": "253"}, {"size": 2821, "mtime": 1758275343644, "results": "383", "hashOfConfig": "253"}, {"size": 823, "mtime": 1745213847353, "results": "384", "hashOfConfig": "253"}, {"size": 879, "mtime": 1745213847356, "results": "385", "hashOfConfig": "253"}, {"size": 1098, "mtime": 1745213847355, "results": "386", "hashOfConfig": "253"}, {"size": 3067, "mtime": 1745213863355, "results": "387", "hashOfConfig": "253"}, {"size": 1228, "mtime": 1745213847354, "results": "388", "hashOfConfig": "253"}, {"size": 1774, "mtime": 1745213847356, "results": "389", "hashOfConfig": "253"}, {"size": 1756, "mtime": 1745213847355, "results": "390", "hashOfConfig": "253"}, {"size": 11351, "mtime": 1758275353631, "results": "391", "hashOfConfig": "253"}, {"size": 10028, "mtime": 1758275353632, "results": "392", "hashOfConfig": "253"}, {"size": 16423, "mtime": 1760520728821, "results": "393", "hashOfConfig": "253"}, {"size": 11791, "mtime": 1758275353633, "results": "394", "hashOfConfig": "253"}, {"size": 4018, "mtime": 1760621504739, "results": "395", "hashOfConfig": "253"}, {"size": 617, "mtime": 1758275343628, "results": "396", "hashOfConfig": "253"}, {"size": 3023, "mtime": 1758281087461, "results": "397", "hashOfConfig": "253"}, {"size": 11043, "mtime": 1758275353547, "results": "398", "hashOfConfig": "253"}, {"size": 4634, "mtime": 1758275343540, "results": "399", "hashOfConfig": "253"}, {"size": 5250, "mtime": 1761720888616, "results": "400", "hashOfConfig": "253"}, {"size": 445, "mtime": 1758275343528, "results": "401", "hashOfConfig": "253"}, {"size": 4554, "mtime": 1761720888614, "results": "402", "hashOfConfig": "253"}, {"size": 2045, "mtime": 1758275343568, "results": "403", "hashOfConfig": "253"}, {"size": 7676, "mtime": 1762361125676, "results": "404", "hashOfConfig": "253"}, {"size": 1660, "mtime": 1758275343570, "results": "405", "hashOfConfig": "253"}, {"size": 17226, "mtime": 1758275375466, "results": "406", "hashOfConfig": "253"}, {"size": 9020, "mtime": 1762361125676, "results": "407", "hashOfConfig": "253"}, {"size": 14556, "mtime": 1758275353567, "results": "408", "hashOfConfig": "253"}, {"size": 26131, "mtime": 1762359156425, "results": "409", "hashOfConfig": "253"}, {"size": 5686, "mtime": 1761720888609, "results": "410", "hashOfConfig": "253"}, {"size": 1238, "mtime": 1758275343612, "results": "411", "hashOfConfig": "253"}, {"size": 3045, "mtime": 1758275343535, "results": "412", "hashOfConfig": "253"}, {"size": 4537, "mtime": 1762242324186, "results": "413", "hashOfConfig": "253"}, {"size": 5879, "mtime": 1758275343562, "results": "414", "hashOfConfig": "253"}, {"size": 53071, "mtime": 1761722684823, "results": "415", "hashOfConfig": "253"}, {"size": 10207, "mtime": 1761649770950, "results": "416", "hashOfConfig": "253"}, {"size": 14414, "mtime": 1761720888645, "results": "417", "hashOfConfig": "253"}, {"size": 13130, "mtime": 1760621504818, "results": "418", "hashOfConfig": "253"}, {"size": 25960, "mtime": 1761720888643, "results": "419", "hashOfConfig": "253"}, {"size": 26169, "mtime": 1762335213116, "results": "420", "hashOfConfig": "253"}, {"size": 18123, "mtime": 1760682962048, "results": "421", "hashOfConfig": "253"}, {"size": 8924, "mtime": 1760679787300, "results": "422", "hashOfConfig": "253"}, {"size": 8511, "mtime": 1760520728794, "results": "423", "hashOfConfig": "253"}, {"size": 12036, "mtime": 1760520728804, "results": "424", "hashOfConfig": "253"}, {"size": 12655, "mtime": 1760520728808, "results": "425", "hashOfConfig": "253"}, {"size": 12760, "mtime": 1760693930986, "results": "426", "hashOfConfig": "253"}, {"size": 6138, "mtime": 1760520728792, "results": "427", "hashOfConfig": "253"}, {"size": 8643, "mtime": 1760681220269, "results": "428", "hashOfConfig": "253"}, {"size": 4554, "mtime": 1759989927775, "results": "429", "hashOfConfig": "253"}, {"size": 4536, "mtime": 1758275343625, "results": "430", "hashOfConfig": "253"}, {"size": 10823, "mtime": 1759310566343, "results": "431", "hashOfConfig": "253"}, {"size": 19848, "mtime": 1761624294800, "results": "432", "hashOfConfig": "253"}, {"size": 16148, "mtime": 1761545806449, "results": "433", "hashOfConfig": "253"}, {"size": 9310, "mtime": 1758275343608, "results": "434", "hashOfConfig": "253"}, {"size": 11565, "mtime": 1760520728814, "results": "435", "hashOfConfig": "253"}, {"size": 12288, "mtime": 1760520728811, "results": "436", "hashOfConfig": "253"}, {"size": 13959, "mtime": 1760520728810, "results": "437", "hashOfConfig": "253"}, {"size": 5458, "mtime": 1760696077107, "results": "438", "hashOfConfig": "253"}, {"size": 15935, "mtime": 1758275365508, "results": "439", "hashOfConfig": "253"}, {"size": 19365, "mtime": 1761720888654, "results": "440", "hashOfConfig": "253"}, {"size": 2862, "mtime": 1760621527139, "results": "441", "hashOfConfig": "253"}, {"size": 19250, "mtime": 1761720888649, "results": "442", "hashOfConfig": "253"}, {"size": 5755, "mtime": 1760520728755, "results": "443", "hashOfConfig": "253"}, {"size": 1643, "mtime": 1745213847296, "results": "444", "hashOfConfig": "253"}, {"size": 15869, "mtime": 1761720888651, "results": "445", "hashOfConfig": "253"}, {"size": 42610, "mtime": 1761651456528, "results": "446", "hashOfConfig": "253"}, {"size": 13859, "mtime": 1760520728815, "results": "447", "hashOfConfig": "253"}, {"size": 10311, "mtime": 1761720888627, "results": "448", "hashOfConfig": "253"}, {"size": 30189, "mtime": 1761720888622, "results": "449", "hashOfConfig": "253"}, {"size": 13989, "mtime": 1761721669572, "results": "450", "hashOfConfig": "253"}, {"size": 11194, "mtime": 1761653534159, "results": "451", "hashOfConfig": "253"}, {"size": 9530, "mtime": 1760681112436, "results": "452", "hashOfConfig": "253"}, {"size": 9841, "mtime": 1761021732418, "results": "453", "hashOfConfig": "253"}, {"size": 7197, "mtime": 1760687790604, "results": "454", "hashOfConfig": "253"}, {"size": 8130, "mtime": 1760679876928, "results": "455", "hashOfConfig": "253"}, {"size": 15593, "mtime": 1762249092590, "results": "456", "hashOfConfig": "253"}, {"size": 5319, "mtime": 1762360325411, "results": "457", "hashOfConfig": "253"}, {"size": 7498, "mtime": 1760621504809, "results": "458", "hashOfConfig": "253"}, {"size": 5577, "mtime": 1760621504788, "results": "459", "hashOfConfig": "253"}, {"size": 12848, "mtime": 1760687790611, "results": "460", "hashOfConfig": "253"}, {"size": 5222, "mtime": 1760695690034, "results": "461", "hashOfConfig": "253"}, {"size": 17510, "mtime": 1761720888632, "results": "462", "hashOfConfig": "253"}, {"size": 12565, "mtime": 1760693869588, "results": "463", "hashOfConfig": "253"}, {"size": 18053, "mtime": 1761720888635, "results": "464", "hashOfConfig": "253"}, {"size": 14362, "mtime": 1761720888629, "results": "465", "hashOfConfig": "253"}, {"size": 15482, "mtime": 1762360325393, "results": "466", "hashOfConfig": "253"}, {"size": 22022, "mtime": 1762249092588, "results": "467", "hashOfConfig": "253"}, {"size": 15449, "mtime": 1760700292045, "results": "468", "hashOfConfig": "253"}, {"size": 15281, "mtime": 1762249092586, "results": "469", "hashOfConfig": "253"}, {"size": 12220, "mtime": 1760699160225, "results": "470", "hashOfConfig": "253"}, {"size": 8933, "mtime": 1760853617115, "results": "471", "hashOfConfig": "253"}, {"size": 6471, "mtime": 1762249092589, "results": "472", "hashOfConfig": "253"}, {"size": 16413, "mtime": 1760703867335, "results": "473", "hashOfConfig": "253"}, {"size": 17304, "mtime": 1762249092593, "results": "474", "hashOfConfig": "253"}, {"size": 17374, "mtime": 1761720888638, "results": "475", "hashOfConfig": "253"}, {"size": 4240, "mtime": 1758275343263, "results": "476", "hashOfConfig": "253"}, {"size": 5227, "mtime": 1745213847204, "results": "477", "hashOfConfig": "253"}, {"size": 2654, "mtime": 1745213847332, "results": "478", "hashOfConfig": "253"}, {"size": 1728, "mtime": 1745213847331, "results": "479", "hashOfConfig": "253"}, {"size": 46272, "mtime": 1758961957737, "results": "480", "hashOfConfig": "481"}, {"size": 19593, "mtime": 1762359156425, "results": "482", "hashOfConfig": "253"}, {"size": 9365, "mtime": 1762249092577, "results": "483", "hashOfConfig": "253"}, {"size": 4642, "mtime": 1758275367450, "results": "484", "hashOfConfig": "253"}, {"size": 10857, "mtime": 1762249092582, "results": "485", "hashOfConfig": "253"}, {"size": 18421, "mtime": 1762359155493, "results": "486", "hashOfConfig": "253"}, {"size": 8603, "mtime": 1762249092582, "results": "487", "hashOfConfig": "253"}, {"size": 4069, "mtime": 1761651456525, "results": "488", "hashOfConfig": "253"}, {"size": 815, "mtime": 1758275343642, "results": "489", "hashOfConfig": "253"}, {"size": 453, "mtime": 1760520728833, "results": "490", "hashOfConfig": "253"}, {"size": 29725, "mtime": 1760520728740, "results": "491", "hashOfConfig": "253"}, {"size": 2112, "mtime": 1760520728730, "results": "492", "hashOfConfig": "253"}, {"size": 1664, "mtime": 1760520728729, "results": "493", "hashOfConfig": "253"}, {"size": 1167, "mtime": 1760520728729, "results": "494", "hashOfConfig": "253"}, {"size": 1090, "mtime": 1759989927747, "results": "495", "hashOfConfig": "253"}, {"size": 1040, "mtime": 1759989927746, "results": "496", "hashOfConfig": "253"}, {"size": 6612, "mtime": 1761647794632, "results": "497", "hashOfConfig": "253"}, {"size": 6184, "mtime": 1761647794625, "results": "498", "hashOfConfig": "253"}, {"size": 17368, "mtime": 1762250309453, "results": "499", "hashOfConfig": "253"}, {"size": 14640, "mtime": 1762359156430, "results": "500", "hashOfConfig": "253"}, {"size": 396, "mtime": 1762402512813, "results": "501", "hashOfConfig": "253"}, {"size": 8142, "mtime": 1762408087129, "results": "502", "hashOfConfig": "253"}, {"size": 11478, "mtime": 1762402595581, "results": "503", "hashOfConfig": "253"}, {"size": 39172, "mtime": 1762407620799, "results": "504", "hashOfConfig": "253"}, {"filePath": "505", "messages": "506", "suppressedMessages": "507", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "r95v4w", {"filePath": "508", "messages": "509", "suppressedMessages": "510", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "511", "messages": "512", "suppressedMessages": "513", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "514", "messages": "515", "suppressedMessages": "516", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "517", "messages": "518", "suppressedMessages": "519", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "520", "messages": "521", "suppressedMessages": "522", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "523", "messages": "524", "suppressedMessages": "525", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "526", "messages": "527", "suppressedMessages": "528", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "529", "messages": "530", "suppressedMessages": "531", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "532", "messages": "533", "suppressedMessages": "534", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "535", "messages": "536", "suppressedMessages": "537", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "538", "messages": "539", "suppressedMessages": "540", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "541", "messages": "542", "suppressedMessages": "543", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "544", "messages": "545", "suppressedMessages": "546", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "547", "messages": "548", "suppressedMessages": "549", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "550", "messages": "551", "suppressedMessages": "552", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "553", "messages": "554", "suppressedMessages": "555", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "556", "messages": "557", "suppressedMessages": "558", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "559", "messages": "560", "suppressedMessages": "561", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "562", "messages": "563", "suppressedMessages": "564", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "565", "messages": "566", "suppressedMessages": "567", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "568", "messages": "569", "suppressedMessages": "570", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "571", "messages": "572", "suppressedMessages": "573", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "574", "messages": "575", "suppressedMessages": "576", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "577", "messages": "578", "suppressedMessages": "579", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "580", "messages": "581", "suppressedMessages": "582", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "583", "messages": "584", "suppressedMessages": "585", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "586", "messages": "587", "suppressedMessages": "588", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "589", "messages": "590", "suppressedMessages": "591", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "592", "messages": "593", "suppressedMessages": "594", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "595", "messages": "596", "suppressedMessages": "597", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "598", "messages": "599", "suppressedMessages": "600", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "601", "messages": "602", "suppressedMessages": "603", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "604", "messages": "605", "suppressedMessages": "606", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "607", "messages": "608", "suppressedMessages": "609", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "610", "messages": "611", "suppressedMessages": "612", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "613", "messages": "614", "suppressedMessages": "615", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "616", "messages": "617", "suppressedMessages": "618", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "619", "messages": "620", "suppressedMessages": "621", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "622", "messages": "623", "suppressedMessages": "624", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "625", "messages": "626", "suppressedMessages": "627", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "628", "messages": "629", "suppressedMessages": "630", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "631", "messages": "632", "suppressedMessages": "633", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "634", "messages": "635", "suppressedMessages": "636", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 17, "fixableErrorCount": 0, "fixableWarningCount": 13, "source": null}, {"filePath": "637", "messages": "638", "suppressedMessages": "639", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "640", "messages": "641", "suppressedMessages": "642", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "643", "messages": "644", "suppressedMessages": "645", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "646", "messages": "647", "suppressedMessages": "648", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "649", "messages": "650", "suppressedMessages": "651", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "652", "messages": "653", "suppressedMessages": "654", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "655", "messages": "656", "suppressedMessages": "657", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "658", "messages": "659", "suppressedMessages": "660", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "661", "messages": "662", "suppressedMessages": "663", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "664", "messages": "665", "suppressedMessages": "666", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "667", "messages": "668", "suppressedMessages": "669", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "670", "messages": "671", "suppressedMessages": "672", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "673", "messages": "674", "suppressedMessages": "675", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "676", "messages": "677", "suppressedMessages": "678", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "679", "messages": "680", "suppressedMessages": "681", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "682", "messages": "683", "suppressedMessages": "684", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "685", "messages": "686", "suppressedMessages": "687", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "688", "messages": "689", "suppressedMessages": "690", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "691", "messages": "692", "suppressedMessages": "693", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "694", "messages": "695", "suppressedMessages": "696", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "697", "messages": "698", "suppressedMessages": "699", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "700", "messages": "701", "suppressedMessages": "702", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "703", "messages": "704", "suppressedMessages": "705", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "706", "messages": "707", "suppressedMessages": "708", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "709", "messages": "710", "suppressedMessages": "711", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "712", "messages": "713", "suppressedMessages": "714", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "715", "messages": "716", "suppressedMessages": "717", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "718", "messages": "719", "suppressedMessages": "720", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "721", "messages": "722", "suppressedMessages": "723", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "724", "messages": "725", "suppressedMessages": "726", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "727", "messages": "728", "suppressedMessages": "729", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "730", "messages": "731", "suppressedMessages": "732", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "733", "messages": "734", "suppressedMessages": "735", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "736", "messages": "737", "suppressedMessages": "738", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "739", "messages": "740", "suppressedMessages": "741", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "742", "messages": "743", "suppressedMessages": "744", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "745", "messages": "746", "suppressedMessages": "747", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "748", "messages": "749", "suppressedMessages": "750", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "751", "messages": "752", "suppressedMessages": "753", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "754", "messages": "755", "suppressedMessages": "756", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "757", "messages": "758", "suppressedMessages": "759", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "760", "messages": "761", "suppressedMessages": "762", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "763", "messages": "764", "suppressedMessages": "765", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "766", "messages": "767", "suppressedMessages": "768", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "769", "messages": "770", "suppressedMessages": "771", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "772", "messages": "773", "suppressedMessages": "774", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "775", "messages": "776", "suppressedMessages": "777", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "778", "messages": "779", "suppressedMessages": "780", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "781", "messages": "782", "suppressedMessages": "783", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "784", "messages": "785", "suppressedMessages": "786", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "787", "messages": "788", "suppressedMessages": "789", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "790", "messages": "791", "suppressedMessages": "792", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "793", "messages": "794", "suppressedMessages": "795", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "796", "messages": "797", "suppressedMessages": "798", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "799", "messages": "800", "suppressedMessages": "801", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "802", "messages": "803", "suppressedMessages": "804", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "805", "messages": "806", "suppressedMessages": "807", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "808", "messages": "809", "suppressedMessages": "810", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "811", "messages": "812", "suppressedMessages": "813", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "814", "messages": "815", "suppressedMessages": "816", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "817", "messages": "818", "suppressedMessages": "819", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "820", "messages": "821", "suppressedMessages": "822", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "823", "messages": "824", "suppressedMessages": "825", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "826", "messages": "827", "suppressedMessages": "828", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "829", "messages": "830", "suppressedMessages": "831", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "832", "messages": "833", "suppressedMessages": "834", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "835", "messages": "836", "suppressedMessages": "837", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "838", "messages": "839", "suppressedMessages": "840", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "841", "messages": "842", "suppressedMessages": "843", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "844", "messages": "845", "suppressedMessages": "846", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "847", "messages": "848", "suppressedMessages": "849", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "850", "messages": "851", "suppressedMessages": "852", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "853", "messages": "854", "suppressedMessages": "855", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "856", "messages": "857", "suppressedMessages": "858", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "859", "messages": "860", "suppressedMessages": "861", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "862", "messages": "863", "suppressedMessages": "864", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "865", "messages": "866", "suppressedMessages": "867", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 15, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "868", "messages": "869", "suppressedMessages": "870", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "871", "messages": "872", "suppressedMessages": "873", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 25, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "874", "messages": "875", "suppressedMessages": "876", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "877", "messages": "878", "suppressedMessages": "879", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "880", "messages": "881", "suppressedMessages": "882", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "883", "messages": "884", "suppressedMessages": "885", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "886", "messages": "887", "suppressedMessages": "888", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "889", "messages": "890", "suppressedMessages": "891", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "892", "messages": "893", "suppressedMessages": "894", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "895", "messages": "896", "suppressedMessages": "897", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "898", "messages": "899", "suppressedMessages": "900", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "901", "messages": "902", "suppressedMessages": "903", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "904", "messages": "905", "suppressedMessages": "906", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "907", "messages": "908", "suppressedMessages": "909", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "910", "messages": "911", "suppressedMessages": "912", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "913", "messages": "914", "suppressedMessages": "915", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "916", "messages": "917", "suppressedMessages": "918", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "919", "messages": "920", "suppressedMessages": "921", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "922", "messages": "923", "suppressedMessages": "924", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "925", "messages": "926", "suppressedMessages": "927", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "928", "messages": "929", "suppressedMessages": "930", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "931", "messages": "932", "suppressedMessages": "933", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "934", "messages": "935", "suppressedMessages": "936", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "937", "messages": "938", "suppressedMessages": "939", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "940", "messages": "941", "suppressedMessages": "942", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "943", "messages": "944", "suppressedMessages": "945", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "946", "messages": "947", "suppressedMessages": "948", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "949", "messages": "950", "suppressedMessages": "951", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "952", "messages": "953", "suppressedMessages": "954", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "955", "messages": "956", "suppressedMessages": "957", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "958", "messages": "959", "suppressedMessages": "960", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "961", "messages": "962", "suppressedMessages": "963", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "964", "messages": "965", "suppressedMessages": "966", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "967", "messages": "968", "suppressedMessages": "969", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "970", "messages": "971", "suppressedMessages": "972", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "973", "messages": "974", "suppressedMessages": "975", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "976", "messages": "977", "suppressedMessages": "978", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "979", "messages": "980", "suppressedMessages": "981", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "982", "messages": "983", "suppressedMessages": "984", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "985", "messages": "986", "suppressedMessages": "987", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "988", "messages": "989", "suppressedMessages": "990", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "991", "messages": "992", "suppressedMessages": "993", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "994", "messages": "995", "suppressedMessages": "996", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "997", "messages": "998", "suppressedMessages": "999", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1000", "messages": "1001", "suppressedMessages": "1002", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1003", "messages": "1004", "suppressedMessages": "1005", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1006", "messages": "1007", "suppressedMessages": "1008", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1009", "messages": "1010", "suppressedMessages": "1011", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1012", "messages": "1013", "suppressedMessages": "1014", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1015", "messages": "1016", "suppressedMessages": "1017", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1018", "messages": "1019", "suppressedMessages": "1020", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1021", "messages": "1022", "suppressedMessages": "1023", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1024", "messages": "1025", "suppressedMessages": "1026", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1027", "messages": "1028", "suppressedMessages": "1029", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1030", "messages": "1031", "suppressedMessages": "1032", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1033", "messages": "1034", "suppressedMessages": "1035", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1036", "messages": "1037", "suppressedMessages": "1038", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1039", "messages": "1040", "suppressedMessages": "1041", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1042", "messages": "1043", "suppressedMessages": "1044", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1045", "messages": "1046", "suppressedMessages": "1047", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1048", "messages": "1049", "suppressedMessages": "1050", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1051", "messages": "1052", "suppressedMessages": "1053", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1054", "messages": "1055", "suppressedMessages": "1056", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1057", "messages": "1058", "suppressedMessages": "1059", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1060", "messages": "1061", "suppressedMessages": "1062", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1063", "messages": "1064", "suppressedMessages": "1065", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1066", "messages": "1067", "suppressedMessages": "1068", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1069", "messages": "1070", "suppressedMessages": "1071", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1072", "messages": "1073", "suppressedMessages": "1074", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1075", "messages": "1076", "suppressedMessages": "1077", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1078", "messages": "1079", "suppressedMessages": "1080", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1081", "messages": "1082", "suppressedMessages": "1083", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1084", "messages": "1085", "suppressedMessages": "1086", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1087", "messages": "1088", "suppressedMessages": "1089", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1090", "messages": "1091", "suppressedMessages": "1092", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1093", "messages": "1094", "suppressedMessages": "1095", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1096", "messages": "1097", "suppressedMessages": "1098", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1099", "messages": "1100", "suppressedMessages": "1101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1102", "messages": "1103", "suppressedMessages": "1104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1105", "messages": "1106", "suppressedMessages": "1107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1108", "messages": "1109", "suppressedMessages": "1110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1111", "messages": "1112", "suppressedMessages": "1113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1114", "messages": "1115", "suppressedMessages": "1116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1117", "messages": "1118", "suppressedMessages": "1119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1120", "messages": "1121", "suppressedMessages": "1122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1123", "messages": "1124", "suppressedMessages": "1125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1126", "messages": "1127", "suppressedMessages": "1128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1129", "messages": "1130", "suppressedMessages": "1131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1132", "messages": "1133", "suppressedMessages": "1134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1135", "messages": "1136", "suppressedMessages": "1137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1138", "messages": "1139", "suppressedMessages": "1140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1141", "messages": "1142", "suppressedMessages": "1143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1144", "messages": "1145", "suppressedMessages": "1146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1147", "messages": "1148", "suppressedMessages": "1149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1150", "messages": "1151", "suppressedMessages": "1152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1153", "messages": "1154", "suppressedMessages": "1155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1156", "messages": "1157", "suppressedMessages": "1158", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1159", "messages": "1160", "suppressedMessages": "1161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1162", "messages": "1163", "suppressedMessages": "1164", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1165", "messages": "1166", "suppressedMessages": "1167", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1168", "messages": "1169", "suppressedMessages": "1170", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1171", "messages": "1172", "suppressedMessages": "1173", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1174", "messages": "1175", "suppressedMessages": "1176", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1177", "messages": "1178", "suppressedMessages": "1179", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1180", "messages": "1181", "suppressedMessages": "1182", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1183", "messages": "1184", "suppressedMessages": "1185", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1186", "messages": "1187", "suppressedMessages": "1188", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "eb6e59", {"filePath": "1189", "messages": "1190", "suppressedMessages": "1191", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1192", "messages": "1193", "suppressedMessages": "1194", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1195", "messages": "1196", "suppressedMessages": "1197", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1198", "messages": "1199", "suppressedMessages": "1200", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1201", "messages": "1202", "suppressedMessages": "1203", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1204", "messages": "1205", "suppressedMessages": "1206", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1207", "messages": "1208", "suppressedMessages": "1209", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1210", "messages": "1211", "suppressedMessages": "1212", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1213", "messages": "1214", "suppressedMessages": "1215", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1216", "messages": "1217", "suppressedMessages": "1218", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1219", "messages": "1220", "suppressedMessages": "1221", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1222", "messages": "1223", "suppressedMessages": "1224", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1225", "messages": "1226", "suppressedMessages": "1227", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1228", "messages": "1229", "suppressedMessages": "1230", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1231", "messages": "1232", "suppressedMessages": "1233", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1234", "messages": "1235", "suppressedMessages": "1236", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1237", "messages": "1238", "suppressedMessages": "1239", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1240", "messages": "1241", "suppressedMessages": "1242", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1243", "messages": "1244", "suppressedMessages": "1245", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1246", "messages": "1247", "suppressedMessages": "1248", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1249", "messages": "1250", "suppressedMessages": "1251", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1252", "messages": "1253", "suppressedMessages": "1254", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1255", "messages": "1256", "suppressedMessages": "1257", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\index.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\App.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\app\\store.js", ["1258"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\containers\\SuspenseContent.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Context\\Context.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\app\\auth.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\app\\init.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\containers\\Layout.jsx", ["1259", "1260", "1261", "1262"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\pages\\ForgotPassword.js", ["1263", "1264", "1265", "1266"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\pages\\Documentation.js", ["1267"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\pages\\Register.js", ["1268", "1269", "1270"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\pages\\Login.js", ["1271", "1272", "1273"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\common\\modalSlice.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\common\\headerSlice.js", ["1274"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\common\\userSlice.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\common\\debounceSlice.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\common\\SbStateSlice.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\common\\resourceSlice.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\common\\rightDrawerSlice.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\common\\routeLists.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\common\\InitialContentSlice.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\common\\homeContentSlice.js", ["1275"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\common\\platformSlice.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\common\\navbarSlice.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\common\\fontStyle.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\common\\ImagesSlice.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\leadSlice.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\components\\fallbackLoader\\FallbackLoader.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\Socket\\socket.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\app\\fetch.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\containers\\LeftSidebar.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\containers\\RightSidebar.jsx", ["1276", "1277", "1278", "1279", "1280", "1281", "1282"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\containers\\ModalLayout.jsx", ["1283"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\containers\\PageContent.jsx", ["1284", "1285", "1286", "1287"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\utils\\globalConstantUtil.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\app\\checkUser.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\user\\Register.js", ["1288"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\components\\Typography\\Title.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\user\\Login.jsx", ["1289", "1290", "1291", "1292", "1293", "1294", "1295"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\user\\ForgotPassword.jsx", ["1296", "1297", "1298", "1299", "1300"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\documentation\\components\\FeaturesNav.js", ["1301"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\documentation\\components\\GettingStartedNav.js", ["1302"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\documentation\\components\\GettingStartedContent.js", ["1303", "1304", "1305", "1306", "1307", "1308", "1309", "1310", "1311", "1312", "1313", "1314", "1315", "1316", "1317", "1318", "1319"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\documentation\\components\\DocComponentsContent.js", ["1320", "1321", "1322", "1323", "1324"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\documentation\\components\\DocComponentsNav.js", ["1325"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\documentation\\components\\FeaturesContent.js", ["1326", "1327", "1328", "1329"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\VersionDetails.jsx", ["1330", "1331", "1332", "1333", "1334", "1335"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Component\\ToastPlacer.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Requests\\RequestDetails.jsx", ["1336", "1337", "1338", "1339", "1340"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\app\\emailregex.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\app\\toastify.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\app\\deviceId.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\app\\valid.js", ["1341"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\user\\ResetPasswordModalBody.jsx", ["1342"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\containers\\Header.jsx", ["1343", "1344", "1345", "1346"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\routes\\backend.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\routes\\sidebar.js", ["1347"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\common\\components\\NotificationBodyRightDrawer.js", ["1348", "1349", "1350", "1351"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\common\\components\\ConfirmationModalBody.js", ["1352", "1353", "1354", "1355"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\pages\\protected\\404.js", ["1356", "1357"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\calendar\\CalendarEventsBodyRightDrawer.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\user\\LandingIntro.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\components\\Typography\\ErrorText.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\components\\Typography\\Subtitle.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\components\\Typography\\HelperText.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\components\\Input\\InputText.jsx", ["1358", "1359"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\user\\components\\OTP.jsx", ["1360", "1361", "1362"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\user\\components\\BackroundImg.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\user\\components\\PasswordValidation.jsx", ["1363"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\user\\components\\UpdatePassword.jsx", ["1364"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\components\\Button\\Button.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\routes\\index.jsx", ["1365", "1366", "1367", "1368", "1369", "1370", "1371", "1372"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\components\\Cards\\TitleCard.jsx", ["1373"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\app\\TimeFormat.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\app\\capitalizeword.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Context\\CustomContext.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\components\\Input\\SearchBar.jsx", ["1374"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Requests\\Comments.jsx", ["1375", "1376"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Component\\Toaster.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Component\\Paginations.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\components\\CalendarView\\util.js", ["1377"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\app\\OTP.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\pages\\DocFeatures.js", ["1378", "1379", "1380"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\pages\\DocComponents.js", ["1381", "1382", "1383"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\pages\\protected\\ProfileSettings.js", ["1384"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\pages\\protected\\Reminders.js", ["1385"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\pages\\protected\\Roles.js", ["1386"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\pages\\protected\\Calendar.js", ["1387"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\pages\\protected\\Integration.js", ["1388"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\pages\\protected\\Bills.js", ["1389"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\pages\\protected\\Requests.js", ["1390"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\pages\\protected\\Logs.js", ["1391"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\pages\\protected\\Resources.js", ["1392"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\pages\\protected\\Team.js", ["1393"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\pages\\protected\\Blank.js", ["1394"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\pages\\GettingStarted.js", ["1395", "1396", "1397"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\pages\\protected\\Users.js", ["1398"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\pages\\protected\\Dashboard.js", ["1399"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\pages\\protected\\Welcome.js", ["1400", "1401"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\documentation\\DocComponents.js", ["1402", "1403", "1404", "1405", "1406", "1407", "1408"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\documentation\\DocFeatures.js", ["1409", "1410", "1411", "1412", "1413", "1414", "1415"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\documentation\\DocGettingStarted.js", ["1416", "1417", "1418", "1419", "1420"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\index.jsx", ["1421"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\calendar\\index.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\user\\components\\TemplatePointers.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Requests\\index.jsx", ["1422", "1423", "1424", "1425", "1426", "1427", "1428", "1429", "1430", "1431", "1432", "1433", "1434"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\dashboard\\index.js", ["1435", "1436", "1437", "1438", "1439", "1440", "1441", "1442", "1443", "1444", "1445"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\logstable\\index.jsx", ["1446", "1447", "1448", "1449", "1450", "1451", "1452", "1453", "1454", "1455", "1456", "1457", "1458"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Reminders\\index.jsx", ["1459", "1460", "1461", "1462", "1463"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Roles\\index.jsx", ["1464", "1465", "1466", "1467", "1468", "1469", "1470"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\charts\\index.jsx", ["1471", "1472", "1473", "1474", "1475", "1476"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\integration\\index.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\settings\\team\\index.js", ["1477", "1478", "1479"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\settings\\billing\\index.js", ["1480", "1481", "1482", "1483", "1484"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\settings\\profilesettings\\index.jsx", ["1485", "1486", "1487", "1488", "1489", "1490", "1491", "1492"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Requests\\Showdifference.jsx", ["1493", "1494", "1495", "1496", "1497", "1498"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\utils\\dummyData.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Requests\\ShowVerifierTooltip.jsx", ["1499"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Requests\\ShowPDF.jsx", ["1500", "1501"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\Resources.jsx", ["1502", "1503", "1504", "1505", "1506", "1507", "1508", "1509", "1510", "1511", "1512", "1513", "1514", "1515", "1516"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\EditPage.jsx", ["1517", "1518", "1519", "1520"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\VersionTable.jsx", ["1521", "1522", "1523", "1524", "1525", "1526", "1527", "1528", "1529", "1530", "1531", "1532", "1533", "1534", "1535", "1536", "1537", "1538", "1539", "1540", "1541", "1542", "1543", "1544", "1545"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\RequestTable.jsx", ["1546", "1547", "1548", "1549", "1550", "1551", "1552"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\components\\Input\\ToogleInput.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\components\\CalendarView\\index.js", ["1553", "1554", "1555"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\components\\Toggle\\Toggle.jsx", ["1556"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\components\\Input\\Select.jsx", ["1557", "1558"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\components\\Input\\TextAreaInput.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\logstable\\ShowLog.jsx", ["1559", "1560", "1561", "1562", "1563", "1564", "1565", "1566"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\dashboard\\components\\DashboardStats.js", ["1567"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\dashboard\\components\\AmountStats.js", ["1568"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\dashboard\\components\\PageStats.js", ["1569"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\dashboard\\components\\LineChart.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\dashboard\\components\\DashboardTopBar.js", ["1570", "1571", "1572"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\dashboard\\components\\BarChart.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\dashboard\\components\\UserChannels.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\dashboard\\components\\DoughnutChart.js", ["1573", "1574"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Roles\\AddRole.jsx", ["1575", "1576", "1577", "1578", "1579", "1580", "1581", "1582", "1583", "1584", "1585"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Roles\\ShowRole.jsx", ["1586", "1587", "1588"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\charts\\ShowRole.jsx", ["1589", "1590"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\charts\\AddUser.jsx", ["1591", "1592", "1593", "1594", "1595", "1596", "1597", "1598", "1599", "1600", "1601"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\components\\Input\\DirectInputFile.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\defineContent.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Requests\\RejectPopup.jsx", ["1602"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Requests\\DateTime.jsx", ["1603", "1604", "1605"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\containers\\Navbar.jsx", ["1606", "1607"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\ResourceCard.jsx", ["1608", "1609", "1610", "1611", "1612", "1613"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\components\\Button\\CloseButton.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\NewProjectDialog.jsx", ["1614", "1615"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\breakUI\\Popups.jsx", ["1616"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\AllForOne.jsx", ["1617", "1618", "1619", "1620", "1621", "1622"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\breakUI\\SwitchLang.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\breakUI\\ConfigBar.jsx", ["1623", "1624"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\AllForOneManager.jsx", ["1625", "1626"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\breakUI\\PageDetails.jsx", ["1627", "1628"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\breakUI\\ContentTopBar.jsx", ["1629", "1630", "1631"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\components\\Input\\InputFileForm.jsx", ["1632", "1633", "1634", "1635", "1636"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\structures\\PageStructure.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\components\\Loader\\SkeletonLoader.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\resourcedata.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\breakUI\\DropDownMenu.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\Home.jsx", ["1637", "1638", "1639", "1640", "1641"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\About.jsx", ["1642", "1643", "1644", "1645", "1646", "1647"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\Projects.jsx", ["1648", "1649", "1650", "1651", "1652", "1653", "1654", "1655"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\Solutions.jsx", ["1656"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\Market.jsx", ["1657", "1658", "1659", "1660", "1661", "1662", "1663", "1664"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\CareersPage.jsx", ["1665", "1666", "1667", "1668", "1669", "1670"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\NewsPage.jsx", ["1671", "1672", "1673", "1674", "1675", "1676", "1677"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\Service.jsx", ["1678"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\HistoryPage.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\SafetyAndResponsibility.jsx", ["1679", "1680"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\VisionPage.jsx", ["1681", "1682", "1683"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\HSE.jsx", ["1684", "1685", "1686", "1687"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\Affiliates.jsx", ["1688", "1689", "1690"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\subparts\\Footerweb.jsx", ["1691"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\subparts\\Testimonials.jsx", ["1692", "1693", "1694", "1695", "1696", "1697", "1698", "1699", "1700", "1701", "1702"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\subparts\\ContactUsModal.jsx", ["1703", "1704", "1705", "1706"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\subparts\\Headerweb.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\detailspages\\ProjectDetails.jsx", ["1707", "1708", "1709", "1710", "1711", "1712"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\detailspages\\ServiceDetails.jsx", ["1713", "1714", "1715", "1716"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\detailspages\\CareersDetails.jsx", ["1717", "1718", "1719", "1720", "1721", "1722", "1723"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\detailspages\\SnRPolicies.jsx", ["1724", "1725", "1726", "1727", "1728"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\detailspages\\NewsDetails.jsx", ["1729", "1730", "1731", "1732", "1733", "1734", "1735", "1736", "1737"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\detailspages\\MarketDetails.jsx", ["1738", "1739", "1740"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\Organizational.jsx", ["1741", "1742", "1743", "1744"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\TemplateOne.jsx", ["1745", "1746", "1747", "1748"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\TemplateTwo.jsx", ["1749", "1750"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\app\\convertContent.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\TempThree.jsx", ["1751", "1752", "1753", "1754"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\breakUI\\SelectorAccordion.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\breakUI\\Statusbar.jsx", ["1755", "1756"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\TemplateFour.jsx", ["1757"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\components\\Input\\ImageSelector.jsx", ["1758", "1759", "1760"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\subDetailsPages\\SubServiceDetails.jsx", ["1761", "1762", "1763"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\contentmanager\\SolutionManager.jsx", ["1764", "1765", "1766"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\contentmanager\\HomeManager.jsx", ["1767", "1768", "1769", "1770", "1771", "1772"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\contentmanager\\MarketManager.jsx", ["1773", "1774", "1775", "1776", "1777"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\contentmanager\\AboutManager.jsx", ["1778", "1779", "1780", "1781", "1782", "1783", "1784", "1785", "1786"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\contentmanager\\ProjectContentManager.jsx", ["1787", "1788", "1789"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\contentmanager\\NewsManager.jsx", [], ["1790"], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\contentmanager\\HistoryManager.jsx", ["1791", "1792"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\contentmanager\\ServiceManager.jsx", ["1793", "1794", "1795"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\contentmanager\\SnRPolicyManager.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\contentmanager\\CareersManager.jsx", ["1796", "1797", "1798"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\contentmanager\\SnRManager.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\contentmanager\\AffiliatesManager.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\contentmanager\\VisionManager.jsx", ["1799", "1800", "1801"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\contentmanager\\OrganizationManager.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\contentmanager\\TemplateOneManager.jsx", ["1802", "1803"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\contentmanager\\HSnEManager.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\contentmanager\\TemplateThreeManager.jsx", ["1804", "1805"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\contentmanager\\TemplateFourManager.jsx", ["1806", "1807"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\contentmanager\\CMforDetails\\CareerDetailManager.jsx", ["1808", "1809", "1810", "1811", "1812", "1813", "1814", "1815"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\contentmanager\\CMforDetails\\ProjectDetailManager.jsx", ["1816", "1817", "1818", "1819", "1820"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\contentmanager\\CMforDetails\\MarketDetailsManager.jsx", ["1821", "1822", "1823"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\contentmanager\\CMforDetails\\NewsDetailsManager.jsx", ["1824", "1825", "1826", "1827", "1828", "1829"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\contentmanager\\CMforDetails\\ServiceDetailsManager.jsx", ["1830", "1831"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\contentmanager\\CMforSubParts\\HeaderManager.jsx", ["1832", "1833"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\contentmanager\\CMforSubParts\\TestimonyManager.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\contentmanager\\CMforSubParts\\FooterManager.jsx", [], ["1834"], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\contentmanager\\subDetailsManagement\\SubServiceDetailManagement.jsx", ["1835", "1836", "1837"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\contentmanager\\TemplateTwoManager.jsx", ["1838", "1839"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\app\\fontSizes.js", ["1840"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\assets\\index.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\subparts\\Pagination.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\subparts\\ModalPortal.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\components\\Input\\useImageUpload.js", ["1841"], ["1842", "1843", "1844", "1845", "1846", "1847", "1848", "1849", "1850", "1851", "1852", "1853", "1854", "1855"], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\breakUI\\ContentSections.jsx", ["1856", "1857", "1858", "1859", "1860"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\breakUI\\MultiSelect.jsx", ["1861", "1862", "1863", "1864"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\components\\Input\\InputFileUploader.jsx", ["1865", "1866", "1867", "1868", "1869", "1870", "1871"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\breakUI\\MultiSelectPro.jsx", ["1872", "1873", "1874", "1875"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\breakUI\\DynamicContentSection.jsx", ["1876", "1877"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\breakUI\\MultiSelectSM.jsx", ["1878", "1879", "1880", "1881"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\components\\Input\\InputFile.jsx", ["1882", "1883", "1884", "1885", "1886"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\common\\thunk\\smsThunk.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\pages\\protected\\ContactQueries.js", ["1887"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\ContactQueries\\index.jsx", ["1888", "1889", "1890", "1891", "1892", "1893"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\components\\TableStates\\TableSkeleton.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\components\\TableStates\\ErrorState.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\components\\TableStates\\EmptyState.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\breakUI\\ScrollableParagraph.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\breakUI\\ScrollableHtml.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\components\\Input\\UploadProgress.jsx", ["1894"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\components\\Input\\useBatchUpload.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\ContactUsPage.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\contentmanager\\ContactUsManager.jsx", ["1895"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\pages\\protected\\Jobs.js", ["1896"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\jobs\\index.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\jobs\\components\\JobsTable.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\jobs\\components\\JobModal.js", [], [], {"ruleId": "1897", "severity": 1, "message": "1898", "line": 9, "column": 8, "nodeType": "1899", "messageId": "1900", "endLine": 9, "endColumn": 21}, {"ruleId": "1897", "severity": 1, "message": "1901", "line": 12, "column": 10, "nodeType": "1899", "messageId": "1900", "endLine": 12, "endColumn": 15}, {"ruleId": "1897", "severity": 1, "message": "1902", "line": 13, "column": 10, "nodeType": "1899", "messageId": "1900", "endLine": 13, "endColumn": 30}, {"ruleId": "1903", "severity": 1, "message": "1904", "line": 34, "column": 6, "nodeType": "1905", "endLine": 34, "endColumn": 30, "suggestions": "1906"}, {"ruleId": "1903", "severity": 1, "message": "1907", "line": 45, "column": 6, "nodeType": "1905", "endLine": 45, "endColumn": 8, "suggestions": "1908"}, {"ruleId": "1897", "severity": 1, "message": "1909", "line": 1, "column": 9, "nodeType": "1899", "messageId": "1900", "endLine": 1, "endColumn": 17}, {"ruleId": "1897", "severity": 1, "message": "1910", "line": 1, "column": 19, "nodeType": "1899", "messageId": "1900", "endLine": 1, "endColumn": 25}, {"ruleId": "1897", "severity": 1, "message": "1911", "line": 2, "column": 9, "nodeType": "1899", "messageId": "1900", "endLine": 2, "endColumn": 13}, {"ruleId": "1897", "severity": 1, "message": "1912", "line": 4, "column": 8, "nodeType": "1899", "messageId": "1900", "endLine": 4, "endColumn": 13}, {"ruleId": "1897", "severity": 1, "message": "1913", "line": 5, "column": 8, "nodeType": "1899", "messageId": "1900", "endLine": 5, "endColumn": 13}, {"ruleId": "1897", "severity": 1, "message": "1909", "line": 1, "column": 9, "nodeType": "1899", "messageId": "1900", "endLine": 1, "endColumn": 17}, {"ruleId": "1897", "severity": 1, "message": "1910", "line": 1, "column": 19, "nodeType": "1899", "messageId": "1900", "endLine": 1, "endColumn": 25}, {"ruleId": "1897", "severity": 1, "message": "1911", "line": 2, "column": 9, "nodeType": "1899", "messageId": "1900", "endLine": 2, "endColumn": 13}, {"ruleId": "1897", "severity": 1, "message": "1909", "line": 1, "column": 9, "nodeType": "1899", "messageId": "1900", "endLine": 1, "endColumn": 17}, {"ruleId": "1897", "severity": 1, "message": "1910", "line": 1, "column": 19, "nodeType": "1899", "messageId": "1900", "endLine": 1, "endColumn": 25}, {"ruleId": "1897", "severity": 1, "message": "1911", "line": 2, "column": 9, "nodeType": "1899", "messageId": "1900", "endLine": 2, "endColumn": 13}, {"ruleId": "1897", "severity": 1, "message": "1914", "line": 30, "column": 11, "nodeType": "1899", "messageId": "1900", "endLine": 30, "endColumn": 16}, {"ruleId": "1897", "severity": 1, "message": "1915", "line": 14, "column": 10, "nodeType": "1899", "messageId": "1900", "endLine": 14, "endColumn": 21}, {"ruleId": "1897", "severity": 1, "message": "1916", "line": 6, "column": 3, "nodeType": "1899", "messageId": "1900", "endLine": 6, "endColumn": 18}, {"ruleId": "1897", "severity": 1, "message": "1917", "line": 10, "column": 9, "nodeType": "1899", "messageId": "1900", "endLine": 10, "endColumn": 20}, {"ruleId": "1897", "severity": 1, "message": "1918", "line": 10, "column": 22, "nodeType": "1899", "messageId": "1900", "endLine": 10, "endColumn": 31}, {"ruleId": "1897", "severity": 1, "message": "1919", "line": 18, "column": 3, "nodeType": "1899", "messageId": "1900", "endLine": 18, "endColumn": 29}, {"ruleId": "1897", "severity": 1, "message": "1920", "line": 20, "column": 8, "nodeType": "1899", "messageId": "1900", "endLine": 20, "endColumn": 14}, {"ruleId": "1897", "severity": 1, "message": "1921", "line": 23, "column": 9, "nodeType": "1899", "messageId": "1900", "endLine": 23, "endColumn": 23}, {"ruleId": "1897", "severity": 1, "message": "1901", "line": 25, "column": 9, "nodeType": "1899", "messageId": "1900", "endLine": 25, "endColumn": 14}, {"ruleId": "1897", "severity": 1, "message": "1922", "line": 8, "column": 10, "nodeType": "1899", "messageId": "1900", "endLine": 8, "endColumn": 11}, {"ruleId": "1897", "severity": 1, "message": "1923", "line": 6, "column": 10, "nodeType": "1899", "messageId": "1900", "endLine": 6, "endColumn": 21}, {"ruleId": "1897", "severity": 1, "message": "1910", "line": 7, "column": 21, "nodeType": "1899", "messageId": "1900", "endLine": 7, "endColumn": 27}, {"ruleId": "1903", "severity": 1, "message": "1924", "line": 33, "column": 8, "nodeType": "1905", "endLine": 33, "endColumn": 19, "suggestions": "1925"}, {"ruleId": "1903", "severity": 1, "message": "1926", "line": 42, "column": 8, "nodeType": "1905", "endLine": 42, "endColumn": 10, "suggestions": "1927"}, {"ruleId": "1897", "severity": 1, "message": "1910", "line": 1, "column": 19, "nodeType": "1899", "messageId": "1900", "endLine": 1, "endColumn": 25}, {"ruleId": "1897", "severity": 1, "message": "1921", "line": 7, "column": 10, "nodeType": "1899", "messageId": "1900", "endLine": 7, "endColumn": 24}, {"ruleId": "1897", "severity": 1, "message": "1901", "line": 7, "column": 26, "nodeType": "1899", "messageId": "1900", "endLine": 7, "endColumn": 31}, {"ruleId": "1897", "severity": 1, "message": "1928", "line": 11, "column": 8, "nodeType": "1899", "messageId": "1900", "endLine": 11, "endColumn": 21}, {"ruleId": "1897", "severity": 1, "message": "1920", "line": 16, "column": 8, "nodeType": "1899", "messageId": "1900", "endLine": 16, "endColumn": 14}, {"ruleId": "1897", "severity": 1, "message": "1929", "line": 17, "column": 10, "nodeType": "1899", "messageId": "1900", "endLine": 17, "endColumn": 20}, {"ruleId": "1903", "severity": 1, "message": "1930", "line": 152, "column": 6, "nodeType": "1905", "endLine": 152, "endColumn": 8, "suggestions": "1931"}, {"ruleId": "1903", "severity": 1, "message": "1932", "line": 169, "column": 6, "nodeType": "1905", "endLine": 169, "endColumn": 8, "suggestions": "1933"}, {"ruleId": "1897", "severity": 1, "message": "1928", "line": 11, "column": 8, "nodeType": "1899", "messageId": "1900", "endLine": 11, "endColumn": 21}, {"ruleId": "1897", "severity": 1, "message": "1901", "line": 12, "column": 10, "nodeType": "1899", "messageId": "1900", "endLine": 12, "endColumn": 15}, {"ruleId": "1897", "severity": 1, "message": "1921", "line": 12, "column": 17, "nodeType": "1899", "messageId": "1900", "endLine": 12, "endColumn": 31}, {"ruleId": "1903", "severity": 1, "message": "1934", "line": 66, "column": 8, "nodeType": "1905", "endLine": 66, "endColumn": 10, "suggestions": "1935"}, {"ruleId": "1903", "severity": 1, "message": "1936", "line": 76, "column": 8, "nodeType": "1905", "endLine": 76, "endColumn": 10, "suggestions": "1937"}, {"ruleId": "1938", "severity": 1, "message": "1939", "line": 31, "column": 113, "nodeType": "1940", "endLine": 31, "endColumn": 116}, {"ruleId": "1938", "severity": 1, "message": "1939", "line": 32, "column": 113, "nodeType": "1940", "endLine": 32, "endColumn": 116}, {"ruleId": "1897", "severity": 1, "message": "1918", "line": 1, "column": 10, "nodeType": "1899", "messageId": "1900", "endLine": 1, "endColumn": 19}, {"ruleId": "1897", "severity": 1, "message": "1941", "line": 3, "column": 8, "nodeType": "1899", "messageId": "1900", "endLine": 3, "endColumn": 16}, {"ruleId": "1897", "severity": 1, "message": "1942", "line": 4, "column": 10, "nodeType": "1899", "messageId": "1900", "endLine": 4, "endColumn": 22}, {"ruleId": "1897", "severity": 1, "message": "1943", "line": 8, "column": 11, "nodeType": "1899", "messageId": "1900", "endLine": 8, "endColumn": 19}, {"ruleId": "1944", "severity": 1, "message": "1945", "line": 24, "column": 23, "nodeType": "1940", "messageId": "1946", "endLine": 24, "endColumn": 70, "fix": "1947"}, {"ruleId": "1944", "severity": 1, "message": "1945", "line": 25, "column": 23, "nodeType": "1940", "messageId": "1946", "endLine": 25, "endColumn": 81, "fix": "1948"}, {"ruleId": "1944", "severity": 1, "message": "1945", "line": 26, "column": 23, "nodeType": "1940", "messageId": "1946", "endLine": 26, "endColumn": 74, "fix": "1949"}, {"ruleId": "1944", "severity": 1, "message": "1945", "line": 27, "column": 23, "nodeType": "1940", "messageId": "1946", "endLine": 27, "endColumn": 70, "fix": "1950"}, {"ruleId": "1944", "severity": 1, "message": "1945", "line": 28, "column": 23, "nodeType": "1940", "messageId": "1946", "endLine": 28, "endColumn": 72, "fix": "1951"}, {"ruleId": "1944", "severity": 1, "message": "1945", "line": 29, "column": 23, "nodeType": "1940", "messageId": "1946", "endLine": 29, "endColumn": 79, "fix": "1952"}, {"ruleId": "1944", "severity": 1, "message": "1945", "line": 30, "column": 23, "nodeType": "1940", "messageId": "1946", "endLine": 30, "endColumn": 81, "fix": "1953"}, {"ruleId": "1944", "severity": 1, "message": "1945", "line": 52, "column": 21, "nodeType": "1940", "messageId": "1946", "endLine": 52, "endColumn": 143, "fix": "1954"}, {"ruleId": "1944", "severity": 1, "message": "1945", "line": 68, "column": 18, "nodeType": "1940", "messageId": "1946", "endLine": 68, "endColumn": 94, "fix": "1955"}, {"ruleId": "1944", "severity": 1, "message": "1945", "line": 70, "column": 174, "nodeType": "1940", "messageId": "1946", "endLine": 70, "endColumn": 260, "fix": "1956"}, {"ruleId": "1944", "severity": 1, "message": "1945", "line": 101, "column": 102, "nodeType": "1940", "messageId": "1946", "endLine": 101, "endColumn": 160, "fix": "1957"}, {"ruleId": "1944", "severity": 1, "message": "1945", "line": 119, "column": 20, "nodeType": "1940", "messageId": "1946", "endLine": 119, "endColumn": 98, "fix": "1958"}, {"ruleId": "1944", "severity": 1, "message": "1945", "line": 121, "column": 104, "nodeType": "1940", "messageId": "1946", "endLine": 121, "endColumn": 193, "fix": "1959"}, {"ruleId": "1897", "severity": 1, "message": "1918", "line": 1, "column": 10, "nodeType": "1899", "messageId": "1900", "endLine": 1, "endColumn": 19}, {"ruleId": "1897", "severity": 1, "message": "1942", "line": 9, "column": 10, "nodeType": "1899", "messageId": "1900", "endLine": 9, "endColumn": 22}, {"ruleId": "1897", "severity": 1, "message": "1960", "line": 9, "column": 24, "nodeType": "1899", "messageId": "1900", "endLine": 9, "endColumn": 40}, {"ruleId": "1897", "severity": 1, "message": "1943", "line": 14, "column": 11, "nodeType": "1899", "messageId": "1900", "endLine": 14, "endColumn": 19}, {"ruleId": "1944", "severity": 1, "message": "1945", "line": 77, "column": 21, "nodeType": "1940", "messageId": "1946", "endLine": 77, "endColumn": 84, "fix": "1961"}, {"ruleId": "1938", "severity": 1, "message": "1939", "line": 27, "column": 113, "nodeType": "1940", "endLine": 27, "endColumn": 116}, {"ruleId": "1897", "severity": 1, "message": "1918", "line": 1, "column": 10, "nodeType": "1899", "messageId": "1900", "endLine": 1, "endColumn": 19}, {"ruleId": "1897", "severity": 1, "message": "1941", "line": 3, "column": 8, "nodeType": "1899", "messageId": "1900", "endLine": 3, "endColumn": 16}, {"ruleId": "1897", "severity": 1, "message": "1942", "line": 4, "column": 10, "nodeType": "1899", "messageId": "1900", "endLine": 4, "endColumn": 22}, {"ruleId": "1944", "severity": 1, "message": "1945", "line": 84, "column": 407, "nodeType": "1940", "messageId": "1946", "endLine": 84, "endColumn": 466, "fix": "1962"}, {"ruleId": "1897", "severity": 1, "message": "1901", "line": 9, "column": 9, "nodeType": "1899", "messageId": "1900", "endLine": 9, "endColumn": 14}, {"ruleId": "1897", "severity": 1, "message": "1921", "line": 9, "column": 16, "nodeType": "1899", "messageId": "1900", "endLine": 9, "endColumn": 30}, {"ruleId": "1897", "severity": 1, "message": "1963", "line": 12, "column": 8, "nodeType": "1899", "messageId": "1900", "endLine": 12, "endColumn": 19}, {"ruleId": "1897", "severity": 1, "message": "1964", "line": 14, "column": 7, "nodeType": "1899", "messageId": "1900", "endLine": 14, "endColumn": 11}, {"ruleId": "1897", "severity": 1, "message": "1965", "line": 53, "column": 7, "nodeType": "1899", "messageId": "1900", "endLine": 53, "endColumn": 18}, {"ruleId": "1897", "severity": 1, "message": "1966", "line": 65, "column": 10, "nodeType": "1899", "messageId": "1900", "endLine": 65, "endColumn": 18}, {"ruleId": "1897", "severity": 1, "message": "1967", "line": 6, "column": 27, "nodeType": "1899", "messageId": "1900", "endLine": 6, "endColumn": 39}, {"ruleId": "1897", "severity": 1, "message": "1968", "line": 9, "column": 10, "nodeType": "1899", "messageId": "1900", "endLine": 9, "endColumn": 15}, {"ruleId": "1897", "severity": 1, "message": "1964", "line": 13, "column": 7, "nodeType": "1899", "messageId": "1900", "endLine": 13, "endColumn": 11}, {"ruleId": "1897", "severity": 1, "message": "1969", "line": 55, "column": 10, "nodeType": "1899", "messageId": "1900", "endLine": 55, "endColumn": 19}, {"ruleId": "1897", "severity": 1, "message": "1970", "line": 55, "column": 21, "nodeType": "1899", "messageId": "1900", "endLine": 55, "endColumn": 33}, {"ruleId": "1897", "severity": 1, "message": "1971", "line": 26, "column": 19, "nodeType": "1899", "messageId": "1900", "endLine": 26, "endColumn": 26}, {"ruleId": "1903", "severity": 1, "message": "1972", "line": 94, "column": 6, "nodeType": "1905", "endLine": 94, "endColumn": 8, "suggestions": "1973"}, {"ruleId": "1897", "severity": 1, "message": "1974", "line": 13, "column": 8, "nodeType": "1899", "messageId": "1900", "endLine": 13, "endColumn": 17}, {"ruleId": "1897", "severity": 1, "message": "1975", "line": 43, "column": 9, "nodeType": "1899", "messageId": "1900", "endLine": 43, "endColumn": 17}, {"ruleId": "1897", "severity": 1, "message": "1976", "line": 139, "column": 11, "nodeType": "1899", "messageId": "1900", "endLine": 139, "endColumn": 20}, {"ruleId": "1938", "severity": 1, "message": "1939", "line": 345, "column": 17, "nodeType": "1940", "endLine": 345, "endColumn": 41}, {"ruleId": "1897", "severity": 1, "message": "1977", "line": 28, "column": 7, "nodeType": "1899", "messageId": "1900", "endLine": 28, "endColumn": 25}, {"ruleId": "1897", "severity": 1, "message": "1978", "line": 3, "column": 3, "nodeType": "1899", "messageId": "1900", "endLine": 3, "endColumn": 23}, {"ruleId": "1897", "severity": 1, "message": "1979", "line": 4, "column": 3, "nodeType": "1899", "messageId": "1900", "endLine": 4, "endColumn": 28}, {"ruleId": "1897", "severity": 1, "message": "1902", "line": 6, "column": 10, "nodeType": "1899", "messageId": "1900", "endLine": 6, "endColumn": 30}, {"ruleId": "1897", "severity": 1, "message": "1923", "line": 7, "column": 10, "nodeType": "1899", "messageId": "1900", "endLine": 7, "endColumn": 21}, {"ruleId": "1897", "severity": 1, "message": "1980", "line": 1, "column": 22, "nodeType": "1899", "messageId": "1900", "endLine": 1, "endColumn": 33}, {"ruleId": "1897", "severity": 1, "message": "1981", "line": 2, "column": 8, "nodeType": "1899", "messageId": "1900", "endLine": 2, "endColumn": 13}, {"ruleId": "1897", "severity": 1, "message": "1982", "line": 3, "column": 42, "nodeType": "1899", "messageId": "1900", "endLine": 3, "endColumn": 59}, {"ruleId": "1897", "severity": 1, "message": "1983", "line": 11, "column": 28, "nodeType": "1899", "messageId": "1900", "endLine": 11, "endColumn": 31}, {"ruleId": "1897", "severity": 1, "message": "1984", "line": 4, "column": 8, "nodeType": "1899", "messageId": "1900", "endLine": 4, "endColumn": 21}, {"ruleId": "1903", "severity": 1, "message": "1907", "line": 12, "column": 8, "nodeType": "1905", "endLine": 12, "endColumn": 10, "suggestions": "1985"}, {"ruleId": "1897", "severity": 1, "message": "1986", "line": 2, "column": 23, "nodeType": "1899", "messageId": "1900", "endLine": 2, "endColumn": 31}, {"ruleId": "1897", "severity": 1, "message": "1987", "line": 4, "column": 8, "nodeType": "1899", "messageId": "1900", "endLine": 4, "endColumn": 16}, {"ruleId": "1897", "severity": 1, "message": "1901", "line": 8, "column": 10, "nodeType": "1899", "messageId": "1900", "endLine": 8, "endColumn": 15}, {"ruleId": "1897", "severity": 1, "message": "1928", "line": 10, "column": 8, "nodeType": "1899", "messageId": "1900", "endLine": 10, "endColumn": 21}, {"ruleId": "1903", "severity": 1, "message": "1988", "line": 33, "column": 8, "nodeType": "1905", "endLine": 33, "endColumn": 10, "suggestions": "1989"}, {"ruleId": "1990", "severity": 1, "message": "1991", "line": 46, "column": 21, "nodeType": "1940", "endLine": 46, "endColumn": 112}, {"ruleId": "1897", "severity": 1, "message": "1901", "line": 7, "column": 10, "nodeType": "1899", "messageId": "1900", "endLine": 7, "endColumn": 15}, {"ruleId": "1897", "severity": 1, "message": "1992", "line": 8, "column": 7, "nodeType": "1899", "messageId": "1900", "endLine": 8, "endColumn": 12}, {"ruleId": "1897", "severity": 1, "message": "1993", "line": 12, "column": 7, "nodeType": "1899", "messageId": "1900", "endLine": 12, "endColumn": 18}, {"ruleId": "1897", "severity": 1, "message": "1994", "line": 13, "column": 7, "nodeType": "1899", "messageId": "1900", "endLine": 13, "endColumn": 15}, {"ruleId": "1897", "severity": 1, "message": "1995", "line": 14, "column": 7, "nodeType": "1899", "messageId": "1900", "endLine": 14, "endColumn": 11}, {"ruleId": "1897", "severity": 1, "message": "1996", "line": 17, "column": 7, "nodeType": "1899", "messageId": "1900", "endLine": 17, "endColumn": 12}, {"ruleId": "1897", "severity": 1, "message": "1997", "line": 19, "column": 7, "nodeType": "1899", "messageId": "1900", "endLine": 19, "endColumn": 21}, {"ruleId": "1897", "severity": 1, "message": "1998", "line": 20, "column": 7, "nodeType": "1899", "messageId": "1900", "endLine": 20, "endColumn": 18}, {"ruleId": "1897", "severity": 1, "message": "1999", "line": 21, "column": 7, "nodeType": "1899", "messageId": "1900", "endLine": 21, "endColumn": 20}, {"ruleId": "1897", "severity": 1, "message": "2000", "line": 1, "column": 10, "nodeType": "1899", "messageId": "1900", "endLine": 1, "endColumn": 31}, {"ruleId": "1897", "severity": 1, "message": "1918", "line": 1, "column": 17, "nodeType": "1899", "messageId": "1900", "endLine": 1, "endColumn": 26}, {"ruleId": "1897", "severity": 1, "message": "1967", "line": 2, "column": 10, "nodeType": "1899", "messageId": "1900", "endLine": 2, "endColumn": 22}, {"ruleId": "1897", "severity": 1, "message": "1968", "line": 3, "column": 10, "nodeType": "1899", "messageId": "1900", "endLine": 3, "endColumn": 15}, {"ruleId": "1897", "severity": 1, "message": "2001", "line": 1, "column": 7, "nodeType": "1899", "messageId": "1900", "endLine": 1, "endColumn": 13}, {"ruleId": "1897", "severity": 1, "message": "1909", "line": 1, "column": 9, "nodeType": "1899", "messageId": "1900", "endLine": 1, "endColumn": 17}, {"ruleId": "1897", "severity": 1, "message": "1910", "line": 1, "column": 19, "nodeType": "1899", "messageId": "1900", "endLine": 1, "endColumn": 25}, {"ruleId": "1897", "severity": 1, "message": "1911", "line": 2, "column": 9, "nodeType": "1899", "messageId": "1900", "endLine": 2, "endColumn": 13}, {"ruleId": "1897", "severity": 1, "message": "1909", "line": 1, "column": 9, "nodeType": "1899", "messageId": "1900", "endLine": 1, "endColumn": 17}, {"ruleId": "1897", "severity": 1, "message": "1910", "line": 1, "column": 19, "nodeType": "1899", "messageId": "1900", "endLine": 1, "endColumn": 25}, {"ruleId": "1897", "severity": 1, "message": "1911", "line": 2, "column": 9, "nodeType": "1899", "messageId": "1900", "endLine": 2, "endColumn": 13}, {"ruleId": "1903", "severity": 1, "message": "1907", "line": 11, "column": 10, "nodeType": "1905", "endLine": 11, "endColumn": 12, "suggestions": "2002"}, {"ruleId": "1903", "severity": 1, "message": "1907", "line": 11, "column": 10, "nodeType": "1905", "endLine": 11, "endColumn": 12, "suggestions": "2003"}, {"ruleId": "1903", "severity": 1, "message": "1907", "line": 11, "column": 10, "nodeType": "1905", "endLine": 11, "endColumn": 12, "suggestions": "2004"}, {"ruleId": "1903", "severity": 1, "message": "1907", "line": 11, "column": 10, "nodeType": "1905", "endLine": 11, "endColumn": 12, "suggestions": "2005"}, {"ruleId": "1903", "severity": 1, "message": "1907", "line": 12, "column": 10, "nodeType": "1905", "endLine": 12, "endColumn": 12, "suggestions": "2006"}, {"ruleId": "1903", "severity": 1, "message": "1907", "line": 11, "column": 10, "nodeType": "1905", "endLine": 11, "endColumn": 12, "suggestions": "2007"}, {"ruleId": "1903", "severity": 1, "message": "1907", "line": 12, "column": 8, "nodeType": "1905", "endLine": 12, "endColumn": 10, "suggestions": "2008"}, {"ruleId": "1903", "severity": 1, "message": "1907", "line": 11, "column": 10, "nodeType": "1905", "endLine": 11, "endColumn": 12, "suggestions": "2009"}, {"ruleId": "1903", "severity": 1, "message": "1907", "line": 12, "column": 8, "nodeType": "1905", "endLine": 12, "endColumn": 10, "suggestions": "2010"}, {"ruleId": "1903", "severity": 1, "message": "1907", "line": 11, "column": 10, "nodeType": "1905", "endLine": 11, "endColumn": 12, "suggestions": "2011"}, {"ruleId": "1903", "severity": 1, "message": "1907", "line": 13, "column": 10, "nodeType": "1905", "endLine": 13, "endColumn": 12, "suggestions": "2012"}, {"ruleId": "1897", "severity": 1, "message": "1909", "line": 1, "column": 9, "nodeType": "1899", "messageId": "1900", "endLine": 1, "endColumn": 17}, {"ruleId": "1897", "severity": 1, "message": "1910", "line": 1, "column": 19, "nodeType": "1899", "messageId": "1900", "endLine": 1, "endColumn": 25}, {"ruleId": "1897", "severity": 1, "message": "1911", "line": 2, "column": 9, "nodeType": "1899", "messageId": "1900", "endLine": 2, "endColumn": 13}, {"ruleId": "1903", "severity": 1, "message": "1907", "line": 11, "column": 10, "nodeType": "1905", "endLine": 11, "endColumn": 12, "suggestions": "2013"}, {"ruleId": "1903", "severity": 1, "message": "1907", "line": 11, "column": 10, "nodeType": "1905", "endLine": 11, "endColumn": 12, "suggestions": "2014"}, {"ruleId": "1897", "severity": 1, "message": "1911", "line": 4, "column": 9, "nodeType": "1899", "messageId": "1900", "endLine": 4, "endColumn": 13}, {"ruleId": "1903", "severity": 1, "message": "1907", "line": 12, "column": 6, "nodeType": "1905", "endLine": 12, "endColumn": 8, "suggestions": "2015"}, {"ruleId": "1897", "severity": 1, "message": "1909", "line": 1, "column": 21, "nodeType": "1899", "messageId": "1900", "endLine": 1, "endColumn": 29}, {"ruleId": "1897", "severity": 1, "message": "2016", "line": 3, "column": 8, "nodeType": "1899", "messageId": "1900", "endLine": 3, "endColumn": 17}, {"ruleId": "1897", "severity": 1, "message": "1960", "line": 4, "column": 24, "nodeType": "1899", "messageId": "1900", "endLine": 4, "endColumn": 40}, {"ruleId": "1897", "severity": 1, "message": "2017", "line": 6, "column": 8, "nodeType": "1899", "messageId": "1900", "endLine": 6, "endColumn": 14}, {"ruleId": "1897", "severity": 1, "message": "2018", "line": 8, "column": 8, "nodeType": "1899", "messageId": "1900", "endLine": 8, "endColumn": 19}, {"ruleId": "1897", "severity": 1, "message": "2019", "line": 9, "column": 8, "nodeType": "1899", "messageId": "1900", "endLine": 9, "endColumn": 23}, {"ruleId": "1903", "severity": 1, "message": "1907", "line": 19, "column": 10, "nodeType": "1905", "endLine": 19, "endColumn": 12, "suggestions": "2020"}, {"ruleId": "1897", "severity": 1, "message": "1909", "line": 1, "column": 21, "nodeType": "1899", "messageId": "1900", "endLine": 1, "endColumn": 29}, {"ruleId": "1897", "severity": 1, "message": "2016", "line": 3, "column": 8, "nodeType": "1899", "messageId": "1900", "endLine": 3, "endColumn": 17}, {"ruleId": "1897", "severity": 1, "message": "1960", "line": 4, "column": 24, "nodeType": "1899", "messageId": "1900", "endLine": 4, "endColumn": 40}, {"ruleId": "1897", "severity": 1, "message": "2021", "line": 5, "column": 8, "nodeType": "1899", "messageId": "1900", "endLine": 5, "endColumn": 25}, {"ruleId": "1897", "severity": 1, "message": "2017", "line": 6, "column": 8, "nodeType": "1899", "messageId": "1900", "endLine": 6, "endColumn": 14}, {"ruleId": "1897", "severity": 1, "message": "2022", "line": 7, "column": 8, "nodeType": "1899", "messageId": "1900", "endLine": 7, "endColumn": 29}, {"ruleId": "1903", "severity": 1, "message": "1907", "line": 19, "column": 10, "nodeType": "1905", "endLine": 19, "endColumn": 12, "suggestions": "2023"}, {"ruleId": "1897", "severity": 1, "message": "1909", "line": 1, "column": 21, "nodeType": "1899", "messageId": "1900", "endLine": 1, "endColumn": 29}, {"ruleId": "1897", "severity": 1, "message": "2016", "line": 3, "column": 8, "nodeType": "1899", "messageId": "1900", "endLine": 3, "endColumn": 17}, {"ruleId": "1897", "severity": 1, "message": "1960", "line": 4, "column": 24, "nodeType": "1899", "messageId": "1900", "endLine": 4, "endColumn": 40}, {"ruleId": "1897", "severity": 1, "message": "2017", "line": 6, "column": 8, "nodeType": "1899", "messageId": "1900", "endLine": 6, "endColumn": 14}, {"ruleId": "1903", "severity": 1, "message": "1907", "line": 17, "column": 10, "nodeType": "1905", "endLine": 17, "endColumn": 12, "suggestions": "2024"}, {"ruleId": "1903", "severity": 1, "message": "1934", "line": 31, "column": 8, "nodeType": "1905", "endLine": 31, "endColumn": 29, "suggestions": "2025"}, {"ruleId": "1897", "severity": 1, "message": "1921", "line": 3, "column": 10, "nodeType": "1899", "messageId": "1900", "endLine": 3, "endColumn": 24}, {"ruleId": "1903", "severity": 1, "message": "2026", "line": 60, "column": 8, "nodeType": "1905", "endLine": 60, "endColumn": 20, "suggestions": "2027"}, {"ruleId": "1938", "severity": 1, "message": "1939", "line": 95, "column": 17, "nodeType": "1940", "endLine": 99, "endColumn": 18}, {"ruleId": "1938", "severity": 1, "message": "1939", "line": 106, "column": 15, "nodeType": "1940", "endLine": 109, "endColumn": 16}, {"ruleId": "1903", "severity": 1, "message": "2028", "line": 195, "column": 5, "nodeType": "1905", "endLine": 195, "endColumn": 15, "suggestions": "2029"}, {"ruleId": "2030", "severity": 1, "message": "2031", "line": 205, "column": 5, "nodeType": "2032", "messageId": "2033", "endLine": 224, "endColumn": 6}, {"ruleId": "1903", "severity": 1, "message": "2034", "line": 305, "column": 6, "nodeType": "1905", "endLine": 305, "endColumn": 78, "suggestions": "2035"}, {"ruleId": "1903", "severity": 1, "message": "2036", "line": 311, "column": 6, "nodeType": "1905", "endLine": 311, "endColumn": 22, "suggestions": "2037"}, {"ruleId": "1903", "severity": 1, "message": "1934", "line": 317, "column": 6, "nodeType": "1905", "endLine": 317, "endColumn": 18, "suggestions": "2038"}, {"ruleId": "1903", "severity": 1, "message": "2039", "line": 326, "column": 6, "nodeType": "1905", "endLine": 326, "endColumn": 18, "suggestions": "2040"}, {"ruleId": "1897", "severity": 1, "message": "2041", "line": 599, "column": 39, "nodeType": "1899", "messageId": "1900", "endLine": 599, "endColumn": 51}, {"ruleId": "1897", "severity": 1, "message": "2042", "line": 601, "column": 39, "nodeType": "1899", "messageId": "1900", "endLine": 601, "endColumn": 46}, {"ruleId": "1897", "severity": 1, "message": "2043", "line": 602, "column": 39, "nodeType": "1899", "messageId": "1900", "endLine": 602, "endColumn": 51}, {"ruleId": "1897", "severity": 1, "message": "2044", "line": 2, "column": 8, "nodeType": "1899", "messageId": "1900", "endLine": 2, "endColumn": 19}, {"ruleId": "1897", "severity": 1, "message": "2045", "line": 3, "column": 8, "nodeType": "1899", "messageId": "1900", "endLine": 3, "endColumn": 17}, {"ruleId": "1897", "severity": 1, "message": "2046", "line": 10, "column": 8, "nodeType": "1899", "messageId": "1900", "endLine": 10, "endColumn": 17}, {"ruleId": "1897", "severity": 1, "message": "2047", "line": 11, "column": 8, "nodeType": "1899", "messageId": "1900", "endLine": 11, "endColumn": 23}, {"ruleId": "1897", "severity": 1, "message": "2048", "line": 12, "column": 8, "nodeType": "1899", "messageId": "1900", "endLine": 12, "endColumn": 22}, {"ruleId": "1897", "severity": 1, "message": "2049", "line": 13, "column": 8, "nodeType": "1899", "messageId": "1900", "endLine": 13, "endColumn": 20}, {"ruleId": "1897", "severity": 1, "message": "2050", "line": 14, "column": 8, "nodeType": "1899", "messageId": "1900", "endLine": 14, "endColumn": 17}, {"ruleId": "1897", "severity": 1, "message": "2051", "line": 15, "column": 8, "nodeType": "1899", "messageId": "1900", "endLine": 15, "endColumn": 16}, {"ruleId": "1897", "severity": 1, "message": "2052", "line": 16, "column": 8, "nodeType": "1899", "messageId": "1900", "endLine": 16, "endColumn": 23}, {"ruleId": "1897", "severity": 1, "message": "2053", "line": 19, "column": 8, "nodeType": "1899", "messageId": "1900", "endLine": 19, "endColumn": 21}, {"ruleId": "1897", "severity": 1, "message": "2054", "line": 113, "column": 9, "nodeType": "1899", "messageId": "1900", "endLine": 113, "endColumn": 30}, {"ruleId": "1897", "severity": 1, "message": "1910", "line": 2, "column": 38, "nodeType": "1899", "messageId": "1900", "endLine": 2, "endColumn": 44}, {"ruleId": "1897", "severity": 1, "message": "1981", "line": 17, "column": 8, "nodeType": "1899", "messageId": "1900", "endLine": 17, "endColumn": 13}, {"ruleId": "2055", "severity": 1, "message": "2056", "line": 43, "column": 46, "nodeType": "2057", "messageId": "2058", "endLine": 43, "endColumn": 47, "suggestions": "2059"}, {"ruleId": "2060", "severity": 1, "message": "2061", "line": 52, "column": 28, "nodeType": "2062", "messageId": "2063", "endLine": 52, "endColumn": 30}, {"ruleId": "2060", "severity": 1, "message": "2061", "line": 52, "column": 54, "nodeType": "2062", "messageId": "2063", "endLine": 52, "endColumn": 56}, {"ruleId": "2060", "severity": 1, "message": "2061", "line": 52, "column": 89, "nodeType": "2062", "messageId": "2063", "endLine": 52, "endColumn": 91}, {"ruleId": "2060", "severity": 1, "message": "2061", "line": 62, "column": 28, "nodeType": "2062", "messageId": "2063", "endLine": 62, "endColumn": 30}, {"ruleId": "2060", "severity": 1, "message": "2061", "line": 62, "column": 54, "nodeType": "2062", "messageId": "2063", "endLine": 62, "endColumn": 56}, {"ruleId": "2060", "severity": 1, "message": "2061", "line": 62, "column": 89, "nodeType": "2062", "messageId": "2063", "endLine": 62, "endColumn": 91}, {"ruleId": "1897", "severity": 1, "message": "2064", "line": 437, "column": 10, "nodeType": "1899", "messageId": "1900", "endLine": 437, "endColumn": 22}, {"ruleId": "1897", "severity": 1, "message": "2065", "line": 443, "column": 10, "nodeType": "1899", "messageId": "1900", "endLine": 443, "endColumn": 22}, {"ruleId": "1897", "severity": 1, "message": "2066", "line": 483, "column": 12, "nodeType": "1899", "messageId": "1900", "endLine": 483, "endColumn": 29}, {"ruleId": "1903", "severity": 1, "message": "2067", "line": 536, "column": 6, "nodeType": "1905", "endLine": 542, "endColumn": 4, "suggestions": "2068"}, {"ruleId": "1897", "severity": 1, "message": "2069", "line": 6, "column": 8, "nodeType": "1899", "messageId": "1900", "endLine": 6, "endColumn": 17}, {"ruleId": "1897", "severity": 1, "message": "2070", "line": 7, "column": 8, "nodeType": "1899", "messageId": "1900", "endLine": 7, "endColumn": 21}, {"ruleId": "1897", "severity": 1, "message": "2071", "line": 9, "column": 8, "nodeType": "1899", "messageId": "1900", "endLine": 9, "endColumn": 20}, {"ruleId": "1897", "severity": 1, "message": "2072", "line": 68, "column": 9, "nodeType": "1899", "messageId": "1900", "endLine": 68, "endColumn": 26}, {"ruleId": "1897", "severity": 1, "message": "2073", "line": 72, "column": 9, "nodeType": "1899", "messageId": "1900", "endLine": 72, "endColumn": 21}, {"ruleId": "1897", "severity": 1, "message": "1901", "line": 4, "column": 10, "nodeType": "1899", "messageId": "1900", "endLine": 4, "endColumn": 15}, {"ruleId": "1897", "severity": 1, "message": "1921", "line": 4, "column": 17, "nodeType": "1899", "messageId": "1900", "endLine": 4, "endColumn": 31}, {"ruleId": "1897", "severity": 1, "message": "1928", "line": 18, "column": 8, "nodeType": "1899", "messageId": "1900", "endLine": 18, "endColumn": 21}, {"ruleId": "1897", "severity": 1, "message": "2074", "line": 22, "column": 8, "nodeType": "1899", "messageId": "1900", "endLine": 22, "endColumn": 22}, {"ruleId": "1903", "severity": 1, "message": "2075", "line": 50, "column": 6, "nodeType": "1905", "endLine": 50, "endColumn": 18, "suggestions": "2076"}, {"ruleId": "1938", "severity": 1, "message": "1939", "line": 88, "column": 15, "nodeType": "1940", "endLine": 92, "endColumn": 16}, {"ruleId": "1938", "severity": 1, "message": "1939", "line": 99, "column": 13, "nodeType": "1940", "endLine": 99, "endColumn": 85}, {"ruleId": "1897", "severity": 1, "message": "1901", "line": 4, "column": 10, "nodeType": "1899", "messageId": "1900", "endLine": 4, "endColumn": 15}, {"ruleId": "1897", "severity": 1, "message": "1921", "line": 4, "column": 17, "nodeType": "1899", "messageId": "1900", "endLine": 4, "endColumn": 31}, {"ruleId": "1897", "severity": 1, "message": "2077", "line": 17, "column": 8, "nodeType": "1899", "messageId": "1900", "endLine": 17, "endColumn": 22}, {"ruleId": "1903", "severity": 1, "message": "2075", "line": 51, "column": 6, "nodeType": "1905", "endLine": 51, "endColumn": 18, "suggestions": "2078"}, {"ruleId": "1938", "severity": 1, "message": "1939", "line": 91, "column": 15, "nodeType": "1940", "endLine": 95, "endColumn": 16}, {"ruleId": "1938", "severity": 1, "message": "1939", "line": 102, "column": 13, "nodeType": "1940", "endLine": 105, "endColumn": 14}, {"ruleId": "1897", "severity": 1, "message": "1918", "line": 2, "column": 10, "nodeType": "1899", "messageId": "1900", "endLine": 2, "endColumn": 19}, {"ruleId": "1897", "severity": 1, "message": "1980", "line": 3, "column": 23, "nodeType": "1899", "messageId": "1900", "endLine": 3, "endColumn": 34}, {"ruleId": "1897", "severity": 1, "message": "2079", "line": 36, "column": 21, "nodeType": "1899", "messageId": "1900", "endLine": 36, "endColumn": 31}, {"ruleId": "1897", "severity": 1, "message": "1918", "line": 2, "column": 10, "nodeType": "1899", "messageId": "1900", "endLine": 2, "endColumn": 19}, {"ruleId": "1897", "severity": 1, "message": "1923", "line": 3, "column": 10, "nodeType": "1899", "messageId": "1900", "endLine": 3, "endColumn": 21}, {"ruleId": "1897", "severity": 1, "message": "1980", "line": 3, "column": 23, "nodeType": "1899", "messageId": "1900", "endLine": 3, "endColumn": 34}, {"ruleId": "1897", "severity": 1, "message": "1960", "line": 5, "column": 10, "nodeType": "1899", "messageId": "1900", "endLine": 5, "endColumn": 26}, {"ruleId": "1897", "severity": 1, "message": "2080", "line": 32, "column": 19, "nodeType": "1899", "messageId": "1900", "endLine": 32, "endColumn": 27}, {"ruleId": "1897", "severity": 1, "message": "1960", "line": 3, "column": 10, "nodeType": "1899", "messageId": "1900", "endLine": 3, "endColumn": 26}, {"ruleId": "1897", "severity": 1, "message": "2081", "line": 4, "column": 10, "nodeType": "1899", "messageId": "1900", "endLine": 4, "endColumn": 21}, {"ruleId": "1897", "severity": 1, "message": "2082", "line": 7, "column": 8, "nodeType": "1899", "messageId": "1900", "endLine": 7, "endColumn": 23}, {"ruleId": "1897", "severity": 1, "message": "1921", "line": 11, "column": 17, "nodeType": "1899", "messageId": "1900", "endLine": 11, "endColumn": 31}, {"ruleId": "1897", "severity": 1, "message": "2083", "line": 27, "column": 12, "nodeType": "1899", "messageId": "1900", "endLine": 27, "endColumn": 23}, {"ruleId": "1897", "severity": 1, "message": "2084", "line": 44, "column": 11, "nodeType": "1899", "messageId": "1900", "endLine": 44, "endColumn": 22}, {"ruleId": "1903", "severity": 1, "message": "2085", "line": 86, "column": 8, "nodeType": "1905", "endLine": 86, "endColumn": 14, "suggestions": "2086"}, {"ruleId": "2087", "severity": 1, "message": "2088", "line": 95, "column": 21, "nodeType": "2089", "messageId": "2090", "endLine": 95, "endColumn": 96}, {"ruleId": "1897", "severity": 1, "message": "2091", "line": 2, "column": 18, "nodeType": "1899", "messageId": "1900", "endLine": 2, "endColumn": 24}, {"ruleId": "1897", "severity": 1, "message": "1901", "line": 23, "column": 10, "nodeType": "1899", "messageId": "1900", "endLine": 23, "endColumn": 15}, {"ruleId": "1897", "severity": 1, "message": "2092", "line": 43, "column": 11, "nodeType": "1899", "messageId": "1900", "endLine": 43, "endColumn": 21}, {"ruleId": "1903", "severity": 1, "message": "2093", "line": 131, "column": 8, "nodeType": "1905", "endLine": 131, "endColumn": 10, "suggestions": "2094"}, {"ruleId": "1903", "severity": 1, "message": "2095", "line": 160, "column": 8, "nodeType": "1905", "endLine": 160, "endColumn": 10, "suggestions": "2096"}, {"ruleId": "1903", "severity": 1, "message": "2097", "line": 179, "column": 43, "nodeType": "1899", "endLine": 179, "endColumn": 50}, {"ruleId": "1903", "severity": 1, "message": "2098", "line": 16, "column": 6, "nodeType": "1905", "endLine": 16, "endColumn": 8, "suggestions": "2099"}, {"ruleId": "1897", "severity": 1, "message": "1918", "line": 2, "column": 10, "nodeType": "1899", "messageId": "1900", "endLine": 2, "endColumn": 19}, {"ruleId": "1897", "severity": 1, "message": "1910", "line": 2, "column": 21, "nodeType": "1899", "messageId": "1900", "endLine": 2, "endColumn": 27}, {"ruleId": "1897", "severity": 1, "message": "1967", "line": 13, "column": 27, "nodeType": "1899", "messageId": "1900", "endLine": 13, "endColumn": 39}, {"ruleId": "1897", "severity": 1, "message": "2100", "line": 23, "column": 23, "nodeType": "1899", "messageId": "1900", "endLine": 23, "endColumn": 31}, {"ruleId": "1897", "severity": 1, "message": "2101", "line": 42, "column": 10, "nodeType": "1899", "messageId": "1900", "endLine": 42, "endColumn": 16}, {"ruleId": "1897", "severity": 1, "message": "2102", "line": 43, "column": 10, "nodeType": "1899", "messageId": "1900", "endLine": 43, "endColumn": 21}, {"ruleId": "1897", "severity": 1, "message": "2103", "line": 44, "column": 10, "nodeType": "1899", "messageId": "1900", "endLine": 44, "endColumn": 17}, {"ruleId": "1897", "severity": 1, "message": "2104", "line": 59, "column": 11, "nodeType": "1899", "messageId": "1900", "endLine": 59, "endColumn": 23}, {"ruleId": "1903", "severity": 1, "message": "2028", "line": 89, "column": 5, "nodeType": "1905", "endLine": 89, "endColumn": 15, "suggestions": "2105"}, {"ruleId": "2106", "severity": 1, "message": "2107", "line": 115, "column": 64, "nodeType": "2108", "messageId": "2109", "endLine": 115, "endColumn": 66}, {"ruleId": "1903", "severity": 1, "message": "2110", "line": 151, "column": 6, "nodeType": "1905", "endLine": 151, "endColumn": 61, "suggestions": "2111"}, {"ruleId": "1903", "severity": 1, "message": "2112", "line": 192, "column": 6, "nodeType": "1905", "endLine": 192, "endColumn": 25, "suggestions": "2113"}, {"ruleId": "1897", "severity": 1, "message": "2042", "line": 216, "column": 44, "nodeType": "1899", "messageId": "1900", "endLine": 216, "endColumn": 51}, {"ruleId": "1897", "severity": 1, "message": "2043", "line": 216, "column": 53, "nodeType": "1899", "messageId": "1900", "endLine": 216, "endColumn": 65}, {"ruleId": "1897", "severity": 1, "message": "2041", "line": 235, "column": 17, "nodeType": "1899", "messageId": "1900", "endLine": 235, "endColumn": 29}, {"ruleId": "1897", "severity": 1, "message": "2042", "line": 235, "column": 44, "nodeType": "1899", "messageId": "1900", "endLine": 235, "endColumn": 51}, {"ruleId": "1897", "severity": 1, "message": "2043", "line": 235, "column": 53, "nodeType": "1899", "messageId": "1900", "endLine": 235, "endColumn": 65}, {"ruleId": "1897", "severity": 1, "message": "2114", "line": 11, "column": 8, "nodeType": "1899", "messageId": "1900", "endLine": 11, "endColumn": 14}, {"ruleId": "1897", "severity": 1, "message": "2115", "line": 40, "column": 12, "nodeType": "1899", "messageId": "1900", "endLine": 40, "endColumn": 25}, {"ruleId": "1903", "severity": 1, "message": "1907", "line": 74, "column": 8, "nodeType": "1905", "endLine": 74, "endColumn": 10, "suggestions": "2116"}, {"ruleId": "1903", "severity": 1, "message": "2117", "line": 151, "column": 8, "nodeType": "1905", "endLine": 151, "endColumn": 43, "suggestions": "2118"}, {"ruleId": "1897", "severity": 1, "message": "1921", "line": 3, "column": 10, "nodeType": "1899", "messageId": "1900", "endLine": 3, "endColumn": 24}, {"ruleId": "1897", "severity": 1, "message": "1974", "line": 9, "column": 8, "nodeType": "1899", "messageId": "1900", "endLine": 9, "endColumn": 17}, {"ruleId": "1897", "severity": 1, "message": "1967", "line": 11, "column": 27, "nodeType": "1899", "messageId": "1900", "endLine": 11, "endColumn": 39}, {"ruleId": "1897", "severity": 1, "message": "2071", "line": 15, "column": 8, "nodeType": "1899", "messageId": "1900", "endLine": 15, "endColumn": 20}, {"ruleId": "1897", "severity": 1, "message": "2119", "line": 22, "column": 10, "nodeType": "1899", "messageId": "1900", "endLine": 22, "endColumn": 20}, {"ruleId": "1897", "severity": 1, "message": "2091", "line": 23, "column": 10, "nodeType": "1899", "messageId": "1900", "endLine": 23, "endColumn": 16}, {"ruleId": "1897", "severity": 1, "message": "2120", "line": 31, "column": 10, "nodeType": "1899", "messageId": "1900", "endLine": 31, "endColumn": 21}, {"ruleId": "1903", "severity": 1, "message": "2121", "line": 68, "column": 8, "nodeType": "1905", "endLine": 68, "endColumn": 20, "suggestions": "2122"}, {"ruleId": "1938", "severity": 1, "message": "1939", "line": 103, "column": 17, "nodeType": "1940", "endLine": 107, "endColumn": 18}, {"ruleId": "1938", "severity": 1, "message": "1939", "line": 114, "column": 15, "nodeType": "1940", "endLine": 117, "endColumn": 16}, {"ruleId": "1897", "severity": 1, "message": "2123", "line": 137, "column": 9, "nodeType": "1899", "messageId": "1900", "endLine": 137, "endColumn": 27}, {"ruleId": "1897", "severity": 1, "message": "2124", "line": 140, "column": 10, "nodeType": "1899", "messageId": "1900", "endLine": 140, "endColumn": 26}, {"ruleId": "1897", "severity": 1, "message": "2125", "line": 142, "column": 10, "nodeType": "1899", "messageId": "1900", "endLine": 142, "endColumn": 26}, {"ruleId": "1897", "severity": 1, "message": "2126", "line": 142, "column": 28, "nodeType": "1899", "messageId": "1900", "endLine": 142, "endColumn": 47}, {"ruleId": "1897", "severity": 1, "message": "2127", "line": 151, "column": 10, "nodeType": "1899", "messageId": "1900", "endLine": 151, "endColumn": 21}, {"ruleId": "1897", "severity": 1, "message": "2128", "line": 151, "column": 23, "nodeType": "1899", "messageId": "1900", "endLine": 151, "endColumn": 37}, {"ruleId": "1897", "severity": 1, "message": "2129", "line": 152, "column": 10, "nodeType": "1899", "messageId": "1900", "endLine": 152, "endColumn": 29}, {"ruleId": "1897", "severity": 1, "message": "2130", "line": 152, "column": 31, "nodeType": "1899", "messageId": "1900", "endLine": 152, "endColumn": 47}, {"ruleId": "1897", "severity": 1, "message": "2131", "line": 171, "column": 11, "nodeType": "1899", "messageId": "1900", "endLine": 171, "endColumn": 20}, {"ruleId": "1897", "severity": 1, "message": "1975", "line": 174, "column": 9, "nodeType": "1899", "messageId": "1900", "endLine": 174, "endColumn": 17}, {"ruleId": "1897", "severity": 1, "message": "2041", "line": 210, "column": 7, "nodeType": "1899", "messageId": "1900", "endLine": 210, "endColumn": 19}, {"ruleId": "1897", "severity": 1, "message": "2042", "line": 213, "column": 7, "nodeType": "1899", "messageId": "1900", "endLine": 213, "endColumn": 14}, {"ruleId": "1897", "severity": 1, "message": "2043", "line": 214, "column": 7, "nodeType": "1899", "messageId": "1900", "endLine": 214, "endColumn": 19}, {"ruleId": "1903", "severity": 1, "message": "2132", "line": 273, "column": 6, "nodeType": "1905", "endLine": 273, "endColumn": 8, "suggestions": "2133"}, {"ruleId": "1903", "severity": 1, "message": "1907", "line": 321, "column": 6, "nodeType": "1905", "endLine": 321, "endColumn": 34, "suggestions": "2134"}, {"ruleId": "1903", "severity": 1, "message": "2026", "line": 56, "column": 8, "nodeType": "1905", "endLine": 56, "endColumn": 20, "suggestions": "2135"}, {"ruleId": "1897", "severity": 1, "message": "2136", "line": 133, "column": 11, "nodeType": "1899", "messageId": "1900", "endLine": 133, "endColumn": 19}, {"ruleId": "2030", "severity": 1, "message": "2031", "line": 166, "column": 9, "nodeType": "2032", "messageId": "2033", "endLine": 185, "endColumn": 10}, {"ruleId": "1903", "severity": 1, "message": "2034", "line": 260, "column": 8, "nodeType": "1905", "endLine": 260, "endColumn": 47, "suggestions": "2137"}, {"ruleId": "1903", "severity": 1, "message": "2138", "line": 266, "column": 8, "nodeType": "1905", "endLine": 266, "endColumn": 24, "suggestions": "2139"}, {"ruleId": "1903", "severity": 1, "message": "1934", "line": 272, "column": 8, "nodeType": "1905", "endLine": 272, "endColumn": 20, "suggestions": "2140"}, {"ruleId": "1903", "severity": 1, "message": "2039", "line": 281, "column": 8, "nodeType": "1905", "endLine": 281, "endColumn": 20, "suggestions": "2141"}, {"ruleId": "1897", "severity": 1, "message": "2142", "line": 25, "column": 12, "nodeType": "1899", "messageId": "1900", "endLine": 25, "endColumn": 21}, {"ruleId": "2060", "severity": 1, "message": "2143", "line": 55, "column": 18, "nodeType": "2062", "messageId": "2063", "endLine": 55, "endColumn": 20}, {"ruleId": "2060", "severity": 1, "message": "2143", "line": 65, "column": 37, "nodeType": "2062", "messageId": "2063", "endLine": 65, "endColumn": 39}, {"ruleId": "1903", "severity": 1, "message": "2144", "line": 24, "column": 8, "nodeType": "1905", "endLine": 24, "endColumn": 10, "suggestions": "2145"}, {"ruleId": "1897", "severity": 1, "message": "1901", "line": 3, "column": 10, "nodeType": "1899", "messageId": "1900", "endLine": 3, "endColumn": 15}, {"ruleId": "1897", "severity": 1, "message": "2146", "line": 39, "column": 11, "nodeType": "1899", "messageId": "1900", "endLine": 39, "endColumn": 19}, {"ruleId": "1897", "severity": 1, "message": "2147", "line": 6, "column": 10, "nodeType": "1899", "messageId": "1900", "endLine": 6, "endColumn": 21}, {"ruleId": "1897", "severity": 1, "message": "2148", "line": 8, "column": 8, "nodeType": "1899", "messageId": "1900", "endLine": 8, "endColumn": 22}, {"ruleId": "1897", "severity": 1, "message": "2149", "line": 13, "column": 10, "nodeType": "1899", "messageId": "1900", "endLine": 13, "endColumn": 21}, {"ruleId": "1897", "severity": 1, "message": "2150", "line": 13, "column": 23, "nodeType": "1899", "messageId": "1900", "endLine": 13, "endColumn": 37}, {"ruleId": "1897", "severity": 1, "message": "2151", "line": 16, "column": 9, "nodeType": "1899", "messageId": "1900", "endLine": 16, "endColumn": 22}, {"ruleId": "1897", "severity": 1, "message": "2152", "line": 50, "column": 12, "nodeType": "1899", "messageId": "1900", "endLine": 50, "endColumn": 27}, {"ruleId": "1897", "severity": 1, "message": "1964", "line": 55, "column": 9, "nodeType": "1899", "messageId": "1900", "endLine": 55, "endColumn": 13}, {"ruleId": "1903", "severity": 1, "message": "2093", "line": 83, "column": 6, "nodeType": "1905", "endLine": 83, "endColumn": 8, "suggestions": "2153"}, {"ruleId": "1897", "severity": 1, "message": "2154", "line": 15, "column": 9, "nodeType": "1899", "messageId": "1900", "endLine": 15, "endColumn": 21}, {"ruleId": "2155", "severity": 1, "message": "2156", "line": 3, "column": 22, "nodeType": "2157", "messageId": "2063", "endLine": 3, "endColumn": 24}, {"ruleId": "2155", "severity": 1, "message": "2156", "line": 5, "column": 20, "nodeType": "2157", "messageId": "2063", "endLine": 5, "endColumn": 22}, {"ruleId": "1897", "severity": 1, "message": "2158", "line": 11, "column": 7, "nodeType": "1899", "messageId": "1900", "endLine": 11, "endColumn": 20}, {"ruleId": "1938", "severity": 1, "message": "1939", "line": 65, "column": 29, "nodeType": "1940", "endLine": 65, "endColumn": 32}, {"ruleId": "1938", "severity": 1, "message": "1939", "line": 66, "column": 29, "nodeType": "1940", "endLine": 66, "endColumn": 32}, {"ruleId": "1897", "severity": 1, "message": "1913", "line": 5, "column": 3, "nodeType": "1899", "messageId": "1900", "endLine": 5, "endColumn": 8}, {"ruleId": "1897", "severity": 1, "message": "1941", "line": 11, "column": 8, "nodeType": "1899", "messageId": "1900", "endLine": 11, "endColumn": 16}, {"ruleId": "1897", "severity": 1, "message": "1901", "line": 10, "column": 10, "nodeType": "1899", "messageId": "1900", "endLine": 10, "endColumn": 15}, {"ruleId": "1897", "severity": 1, "message": "1921", "line": 10, "column": 17, "nodeType": "1899", "messageId": "1900", "endLine": 10, "endColumn": 31}, {"ruleId": "1897", "severity": 1, "message": "1928", "line": 12, "column": 8, "nodeType": "1899", "messageId": "1900", "endLine": 12, "endColumn": 21}, {"ruleId": "1897", "severity": 1, "message": "2159", "line": 32, "column": 10, "nodeType": "1899", "messageId": "1900", "endLine": 32, "endColumn": 27}, {"ruleId": "1897", "severity": 1, "message": "2160", "line": 32, "column": 29, "nodeType": "1899", "messageId": "1900", "endLine": 32, "endColumn": 49}, {"ruleId": "1903", "severity": 1, "message": "2161", "line": 133, "column": 6, "nodeType": "1905", "endLine": 133, "endColumn": 33, "suggestions": "2162"}, {"ruleId": "2163", "severity": 1, "message": "2164", "line": 218, "column": 9, "nodeType": "2165", "messageId": "2063", "endLine": 218, "endColumn": 13}, {"ruleId": "2163", "severity": 1, "message": "2166", "line": 225, "column": 9, "nodeType": "2165", "messageId": "2063", "endLine": 225, "endColumn": 25}, {"ruleId": "1903", "severity": 1, "message": "2167", "line": 230, "column": 6, "nodeType": "1905", "endLine": 230, "endColumn": 18, "suggestions": "2168"}, {"ruleId": "1903", "severity": 1, "message": "2169", "line": 242, "column": 6, "nodeType": "1905", "endLine": 242, "endColumn": 34, "suggestions": "2170"}, {"ruleId": "1903", "severity": 1, "message": "2171", "line": 258, "column": 6, "nodeType": "1905", "endLine": 258, "endColumn": 8, "suggestions": "2172"}, {"ruleId": "1897", "severity": 1, "message": "2173", "line": 4, "column": 8, "nodeType": "1899", "messageId": "1900", "endLine": 4, "endColumn": 16}, {"ruleId": "1903", "severity": 1, "message": "2093", "line": 40, "column": 8, "nodeType": "1905", "endLine": 40, "endColumn": 10, "suggestions": "2174"}, {"ruleId": "2087", "severity": 1, "message": "2088", "line": 49, "column": 21, "nodeType": "2089", "messageId": "2090", "endLine": 49, "endColumn": 97}, {"ruleId": "1903", "severity": 1, "message": "2093", "line": 44, "column": 6, "nodeType": "1905", "endLine": 44, "endColumn": 8, "suggestions": "2175"}, {"ruleId": "2087", "severity": 1, "message": "2088", "line": 53, "column": 11, "nodeType": "2089", "messageId": "2090", "endLine": 53, "endColumn": 86}, {"ruleId": "1897", "severity": 1, "message": "1901", "line": 4, "column": 10, "nodeType": "1899", "messageId": "1900", "endLine": 4, "endColumn": 15}, {"ruleId": "1897", "severity": 1, "message": "1921", "line": 4, "column": 17, "nodeType": "1899", "messageId": "1900", "endLine": 4, "endColumn": 31}, {"ruleId": "1897", "severity": 1, "message": "2077", "line": 6, "column": 8, "nodeType": "1899", "messageId": "1900", "endLine": 6, "endColumn": 22}, {"ruleId": "1897", "severity": 1, "message": "2176", "line": 7, "column": 8, "nodeType": "1899", "messageId": "1900", "endLine": 7, "endColumn": 21}, {"ruleId": "1897", "severity": 1, "message": "2177", "line": 8, "column": 8, "nodeType": "1899", "messageId": "1900", "endLine": 8, "endColumn": 13}, {"ruleId": "1897", "severity": 1, "message": "1922", "line": 9, "column": 10, "nodeType": "1899", "messageId": "1900", "endLine": 9, "endColumn": 11}, {"ruleId": "1897", "severity": 1, "message": "1963", "line": 16, "column": 8, "nodeType": "1899", "messageId": "1900", "endLine": 16, "endColumn": 19}, {"ruleId": "1897", "severity": 1, "message": "2178", "line": 24, "column": 10, "nodeType": "1899", "messageId": "1900", "endLine": 24, "endColumn": 27}, {"ruleId": "1897", "severity": 1, "message": "2179", "line": 26, "column": 10, "nodeType": "1899", "messageId": "1900", "endLine": 26, "endColumn": 21}, {"ruleId": "1903", "severity": 1, "message": "2180", "line": 135, "column": 6, "nodeType": "1905", "endLine": 135, "endColumn": 12, "suggestions": "2181"}, {"ruleId": "1903", "severity": 1, "message": "2182", "line": 173, "column": 6, "nodeType": "1905", "endLine": 173, "endColumn": 15, "suggestions": "2183"}, {"ruleId": "1903", "severity": 1, "message": "2184", "line": 41, "column": 6, "nodeType": "1905", "endLine": 41, "endColumn": 8, "suggestions": "2185"}, {"ruleId": "1903", "severity": 1, "message": "2186", "line": 97, "column": 8, "nodeType": "1905", "endLine": 97, "endColumn": 17, "suggestions": "2187"}, {"ruleId": "1897", "severity": 1, "message": "2188", "line": 168, "column": 55, "nodeType": "1899", "messageId": "1900", "endLine": 168, "endColumn": 62}, {"ruleId": "1897", "severity": 1, "message": "2188", "line": 187, "column": 55, "nodeType": "1899", "messageId": "1900", "endLine": 187, "endColumn": 62}, {"ruleId": "1897", "severity": 1, "message": "1909", "line": 4, "column": 21, "nodeType": "1899", "messageId": "1900", "endLine": 4, "endColumn": 29}, {"ruleId": "1903", "severity": 1, "message": "1907", "line": 51, "column": 8, "nodeType": "1905", "endLine": 51, "endColumn": 10, "suggestions": "2189"}, {"ruleId": "1897", "severity": 1, "message": "2190", "line": 2, "column": 36, "nodeType": "1899", "messageId": "1900", "endLine": 2, "endColumn": 41}, {"ruleId": "1897", "severity": 1, "message": "2191", "line": 2, "column": 43, "nodeType": "1899", "messageId": "1900", "endLine": 2, "endColumn": 51}, {"ruleId": "1897", "severity": 1, "message": "2192", "line": 4, "column": 8, "nodeType": "1899", "messageId": "1900", "endLine": 4, "endColumn": 12}, {"ruleId": "1897", "severity": 1, "message": "1918", "line": 6, "column": 10, "nodeType": "1899", "messageId": "1900", "endLine": 6, "endColumn": 19}, {"ruleId": "1897", "severity": 1, "message": "1910", "line": 6, "column": 21, "nodeType": "1899", "messageId": "1900", "endLine": 6, "endColumn": 27}, {"ruleId": "1897", "severity": 1, "message": "1909", "line": 6, "column": 29, "nodeType": "1899", "messageId": "1900", "endLine": 6, "endColumn": 37}, {"ruleId": "2106", "severity": 1, "message": "2107", "line": 11, "column": 46, "nodeType": "2108", "messageId": "2109", "endLine": 11, "endColumn": 48}, {"ruleId": "2060", "severity": 1, "message": "2061", "line": 54, "column": 113, "nodeType": "2062", "messageId": "2063", "endLine": 54, "endColumn": 115}, {"ruleId": "1903", "severity": 1, "message": "2193", "line": 26, "column": 8, "nodeType": "1905", "endLine": 26, "endColumn": 10, "suggestions": "2194"}, {"ruleId": "1897", "severity": 1, "message": "2195", "line": 18, "column": 8, "nodeType": "1899", "messageId": "1900", "endLine": 18, "endColumn": 19}, {"ruleId": "1897", "severity": 1, "message": "2196", "line": 21, "column": 10, "nodeType": "1899", "messageId": "1900", "endLine": 21, "endColumn": 27}, {"ruleId": "1897", "severity": 1, "message": "2197", "line": 47, "column": 11, "nodeType": "1899", "messageId": "1900", "endLine": 47, "endColumn": 19}, {"ruleId": "1897", "severity": 1, "message": "2198", "line": 48, "column": 11, "nodeType": "1899", "messageId": "1900", "endLine": 48, "endColumn": 18}, {"ruleId": "1897", "severity": 1, "message": "1943", "line": 49, "column": 11, "nodeType": "1899", "messageId": "1900", "endLine": 49, "endColumn": 19}, {"ruleId": "1903", "severity": 1, "message": "2097", "line": 63, "column": 59, "nodeType": "1899", "endLine": 63, "endColumn": 66}, {"ruleId": "1903", "severity": 1, "message": "2199", "line": 210, "column": 6, "nodeType": "1905", "endLine": 210, "endColumn": 22, "suggestions": "2200"}, {"ruleId": "1903", "severity": 1, "message": "2095", "line": 229, "column": 6, "nodeType": "1905", "endLine": 229, "endColumn": 24, "suggestions": "2201"}, {"ruleId": "2202", "severity": 1, "message": "2203", "line": 61, "column": 9, "nodeType": "2204", "messageId": "2063", "endLine": 64, "endColumn": 19}, {"ruleId": "1903", "severity": 1, "message": "1907", "line": 159, "column": 8, "nodeType": "1905", "endLine": 159, "endColumn": 10, "suggestions": "2205"}, {"ruleId": "1897", "severity": 1, "message": "2206", "line": 21, "column": 10, "nodeType": "1899", "messageId": "1900", "endLine": 21, "endColumn": 15}, {"ruleId": "1897", "severity": 1, "message": "2207", "line": 24, "column": 9, "nodeType": "1899", "messageId": "1900", "endLine": 24, "endColumn": 19}, {"ruleId": "1897", "severity": 1, "message": "1901", "line": 4, "column": 10, "nodeType": "1899", "messageId": "1900", "endLine": 4, "endColumn": 15}, {"ruleId": "1903", "severity": 1, "message": "2208", "line": 367, "column": 8, "nodeType": "1905", "endLine": 367, "endColumn": 30, "suggestions": "2209"}, {"ruleId": "1903", "severity": 1, "message": "2210", "line": 377, "column": 8, "nodeType": "1905", "endLine": 377, "endColumn": 37, "suggestions": "2211"}, {"ruleId": "1897", "severity": 1, "message": "1910", "line": 1, "column": 21, "nodeType": "1899", "messageId": "1900", "endLine": 1, "endColumn": 27}, {"ruleId": "1897", "severity": 1, "message": "1922", "line": 2, "column": 18, "nodeType": "1899", "messageId": "1900", "endLine": 2, "endColumn": 19}, {"ruleId": "1897", "severity": 1, "message": "2212", "line": 4, "column": 10, "nodeType": "1899", "messageId": "1900", "endLine": 4, "endColumn": 22}, {"ruleId": "1897", "severity": 1, "message": "2213", "line": 4, "column": 52, "nodeType": "1899", "messageId": "1900", "endLine": 4, "endColumn": 73}, {"ruleId": "1897", "severity": 1, "message": "2214", "line": 13, "column": 10, "nodeType": "1899", "messageId": "1900", "endLine": 13, "endColumn": 17}, {"ruleId": "1897", "severity": 1, "message": "2215", "line": 28, "column": 8, "nodeType": "1899", "messageId": "1900", "endLine": 28, "endColumn": 27}, {"ruleId": "1897", "severity": 1, "message": "2216", "line": 74, "column": 11, "nodeType": "1899", "messageId": "1900", "endLine": 74, "endColumn": 24}, {"ruleId": "1903", "severity": 1, "message": "2217", "line": 86, "column": 8, "nodeType": "1905", "endLine": 86, "endColumn": 18, "suggestions": "2218"}, {"ruleId": "1897", "severity": 1, "message": "2219", "line": 119, "column": 11, "nodeType": "1899", "messageId": "1900", "endLine": 119, "endColumn": 27}, {"ruleId": "2220", "severity": 1, "message": "2221", "line": 319, "column": 86, "nodeType": "2222", "messageId": "2223", "endLine": 319, "endColumn": 87}, {"ruleId": "1897", "severity": 1, "message": "1918", "line": 1, "column": 10, "nodeType": "1899", "messageId": "1900", "endLine": 1, "endColumn": 19}, {"ruleId": "1897", "severity": 1, "message": "1910", "line": 1, "column": 21, "nodeType": "1899", "messageId": "1900", "endLine": 1, "endColumn": 27}, {"ruleId": "1897", "severity": 1, "message": "1909", "line": 1, "column": 29, "nodeType": "1899", "messageId": "1900", "endLine": 1, "endColumn": 37}, {"ruleId": "1897", "severity": 1, "message": "1923", "line": 2, "column": 10, "nodeType": "1899", "messageId": "1900", "endLine": 2, "endColumn": 21}, {"ruleId": "1897", "severity": 1, "message": "2215", "line": 6, "column": 8, "nodeType": "1899", "messageId": "1900", "endLine": 6, "endColumn": 27}, {"ruleId": "1897", "severity": 1, "message": "2224", "line": 7, "column": 8, "nodeType": "1899", "messageId": "1900", "endLine": 7, "endColumn": 22}, {"ruleId": "1897", "severity": 1, "message": "2225", "line": 5, "column": 10, "nodeType": "1899", "messageId": "1900", "endLine": 5, "endColumn": 25}, {"ruleId": "1897", "severity": 1, "message": "2215", "line": 9, "column": 8, "nodeType": "1899", "messageId": "1900", "endLine": 9, "endColumn": 27}, {"ruleId": "1897", "severity": 1, "message": "1943", "line": 17, "column": 11, "nodeType": "1899", "messageId": "1900", "endLine": 17, "endColumn": 19}, {"ruleId": "1897", "severity": 1, "message": "2226", "line": 31, "column": 11, "nodeType": "1899", "messageId": "1900", "endLine": 31, "endColumn": 20}, {"ruleId": "2060", "severity": 1, "message": "2061", "line": 32, "column": 77, "nodeType": "2062", "messageId": "2063", "endLine": 32, "endColumn": 79}, {"ruleId": "1903", "severity": 1, "message": "2227", "line": 39, "column": 8, "nodeType": "1905", "endLine": 39, "endColumn": 35, "suggestions": "2228"}, {"ruleId": "1903", "severity": 1, "message": "2097", "line": 55, "column": 43, "nodeType": "1899", "endLine": 55, "endColumn": 50}, {"ruleId": "2060", "severity": 1, "message": "2061", "line": 136, "column": 53, "nodeType": "2062", "messageId": "2063", "endLine": 136, "endColumn": 55}, {"ruleId": "2229", "severity": 1, "message": "2230", "line": 179, "column": 33, "nodeType": "1940", "endLine": 185, "endColumn": 35}, {"ruleId": "1897", "severity": 1, "message": "1923", "line": 6, "column": 10, "nodeType": "1899", "messageId": "1900", "endLine": 6, "endColumn": 21}, {"ruleId": "1897", "severity": 1, "message": "2215", "line": 23, "column": 8, "nodeType": "1899", "messageId": "1900", "endLine": 23, "endColumn": 27}, {"ruleId": "1897", "severity": 1, "message": "2224", "line": 24, "column": 8, "nodeType": "1899", "messageId": "1900", "endLine": 24, "endColumn": 22}, {"ruleId": "1897", "severity": 1, "message": "2231", "line": 32, "column": 23, "nodeType": "1899", "messageId": "1900", "endLine": 32, "endColumn": 35}, {"ruleId": "1897", "severity": 1, "message": "2232", "line": 34, "column": 12, "nodeType": "1899", "messageId": "1900", "endLine": 34, "endColumn": 29}, {"ruleId": "1897", "severity": 1, "message": "2233", "line": 36, "column": 12, "nodeType": "1899", "messageId": "1900", "endLine": 36, "endColumn": 35}, {"ruleId": "1903", "severity": 1, "message": "2097", "line": 75, "column": 43, "nodeType": "1899", "endLine": 75, "endColumn": 50}, {"ruleId": "1903", "severity": 1, "message": "2217", "line": 89, "column": 8, "nodeType": "1905", "endLine": 89, "endColumn": 18, "suggestions": "2234"}, {"ruleId": "1897", "severity": 1, "message": "2235", "line": 10, "column": 8, "nodeType": "1899", "messageId": "1900", "endLine": 10, "endColumn": 13}, {"ruleId": "1897", "severity": 1, "message": "2236", "line": 23, "column": 12, "nodeType": "1899", "messageId": "1900", "endLine": 23, "endColumn": 19}, {"ruleId": "1897", "severity": 1, "message": "2237", "line": 24, "column": 12, "nodeType": "1899", "messageId": "1900", "endLine": 24, "endColumn": 23}, {"ruleId": "1897", "severity": 1, "message": "2238", "line": 28, "column": 12, "nodeType": "1899", "messageId": "1900", "endLine": 28, "endColumn": 22}, {"ruleId": "1897", "severity": 1, "message": "2239", "line": 77, "column": 13, "nodeType": "1899", "messageId": "1900", "endLine": 77, "endColumn": 22}, {"ruleId": "1903", "severity": 1, "message": "1907", "line": 84, "column": 8, "nodeType": "1905", "endLine": 84, "endColumn": 10, "suggestions": "2240"}, {"ruleId": "1897", "severity": 1, "message": "2241", "line": 1, "column": 10, "nodeType": "1899", "messageId": "1900", "endLine": 1, "endColumn": 19}, {"ruleId": "1897", "severity": 1, "message": "2215", "line": 7, "column": 8, "nodeType": "1899", "messageId": "1900", "endLine": 7, "endColumn": 27}, {"ruleId": "2060", "severity": 1, "message": "2061", "line": 186, "column": 158, "nodeType": "2062", "messageId": "2063", "endLine": 186, "endColumn": 160}, {"ruleId": "2060", "severity": 1, "message": "2061", "line": 191, "column": 63, "nodeType": "2062", "messageId": "2063", "endLine": 191, "endColumn": 65}, {"ruleId": "2229", "severity": 1, "message": "2230", "line": 256, "column": 33, "nodeType": "1940", "endLine": 260, "endColumn": 35}, {"ruleId": "2242", "severity": 1, "message": "2243", "line": 259, "column": 155, "nodeType": "2244", "messageId": "2245", "endLine": 259, "endColumn": 157}, {"ruleId": "2242", "severity": 1, "message": "2243", "line": 259, "column": 166, "nodeType": "2244", "messageId": "2245", "endLine": 259, "endColumn": 168}, {"ruleId": "1897", "severity": 1, "message": "2225", "line": 2, "column": 10, "nodeType": "1899", "messageId": "1900", "endLine": 2, "endColumn": 25}, {"ruleId": "1897", "severity": 1, "message": "2235", "line": 2, "column": 8, "nodeType": "1899", "messageId": "1900", "endLine": 2, "endColumn": 13}, {"ruleId": "1897", "severity": 1, "message": "1943", "line": 19, "column": 11, "nodeType": "1899", "messageId": "1900", "endLine": 19, "endColumn": 19}, {"ruleId": "1897", "severity": 1, "message": "2246", "line": 5, "column": 10, "nodeType": "1899", "messageId": "1900", "endLine": 5, "endColumn": 18}, {"ruleId": "1897", "severity": 1, "message": "2225", "line": 5, "column": 20, "nodeType": "1899", "messageId": "1900", "endLine": 5, "endColumn": 35}, {"ruleId": "1897", "severity": 1, "message": "2146", "line": 30, "column": 11, "nodeType": "1899", "messageId": "1900", "endLine": 30, "endColumn": 19}, {"ruleId": "1897", "severity": 1, "message": "2225", "line": 6, "column": 10, "nodeType": "1899", "messageId": "1900", "endLine": 6, "endColumn": 25}, {"ruleId": "1897", "severity": 1, "message": "2215", "line": 7, "column": 8, "nodeType": "1899", "messageId": "1900", "endLine": 7, "endColumn": 27}, {"ruleId": "1897", "severity": 1, "message": "2224", "line": 8, "column": 8, "nodeType": "1899", "messageId": "1900", "endLine": 8, "endColumn": 22}, {"ruleId": "1897", "severity": 1, "message": "2146", "line": 18, "column": 11, "nodeType": "1899", "messageId": "1900", "endLine": 18, "endColumn": 19}, {"ruleId": "1897", "severity": 1, "message": "2225", "line": 3, "column": 10, "nodeType": "1899", "messageId": "1900", "endLine": 3, "endColumn": 25}, {"ruleId": "1897", "severity": 1, "message": "2215", "line": 5, "column": 8, "nodeType": "1899", "messageId": "1900", "endLine": 5, "endColumn": 27}, {"ruleId": "1897", "severity": 1, "message": "2146", "line": 16, "column": 11, "nodeType": "1899", "messageId": "1900", "endLine": 16, "endColumn": 19}, {"ruleId": "1903", "severity": 1, "message": "2097", "line": 43, "column": 43, "nodeType": "1899", "endLine": 43, "endColumn": 50}, {"ruleId": "1897", "severity": 1, "message": "1918", "line": 2, "column": 10, "nodeType": "1899", "messageId": "1900", "endLine": 2, "endColumn": 19}, {"ruleId": "1897", "severity": 1, "message": "1980", "line": 3, "column": 10, "nodeType": "1899", "messageId": "1900", "endLine": 3, "endColumn": 21}, {"ruleId": "1897", "severity": 1, "message": "1923", "line": 3, "column": 23, "nodeType": "1899", "messageId": "1900", "endLine": 3, "endColumn": 34}, {"ruleId": "1897", "severity": 1, "message": "2247", "line": 5, "column": 8, "nodeType": "1899", "messageId": "1900", "endLine": 5, "endColumn": 15}, {"ruleId": "1897", "severity": 1, "message": "2196", "line": 6, "column": 10, "nodeType": "1899", "messageId": "1900", "endLine": 6, "endColumn": 27}, {"ruleId": "1897", "severity": 1, "message": "2248", "line": 7, "column": 10, "nodeType": "1899", "messageId": "1900", "endLine": 7, "endColumn": 22}, {"ruleId": "1897", "severity": 1, "message": "2173", "line": 8, "column": 8, "nodeType": "1899", "messageId": "1900", "endLine": 8, "endColumn": 16}, {"ruleId": "1897", "severity": 1, "message": "2249", "line": 9, "column": 8, "nodeType": "1899", "messageId": "1900", "endLine": 9, "endColumn": 28}, {"ruleId": "1897", "severity": 1, "message": "2250", "line": 17, "column": 11, "nodeType": "1899", "messageId": "1900", "endLine": 17, "endColumn": 21}, {"ruleId": "1897", "severity": 1, "message": "2197", "line": 18, "column": 11, "nodeType": "1899", "messageId": "1900", "endLine": 18, "endColumn": 19}, {"ruleId": "1897", "severity": 1, "message": "2198", "line": 19, "column": 11, "nodeType": "1899", "messageId": "1900", "endLine": 19, "endColumn": 18}, {"ruleId": "1897", "severity": 1, "message": "2251", "line": 1, "column": 8, "nodeType": "1899", "messageId": "1900", "endLine": 1, "endColumn": 19}, {"ruleId": "1897", "severity": 1, "message": "1980", "line": 5, "column": 23, "nodeType": "1899", "messageId": "1900", "endLine": 5, "endColumn": 34}, {"ruleId": "1897", "severity": 1, "message": "2252", "line": 28, "column": 11, "nodeType": "1899", "messageId": "1900", "endLine": 28, "endColumn": 27}, {"ruleId": "1903", "severity": 1, "message": "1907", "line": 38, "column": 8, "nodeType": "1905", "endLine": 38, "endColumn": 10, "suggestions": "2253"}, {"ruleId": "1897", "severity": 1, "message": "2225", "line": 3, "column": 10, "nodeType": "1899", "messageId": "1900", "endLine": 3, "endColumn": 25}, {"ruleId": "1897", "severity": 1, "message": "2215", "line": 18, "column": 8, "nodeType": "1899", "messageId": "1900", "endLine": 18, "endColumn": 27}, {"ruleId": "1897", "severity": 1, "message": "1943", "line": 27, "column": 11, "nodeType": "1899", "messageId": "1900", "endLine": 27, "endColumn": 19}, {"ruleId": "1897", "severity": 1, "message": "2254", "line": 40, "column": 11, "nodeType": "1899", "messageId": "1900", "endLine": 40, "endColumn": 21}, {"ruleId": "1897", "severity": 1, "message": "2255", "line": 47, "column": 11, "nodeType": "1899", "messageId": "1900", "endLine": 47, "endColumn": 25}, {"ruleId": "1897", "severity": 1, "message": "2256", "line": 49, "column": 11, "nodeType": "1899", "messageId": "1900", "endLine": 49, "endColumn": 33}, {"ruleId": "1897", "severity": 1, "message": "2225", "line": 2, "column": 10, "nodeType": "1899", "messageId": "1900", "endLine": 2, "endColumn": 25}, {"ruleId": "1897", "severity": 1, "message": "1967", "line": 3, "column": 10, "nodeType": "1899", "messageId": "1900", "endLine": 3, "endColumn": 22}, {"ruleId": "1897", "severity": 1, "message": "2215", "line": 6, "column": 8, "nodeType": "1899", "messageId": "1900", "endLine": 6, "endColumn": 27}, {"ruleId": "1897", "severity": 1, "message": "2224", "line": 7, "column": 8, "nodeType": "1899", "messageId": "1900", "endLine": 7, "endColumn": 22}, {"ruleId": "1897", "severity": 1, "message": "1918", "line": 1, "column": 17, "nodeType": "1899", "messageId": "1900", "endLine": 1, "endColumn": 26}, {"ruleId": "1897", "severity": 1, "message": "1909", "line": 1, "column": 28, "nodeType": "1899", "messageId": "1900", "endLine": 1, "endColumn": 36}, {"ruleId": "1897", "severity": 1, "message": "2196", "line": 4, "column": 10, "nodeType": "1899", "messageId": "1900", "endLine": 4, "endColumn": 27}, {"ruleId": "1897", "severity": 1, "message": "2247", "line": 5, "column": 8, "nodeType": "1899", "messageId": "1900", "endLine": 5, "endColumn": 15}, {"ruleId": "1897", "severity": 1, "message": "2250", "line": 22, "column": 11, "nodeType": "1899", "messageId": "1900", "endLine": 22, "endColumn": 21}, {"ruleId": "1897", "severity": 1, "message": "1943", "line": 25, "column": 11, "nodeType": "1899", "messageId": "1900", "endLine": 25, "endColumn": 19}, {"ruleId": "2060", "severity": 1, "message": "2061", "line": 31, "column": 28, "nodeType": "2062", "messageId": "2063", "endLine": 31, "endColumn": 30}, {"ruleId": "1897", "severity": 1, "message": "1967", "line": 5, "column": 10, "nodeType": "1899", "messageId": "1900", "endLine": 5, "endColumn": 22}, {"ruleId": "1897", "severity": 1, "message": "2257", "line": 8, "column": 8, "nodeType": "1899", "messageId": "1900", "endLine": 8, "endColumn": 21}, {"ruleId": "1897", "severity": 1, "message": "2215", "line": 9, "column": 8, "nodeType": "1899", "messageId": "1900", "endLine": 9, "endColumn": 27}, {"ruleId": "1897", "severity": 1, "message": "1943", "line": 16, "column": 11, "nodeType": "1899", "messageId": "1900", "endLine": 16, "endColumn": 19}, {"ruleId": "1897", "severity": 1, "message": "2146", "line": 19, "column": 11, "nodeType": "1899", "messageId": "1900", "endLine": 19, "endColumn": 19}, {"ruleId": "1897", "severity": 1, "message": "1918", "line": 1, "column": 17, "nodeType": "1899", "messageId": "1900", "endLine": 1, "endColumn": 26}, {"ruleId": "1897", "severity": 1, "message": "2247", "line": 3, "column": 8, "nodeType": "1899", "messageId": "1900", "endLine": 3, "endColumn": 15}, {"ruleId": "1897", "severity": 1, "message": "2196", "line": 5, "column": 10, "nodeType": "1899", "messageId": "1900", "endLine": 5, "endColumn": 27}, {"ruleId": "1897", "severity": 1, "message": "1923", "line": 6, "column": 10, "nodeType": "1899", "messageId": "1900", "endLine": 6, "endColumn": 21}, {"ruleId": "1897", "severity": 1, "message": "2258", "line": 7, "column": 8, "nodeType": "1899", "messageId": "1900", "endLine": 7, "endColumn": 30}, {"ruleId": "1897", "severity": 1, "message": "2215", "line": 10, "column": 8, "nodeType": "1899", "messageId": "1900", "endLine": 10, "endColumn": 27}, {"ruleId": "1897", "severity": 1, "message": "2224", "line": 11, "column": 8, "nodeType": "1899", "messageId": "1900", "endLine": 11, "endColumn": 22}, {"ruleId": "2060", "severity": 1, "message": "2061", "line": 174, "column": 158, "nodeType": "2062", "messageId": "2063", "endLine": 174, "endColumn": 160}, {"ruleId": "2060", "severity": 1, "message": "2061", "line": 179, "column": 63, "nodeType": "2062", "messageId": "2063", "endLine": 179, "endColumn": 65}, {"ruleId": "1897", "severity": 1, "message": "2259", "line": 4, "column": 8, "nodeType": "1899", "messageId": "1900", "endLine": 4, "endColumn": 25}, {"ruleId": "1897", "severity": 1, "message": "2215", "line": 7, "column": 8, "nodeType": "1899", "messageId": "1900", "endLine": 7, "endColumn": 27}, {"ruleId": "1897", "severity": 1, "message": "2224", "line": 8, "column": 8, "nodeType": "1899", "messageId": "1900", "endLine": 8, "endColumn": 22}, {"ruleId": "1897", "severity": 1, "message": "2260", "line": 4, "column": 8, "nodeType": "1899", "messageId": "1900", "endLine": 4, "endColumn": 17}, {"ruleId": "1897", "severity": 1, "message": "2215", "line": 5, "column": 8, "nodeType": "1899", "messageId": "1900", "endLine": 5, "endColumn": 27}, {"ruleId": "1897", "severity": 1, "message": "2261", "line": 13, "column": 11, "nodeType": "1899", "messageId": "1900", "endLine": 13, "endColumn": 15}, {"ruleId": "1897", "severity": 1, "message": "2146", "line": 16, "column": 11, "nodeType": "1899", "messageId": "1900", "endLine": 16, "endColumn": 19}, {"ruleId": "1897", "severity": 1, "message": "2262", "line": 4, "column": 10, "nodeType": "1899", "messageId": "1900", "endLine": 4, "endColumn": 22}, {"ruleId": "1897", "severity": 1, "message": "2225", "line": 4, "column": 24, "nodeType": "1899", "messageId": "1900", "endLine": 4, "endColumn": 39}, {"ruleId": "1897", "severity": 1, "message": "2246", "line": 5, "column": 10, "nodeType": "1899", "messageId": "1900", "endLine": 5, "endColumn": 18}, {"ruleId": "1897", "severity": 1, "message": "2263", "line": 20, "column": 11, "nodeType": "1899", "messageId": "1900", "endLine": 20, "endColumn": 18}, {"ruleId": "1897", "severity": 1, "message": "2246", "line": 3, "column": 27, "nodeType": "1899", "messageId": "1900", "endLine": 3, "endColumn": 35}, {"ruleId": "1897", "severity": 1, "message": "2146", "line": 14, "column": 11, "nodeType": "1899", "messageId": "1900", "endLine": 14, "endColumn": 19}, {"ruleId": "1897", "severity": 1, "message": "2262", "line": 4, "column": 10, "nodeType": "1899", "messageId": "1900", "endLine": 4, "endColumn": 22}, {"ruleId": "1897", "severity": 1, "message": "2246", "line": 5, "column": 10, "nodeType": "1899", "messageId": "1900", "endLine": 5, "endColumn": 18}, {"ruleId": "1897", "severity": 1, "message": "2146", "line": 15, "column": 11, "nodeType": "1899", "messageId": "1900", "endLine": 15, "endColumn": 19}, {"ruleId": "1897", "severity": 1, "message": "2263", "line": 21, "column": 11, "nodeType": "1899", "messageId": "1900", "endLine": 21, "endColumn": 18}, {"ruleId": "1897", "severity": 1, "message": "1909", "line": 1, "column": 10, "nodeType": "1899", "messageId": "1900", "endLine": 1, "endColumn": 18}, {"ruleId": "1897", "severity": 1, "message": "1918", "line": 1, "column": 20, "nodeType": "1899", "messageId": "1900", "endLine": 1, "endColumn": 29}, {"ruleId": "1897", "severity": 1, "message": "2262", "line": 4, "column": 10, "nodeType": "1899", "messageId": "1900", "endLine": 4, "endColumn": 22}, {"ruleId": "1897", "severity": 1, "message": "1980", "line": 8, "column": 10, "nodeType": "1899", "messageId": "1900", "endLine": 8, "endColumn": 21}, {"ruleId": "1903", "severity": 1, "message": "2264", "line": 198, "column": 8, "nodeType": "1905", "endLine": 198, "endColumn": 46, "suggestions": "2265"}, {"ruleId": "2229", "severity": 1, "message": "2230", "line": 378, "column": 57, "nodeType": "1940", "endLine": 387, "endColumn": 59}, {"ruleId": "1897", "severity": 1, "message": "2266", "line": 7, "column": 5, "nodeType": "1899", "messageId": "1900", "endLine": 7, "endColumn": 15}, {"ruleId": "1897", "severity": 1, "message": "2224", "line": 16, "column": 8, "nodeType": "1899", "messageId": "1900", "endLine": 16, "endColumn": 22}, {"ruleId": "1897", "severity": 1, "message": "2215", "line": 17, "column": 8, "nodeType": "1899", "messageId": "1900", "endLine": 17, "endColumn": 27}, {"ruleId": "1897", "severity": 1, "message": "2267", "line": 13, "column": 12, "nodeType": "1899", "messageId": "1900", "endLine": 13, "endColumn": 24}, {"ruleId": "1903", "severity": 1, "message": "2268", "line": 110, "column": 8, "nodeType": "1905", "endLine": 110, "endColumn": 34, "suggestions": "2269"}, {"ruleId": "1897", "severity": 1, "message": "2270", "line": 160, "column": 31, "nodeType": "1899", "messageId": "1900", "endLine": 160, "endColumn": 37}, {"ruleId": "1897", "severity": 1, "message": "2196", "line": 6, "column": 10, "nodeType": "1899", "messageId": "1900", "endLine": 6, "endColumn": 27}, {"ruleId": "1897", "severity": 1, "message": "2271", "line": 14, "column": 23, "nodeType": "1899", "messageId": "1900", "endLine": 14, "endColumn": 35}, {"ruleId": "1897", "severity": 1, "message": "2267", "line": 24, "column": 12, "nodeType": "1899", "messageId": "1900", "endLine": 24, "endColumn": 24}, {"ruleId": "1897", "severity": 1, "message": "2272", "line": 24, "column": 26, "nodeType": "1899", "messageId": "1900", "endLine": 24, "endColumn": 41}, {"ruleId": "1897", "severity": 1, "message": "1943", "line": 39, "column": 11, "nodeType": "1899", "messageId": "1900", "endLine": 39, "endColumn": 19}, {"ruleId": "1903", "severity": 1, "message": "2268", "line": 338, "column": 8, "nodeType": "1905", "endLine": 338, "endColumn": 27, "suggestions": "2273"}, {"ruleId": "1897", "severity": 1, "message": "2274", "line": 2, "column": 8, "nodeType": "1899", "messageId": "1900", "endLine": 2, "endColumn": 22}, {"ruleId": "1897", "severity": 1, "message": "2196", "line": 6, "column": 10, "nodeType": "1899", "messageId": "1900", "endLine": 6, "endColumn": 27}, {"ruleId": "1897", "severity": 1, "message": "1943", "line": 19, "column": 9, "nodeType": "1899", "messageId": "1900", "endLine": 19, "endColumn": 17}, {"ruleId": "1897", "severity": 1, "message": "2267", "line": 26, "column": 10, "nodeType": "1899", "messageId": "1900", "endLine": 26, "endColumn": 22}, {"ruleId": "1903", "severity": 1, "message": "2268", "line": 152, "column": 6, "nodeType": "1905", "endLine": 152, "endColumn": 32, "suggestions": "2275"}, {"ruleId": "1897", "severity": 1, "message": "2196", "line": 7, "column": 10, "nodeType": "1899", "messageId": "1900", "endLine": 7, "endColumn": 27}, {"ruleId": "1897", "severity": 1, "message": "2119", "line": 10, "column": 10, "nodeType": "1899", "messageId": "1900", "endLine": 10, "endColumn": 20}, {"ruleId": "1897", "severity": 1, "message": "1943", "line": 13, "column": 11, "nodeType": "1899", "messageId": "1900", "endLine": 13, "endColumn": 19}, {"ruleId": "1897", "severity": 1, "message": "2276", "line": 14, "column": 12, "nodeType": "1899", "messageId": "1900", "endLine": 14, "endColumn": 21}, {"ruleId": "1897", "severity": 1, "message": "2131", "line": 16, "column": 13, "nodeType": "1899", "messageId": "1900", "endLine": 16, "endColumn": 22}, {"ruleId": "1897", "severity": 1, "message": "2277", "line": 16, "column": 24, "nodeType": "1899", "messageId": "1900", "endLine": 16, "endColumn": 32}, {"ruleId": "1897", "severity": 1, "message": "2267", "line": 21, "column": 12, "nodeType": "1899", "messageId": "1900", "endLine": 21, "endColumn": 24}, {"ruleId": "1897", "severity": 1, "message": "2278", "line": 105, "column": 15, "nodeType": "1899", "messageId": "1900", "endLine": 105, "endColumn": 24}, {"ruleId": "1903", "severity": 1, "message": "2268", "line": 134, "column": 8, "nodeType": "1905", "endLine": 134, "endColumn": 27, "suggestions": "2279"}, {"ruleId": "1897", "severity": 1, "message": "2267", "line": 19, "column": 12, "nodeType": "1899", "messageId": "1900", "endLine": 19, "endColumn": 24}, {"ruleId": "1903", "severity": 1, "message": "2268", "line": 99, "column": 8, "nodeType": "1905", "endLine": 99, "endColumn": 34, "suggestions": "2280"}, {"ruleId": "1903", "severity": 1, "message": "1907", "line": 145, "column": 8, "nodeType": "1905", "endLine": 145, "endColumn": 10, "suggestions": "2281"}, {"ruleId": "1897", "severity": 1, "message": "2267", "line": 15, "column": 12, "nodeType": "1899", "messageId": "1900", "endLine": 15, "endColumn": 24, "suppressions": "2282"}, {"ruleId": "1897", "severity": 1, "message": "2267", "line": 12, "column": 12, "nodeType": "1899", "messageId": "1900", "endLine": 12, "endColumn": 24}, {"ruleId": "1903", "severity": 1, "message": "2268", "line": 87, "column": 8, "nodeType": "1905", "endLine": 87, "endColumn": 27, "suggestions": "2283"}, {"ruleId": "1897", "severity": 1, "message": "1980", "line": 6, "column": 10, "nodeType": "1899", "messageId": "1900", "endLine": 6, "endColumn": 21}, {"ruleId": "1897", "severity": 1, "message": "2267", "line": 13, "column": 12, "nodeType": "1899", "messageId": "1900", "endLine": 13, "endColumn": 24}, {"ruleId": "1903", "severity": 1, "message": "2268", "line": 95, "column": 8, "nodeType": "1905", "endLine": 95, "endColumn": 34, "suggestions": "2284"}, {"ruleId": "1897", "severity": 1, "message": "2267", "line": 15, "column": 12, "nodeType": "1899", "messageId": "1900", "endLine": 15, "endColumn": 24}, {"ruleId": "1903", "severity": 1, "message": "2268", "line": 82, "column": 8, "nodeType": "1905", "endLine": 82, "endColumn": 34, "suggestions": "2285"}, {"ruleId": "1903", "severity": 1, "message": "1907", "line": 86, "column": 8, "nodeType": "1905", "endLine": 86, "endColumn": 10, "suggestions": "2286"}, {"ruleId": "1897", "severity": 1, "message": "2287", "line": 2, "column": 10, "nodeType": "1899", "messageId": "1900", "endLine": 2, "endColumn": 22}, {"ruleId": "1897", "severity": 1, "message": "2267", "line": 15, "column": 12, "nodeType": "1899", "messageId": "1900", "endLine": 15, "endColumn": 24}, {"ruleId": "1903", "severity": 1, "message": "2268", "line": 128, "column": 8, "nodeType": "1905", "endLine": 128, "endColumn": 27, "suggestions": "2288"}, {"ruleId": "1897", "severity": 1, "message": "2267", "line": 69, "column": 12, "nodeType": "1899", "messageId": "1900", "endLine": 69, "endColumn": 24}, {"ruleId": "1903", "severity": 1, "message": "2268", "line": 208, "column": 8, "nodeType": "1905", "endLine": 208, "endColumn": 36, "suggestions": "2289"}, {"ruleId": "1897", "severity": 1, "message": "2267", "line": 51, "column": 10, "nodeType": "1899", "messageId": "1900", "endLine": 51, "endColumn": 22}, {"ruleId": "1903", "severity": 1, "message": "2268", "line": 235, "column": 6, "nodeType": "1905", "endLine": 235, "endColumn": 34, "suggestions": "2290"}, {"ruleId": "1897", "severity": 1, "message": "2267", "line": 39, "column": 12, "nodeType": "1899", "messageId": "1900", "endLine": 39, "endColumn": 24}, {"ruleId": "1903", "severity": 1, "message": "2268", "line": 163, "column": 8, "nodeType": "1905", "endLine": 163, "endColumn": 36, "suggestions": "2291"}, {"ruleId": "1897", "severity": 1, "message": "2292", "line": 5, "column": 29, "nodeType": "1899", "messageId": "1900", "endLine": 5, "endColumn": 56}, {"ruleId": "2060", "severity": 1, "message": "2061", "line": 11, "column": 61, "nodeType": "2062", "messageId": "2063", "endLine": 11, "endColumn": 63}, {"ruleId": "1897", "severity": 1, "message": "2267", "line": 15, "column": 12, "nodeType": "1899", "messageId": "1900", "endLine": 15, "endColumn": 24}, {"ruleId": "1897", "severity": 1, "message": "2293", "line": 41, "column": 11, "nodeType": "1899", "messageId": "1900", "endLine": 41, "endColumn": 24}, {"ruleId": "1903", "severity": 1, "message": "2268", "line": 143, "column": 8, "nodeType": "1905", "endLine": 143, "endColumn": 47, "suggestions": "2294"}, {"ruleId": "1903", "severity": 1, "message": "1907", "line": 168, "column": 8, "nodeType": "1905", "endLine": 168, "endColumn": 10, "suggestions": "2295"}, {"ruleId": "1897", "severity": 1, "message": "2270", "line": 195, "column": 31, "nodeType": "1899", "messageId": "1900", "endLine": 195, "endColumn": 37}, {"ruleId": "1897", "severity": 1, "message": "2270", "line": 248, "column": 31, "nodeType": "1899", "messageId": "1900", "endLine": 248, "endColumn": 37}, {"ruleId": "1903", "severity": 1, "message": "2296", "line": 36, "column": 11, "nodeType": "2297", "endLine": 36, "endColumn": 57}, {"ruleId": "1903", "severity": 1, "message": "2298", "line": 37, "column": 11, "nodeType": "2297", "endLine": 37, "endColumn": 59}, {"ruleId": "2106", "severity": 1, "message": "2107", "line": 212, "column": 61, "nodeType": "2108", "messageId": "2109", "endLine": 212, "endColumn": 63}, {"ruleId": "1903", "severity": 1, "message": "2299", "line": 272, "column": 8, "nodeType": "1905", "endLine": 272, "endColumn": 10, "suggestions": "2300"}, {"ruleId": "1897", "severity": 1, "message": "2270", "line": 368, "column": 31, "nodeType": "1899", "messageId": "1900", "endLine": 368, "endColumn": 37}, {"ruleId": "1897", "severity": 1, "message": "2267", "line": 21, "column": 12, "nodeType": "1899", "messageId": "1900", "endLine": 21, "endColumn": 24}, {"ruleId": "1903", "severity": 1, "message": "2299", "line": 192, "column": 8, "nodeType": "1905", "endLine": 192, "endColumn": 10, "suggestions": "2301"}, {"ruleId": "2163", "severity": 1, "message": "2302", "line": 272, "column": 142, "nodeType": "2165", "messageId": "2063", "endLine": 272, "endColumn": 151}, {"ruleId": "1897", "severity": 1, "message": "2292", "line": 5, "column": 35, "nodeType": "1899", "messageId": "1900", "endLine": 5, "endColumn": 62}, {"ruleId": "1897", "severity": 1, "message": "2267", "line": 21, "column": 12, "nodeType": "1899", "messageId": "1900", "endLine": 21, "endColumn": 24}, {"ruleId": "1903", "severity": 1, "message": "2268", "line": 136, "column": 8, "nodeType": "1905", "endLine": 136, "endColumn": 36, "suggestions": "2303"}, {"ruleId": "1897", "severity": 1, "message": "2304", "line": 162, "column": 11, "nodeType": "1899", "messageId": "1900", "endLine": 162, "endColumn": 21}, {"ruleId": "2163", "severity": 1, "message": "2302", "line": 246, "column": 129, "nodeType": "2165", "messageId": "2063", "endLine": 246, "endColumn": 138}, {"ruleId": "2163", "severity": 1, "message": "2302", "line": 247, "column": 153, "nodeType": "2165", "messageId": "2063", "endLine": 247, "endColumn": 162}, {"ruleId": "1897", "severity": 1, "message": "2267", "line": 21, "column": 12, "nodeType": "1899", "messageId": "1900", "endLine": 21, "endColumn": 24}, {"ruleId": "1903", "severity": 1, "message": "2299", "line": 159, "column": 8, "nodeType": "1905", "endLine": 159, "endColumn": 12, "suggestions": "2305"}, {"ruleId": "1897", "severity": 1, "message": "2267", "line": 17, "column": 12, "nodeType": "1899", "messageId": "1900", "endLine": 17, "endColumn": 24}, {"ruleId": "1903", "severity": 1, "message": "2268", "line": 87, "column": 8, "nodeType": "1905", "endLine": 87, "endColumn": 34, "suggestions": "2306"}, {"ruleId": "1897", "severity": 1, "message": "2267", "line": 21, "column": 12, "nodeType": "1899", "messageId": "1900", "endLine": 21, "endColumn": 24, "suppressions": "2307"}, {"ruleId": "1903", "severity": 1, "message": "2308", "line": 13, "column": 11, "nodeType": "2297", "endLine": 13, "endColumn": 84}, {"ruleId": "1897", "severity": 1, "message": "2309", "line": 15, "column": 11, "nodeType": "1899", "messageId": "1900", "endLine": 15, "endColumn": 20}, {"ruleId": "1897", "severity": 1, "message": "2267", "line": 20, "column": 12, "nodeType": "1899", "messageId": "1900", "endLine": 20, "endColumn": 24}, {"ruleId": "1897", "severity": 1, "message": "2267", "line": 72, "column": 10, "nodeType": "1899", "messageId": "1900", "endLine": 72, "endColumn": 22}, {"ruleId": "1903", "severity": 1, "message": "2268", "line": 248, "column": 6, "nodeType": "1905", "endLine": 248, "endColumn": 34, "suggestions": "2310"}, {"ruleId": "1897", "severity": 1, "message": "2311", "line": 1, "column": 10, "nodeType": "1899", "messageId": "1900", "endLine": 1, "endColumn": 14}, {"ruleId": "1897", "severity": 1, "message": "2206", "line": 8, "column": 12, "nodeType": "1899", "messageId": "1900", "endLine": 8, "endColumn": 17}, {"ruleId": "2312", "severity": 1, "message": "2313", "line": 44, "column": 13, "nodeType": "2314", "messageId": "2315", "endLine": 44, "endColumn": 25, "suppressions": "2316"}, {"ruleId": "2317", "severity": 1, "message": "2318", "line": 52, "column": 100, "nodeType": "1899", "messageId": "2063", "endLine": 52, "endColumn": 104, "suppressions": "2319"}, {"ruleId": "2317", "severity": 1, "message": "2318", "line": 52, "column": 141, "nodeType": "1899", "messageId": "2063", "endLine": 52, "endColumn": 145, "suppressions": "2320"}, {"ruleId": "1897", "severity": 1, "message": "2321", "line": 52, "column": 43925, "nodeType": "1899", "messageId": "1900", "endLine": 52, "endColumn": 43930, "suppressions": "2322"}, {"ruleId": "1897", "severity": 1, "message": "2323", "line": 52, "column": 44059, "nodeType": "1899", "messageId": "1900", "endLine": 52, "endColumn": 44064, "suppressions": "2324"}, {"ruleId": "1897", "severity": 1, "message": "2325", "line": 52, "column": 44193, "nodeType": "1899", "messageId": "1900", "endLine": 52, "endColumn": 44198, "suppressions": "2326"}, {"ruleId": "1897", "severity": 1, "message": "2327", "line": 52, "column": 44303, "nodeType": "1899", "messageId": "1900", "endLine": 52, "endColumn": 44308, "suppressions": "2328"}, {"ruleId": "2329", "message": "2330", "line": 52, "column": 44403, "endLine": 52, "endColumn": 44670, "severity": 2, "nodeType": null, "suppressions": "2331"}, {"ruleId": "2332", "message": "2333", "line": 52, "column": 44403, "endLine": 52, "endColumn": 44670, "severity": 2, "nodeType": null, "suppressions": "2334"}, {"ruleId": "2335", "message": "2336", "line": 52, "column": 44403, "endLine": 52, "endColumn": 44670, "severity": 2, "nodeType": null, "suppressions": "2337"}, {"ruleId": "2338", "message": "2339", "line": 52, "column": 44403, "endLine": 52, "endColumn": 44670, "severity": 2, "nodeType": null, "suppressions": "2340"}, {"ruleId": "2341", "message": "2342", "line": 52, "column": 44403, "endLine": 52, "endColumn": 44670, "severity": 2, "nodeType": null, "suppressions": "2343"}, {"ruleId": "2344", "message": "2345", "line": 52, "column": 44403, "endLine": 52, "endColumn": 44670, "severity": 2, "nodeType": null, "suppressions": "2346"}, {"ruleId": "2347", "message": "2348", "line": 52, "column": 44403, "endLine": 52, "endColumn": 44670, "severity": 2, "nodeType": null, "suppressions": "2349"}, {"ruleId": "1897", "severity": 1, "message": "1909", "line": 1, "column": 27, "nodeType": "1899", "messageId": "1900", "endLine": 1, "endColumn": 35}, {"ruleId": "1897", "severity": 1, "message": "2350", "line": 9, "column": 3, "nodeType": "1899", "messageId": "1900", "endLine": 9, "endColumn": 15}, {"ruleId": "1897", "severity": 1, "message": "2351", "line": 10, "column": 3, "nodeType": "1899", "messageId": "1900", "endLine": 10, "endColumn": 14}, {"ruleId": "1897", "severity": 1, "message": "2352", "line": 15, "column": 3, "nodeType": "1899", "messageId": "1900", "endLine": 15, "endColumn": 37}, {"ruleId": "1903", "severity": 1, "message": "2353", "line": 349, "column": 5, "nodeType": "1905", "endLine": 349, "endColumn": 19, "suggestions": "2354"}, {"ruleId": "1897", "severity": 1, "message": "2247", "line": 3, "column": 8, "nodeType": "1899", "messageId": "1900", "endLine": 3, "endColumn": 15}, {"ruleId": "1897", "severity": 1, "message": "2355", "line": 71, "column": 7, "nodeType": "1899", "messageId": "1900", "endLine": 71, "endColumn": 16}, {"ruleId": "1903", "severity": 1, "message": "2356", "line": 161, "column": 6, "nodeType": "1905", "endLine": 161, "endColumn": 14, "suggestions": "2357"}, {"ruleId": "1903", "severity": 1, "message": "2358", "line": 170, "column": 6, "nodeType": "1905", "endLine": 170, "endColumn": 15, "suggestions": "2359"}, {"ruleId": "1897", "severity": 1, "message": "1918", "line": 1, "column": 10, "nodeType": "1899", "messageId": "1900", "endLine": 1, "endColumn": 19}, {"ruleId": "1897", "severity": 1, "message": "2360", "line": 2, "column": 10, "nodeType": "1899", "messageId": "1900", "endLine": 2, "endColumn": 16}, {"ruleId": "1897", "severity": 1, "message": "1922", "line": 2, "column": 18, "nodeType": "1899", "messageId": "1900", "endLine": 2, "endColumn": 19}, {"ruleId": "1897", "severity": 1, "message": "2361", "line": 2, "column": 21, "nodeType": "1899", "messageId": "1900", "endLine": 2, "endColumn": 29}, {"ruleId": "1897", "severity": 1, "message": "2362", "line": 12, "column": 12, "nodeType": "1899", "messageId": "1900", "endLine": 12, "endColumn": 20}, {"ruleId": "1897", "severity": 1, "message": "2214", "line": 13, "column": 12, "nodeType": "1899", "messageId": "1900", "endLine": 13, "endColumn": 19}, {"ruleId": "1897", "severity": 1, "message": "2363", "line": 28, "column": 11, "nodeType": "1899", "messageId": "1900", "endLine": 28, "endColumn": 20}, {"ruleId": "1897", "severity": 1, "message": "2247", "line": 3, "column": 8, "nodeType": "1899", "messageId": "1900", "endLine": 3, "endColumn": 15}, {"ruleId": "1897", "severity": 1, "message": "2355", "line": 72, "column": 9, "nodeType": "1899", "messageId": "1900", "endLine": 72, "endColumn": 18}, {"ruleId": "1903", "severity": 1, "message": "2356", "line": 163, "column": 8, "nodeType": "1905", "endLine": 163, "endColumn": 16, "suggestions": "2364"}, {"ruleId": "1903", "severity": 1, "message": "2358", "line": 172, "column": 8, "nodeType": "1905", "endLine": 172, "endColumn": 17, "suggestions": "2365"}, {"ruleId": "1897", "severity": 1, "message": "2292", "line": 13, "column": 3, "nodeType": "1899", "messageId": "1900", "endLine": 13, "endColumn": 30}, {"ruleId": "1903", "severity": 1, "message": "2353", "line": 331, "column": 5, "nodeType": "1905", "endLine": 331, "endColumn": 19, "suggestions": "2366"}, {"ruleId": "1897", "severity": 1, "message": "2247", "line": 3, "column": 8, "nodeType": "1899", "messageId": "1900", "endLine": 3, "endColumn": 15}, {"ruleId": "1897", "severity": 1, "message": "2355", "line": 72, "column": 7, "nodeType": "1899", "messageId": "1900", "endLine": 72, "endColumn": 16}, {"ruleId": "1903", "severity": 1, "message": "2367", "line": 147, "column": 6, "nodeType": "1905", "endLine": 147, "endColumn": 14, "suggestions": "2368"}, {"ruleId": "1903", "severity": 1, "message": "2358", "line": 154, "column": 6, "nodeType": "1905", "endLine": 154, "endColumn": 15, "suggestions": "2369"}, {"ruleId": "1897", "severity": 1, "message": "1922", "line": 2, "column": 18, "nodeType": "1899", "messageId": "1900", "endLine": 2, "endColumn": 19}, {"ruleId": "1897", "severity": 1, "message": "1980", "line": 3, "column": 23, "nodeType": "1899", "messageId": "1900", "endLine": 3, "endColumn": 34}, {"ruleId": "1897", "severity": 1, "message": "2212", "line": 4, "column": 10, "nodeType": "1899", "messageId": "1900", "endLine": 4, "endColumn": 22}, {"ruleId": "1897", "severity": 1, "message": "2214", "line": 11, "column": 10, "nodeType": "1899", "messageId": "1900", "endLine": 11, "endColumn": 17}, {"ruleId": "1897", "severity": 1, "message": "2370", "line": 11, "column": 19, "nodeType": "1899", "messageId": "1900", "endLine": 11, "endColumn": 29}, {"ruleId": "1903", "severity": 1, "message": "1907", "line": 11, "column": 10, "nodeType": "1905", "endLine": 11, "endColumn": 12, "suggestions": "2371"}, {"ruleId": "1897", "severity": 1, "message": "1901", "line": 3, "column": 10, "nodeType": "1899", "messageId": "1900", "endLine": 3, "endColumn": 15}, {"ruleId": "1897", "severity": 1, "message": "2372", "line": 8, "column": 17, "nodeType": "1899", "messageId": "1900", "endLine": 8, "endColumn": 23}, {"ruleId": "1897", "severity": 1, "message": "2373", "line": 10, "column": 10, "nodeType": "1899", "messageId": "1900", "endLine": 10, "endColumn": 27}, {"ruleId": "1903", "severity": 1, "message": "2075", "line": 50, "column": 6, "nodeType": "1905", "endLine": 50, "endColumn": 18, "suggestions": "2374"}, {"ruleId": "1938", "severity": 1, "message": "1939", "line": 87, "column": 15, "nodeType": "1940", "endLine": 91, "endColumn": 16}, {"ruleId": "1938", "severity": 1, "message": "1939", "line": 98, "column": 13, "nodeType": "1940", "endLine": 98, "endColumn": 85}, {"ruleId": "1897", "severity": 1, "message": "2375", "line": 5, "column": 13, "nodeType": "1899", "messageId": "1900", "endLine": 5, "endColumn": 20}, {"ruleId": "1903", "severity": 1, "message": "2268", "line": 154, "column": 6, "nodeType": "1905", "endLine": 154, "endColumn": 25, "suggestions": "2376"}, {"ruleId": "1903", "severity": 1, "message": "1907", "line": 11, "column": 8, "nodeType": "1905", "endLine": 11, "endColumn": 10, "suggestions": "2377"}, "no-unused-vars", "'imagesReducer' is defined but never used.", "Identifier", "unusedVar", "'toast' is defined but never used.", "'setNotificationCount' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'dispatch' and 'newNotificationStatus'. Either include them or remove the dependency array.", "ArrayExpression", ["2378"], "React Hook useEffect has a missing dependency: 'dispatch'. Either include it or remove the dependency array.", ["2379"], "'useState' is defined but never used.", "'useRef' is defined but never used.", "'Link' is defined but never used.", "'Login' is defined but never used.", "'Title' is defined but never used.", "'count' is assigned a value but never used.", "'jsonConsole' is defined but never used.", "'openRightDrawer' is defined but never used.", "'useCallback' is defined but never used.", "'useEffect' is defined but never used.", "'incrementNotificationCount' is defined but never used.", "'socket' is defined but never used.", "'ToastContainer' is defined but never used.", "'X' is defined but never used.", "'useDispatch' is defined but never used.", "React Hook useEffect has a missing dependency: 'scrollContainerRef'. Either include it or remove the dependency array.", ["2380"], "React Hook useEffect has missing dependencies: 'location.pathname' and 'navigate'. Either include them or remove the dependency array.", ["2381"], "'updateToasify' is defined but never used.", "'CodeSquare' is defined but never used.", "React Hook useEffect has missing dependencies: 'formObj' and 'navigate'. Either include them or remove the dependency array. You can also do a functional update 'setFormObj(f => ...)' if you only need 'formObj' in the 'setFormObj' call.", ["2382"], "React Hook useEffect has a missing dependency: 'formObj.otpOrigin'. Either include it or remove the dependency array.", ["2383"], "React Hook useEffect has a missing dependency: 'navigate'. Either include it or remove the dependency array.", ["2384"], "React Hook useEffect has a missing dependency: 'formObj'. Either include it or remove the dependency array. You can also do a functional update 'setFormObj(f => ...)' if you only need 'formObj' in the 'setFormObj' call.", ["2385"], "jsx-a11y/anchor-is-valid", "The href attribute is required for an anchor to be keyboard accessible. Provide a valid, navigable address as the href value. If you cannot provide an href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", "'Subtitle' is defined but never used.", "'setPageTitle' is defined but never used.", "'dispatch' is assigned a value but never used.", "react/jsx-no-target-blank", "Using target=\"_blank\" without rel=\"noreferrer\" (which implies rel=\"noopener\") is a security risk in older browsers: see https://mathiasbynens.github.io/rel-noopener/#recommendations", "noTargetBlankWithoutNoreferrer", {"range": "2386", "text": "2387"}, {"range": "2388", "text": "2387"}, {"range": "2389", "text": "2387"}, {"range": "2390", "text": "2387"}, {"range": "2391", "text": "2387"}, {"range": "2392", "text": "2387"}, {"range": "2393", "text": "2387"}, {"range": "2394", "text": "2387"}, {"range": "2395", "text": "2387"}, {"range": "2396", "text": "2387"}, {"range": "2397", "text": "2387"}, {"range": "2398", "text": "2387"}, {"range": "2399", "text": "2387"}, "'showNotification' is defined but never used.", {"range": "2400", "text": "2387"}, {"range": "2401", "text": "2387"}, "'ToastPlacer' is defined but never used.", "'data' is assigned a value but never used.", "'toastObject' is assigned a value but never used.", "'resource' is assigned a value but never used.", "'TruncateText' is defined but never used.", "'FiEye' is defined but never used.", "'commentOn' is assigned a value but never used.", "'setCommentOn' is assigned a value but never used.", "'keyName' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'close'. Either include it or remove the dependency array. If 'close' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["2402"], "'SearchBar' is defined but never used.", "'navigate' is assigned a value but never used.", "'handleNew' is assigned a value but never used.", "'submenuIconClasses' is assigned a value but never used.", "'getNotificationsbyId' is defined but never used.", "'markAllNotificationAsRead' is defined but never used.", "'useSelector' is defined but never used.", "'axios' is defined but never used.", "'MODAL_CLOSE_TYPES' is defined but never used.", "'_id' is assigned a value but never used.", "'FaceFrownIcon' is defined but never used.", ["2403"], "'UndoIcon' is defined but never used.", "'greenDot' is defined but never used.", "React Hook useEffect has a missing dependency: 'formObj.otpOrigin'. Either include it or remove the dependency array. If 'setPayload' needs the current value of 'formObj.otpOrigin', you can also switch to useReducer instead of useState and read 'formObj.otpOrigin' in the reducer.", ["2404"], "jsx-a11y/alt-text", "img elements must have an alt prop, either with meaningful text, or an empty string for decorative images.", "'Blank' is assigned a value but never used.", "'Integration' is assigned a value but never used.", "'Calendar' is assigned a value but never used.", "'Team' is assigned a value but never used.", "'Bills' is assigned a value but never used.", "'GettingStarted' is assigned a value but never used.", "'DocFeatures' is assigned a value but never used.", "'DocComponents' is assigned a value but never used.", "'RxQuestionMarkCircled' is defined but never used.", "'moment' is assigned a value but never used.", ["2405"], ["2406"], ["2407"], ["2408"], ["2409"], ["2410"], ["2411"], ["2412"], ["2413"], ["2414"], ["2415"], ["2416"], ["2417"], ["2418"], "'TitleCard' is defined but never used.", "'ReadMe' is defined but never used.", "'FeaturesNav' is defined but never used.", "'FeaturesContent' is defined but never used.", ["2419"], "'GettingStartedNav' is defined but never used.", "'GettingStartedContent' is defined but never used.", ["2420"], ["2421"], ["2422"], "React Hook useEffect has missing dependencies: 'applySearch' and 'removeAppliedFilter'. Either include them or remove the dependency array. If 'applySearch' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["2423"], "React Hook useCallback has an unnecessary dependency: 'navigate'. Either exclude it or remove the dependency array.", ["2424"], "default-case", "Expected a default case.", "SwitchStatement", "missingDefaultCase", "React Hook useEffect has missing dependencies: 'RoleTypeIsUser', 'activeRole?.permissions', and 'roleId'. Either include them or remove the dependency array.", ["2425"], "React Hook useEffect has missing dependencies: 'isEditor', 'isManager', 'isPublisher', and 'isVerifier'. Either include them or remove the dependency array.", ["2426"], ["2427"], "React Hook useEffect has a missing dependency: 'RoleTypeIsUser'. Either include it or remove the dependency array.", ["2428"], "'relationType' is assigned a value but never used.", "'subPage' is assigned a value but never used.", "'subOfSubPage' is assigned a value but never used.", "'AmountStats' is defined but never used.", "'PageStats' is defined but never used.", "'UsersIcon' is defined but never used.", "'CircleStackIcon' is defined but never used.", "'CreditCardIcon' is defined but never used.", "'UserChannels' is defined but never used.", "'LineChart' is defined but never used.", "'BarChart' is defined but never used.", "'DashboardTopBar' is defined but never used.", "'DoughnutChart' is defined but never used.", "'updateDashboardPeriod' is assigned a value but never used.", "no-useless-escape", "Unnecessary escape character: \\/.", "Literal", "unnecessaryEscape", ["2429", "2430"], "eqeqeq", "Expected '===' and instead saw '=='.", "BinaryExpression", "unexpected", "'originalLogs' is assigned a value but never used.", "'filteredLogs' is assigned a value but never used.", "'handleSearchInput' is defined but never used.", "React Hook useEffect has missing dependencies: 'endDate' and 'startDate'. Either include them or remove the dependency array.", ["2431"], "'InputText' is defined but never used.", "'TextAreaInput' is defined but never used.", "'ToggleSwitch' is defined but never used.", "'handleEmailToggle' is assigned a value but never used.", "'handleDelete' is assigned a value but never used.", "'FallBackLoader' is defined but never used.", "React Hook useEffect has a missing dependency: 'applySearch'. Either include it or remove the dependency array. If 'applySearch' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["2432"], "'updateToastify' is defined but never used.", ["2433"], "'setMembers' is assigned a value but never used.", "'setBills' is assigned a value but never used.", "'FaAngleDown' is defined but never used.", "'formatTimestamp' is defined but never used.", "'fetchedData' is assigned a value but never used.", "'toggleIndex' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'normalUserState'. Either include it or remove the dependency array.", ["2434"], "no-throw-literal", "Expected an error object to be thrown.", "ThrowStatement", "object", "'Switch' is defined but never used.", "'pageStages' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'onClose'. Either include it or remove the dependency array. If 'onClose' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["2435"], "React Hook useEffect has a missing dependency: 'resourceId'. Either include it or remove the dependency array.", ["2436"], "The ref value 'divRef.current' will likely have changed by the time this effect cleanup function runs. If this ref points to a node rendered by React, copy 'divRef.current' to a variable inside the effect, and use that variable in the cleanup function.", "React Hook useEffect has a missing dependency: 'setOnView'. Either include it or remove the dependency array. If 'setOnView' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["2437"], "'runToast' is defined but never used.", "'screen' is assigned a value but never used.", "'isCollapsed' is assigned a value but never used.", "'isSmall' is assigned a value but never used.", "'showVersions' is assigned a value but never used.", ["2438"], "array-callback-return", "Array.prototype.map() expects a value to be returned at the end of arrow function.", "ArrowFunctionExpression", "expectedAtEnd", "React Hook useEffect has missing dependencies: 'activeRoleId' and 'superUser'. Either include them or remove the dependency array.", ["2439"], "React Hook useEffect has missing dependencies: 'dispatch', 'isManager', and 'preview'. Either include them or remove the dependency array.", ["2440"], "'Popups' is defined but never used.", "'subRoutesList' is assigned a value but never used.", ["2441"], "React Hook useEffect has missing dependencies: 'dispatch' and 'resourceType'. Either include them or remove the dependency array.", ["2442"], "'getContent' is defined but never used.", "'setPlatform' is defined but never used.", "React Hook useEffect has a missing dependency: 'removeAppliedFilter'. Either include it or remove the dependency array.", ["2443"], "'userPermissionsSet' is assigned a value but never used.", "'originalVersions' is assigned a value but never used.", "'showDetailsModal' is assigned a value but never used.", "'setShowDetailsModal' is assigned a value but never used.", "'searchValue' is assigned a value but never used.", "'setSearchValue' is assigned a value but never used.", "'debounceSearchValue' is assigned a value but never used.", "'setDebounceValue' is assigned a value but never used.", "'isManager' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'currentResource'. Either include it or remove the dependency array. You can also do a functional update 'setCurrentResource(c => ...)' if you only need 'currentResource' in the 'setCurrentResource' call.", ["2444"], ["2445"], ["2446"], "'userRole' is assigned a value but never used.", ["2447"], "React Hook useEffect has missing dependencies: 'isManager', 'isPublisher', and 'isVerifier'. Either include them or remove the dependency array.", ["2448"], ["2449"], ["2450"], "'currMonth' is assigned a value but never used.", "Expected '!==' and instead saw '!='.", "React Hook useEffect has missing dependencies: 'options' and 'switchToggles'. Either include them or remove the dependency array. If 'switchToggles' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["2451"], "'titleLan' is assigned a value but never used.", "'getRoleById' is defined but never used.", "'SkeletonLoader' is defined but never used.", "'fetchedRole' is assigned a value but never used.", "'setFetchedRole' is assigned a value but never used.", "'emptyOfObject' is assigned a value but never used.", "'isValidDateTime' is defined but never used.", ["2452"], "'getDescStyle' is assigned a value but never used.", "no-empty-pattern", "Unexpected empty object pattern.", "ObjectPattern", "'periodOptions' is assigned a value but never used.", "'PermissionOptions' is assigned a value but never used.", "'setPermissionOptions' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'activeRole?.permissions' and 'role?.roleTypeId'. Either include them or remove the dependency array. If 'setRoleData' needs the current value of 'role.roleTypeId', you can also switch to useReducer instead of useState and read 'role.roleTypeId' in the reducer.", ["2453"], "no-dupe-keys", "Duplicate key 'name'.", "ObjectExpression", "Duplicate key 'selectedRoletype'.", "React Hook useEffect has missing dependencies: 'freshObject' and 'role'. Either include them or remove the dependency array.", ["2454"], "React Hook useEffect has missing dependencies: 'activeRole?.permissions' and 'role?.roleTypeId'. Either include them or remove the dependency array. You can also replace multiple useState variables with useReducer if 'setRoleData' needs the current value of 'activeRole.permissions'.", ["2455"], "React Hook useEffect has a missing dependency: 'modalClose'. Either include it or remove the dependency array.", ["2456"], "'userIcon' is defined but never used.", ["2457"], ["2458"], "'InputFileForm' is defined but never used.", "'dummy' is defined but never used.", "'errorMessageRoles' is assigned a value but never used.", "'fetchedUser' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'initialUserState'. Either include it or remove the dependency array.", ["2459"], "React Hook useEffect has a missing dependency: 'onCloseModal'. Either include it or remove the dependency array.", ["2460"], "React Hook useEffect has a missing dependency: 'closeThisPopup'. Either include it or remove the dependency array.", ["2461"], "React Hook useEffect has a missing dependency: 'closeModal'. Either include it or remove the dependency array.", ["2462"], "'tooPast' is assigned a value but never used.", ["2463"], "'Globe' is defined but never used.", "'Calendar' is defined but never used.", "'temp' is defined but never used.", "React Hook useEffect has a missing dependency: 'setClose'. Either include it or remove the dependency array. If 'setClose' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["2464"], "'tempContent' is defined but never used.", "'updateMainContent' is defined but never used.", "'isTablet' is assigned a value but never used.", "'isPhone' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'closeButton'. Either include it or remove the dependency array.", ["2465"], ["2466"], "no-duplicate-case", "Duplicate case label.", "SwitchCase", ["2467"], "'error' is assigned a value but never used.", "'activeRole' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'saveTheDraft'. Either include it or remove the dependency array.", ["2468"], "React Hook useEffect has a missing dependency: 'savedInitialState'. Either include it or remove the dependency array.", ["2469"], "'removeImages' is defined but never used.", "'updateSpecificContent' is defined but never used.", "'fileURL' is assigned a value but never used.", "'ScrollableParagraph' is defined but never used.", "'ProjectSlider' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'swiperInstance'. Either include it or remove the dependency array.", ["2470"], "'handleSwiperInit' is assigned a value but never used.", "no-sequences", "Unexpected use of comma operator.", "SequenceExpression", "unexpectedCommaExpression", "'ScrollableHTML' is defined but never used.", "'projectPageData' is defined but never used.", "'fontLight' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'tabIndex'. Either include it or remove the dependency array.", ["2471"], "jsx-a11y/img-redundant-alt", "Redundant alt attribute. Screen-readers already announce `img` tags as an image. You don’t need to use the words `image`, `photo,` or `picture` (or any specified custom words) in the alt prop.", "'setActiveTab' is assigned a value but never used.", "'filterMarketItems' is assigned a value but never used.", "'visibleMarketItemsCount' is assigned a value but never used.", ["2472"], "'Arrow' is defined but never used.", "'isModal' is assigned a value but never used.", "'selectedJob' is assigned a value but never used.", "'searchTerm' is assigned a value but never used.", "'isMounted' is assigned a value but never used.", ["2473"], "'newsBlogs' is defined but never used.", "no-mixed-operators", "Unexpected mix of '||' and '&&'. Use parentheses to clarify the intended order of operations.", "LogicalExpression", "unexpectedMixedOperator", "'services' is defined but never used.", "'content' is defined but never used.", "'testimonials' is defined but never used.", "'structureOfTestimony' is defined but never used.", "'isComputer' is assigned a value but never used.", "'ModalPortal' is defined but never used.", "'handleFileChange' is assigned a value but never used.", ["2474"], "'urlSection' is assigned a value but never used.", "'liveUrlSection' is assigned a value but never used.", "'liveDescriptionSection' is assigned a value but never used.", "'blueCheckIcon' is defined but never used.", "'structureOfNewsDetails' is defined but never used.", "'ProjectDetailPage' is defined but never used.", "'org_chart' is defined but never used.", "'slug' is assigned a value but never used.", "'aboutUsIcons' is defined but never used.", "'tempArr' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'type'. Either include it or remove the dependency array.", ["2475"], "'Pagination' is defined but never used.", "'isValidating' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'validateAllFields'. Either include it or remove the dependency array.", ["2476"], "'isLast' is assigned a value but never used.", "'setCurrentId' is assigned a value but never used.", "'setIsValidating' is assigned a value but never used.", ["2477"], "'MultiSelectPro' is defined but never used.", ["2478"], "'currentId' is assigned a value but never used.", "'isEditor' is assigned a value but never used.", "'mainVideo' is assigned a value but never used.", ["2479"], ["2480"], ["2481"], ["2482"], ["2483"], ["2484"], ["2485"], ["2486"], "'getResources' is defined but never used.", ["2487"], ["2488"], ["2489"], ["2490"], "'updateTheProjectSummaryList' is defined but never used.", "'validateArray' is assigned a value but never used.", ["2491"], ["2492"], "The 'projectInforCard' logical expression could make the dependencies of useCallback Hook (at line 173) change on every render. To fix this, wrap the initialization of 'projectInforCard' in its own useMemo() Hook.", "VariableDeclarator", "The 'descriptionSection' logical expression could make the dependencies of useCallback Hook (at line 173) change on every render. To fix this, wrap the initialization of 'descriptionSection' in its own useMemo() Hook.", "React Hook useEffect has a missing dependency: 'slug'. Either include it or remove the dependency array.", ["2493"], ["2494"], "Duplicate key 'maxLength'.", ["2495"], "'latestNews' is assigned a value but never used.", ["2496"], ["2497"], ["2498"], "The 'context' logical expression could make the dependencies of useCallback Hook (at line 157) change on every render. To fix this, wrap the initialization of 'context' in its own useMemo() Hook.", "'thumbIcon' is assigned a value but never used.", ["2499"], "'cond' is defined but never used.", "no-unreachable", "Unreachable code.", "ReturnStatement", "unreachableCode", ["2500"], "no-eval", "eval can be harmful.", ["2501", "2502"], ["2503", "2504"], "'oo_tr' is defined but never used.", ["2505", "2506"], "'oo_tx' is defined but never used.", ["2507", "2508"], "'oo_ts' is defined but never used.", ["2509", "2510"], "'oo_te' is defined but never used.", ["2511", "2512"], "unicorn/no-abusive-eslint-disable", "Definition for rule 'unicorn/no-abusive-eslint-disable' was not found.", ["2513", "2514"], "eslint-comments/disable-enable-pair", "Definition for rule 'eslint-comments/disable-enable-pair' was not found.", ["2515", "2516"], "eslint-comments/no-unlimited-disable", "Definition for rule 'eslint-comments/no-unlimited-disable' was not found.", ["2517", "2518"], "eslint-comments/no-aggregating-enable", "Definition for rule 'eslint-comments/no-aggregating-enable' was not found.", ["2519", "2520"], "eslint-comments/no-duplicate-disable", "Definition for rule 'eslint-comments/no-duplicate-disable' was not found.", ["2521", "2522"], "eslint-comments/no-unused-disable", "Definition for rule 'eslint-comments/no-unused-disable' was not found.", ["2523", "2524"], "eslint-comments/no-unused-enable", "Definition for rule 'eslint-comments/no-unused-enable' was not found.", ["2525", "2526"], "'updateImages' is defined but never used.", "'updateAList' is defined but never used.", "'updateSubServiceDetailsPointsArray' is defined but never used.", "React Hook useMemo has an unnecessary dependency: 'outOfEditing'. Either exclude it or remove the dependency array.", ["2527"], "'operation' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'currentPath', 'dispatch', 'language', 'options', 'projectId', 'referenceOriginal.dir', 'referenceOriginal.index', 'section', 'sectionIndex', 'selectedOptions', and 'titleLan'. Either include them or remove the dependency array.", ["2528"], "React Hook useEffect has a missing dependency: 'showOptions'. Either include it or remove the dependency array.", ["2529"], "'Upload' is defined but never used.", "'FileText' is defined but never used.", "'fileName' is assigned a value but never used.", "'clearFile' is assigned a value but never used.", ["2530"], ["2531"], ["2532"], "React Hook useEffect has missing dependencies: 'dispatch', 'options', 'sectionIndex', and 'selectedOptions'. Either include them or remove the dependency array.", ["2533"], ["2534"], "'setFileURL' is assigned a value but never used.", ["2535"], "'FiEdit' is defined but never used.", "'HiOutlineChatAlt2' is defined but never used.", ["2536"], "'current' is assigned a value but never used.", ["2537"], ["2538"], {"desc": "2539", "fix": "2540"}, {"desc": "2541", "fix": "2542"}, {"desc": "2543", "fix": "2544"}, {"desc": "2545", "fix": "2546"}, {"desc": "2547", "fix": "2548"}, {"desc": "2549", "fix": "2550"}, {"desc": "2551", "fix": "2552"}, {"desc": "2553", "fix": "2554"}, [1291, 1291], " rel=\"noreferrer\"", [1398, 1398], [1501, 1501], [1600, 1600], [1698, 1698], [1804, 1804], [1915, 1915], [3265, 3265], [3968, 3968], [4441, 4441], [6319, 6319], [7353, 7353], [7707, 7707], [4922, 4922], [6079, 6079], {"desc": "2555", "fix": "2556"}, {"desc": "2541", "fix": "2557"}, {"desc": "2549", "fix": "2558"}, {"desc": "2541", "fix": "2559"}, {"desc": "2541", "fix": "2560"}, {"desc": "2541", "fix": "2561"}, {"desc": "2541", "fix": "2562"}, {"desc": "2541", "fix": "2563"}, {"desc": "2541", "fix": "2564"}, {"desc": "2541", "fix": "2565"}, {"desc": "2541", "fix": "2566"}, {"desc": "2541", "fix": "2567"}, {"desc": "2541", "fix": "2568"}, {"desc": "2541", "fix": "2569"}, {"desc": "2541", "fix": "2570"}, {"desc": "2541", "fix": "2571"}, {"desc": "2541", "fix": "2572"}, {"desc": "2541", "fix": "2573"}, {"desc": "2541", "fix": "2574"}, {"desc": "2541", "fix": "2575"}, {"desc": "2576", "fix": "2577"}, {"desc": "2578", "fix": "2579"}, {"desc": "2580", "fix": "2581"}, {"desc": "2582", "fix": "2583"}, {"desc": "2584", "fix": "2585"}, {"desc": "2586", "fix": "2587"}, {"desc": "2588", "fix": "2589"}, {"messageId": "2590", "fix": "2591", "desc": "2592"}, {"messageId": "2593", "fix": "2594", "desc": "2595"}, {"desc": "2596", "fix": "2597"}, {"desc": "2598", "fix": "2599"}, {"desc": "2598", "fix": "2600"}, {"desc": "2601", "fix": "2602"}, {"desc": "2603", "fix": "2604"}, {"desc": "2605", "fix": "2606"}, {"desc": "2607", "fix": "2608"}, {"desc": "2580", "fix": "2609"}, {"desc": "2610", "fix": "2611"}, {"desc": "2612", "fix": "2613"}, {"desc": "2541", "fix": "2614"}, {"desc": "2615", "fix": "2616"}, {"desc": "2617", "fix": "2618"}, {"desc": "2619", "fix": "2620"}, {"desc": "2621", "fix": "2622"}, {"desc": "2578", "fix": "2623"}, {"desc": "2624", "fix": "2625"}, {"desc": "2626", "fix": "2627"}, {"desc": "2586", "fix": "2628"}, {"desc": "2588", "fix": "2629"}, {"desc": "2630", "fix": "2631"}, {"desc": "2603", "fix": "2632"}, {"desc": "2633", "fix": "2634"}, {"desc": "2635", "fix": "2636"}, {"desc": "2633", "fix": "2637"}, {"desc": "2638", "fix": "2639"}, {"desc": "2603", "fix": "2640"}, {"desc": "2603", "fix": "2641"}, {"desc": "2642", "fix": "2643"}, {"desc": "2644", "fix": "2645"}, {"desc": "2646", "fix": "2647"}, {"desc": "2648", "fix": "2649"}, {"desc": "2541", "fix": "2650"}, {"desc": "2651", "fix": "2652"}, {"desc": "2653", "fix": "2654"}, {"desc": "2655", "fix": "2656"}, {"desc": "2541", "fix": "2657"}, {"desc": "2658", "fix": "2659"}, {"desc": "2660", "fix": "2661"}, {"desc": "2662", "fix": "2663"}, {"desc": "2664", "fix": "2665"}, {"desc": "2662", "fix": "2666"}, {"desc": "2541", "fix": "2667"}, {"desc": "2541", "fix": "2668"}, {"desc": "2669", "fix": "2670"}, {"desc": "2671", "fix": "2672"}, {"desc": "2673", "fix": "2674"}, {"desc": "2671", "fix": "2675"}, {"desc": "2673", "fix": "2676"}, {"desc": "2671", "fix": "2677"}, {"desc": "2541", "fix": "2678"}, {"kind": "2679", "justification": "2680"}, {"desc": "2673", "fix": "2681"}, {"desc": "2671", "fix": "2682"}, {"desc": "2671", "fix": "2683"}, {"desc": "2541", "fix": "2684"}, {"desc": "2673", "fix": "2685"}, {"desc": "2686", "fix": "2687"}, {"desc": "2686", "fix": "2688"}, {"desc": "2686", "fix": "2689"}, {"desc": "2690", "fix": "2691"}, {"desc": "2541", "fix": "2692"}, {"desc": "2693", "fix": "2694"}, {"desc": "2693", "fix": "2695"}, {"desc": "2686", "fix": "2696"}, {"desc": "2697", "fix": "2698"}, {"desc": "2671", "fix": "2699"}, {"kind": "2679", "justification": "2680"}, {"desc": "2686", "fix": "2700"}, {"kind": "2679", "justification": "2680"}, {"kind": "2679", "justification": "2680"}, {"kind": "2679", "justification": "2680"}, {"kind": "2679", "justification": "2680"}, {"kind": "2679", "justification": "2680"}, {"kind": "2679", "justification": "2680"}, {"kind": "2679", "justification": "2680"}, {"kind": "2679", "justification": "2680"}, {"kind": "2679", "justification": "2680"}, {"kind": "2679", "justification": "2680"}, {"kind": "2679", "justification": "2680"}, {"kind": "2679", "justification": "2680"}, {"kind": "2679", "justification": "2680"}, {"kind": "2679", "justification": "2680"}, {"kind": "2679", "justification": "2680"}, {"kind": "2679", "justification": "2680"}, {"kind": "2679", "justification": "2680"}, {"kind": "2679", "justification": "2680"}, {"kind": "2679", "justification": "2680"}, {"kind": "2679", "justification": "2680"}, {"kind": "2679", "justification": "2680"}, {"kind": "2679", "justification": "2680"}, {"kind": "2679", "justification": "2680"}, {"kind": "2679", "justification": "2680"}, {"kind": "2679", "justification": "2680"}, {"kind": "2679", "justification": "2680"}, {"kind": "2679", "justification": "2680"}, {"desc": "2580", "fix": "2701"}, {"desc": "2702", "fix": "2703"}, {"desc": "2704", "fix": "2705"}, {"desc": "2702", "fix": "2706"}, {"desc": "2704", "fix": "2707"}, {"desc": "2580", "fix": "2708"}, {"desc": "2709", "fix": "2710"}, {"desc": "2704", "fix": "2711"}, {"desc": "2541", "fix": "2712"}, {"desc": "2598", "fix": "2713"}, {"desc": "2673", "fix": "2714"}, {"desc": "2541", "fix": "2715"}, "Update the dependencies array to be: [dispatch, newNotificationMessage, newNotificationStatus]", {"range": "2716", "text": "2717"}, "Update the dependencies array to be: [dispatch]", {"range": "2718", "text": "2719"}, "Update the dependencies array to be: [pageTitle, scrollContainerRef]", {"range": "2720", "text": "2721"}, "Update the dependencies array to be: [location.pathname, navigate]", {"range": "2722", "text": "2723"}, "Update the dependencies array to be: [formObj, navigate]", {"range": "2724", "text": "2725"}, "Update the dependencies array to be: [formObj.otpOrigin]", {"range": "2726", "text": "2727"}, "Update the dependencies array to be: [navigate]", {"range": "2728", "text": "2729"}, "Update the dependencies array to be: [formObj]", {"range": "2730", "text": "2731"}, "Update the dependencies array to be: [close]", {"range": "2732", "text": "2733"}, {"range": "2734", "text": "2719"}, {"range": "2735", "text": "2727"}, {"range": "2736", "text": "2719"}, {"range": "2737", "text": "2719"}, {"range": "2738", "text": "2719"}, {"range": "2739", "text": "2719"}, {"range": "2740", "text": "2719"}, {"range": "2741", "text": "2719"}, {"range": "2742", "text": "2719"}, {"range": "2743", "text": "2719"}, {"range": "2744", "text": "2719"}, {"range": "2745", "text": "2719"}, {"range": "2746", "text": "2719"}, {"range": "2747", "text": "2719"}, {"range": "2748", "text": "2719"}, {"range": "2749", "text": "2719"}, {"range": "2750", "text": "2719"}, {"range": "2751", "text": "2719"}, {"range": "2752", "text": "2719"}, "Update the dependencies array to be: [isEditor, isManager, navigate]", {"range": "2753", "text": "2754"}, "Update the dependencies array to be: [applySearch, removeAppliedFilter, searchText]", {"range": "2755", "text": "2756"}, "Update the dependencies array to be: []", {"range": "2757", "text": "2758"}, "Update the dependencies array to be: [activeRole?.id, permission, filter, debouncedValue, currentPage, random, activeRole?.permissions, roleId, RoleTypeIsUser]", {"range": "2759", "text": "2760"}, "Update the dependencies array to be: [activeRole.id, isEditor, isManager, isPublisher, isVerifier]", {"range": "2761", "text": "2762"}, "Update the dependencies array to be: [navigate, noneCanSee]", {"range": "2763", "text": "2764"}, "Update the dependencies array to be: [RoleTypeIsUser, activeRole]", {"range": "2765", "text": "2766"}, "removeEscape", {"range": "2767", "text": "2680"}, "Remove the `\\`. This maintains the current functionality.", "escape<PERSON><PERSON><PERSON><PERSON>", {"range": "2768", "text": "2769"}, "Replace the `\\` with `\\\\` to include the actual backslash character.", "Update the dependencies array to be: [currentPage, debouncedValue, filterParam, targetParam, dateFilterReady, startDate, endDate]", {"range": "2770", "text": "2771"}, "Update the dependencies array to be: [applySearch, searchText]", {"range": "2772", "text": "2773"}, {"range": "2774", "text": "2773"}, "Update the dependencies array to be: [normalUserState, user]", {"range": "2775", "text": "2776"}, "Update the dependencies array to be: [onClose]", {"range": "2777", "text": "2778"}, "Update the dependencies array to be: [resourceId]", {"range": "2779", "text": "2780"}, "Update the dependencies array to be: [setOnView]", {"range": "2781", "text": "2782"}, {"range": "2783", "text": "2758"}, "Update the dependencies array to be: [resourceType, resourceTag, randomRender, setRouteList, superUser, activeRoleId]", {"range": "2784", "text": "2785"}, "Update the dependencies array to be: [currentResourceId, dispatch, isManager, preview]", {"range": "2786", "text": "2787"}, {"range": "2788", "text": "2719"}, "Update the dependencies array to be: [currentId, dispatch, isManager, resourceTag, resourceType]", {"range": "2789", "text": "2790"}, "Update the dependencies array to be: [removeAppliedFilter, searchText]", {"range": "2791", "text": "2792"}, "Update the dependencies array to be: [currentResource]", {"range": "2793", "text": "2794"}, "Update the dependencies array to be: [currentResourceId, dispatch, preview]", {"range": "2795", "text": "2796"}, {"range": "2797", "text": "2756"}, "Update the dependencies array to be: [RoleTypeIsUser, activeRole?.id, activeRole?.permissions, permission, resourceId, roleId]", {"range": "2798", "text": "2799"}, "Update the dependencies array to be: [activeRole.id, isManager, isPublisher, isVerifier]", {"range": "2800", "text": "2801"}, {"range": "2802", "text": "2764"}, {"range": "2803", "text": "2766"}, "Update the dependencies array to be: [options, switchToggles]", {"range": "2804", "text": "2805"}, {"range": "2806", "text": "2778"}, "Update the dependencies array to be: [activeRole?.permissions, role?.roleTypeId, roleData?.selectedRoletype]", {"range": "2807", "text": "2808"}, "Update the dependencies array to be: [activeRole, freshObject, role]", {"range": "2809", "text": "2810"}, {"range": "2811", "text": "2808"}, "Update the dependencies array to be: [modalClose]", {"range": "2812", "text": "2813"}, {"range": "2814", "text": "2778"}, {"range": "2815", "text": "2778"}, "Update the dependencies array to be: [initialUserState, user]", {"range": "2816", "text": "2817"}, "Update the dependencies array to be: [onClose, onCloseModal]", {"range": "2818", "text": "2819"}, "Update the dependencies array to be: [closeThisPopup]", {"range": "2820", "text": "2821"}, "Update the dependencies array to be: [closeModal, display]", {"range": "2822", "text": "2823"}, {"range": "2824", "text": "2719"}, "Update the dependencies array to be: [setClose]", {"range": "2825", "text": "2826"}, "Update the dependencies array to be: [closeButton, display, setOn]", {"range": "2827", "text": "2828"}, "Update the dependencies array to be: [preAssignedUsers, resourceId]", {"range": "2829", "text": "2830"}, {"range": "2831", "text": "2719"}, "Update the dependencies array to be: [ReduxState, autoSave, saveTheDraft]", {"range": "2832", "text": "2833"}, "Update the dependencies array to be: [ReduxState.present?.content, savedInitialState]", {"range": "2834", "text": "2835"}, "Update the dependencies array to be: [language, swiperInstance]", {"range": "2836", "text": "2837"}, "Update the dependencies array to be: [activeTab, currentContent, tabIndex]", {"range": "2838", "text": "2839"}, {"range": "2840", "text": "2837"}, {"range": "2841", "text": "2719"}, {"range": "2842", "text": "2719"}, "Update the dependencies array to be: [imagesByResource, random, resourceId, type]", {"range": "2843", "text": "2844"}, "Update the dependencies array to be: [currentContent, language, validateAllFields]", {"range": "2845", "text": "2846"}, "Update the dependencies array to be: [content, language, validateAllFields]", {"range": "2847", "text": "2848"}, {"range": "2849", "text": "2846"}, {"range": "2850", "text": "2848"}, {"range": "2851", "text": "2846"}, {"range": "2852", "text": "2719"}, "directive", "", {"range": "2853", "text": "2848"}, {"range": "2854", "text": "2846"}, {"range": "2855", "text": "2846"}, {"range": "2856", "text": "2719"}, {"range": "2857", "text": "2848"}, "Update the dependencies array to be: [content, language, context, validateAllFields]", {"range": "2858", "text": "2859"}, {"range": "2860", "text": "2859"}, {"range": "2861", "text": "2859"}, "Update the dependencies array to be: [currentContent, language, careerIndex, validateAllFields]", {"range": "2862", "text": "2863"}, {"range": "2864", "text": "2719"}, "Update the dependencies array to be: [slug]", {"range": "2865", "text": "2866"}, {"range": "2867", "text": "2866"}, {"range": "2868", "text": "2859"}, "Update the dependencies array to be: [id, slug]", {"range": "2869", "text": "2870"}, {"range": "2871", "text": "2846"}, {"range": "2872", "text": "2859"}, {"range": "2873", "text": "2758"}, "Update the dependencies array to be: [currentPath, dispatch, language, options, projectId, random, referenceOriginal.dir, referenceOriginal.index, section, sectionIndex, selectedOptions, titleLan]", {"range": "2874", "text": "2875"}, "Update the dependencies array to be: [options, showOptions]", {"range": "2876", "text": "2877"}, {"range": "2878", "text": "2875"}, {"range": "2879", "text": "2877"}, {"range": "2880", "text": "2758"}, "Update the dependencies array to be: [dispatch, options, random, sectionIndex, selectedOptions]", {"range": "2881", "text": "2882"}, {"range": "2883", "text": "2877"}, {"range": "2884", "text": "2719"}, {"range": "2885", "text": "2773"}, {"range": "2886", "text": "2848"}, {"range": "2887", "text": "2719"}, [1679, 1703], "[dispatch, newNotificationMessage, newNotificationStatus]", [2011, 2013], "[dispatch]", [1148, 1159], "[pageTitle, scrollContainerRef]", [1376, 1378], "[location.pathname, navigate]", [5072, 5074], "[formObj, navigate]", [5510, 5512], "[formObj.otp<PERSON><PERSON>in]", [2698, 2700], "[navigate]", [3065, 3067], "[formObj]", [3857, 3859], "[close]", [419, 421], [1494, 1496], [368, 370], [349, 351], [343, 345], [345, 347], [357, 359], [349, 351], [391, 393], [338, 340], [392, 394], [350, 352], [371, 373], [341, 343], [354, 356], [396, 398], [684, 686], [683, 685], [577, 579], [1044, 1065], "[is<PERSON><PERSON><PERSON>, is<PERSON><PERSON><PERSON>, navigate]", [2308, 2320], "[apply<PERSON><PERSON><PERSON>, removeAppliedFilter, searchText]", [7276, 7286], "[]", [10276, 10348], "[activeRole?.id, permission, filter, debouncedValue, currentPage, random, activeRole?.permissions, roleId, RoleTypeIsUser]", [10566, 10582], "[activeRole.id, isE<PERSON>or, isManager, isPublisher, isVerifier]", [10679, 10691], "[navigate, noneCanSee]", [10871, 10883], "[RoleTypeIsUser, activeRole]", [1720, 1721], [1720, 1720], "\\", [19162, 19265], "[currentPage, debouncedValue, filterParam, targetParam, dateFilterReady, startDate, endDate]", [1813, 1825], "[applySearch, searchText]", [2023, 2035], [3288, 3294], "[normalUserState, user]", [5269, 5271], "[onClose]", [6521, 6523], "[resourceId]", [511, 513], "[set<PERSON>n<PERSON>iew]", [3648, 3658], [5766, 5821], "[resourceType, resourceTag, randomRender, setRouteList, superUser, activeRoleId]", [7383, 7402], "[currentResourceId, dispatch, isManager, preview]", [3360, 3362], [6507, 6542], "[currentId, dispatch, isManager, resourceTag, resourceType]", [2561, 2573], "[removeApplied<PERSON><PERSON><PERSON>, searchText]", [9602, 9604], "[currentResource]", [11134, 11162], "[currentResourceId, dispatch, preview]", [2079, 2091], [10250, 10289], "[RoleTypeIsUser, activeRole?.id, activeRole?.permissions, permission, resourceId, roleId]", [10496, 10512], "[activeRole.id, isManager, isPublisher, isVerifier]", [10625, 10637], [10846, 10858], [778, 780], "[options, switchToggles]", [2536, 2538], [4003, 4030], "[activeRole?.permissions, role?.roleTypeId, roleData?.selectedRoletype]", [6784, 6796], "[activeRole, freshObject, role]", [7091, 7119], [7565, 7567], "[modalClose]", [1343, 1345], [1497, 1499], [4402, 4408], "[initialUserState, user]", [5373, 5382], "[onClose, onCloseModal]", [1329, 1331], "[closeThis<PERSON>opup]", [3200, 3209], "[closeModal, display]", [2769, 2771], [878, 880], "[setClose]", [6972, 6988], "[closeButton, display, setOn]", [7556, 7574], "[preAssignedUsers, resourceId]", [8897, 8899], [16695, 16717], "[ReduxState, autoSave, saveTheDraft]", [17046, 17075], "[ReduxState.present?.content, savedInitialState]", [3085, 3095], "[language, swiperInstance]", [1847, 1874], "[activeTab, currentContent, tabIndex]", [3470, 3480], [3147, 3149], [1122, 1124], [7577, 7615], "[imagesByResource, random, resourceId, type]", [4707, 4733], "[currentContent, language, validateAllFields]", [14873, 14892], "[content, language, validateAllFields]", [5278, 5304], [5784, 5803], [3931, 3957], [5572, 5574], [3579, 3598], [3753, 3779], [3013, 3039], [3166, 3168], [5397, 5416], [7710, 7738], "[content, language, context, validateAllFields]", [7480, 7508], [6247, 6275], [6362, 6401], "[currentContent, language, careerIndex, validateAllFields]", [7095, 7097], [11340, 11342], "[slug]", [8004, 8006], [5802, 5830], [6969, 6973], "[id, slug]", [3345, 3371], [7442, 7470], [7766, 7780], [5431, 5439], "[currentPath, dispatch, language, options, projectId, random, referenceOriginal.dir, referenceOriginal.index, section, sectionIndex, selectedOptions, titleLan]", [5648, 5657], "[options, showOptions]", [5919, 5927], [6168, 6177], [7822, 7836], [4726, 4734], "[dispatch, options, random, sectionIndex, selectedOptions]", [4888, 4897], [364, 366], [1596, 1608], [4984, 5003], [328, 330]]