[{"C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\index.js": "1", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\App.js": "2", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\reportWebVitals.js": "3", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\app\\store.js": "4", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\containers\\SuspenseContent.jsx": "5", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Context\\Context.js": "6", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\app\\auth.js": "7", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\app\\init.js": "8", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\containers\\Layout.jsx": "9", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\pages\\ForgotPassword.js": "10", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\pages\\Documentation.js": "11", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\pages\\Register.js": "12", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\pages\\Login.js": "13", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\common\\modalSlice.js": "14", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\common\\headerSlice.js": "15", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\common\\userSlice.js": "16", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\common\\debounceSlice.js": "17", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\common\\SbStateSlice.js": "18", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\common\\resourceSlice.js": "19", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\common\\rightDrawerSlice.js": "20", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\common\\routeLists.js": "21", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\common\\InitialContentSlice.js": "22", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\common\\homeContentSlice.js": "23", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\common\\platformSlice.js": "24", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\common\\navbarSlice.js": "25", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\common\\fontStyle.js": "26", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\common\\ImagesSlice.js": "27", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\leadSlice.js": "28", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\components\\fallbackLoader\\FallbackLoader.jsx": "29", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\Socket\\socket.js": "30", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\app\\fetch.js": "31", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\containers\\LeftSidebar.jsx": "32", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\containers\\RightSidebar.jsx": "33", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\containers\\ModalLayout.jsx": "34", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\containers\\PageContent.jsx": "35", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\utils\\globalConstantUtil.js": "36", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\app\\checkUser.js": "37", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\user\\Register.js": "38", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\components\\Typography\\Title.js": "39", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\user\\Login.jsx": "40", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\user\\ForgotPassword.jsx": "41", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\documentation\\components\\FeaturesNav.js": "42", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\documentation\\components\\GettingStartedNav.js": "43", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\documentation\\components\\GettingStartedContent.js": "44", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\documentation\\components\\DocComponentsContent.js": "45", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\documentation\\components\\DocComponentsNav.js": "46", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\documentation\\components\\FeaturesContent.js": "47", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\VersionDetails.jsx": "48", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Component\\ToastPlacer.jsx": "49", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Requests\\RequestDetails.jsx": "50", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\app\\emailregex.js": "51", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\app\\toastify.js": "52", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\app\\deviceId.js": "53", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\app\\valid.js": "54", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\user\\ResetPasswordModalBody.jsx": "55", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\containers\\Header.jsx": "56", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\routes\\backend.js": "57", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\routes\\sidebar.js": "58", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\common\\components\\NotificationBodyRightDrawer.js": "59", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\common\\components\\ConfirmationModalBody.js": "60", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\pages\\protected\\404.js": "61", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\calendar\\CalendarEventsBodyRightDrawer.js": "62", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\user\\LandingIntro.jsx": "63", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\components\\Typography\\ErrorText.jsx": "64", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\components\\Typography\\Subtitle.js": "65", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\components\\Typography\\HelperText.js": "66", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\components\\Input\\InputText.jsx": "67", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\user\\components\\OTP.jsx": "68", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\user\\components\\BackroundImg.jsx": "69", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\user\\components\\PasswordValidation.jsx": "70", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\user\\components\\UpdatePassword.jsx": "71", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\components\\Button\\Button.jsx": "72", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\routes\\index.jsx": "73", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\components\\Cards\\TitleCard.jsx": "74", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\app\\TimeFormat.js": "75", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\app\\capitalizeword.js": "76", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Context\\CustomContext.js": "77", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\components\\Input\\SearchBar.jsx": "78", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Requests\\Comments.jsx": "79", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Component\\Toaster.jsx": "80", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Component\\Paginations.jsx": "81", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\components\\CalendarView\\util.js": "82", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\app\\OTP.jsx": "83", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\pages\\DocFeatures.js": "84", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\pages\\DocComponents.js": "85", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\pages\\protected\\ProfileSettings.js": "86", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\pages\\protected\\Reminders.js": "87", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\pages\\protected\\Roles.js": "88", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\pages\\protected\\Calendar.js": "89", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\pages\\protected\\Integration.js": "90", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\pages\\protected\\Bills.js": "91", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\pages\\protected\\Requests.js": "92", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\pages\\protected\\Logs.js": "93", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\pages\\protected\\Resources.js": "94", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\pages\\protected\\Team.js": "95", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\pages\\protected\\Blank.js": "96", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\pages\\GettingStarted.js": "97", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\pages\\protected\\Users.js": "98", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\pages\\protected\\Dashboard.js": "99", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\pages\\protected\\Welcome.js": "100", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\documentation\\DocComponents.js": "101", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\documentation\\DocFeatures.js": "102", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\documentation\\DocGettingStarted.js": "103", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\index.jsx": "104", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\calendar\\index.js": "105", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\user\\components\\TemplatePointers.js": "106", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Requests\\index.jsx": "107", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\dashboard\\index.js": "108", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\logstable\\index.jsx": "109", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Reminders\\index.jsx": "110", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Roles\\index.jsx": "111", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\charts\\index.jsx": "112", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\integration\\index.js": "113", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\settings\\team\\index.js": "114", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\settings\\billing\\index.js": "115", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\settings\\profilesettings\\index.jsx": "116", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Requests\\Showdifference.jsx": "117", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\utils\\dummyData.js": "118", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Requests\\ShowVerifierTooltip.jsx": "119", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Requests\\ShowPDF.jsx": "120", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\Resources.jsx": "121", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\EditPage.jsx": "122", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\VersionTable.jsx": "123", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\RequestTable.jsx": "124", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\components\\Input\\ToogleInput.js": "125", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\components\\CalendarView\\index.js": "126", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\components\\Toggle\\Toggle.jsx": "127", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\components\\Input\\Select.jsx": "128", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\components\\Input\\TextAreaInput.jsx": "129", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\logstable\\ShowLog.jsx": "130", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\dashboard\\components\\DashboardStats.js": "131", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\dashboard\\components\\AmountStats.js": "132", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\dashboard\\components\\PageStats.js": "133", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\dashboard\\components\\LineChart.js": "134", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\dashboard\\components\\DashboardTopBar.js": "135", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\dashboard\\components\\BarChart.js": "136", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\dashboard\\components\\UserChannels.js": "137", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\dashboard\\components\\DoughnutChart.js": "138", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Roles\\AddRole.jsx": "139", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Roles\\ShowRole.jsx": "140", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\charts\\ShowRole.jsx": "141", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\charts\\AddUser.jsx": "142", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\components\\Input\\DirectInputFile.jsx": "143", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\defineContent.js": "144", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Requests\\RejectPopup.jsx": "145", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Requests\\DateTime.jsx": "146", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\containers\\Navbar.jsx": "147", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\ResourceCard.jsx": "148", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\components\\Button\\CloseButton.jsx": "149", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\NewProjectDialog.jsx": "150", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\breakUI\\Popups.jsx": "151", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\AllForOne.jsx": "152", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\breakUI\\SwitchLang.jsx": "153", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\breakUI\\ConfigBar.jsx": "154", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\AllForOneManager.jsx": "155", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\breakUI\\PageDetails.jsx": "156", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\breakUI\\ContentTopBar.jsx": "157", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\components\\Input\\InputFileForm.jsx": "158", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\structures\\PageStructure.jsx": "159", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\components\\Loader\\SkeletonLoader.jsx": "160", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\resourcedata.js": "161", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\breakUI\\DropDownMenu.jsx": "162", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\Home.jsx": "163", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\About.jsx": "164", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\Projects.jsx": "165", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\Solutions.jsx": "166", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\Market.jsx": "167", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\CareersPage.jsx": "168", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\NewsPage.jsx": "169", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\Service.jsx": "170", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\HistoryPage.jsx": "171", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\SafetyAndResponsibility.jsx": "172", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\VisionPage.jsx": "173", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\HSE.jsx": "174", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\Affiliates.jsx": "175", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\subparts\\Footerweb.jsx": "176", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\subparts\\Testimonials.jsx": "177", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\subparts\\ContactUsModal.jsx": "178", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\subparts\\Headerweb.jsx": "179", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\detailspages\\ProjectDetails.jsx": "180", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\detailspages\\ServiceDetails.jsx": "181", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\detailspages\\CareersDetails.jsx": "182", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\detailspages\\SnRPolicies.jsx": "183", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\detailspages\\NewsDetails.jsx": "184", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\detailspages\\MarketDetails.jsx": "185", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\Organizational.jsx": "186", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\TemplateOne.jsx": "187", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\TemplateTwo.jsx": "188", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\app\\convertContent.js": "189", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\TempThree.jsx": "190", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\breakUI\\SelectorAccordion.jsx": "191", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\breakUI\\Statusbar.jsx": "192", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\TemplateFour.jsx": "193", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\components\\Input\\ImageSelector.jsx": "194", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\subDetailsPages\\SubServiceDetails.jsx": "195", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\contentmanager\\SolutionManager.jsx": "196", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\contentmanager\\HomeManager.jsx": "197", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\contentmanager\\MarketManager.jsx": "198", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\contentmanager\\AboutManager.jsx": "199", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\contentmanager\\ProjectContentManager.jsx": "200", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\contentmanager\\NewsManager.jsx": "201", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\contentmanager\\HistoryManager.jsx": "202", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\contentmanager\\ServiceManager.jsx": "203", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\contentmanager\\SnRPolicyManager.jsx": "204", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\contentmanager\\CareersManager.jsx": "205", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\contentmanager\\SnRManager.jsx": "206", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\contentmanager\\AffiliatesManager.jsx": "207", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\contentmanager\\VisionManager.jsx": "208", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\contentmanager\\OrganizationManager.jsx": "209", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\contentmanager\\TemplateOneManager.jsx": "210", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\contentmanager\\HSnEManager.jsx": "211", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\contentmanager\\TemplateThreeManager.jsx": "212", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\contentmanager\\TemplateFourManager.jsx": "213", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\contentmanager\\CMforDetails\\CareerDetailManager.jsx": "214", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\contentmanager\\CMforDetails\\ProjectDetailManager.jsx": "215", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\contentmanager\\CMforDetails\\MarketDetailsManager.jsx": "216", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\contentmanager\\CMforDetails\\NewsDetailsManager.jsx": "217", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\contentmanager\\CMforDetails\\ServiceDetailsManager.jsx": "218", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\contentmanager\\CMforSubParts\\HeaderManager.jsx": "219", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\contentmanager\\CMforSubParts\\TestimonyManager.jsx": "220", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\contentmanager\\CMforSubParts\\FooterManager.jsx": "221", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\contentmanager\\subDetailsManagement\\SubServiceDetailManagement.jsx": "222", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\contentmanager\\TemplateTwoManager.jsx": "223", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\app\\fontSizes.js": "224", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\assets\\index.js": "225", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\subparts\\Pagination.jsx": "226", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\subparts\\ModalPortal.jsx": "227", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\components\\Input\\useImageUpload.js": "228", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\breakUI\\ContentSections.jsx": "229", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\breakUI\\MultiSelect.jsx": "230", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\components\\Input\\InputFileUploader.jsx": "231", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\breakUI\\MultiSelectPro.jsx": "232", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\breakUI\\DynamicContentSection.jsx": "233", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\breakUI\\MultiSelectSM.jsx": "234", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\components\\Input\\InputFile.jsx": "235", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\common\\thunk\\smsThunk.js": "236", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\pages\\protected\\ContactQueries.js": "237", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\ContactQueries\\index.jsx": "238", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\components\\TableStates\\TableSkeleton.jsx": "239", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\components\\TableStates\\ErrorState.jsx": "240", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\components\\TableStates\\EmptyState.jsx": "241", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\breakUI\\ScrollableParagraph.jsx": "242", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\breakUI\\ScrollableHtml.jsx": "243", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\components\\Input\\UploadProgress.jsx": "244", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\components\\Input\\useBatchUpload.js": "245", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\ContactUsPage.jsx": "246", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\contentmanager\\ContactUsManager.jsx": "247", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\contentmanager\\CareerManager.jsx": "248", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\CareerPageEditor.jsx": "249"}, {"size": 672, "mtime": 1758275343652, "results": "250", "hashOfConfig": "251"}, {"size": 1536, "mtime": 1758275343257, "results": "252", "hashOfConfig": "251"}, {"size": 375, "mtime": 1744971878440, "results": "253", "hashOfConfig": "251"}, {"size": 1498, "mtime": 1758275343264, "results": "254", "hashOfConfig": "251"}, {"size": 298, "mtime": 1758275343542, "results": "255", "hashOfConfig": "251"}, {"size": 611, "mtime": 1758275343544, "results": "256", "hashOfConfig": "251"}, {"size": 1373, "mtime": 1745213846544, "results": "257", "hashOfConfig": "251"}, {"size": 491, "mtime": 1745213862545, "results": "258", "hashOfConfig": "251"}, {"size": 4582, "mtime": 1758275365452, "results": "259", "hashOfConfig": "251"}, {"size": 349, "mtime": 1745213847378, "results": "260", "hashOfConfig": "251"}, {"size": 1838, "mtime": 1745213847378, "results": "261", "hashOfConfig": "251"}, {"size": 287, "mtime": 1745213847379, "results": "262", "hashOfConfig": "251"}, {"size": 278, "mtime": 1745213847379, "results": "263", "hashOfConfig": "251"}, {"size": 1037, "mtime": 1745213847349, "results": "264", "hashOfConfig": "251"}, {"size": 1402, "mtime": 1758275343638, "results": "265", "hashOfConfig": "251"}, {"size": 3064, "mtime": 1760621504819, "results": "266", "hashOfConfig": "251"}, {"size": 441, "mtime": 1758275343637, "results": "267", "hashOfConfig": "251"}, {"size": 571, "mtime": 1745213847344, "results": "268", "hashOfConfig": "251"}, {"size": 684, "mtime": 1758275343641, "results": "269", "hashOfConfig": "251"}, {"size": 1009, "mtime": 1745213847349, "results": "270", "hashOfConfig": "251"}, {"size": 727, "mtime": 1758275343641, "results": "271", "hashOfConfig": "251"}, {"size": 558, "mtime": 1758275343636, "results": "272", "hashOfConfig": "251"}, {"size": 35289, "mtime": 1762359156425, "results": "273", "hashOfConfig": "251"}, {"size": 522, "mtime": 1758275343640, "results": "274", "hashOfConfig": "251"}, {"size": 751, "mtime": 1758275343640, "results": "275", "hashOfConfig": "251"}, {"size": 516, "mtime": 1758275343638, "results": "276", "hashOfConfig": "251"}, {"size": 632, "mtime": 1745213847344, "results": "277", "hashOfConfig": "251"}, {"size": 1344, "mtime": 1758275343629, "results": "278", "hashOfConfig": "251"}, {"size": 338, "mtime": 1758972567734, "results": "279", "hashOfConfig": "251"}, {"size": 209, "mtime": 1758778372370, "results": "280", "hashOfConfig": "251"}, {"size": 17050, "mtime": 1760520728719, "results": "281", "hashOfConfig": "251"}, {"size": 4699, "mtime": 1760621504749, "results": "282", "hashOfConfig": "251"}, {"size": 7102, "mtime": 1758275353542, "results": "283", "hashOfConfig": "251"}, {"size": 2123, "mtime": 1745213847270, "results": "284", "hashOfConfig": "251"}, {"size": 3849, "mtime": 1760520728733, "results": "285", "hashOfConfig": "251"}, {"size": 490, "mtime": 1758275343660, "results": "286", "hashOfConfig": "251"}, {"size": 638, "mtime": 1758275343260, "results": "287", "hashOfConfig": "251"}, {"size": 3209, "mtime": 1745213847371, "results": "288", "hashOfConfig": "251"}, {"size": 150, "mtime": 1745213847262, "results": "289", "hashOfConfig": "251"}, {"size": 8911, "mtime": 1762249092597, "results": "290", "hashOfConfig": "251"}, {"size": 7941, "mtime": 1759989927780, "results": "291", "hashOfConfig": "251"}, {"size": 1349, "mtime": 1745213847361, "results": "292", "hashOfConfig": "251"}, {"size": 1432, "mtime": 1745213847363, "results": "293", "hashOfConfig": "251"}, {"size": 10535, "mtime": 1745213847362, "results": "294", "hashOfConfig": "251"}, {"size": 5850, "mtime": 1745213847360, "results": "295", "hashOfConfig": "251"}, {"size": 1159, "mtime": 1745213847360, "results": "296", "hashOfConfig": "251"}, {"size": 10102, "mtime": 1745213847361, "results": "297", "hashOfConfig": "251"}, {"size": 14928, "mtime": 1762232154486, "results": "298", "hashOfConfig": "251"}, {"size": 1463, "mtime": 1760698780581, "results": "299", "hashOfConfig": "251"}, {"size": 10165, "mtime": 1758275353550, "results": "300", "hashOfConfig": "251"}, {"size": 510, "mtime": 1745213846545, "results": "301", "hashOfConfig": "251"}, {"size": 1120, "mtime": 1758275343265, "results": "302", "hashOfConfig": "251"}, {"size": 401, "mtime": 1758275343262, "results": "303", "hashOfConfig": "251"}, {"size": 1533, "mtime": 1758275343266, "results": "304", "hashOfConfig": "251"}, {"size": 5754, "mtime": 1760520728832, "results": "305", "hashOfConfig": "251"}, {"size": 12837, "mtime": 1758275375451, "results": "306", "hashOfConfig": "251"}, {"size": 3865, "mtime": 1760520728836, "results": "307", "hashOfConfig": "251"}, {"size": 6771, "mtime": 1760520728841, "results": "308", "hashOfConfig": "251"}, {"size": 6445, "mtime": 1761635587107, "results": "309", "hashOfConfig": "251"}, {"size": 1263, "mtime": 1745213847345, "results": "310", "hashOfConfig": "251"}, {"size": 1182, "mtime": 1758275343652, "results": "311", "hashOfConfig": "251"}, {"size": 557, "mtime": 1745213847337, "results": "312", "hashOfConfig": "251"}, {"size": 829, "mtime": 1745213847369, "results": "313", "hashOfConfig": "251"}, {"size": 202, "mtime": 1760621504748, "results": "314", "hashOfConfig": "251"}, {"size": 232, "mtime": 1745213847252, "results": "315", "hashOfConfig": "251"}, {"size": 168, "mtime": 1745213847251, "results": "316", "hashOfConfig": "251"}, {"size": 7263, "mtime": 1762249092570, "results": "317", "hashOfConfig": "251"}, {"size": 9156, "mtime": 1758275375522, "results": "318", "hashOfConfig": "251"}, {"size": 1640, "mtime": 1745213847372, "results": "319", "hashOfConfig": "251"}, {"size": 2201, "mtime": 1745213847373, "results": "320", "hashOfConfig": "251"}, {"size": 5242, "mtime": 1758275375526, "results": "321", "hashOfConfig": "251"}, {"size": 433, "mtime": 1758275343528, "results": "322", "hashOfConfig": "251"}, {"size": 2960, "mtime": 1760520728839, "results": "323", "hashOfConfig": "251"}, {"size": 1603, "mtime": 1760520728724, "results": "324", "hashOfConfig": "251"}, {"size": 1031, "mtime": 1761635587106, "results": "325", "hashOfConfig": "251"}, {"size": 1859, "mtime": 1758275343260, "results": "326", "hashOfConfig": "251"}, {"size": 156, "mtime": 1758275343545, "results": "327", "hashOfConfig": "251"}, {"size": 898, "mtime": 1745213847248, "results": "328", "hashOfConfig": "251"}, {"size": 1899, "mtime": 1758275343546, "results": "329", "hashOfConfig": "251"}, {"size": 1110, "mtime": 1758275365454, "results": "330", "hashOfConfig": "251"}, {"size": 3271, "mtime": 1758275353543, "results": "331", "hashOfConfig": "251"}, {"size": 551, "mtime": 1745213847240, "results": "332", "hashOfConfig": "251"}, {"size": 1132, "mtime": 1745213846543, "results": "333", "hashOfConfig": "251"}, {"size": 301, "mtime": 1745213847377, "results": "334", "hashOfConfig": "251"}, {"size": 307, "mtime": 1745213847376, "results": "335", "hashOfConfig": "251"}, {"size": 458, "mtime": 1745213847388, "results": "336", "hashOfConfig": "251"}, {"size": 433, "mtime": 1758275343653, "results": "337", "hashOfConfig": "251"}, {"size": 423, "mtime": 1745213847392, "results": "338", "hashOfConfig": "251"}, {"size": 428, "mtime": 1745213847381, "results": "339", "hashOfConfig": "251"}, {"size": 447, "mtime": 1745213847388, "results": "340", "hashOfConfig": "251"}, {"size": 431, "mtime": 1745213847380, "results": "341", "hashOfConfig": "251"}, {"size": 479, "mtime": 1758275343654, "results": "342", "hashOfConfig": "251"}, {"size": 417, "mtime": 1745213847388, "results": "343", "hashOfConfig": "251"}, {"size": 480, "mtime": 1745213847389, "results": "344", "hashOfConfig": "251"}, {"size": 428, "mtime": 1745213847392, "results": "345", "hashOfConfig": "251"}, {"size": 799, "mtime": 1745213847381, "results": "346", "hashOfConfig": "251"}, {"size": 319, "mtime": 1745213847379, "results": "347", "hashOfConfig": "251"}, {"size": 421, "mtime": 1745213847398, "results": "348", "hashOfConfig": "251"}, {"size": 438, "mtime": 1745213847386, "results": "349", "hashOfConfig": "251"}, {"size": 602, "mtime": 1758275343654, "results": "350", "hashOfConfig": "251"}, {"size": 1204, "mtime": 1745213847357, "results": "351", "hashOfConfig": "251"}, {"size": 1188, "mtime": 1745213847358, "results": "352", "hashOfConfig": "251"}, {"size": 1100, "mtime": 1745213847358, "results": "353", "hashOfConfig": "251"}, {"size": 1647, "mtime": 1758275343629, "results": "354", "hashOfConfig": "251"}, {"size": 1687, "mtime": 1745213847337, "results": "355", "hashOfConfig": "251"}, {"size": 7478, "mtime": 1758275343651, "results": "356", "hashOfConfig": "251"}, {"size": 30301, "mtime": 1760621504772, "results": "357", "hashOfConfig": "251"}, {"size": 5654, "mtime": 1760520728827, "results": "358", "hashOfConfig": "251"}, {"size": 29497, "mtime": 1760520728829, "results": "359", "hashOfConfig": "251"}, {"size": 20606, "mtime": 1758275373455, "results": "360", "hashOfConfig": "251"}, {"size": 16781, "mtime": 1760681220273, "results": "361", "hashOfConfig": "251"}, {"size": 20147, "mtime": 1760520728823, "results": "362", "hashOfConfig": "251"}, {"size": 3473, "mtime": 1745213847363, "results": "363", "hashOfConfig": "251"}, {"size": 4723, "mtime": 1745213847368, "results": "364", "hashOfConfig": "251"}, {"size": 4033, "mtime": 1745213847366, "results": "365", "hashOfConfig": "251"}, {"size": 12572, "mtime": 1760520728831, "results": "366", "hashOfConfig": "251"}, {"size": 18063, "mtime": 1762232154489, "results": "367", "hashOfConfig": "251"}, {"size": 5742, "mtime": 1745213847405, "results": "368", "hashOfConfig": "251"}, {"size": 1103, "mtime": 1758275343551, "results": "369", "hashOfConfig": "251"}, {"size": 1644, "mtime": 1758275353550, "results": "370", "hashOfConfig": "251"}, {"size": 13874, "mtime": 1760621527143, "results": "371", "hashOfConfig": "251"}, {"size": 12529, "mtime": 1762242324186, "results": "372", "hashOfConfig": "251"}, {"size": 20780, "mtime": 1760621527145, "results": "373", "hashOfConfig": "251"}, {"size": 32246, "mtime": 1760621504776, "results": "374", "hashOfConfig": "251"}, {"size": 775, "mtime": 1758275343534, "results": "375", "hashOfConfig": "251"}, {"size": 5834, "mtime": 1745213847239, "results": "376", "hashOfConfig": "251"}, {"size": 1952, "mtime": 1758275343536, "results": "377", "hashOfConfig": "251"}, {"size": 3076, "mtime": 1762232702715, "results": "378", "hashOfConfig": "251"}, {"size": 3119, "mtime": 1762249092572, "results": "379", "hashOfConfig": "251"}, {"size": 17062, "mtime": 1758275353645, "results": "380", "hashOfConfig": "251"}, {"size": 2821, "mtime": 1758275343644, "results": "381", "hashOfConfig": "251"}, {"size": 823, "mtime": 1745213847353, "results": "382", "hashOfConfig": "251"}, {"size": 879, "mtime": 1745213847356, "results": "383", "hashOfConfig": "251"}, {"size": 1098, "mtime": 1745213847355, "results": "384", "hashOfConfig": "251"}, {"size": 3067, "mtime": 1745213863355, "results": "385", "hashOfConfig": "251"}, {"size": 1228, "mtime": 1745213847354, "results": "386", "hashOfConfig": "251"}, {"size": 1774, "mtime": 1745213847356, "results": "387", "hashOfConfig": "251"}, {"size": 1756, "mtime": 1745213847355, "results": "388", "hashOfConfig": "251"}, {"size": 11351, "mtime": 1758275353631, "results": "389", "hashOfConfig": "251"}, {"size": 10028, "mtime": 1758275353632, "results": "390", "hashOfConfig": "251"}, {"size": 16423, "mtime": 1760520728821, "results": "391", "hashOfConfig": "251"}, {"size": 11791, "mtime": 1758275353633, "results": "392", "hashOfConfig": "251"}, {"size": 4018, "mtime": 1760621504739, "results": "393", "hashOfConfig": "251"}, {"size": 617, "mtime": 1758275343628, "results": "394", "hashOfConfig": "251"}, {"size": 3023, "mtime": 1758281087461, "results": "395", "hashOfConfig": "251"}, {"size": 11043, "mtime": 1758275353547, "results": "396", "hashOfConfig": "251"}, {"size": 4634, "mtime": 1758275343540, "results": "397", "hashOfConfig": "251"}, {"size": 5250, "mtime": 1761720888616, "results": "398", "hashOfConfig": "251"}, {"size": 445, "mtime": 1758275343528, "results": "399", "hashOfConfig": "251"}, {"size": 4554, "mtime": 1761720888614, "results": "400", "hashOfConfig": "251"}, {"size": 2045, "mtime": 1758275343568, "results": "401", "hashOfConfig": "251"}, {"size": 7676, "mtime": 1762358991587, "results": "402", "hashOfConfig": "251"}, {"size": 1660, "mtime": 1758275343570, "results": "403", "hashOfConfig": "251"}, {"size": 17226, "mtime": 1758275375466, "results": "404", "hashOfConfig": "251"}, {"size": 9020, "mtime": 1762358991694, "results": "405", "hashOfConfig": "251"}, {"size": 14556, "mtime": 1758275353567, "results": "406", "hashOfConfig": "251"}, {"size": 26131, "mtime": 1762359156425, "results": "407", "hashOfConfig": "251"}, {"size": 5686, "mtime": 1761720888609, "results": "408", "hashOfConfig": "251"}, {"size": 1238, "mtime": 1758275343612, "results": "409", "hashOfConfig": "251"}, {"size": 3045, "mtime": 1758275343535, "results": "410", "hashOfConfig": "251"}, {"size": 4537, "mtime": 1762242324186, "results": "411", "hashOfConfig": "251"}, {"size": 5879, "mtime": 1758275343562, "results": "412", "hashOfConfig": "251"}, {"size": 53071, "mtime": 1761722684823, "results": "413", "hashOfConfig": "251"}, {"size": 10207, "mtime": 1761649770950, "results": "414", "hashOfConfig": "251"}, {"size": 14414, "mtime": 1761720888645, "results": "415", "hashOfConfig": "251"}, {"size": 13130, "mtime": 1760621504818, "results": "416", "hashOfConfig": "251"}, {"size": 25960, "mtime": 1761720888643, "results": "417", "hashOfConfig": "251"}, {"size": 25623, "mtime": 1762358920643, "results": "418", "hashOfConfig": "251"}, {"size": 18123, "mtime": 1760682962048, "results": "419", "hashOfConfig": "251"}, {"size": 8924, "mtime": 1760679787300, "results": "420", "hashOfConfig": "251"}, {"size": 8511, "mtime": 1760520728794, "results": "421", "hashOfConfig": "251"}, {"size": 12036, "mtime": 1760520728804, "results": "422", "hashOfConfig": "251"}, {"size": 12655, "mtime": 1760520728808, "results": "423", "hashOfConfig": "251"}, {"size": 12760, "mtime": 1760693930986, "results": "424", "hashOfConfig": "251"}, {"size": 6138, "mtime": 1760520728792, "results": "425", "hashOfConfig": "251"}, {"size": 8643, "mtime": 1760681220269, "results": "426", "hashOfConfig": "251"}, {"size": 4554, "mtime": 1759989927775, "results": "427", "hashOfConfig": "251"}, {"size": 4536, "mtime": 1758275343625, "results": "428", "hashOfConfig": "251"}, {"size": 10823, "mtime": 1759310566343, "results": "429", "hashOfConfig": "251"}, {"size": 19848, "mtime": 1761624294800, "results": "430", "hashOfConfig": "251"}, {"size": 16148, "mtime": 1761545806449, "results": "431", "hashOfConfig": "251"}, {"size": 9310, "mtime": 1758275343608, "results": "432", "hashOfConfig": "251"}, {"size": 11565, "mtime": 1760520728814, "results": "433", "hashOfConfig": "251"}, {"size": 12288, "mtime": 1760520728811, "results": "434", "hashOfConfig": "251"}, {"size": 13959, "mtime": 1760520728810, "results": "435", "hashOfConfig": "251"}, {"size": 5458, "mtime": 1760696077107, "results": "436", "hashOfConfig": "251"}, {"size": 15935, "mtime": 1758275365508, "results": "437", "hashOfConfig": "251"}, {"size": 19365, "mtime": 1761720888654, "results": "438", "hashOfConfig": "251"}, {"size": 2862, "mtime": 1760621527139, "results": "439", "hashOfConfig": "251"}, {"size": 19250, "mtime": 1761720888649, "results": "440", "hashOfConfig": "251"}, {"size": 5755, "mtime": 1760520728755, "results": "441", "hashOfConfig": "251"}, {"size": 1643, "mtime": 1745213847296, "results": "442", "hashOfConfig": "251"}, {"size": 15869, "mtime": 1761720888651, "results": "443", "hashOfConfig": "251"}, {"size": 42610, "mtime": 1761651456528, "results": "444", "hashOfConfig": "251"}, {"size": 13859, "mtime": 1760520728815, "results": "445", "hashOfConfig": "251"}, {"size": 10311, "mtime": 1761720888627, "results": "446", "hashOfConfig": "251"}, {"size": 30189, "mtime": 1761720888622, "results": "447", "hashOfConfig": "251"}, {"size": 13989, "mtime": 1761721669572, "results": "448", "hashOfConfig": "251"}, {"size": 11194, "mtime": 1761653534159, "results": "449", "hashOfConfig": "251"}, {"size": 9530, "mtime": 1760681112436, "results": "450", "hashOfConfig": "251"}, {"size": 9841, "mtime": 1761021732418, "results": "451", "hashOfConfig": "251"}, {"size": 7197, "mtime": 1760687790604, "results": "452", "hashOfConfig": "251"}, {"size": 8130, "mtime": 1760679876928, "results": "453", "hashOfConfig": "251"}, {"size": 15593, "mtime": 1762249092590, "results": "454", "hashOfConfig": "251"}, {"size": 5319, "mtime": 1760621504799, "results": "455", "hashOfConfig": "251"}, {"size": 7498, "mtime": 1760621504809, "results": "456", "hashOfConfig": "251"}, {"size": 5577, "mtime": 1760621504788, "results": "457", "hashOfConfig": "251"}, {"size": 12848, "mtime": 1760687790611, "results": "458", "hashOfConfig": "251"}, {"size": 5222, "mtime": 1760695690034, "results": "459", "hashOfConfig": "251"}, {"size": 17510, "mtime": 1761720888632, "results": "460", "hashOfConfig": "251"}, {"size": 12565, "mtime": 1760693869588, "results": "461", "hashOfConfig": "251"}, {"size": 18053, "mtime": 1761720888635, "results": "462", "hashOfConfig": "251"}, {"size": 14362, "mtime": 1761720888629, "results": "463", "hashOfConfig": "251"}, {"size": 15482, "mtime": 1762359826767, "results": "464", "hashOfConfig": "251"}, {"size": 22022, "mtime": 1762249092588, "results": "465", "hashOfConfig": "251"}, {"size": 15449, "mtime": 1760700292045, "results": "466", "hashOfConfig": "251"}, {"size": 15281, "mtime": 1762249092586, "results": "467", "hashOfConfig": "251"}, {"size": 12220, "mtime": 1760699160225, "results": "468", "hashOfConfig": "251"}, {"size": 8933, "mtime": 1760853617115, "results": "469", "hashOfConfig": "251"}, {"size": 6471, "mtime": 1762249092589, "results": "470", "hashOfConfig": "251"}, {"size": 16413, "mtime": 1760703867335, "results": "471", "hashOfConfig": "251"}, {"size": 17304, "mtime": 1762249092593, "results": "472", "hashOfConfig": "251"}, {"size": 17374, "mtime": 1761720888638, "results": "473", "hashOfConfig": "251"}, {"size": 4240, "mtime": 1758275343263, "results": "474", "hashOfConfig": "251"}, {"size": 5227, "mtime": 1745213847204, "results": "475", "hashOfConfig": "251"}, {"size": 2654, "mtime": 1745213847332, "results": "476", "hashOfConfig": "251"}, {"size": 1728, "mtime": 1745213847331, "results": "477", "hashOfConfig": "251"}, {"size": 46272, "mtime": 1758961957737, "results": "478", "hashOfConfig": "479"}, {"size": 19593, "mtime": 1762359156425, "results": "480", "hashOfConfig": "251"}, {"size": 9365, "mtime": 1762249092577, "results": "481", "hashOfConfig": "251"}, {"size": 4642, "mtime": 1758275367450, "results": "482", "hashOfConfig": "251"}, {"size": 10857, "mtime": 1762249092582, "results": "483", "hashOfConfig": "251"}, {"size": 18421, "mtime": 1762359155493, "results": "484", "hashOfConfig": "251"}, {"size": 8603, "mtime": 1762249092582, "results": "485", "hashOfConfig": "251"}, {"size": 4069, "mtime": 1761651456525, "results": "486", "hashOfConfig": "251"}, {"size": 815, "mtime": 1758275343642, "results": "487", "hashOfConfig": "251"}, {"size": 453, "mtime": 1760520728833, "results": "488", "hashOfConfig": "251"}, {"size": 29725, "mtime": 1760520728740, "results": "489", "hashOfConfig": "251"}, {"size": 2112, "mtime": 1760520728730, "results": "490", "hashOfConfig": "251"}, {"size": 1664, "mtime": 1760520728729, "results": "491", "hashOfConfig": "251"}, {"size": 1167, "mtime": 1760520728729, "results": "492", "hashOfConfig": "251"}, {"size": 1090, "mtime": 1759989927747, "results": "493", "hashOfConfig": "251"}, {"size": 1040, "mtime": 1759989927746, "results": "494", "hashOfConfig": "251"}, {"size": 6612, "mtime": 1761647794632, "results": "495", "hashOfConfig": "251"}, {"size": 6184, "mtime": 1761647794625, "results": "496", "hashOfConfig": "251"}, {"size": 17368, "mtime": 1762250309453, "results": "497", "hashOfConfig": "251"}, {"size": 14640, "mtime": 1762359156430, "results": "498", "hashOfConfig": "251"}, {"size": 4677, "mtime": 1762259448449, "results": "499", "hashOfConfig": "251"}, {"size": 0, "mtime": 1762259438513, "results": "500", "hashOfConfig": "251"}, {"filePath": "501", "messages": "502", "suppressedMessages": "503", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "r95v4w", {"filePath": "504", "messages": "505", "suppressedMessages": "506", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "507", "messages": "508", "suppressedMessages": "509", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "510", "messages": "511", "suppressedMessages": "512", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "513", "messages": "514", "suppressedMessages": "515", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "516", "messages": "517", "suppressedMessages": "518", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "519", "messages": "520", "suppressedMessages": "521", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "522", "messages": "523", "suppressedMessages": "524", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "525", "messages": "526", "suppressedMessages": "527", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "528", "messages": "529", "suppressedMessages": "530", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "531", "messages": "532", "suppressedMessages": "533", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "534", "messages": "535", "suppressedMessages": "536", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "537", "messages": "538", "suppressedMessages": "539", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "540", "messages": "541", "suppressedMessages": "542", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "543", "messages": "544", "suppressedMessages": "545", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "546", "messages": "547", "suppressedMessages": "548", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "549", "messages": "550", "suppressedMessages": "551", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "552", "messages": "553", "suppressedMessages": "554", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "555", "messages": "556", "suppressedMessages": "557", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "558", "messages": "559", "suppressedMessages": "560", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "561", "messages": "562", "suppressedMessages": "563", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "564", "messages": "565", "suppressedMessages": "566", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "567", "messages": "568", "suppressedMessages": "569", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "570", "messages": "571", "suppressedMessages": "572", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "573", "messages": "574", "suppressedMessages": "575", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "576", "messages": "577", "suppressedMessages": "578", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "579", "messages": "580", "suppressedMessages": "581", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "582", "messages": "583", "suppressedMessages": "584", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "585", "messages": "586", "suppressedMessages": "587", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "588", "messages": "589", "suppressedMessages": "590", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "591", "messages": "592", "suppressedMessages": "593", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "594", "messages": "595", "suppressedMessages": "596", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "597", "messages": "598", "suppressedMessages": "599", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "600", "messages": "601", "suppressedMessages": "602", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "603", "messages": "604", "suppressedMessages": "605", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "606", "messages": "607", "suppressedMessages": "608", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "609", "messages": "610", "suppressedMessages": "611", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "612", "messages": "613", "suppressedMessages": "614", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "615", "messages": "616", "suppressedMessages": "617", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "618", "messages": "619", "suppressedMessages": "620", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "621", "messages": "622", "suppressedMessages": "623", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "624", "messages": "625", "suppressedMessages": "626", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "627", "messages": "628", "suppressedMessages": "629", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "630", "messages": "631", "suppressedMessages": "632", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 17, "fixableErrorCount": 0, "fixableWarningCount": 13, "source": null}, {"filePath": "633", "messages": "634", "suppressedMessages": "635", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "636", "messages": "637", "suppressedMessages": "638", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "639", "messages": "640", "suppressedMessages": "641", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "642", "messages": "643", "suppressedMessages": "644", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "645", "messages": "646", "suppressedMessages": "647", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "648", "messages": "649", "suppressedMessages": "650", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "651", "messages": "652", "suppressedMessages": "653", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "654", "messages": "655", "suppressedMessages": "656", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "657", "messages": "658", "suppressedMessages": "659", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "660", "messages": "661", "suppressedMessages": "662", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "663", "messages": "664", "suppressedMessages": "665", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "666", "messages": "667", "suppressedMessages": "668", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "669", "messages": "670", "suppressedMessages": "671", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "672", "messages": "673", "suppressedMessages": "674", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "675", "messages": "676", "suppressedMessages": "677", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "678", "messages": "679", "suppressedMessages": "680", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "681", "messages": "682", "suppressedMessages": "683", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "684", "messages": "685", "suppressedMessages": "686", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "687", "messages": "688", "suppressedMessages": "689", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "690", "messages": "691", "suppressedMessages": "692", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "693", "messages": "694", "suppressedMessages": "695", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "696", "messages": "697", "suppressedMessages": "698", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "699", "messages": "700", "suppressedMessages": "701", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "702", "messages": "703", "suppressedMessages": "704", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "705", "messages": "706", "suppressedMessages": "707", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "708", "messages": "709", "suppressedMessages": "710", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "711", "messages": "712", "suppressedMessages": "713", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "714", "messages": "715", "suppressedMessages": "716", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "717", "messages": "718", "suppressedMessages": "719", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "720", "messages": "721", "suppressedMessages": "722", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "723", "messages": "724", "suppressedMessages": "725", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "726", "messages": "727", "suppressedMessages": "728", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "729", "messages": "730", "suppressedMessages": "731", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "732", "messages": "733", "suppressedMessages": "734", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "735", "messages": "736", "suppressedMessages": "737", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "738", "messages": "739", "suppressedMessages": "740", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "741", "messages": "742", "suppressedMessages": "743", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "744", "messages": "745", "suppressedMessages": "746", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "747", "messages": "748", "suppressedMessages": "749", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "750", "messages": "751", "suppressedMessages": "752", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "753", "messages": "754", "suppressedMessages": "755", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "756", "messages": "757", "suppressedMessages": "758", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "759", "messages": "760", "suppressedMessages": "761", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "762", "messages": "763", "suppressedMessages": "764", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "765", "messages": "766", "suppressedMessages": "767", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "768", "messages": "769", "suppressedMessages": "770", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "771", "messages": "772", "suppressedMessages": "773", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "774", "messages": "775", "suppressedMessages": "776", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "777", "messages": "778", "suppressedMessages": "779", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "780", "messages": "781", "suppressedMessages": "782", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "783", "messages": "784", "suppressedMessages": "785", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "786", "messages": "787", "suppressedMessages": "788", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "789", "messages": "790", "suppressedMessages": "791", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "792", "messages": "793", "suppressedMessages": "794", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "795", "messages": "796", "suppressedMessages": "797", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "798", "messages": "799", "suppressedMessages": "800", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "801", "messages": "802", "suppressedMessages": "803", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "804", "messages": "805", "suppressedMessages": "806", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "807", "messages": "808", "suppressedMessages": "809", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "810", "messages": "811", "suppressedMessages": "812", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "813", "messages": "814", "suppressedMessages": "815", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "816", "messages": "817", "suppressedMessages": "818", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "819", "messages": "820", "suppressedMessages": "821", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "822", "messages": "823", "suppressedMessages": "824", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "825", "messages": "826", "suppressedMessages": "827", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "828", "messages": "829", "suppressedMessages": "830", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "831", "messages": "832", "suppressedMessages": "833", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "834", "messages": "835", "suppressedMessages": "836", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "837", "messages": "838", "suppressedMessages": "839", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "840", "messages": "841", "suppressedMessages": "842", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "843", "messages": "844", "suppressedMessages": "845", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "846", "messages": "847", "suppressedMessages": "848", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "849", "messages": "850", "suppressedMessages": "851", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "852", "messages": "853", "suppressedMessages": "854", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "855", "messages": "856", "suppressedMessages": "857", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "858", "messages": "859", "suppressedMessages": "860", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "861", "messages": "862", "suppressedMessages": "863", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 15, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "864", "messages": "865", "suppressedMessages": "866", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "867", "messages": "868", "suppressedMessages": "869", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 25, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "870", "messages": "871", "suppressedMessages": "872", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "873", "messages": "874", "suppressedMessages": "875", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "876", "messages": "877", "suppressedMessages": "878", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "879", "messages": "880", "suppressedMessages": "881", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "882", "messages": "883", "suppressedMessages": "884", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "885", "messages": "886", "suppressedMessages": "887", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "888", "messages": "889", "suppressedMessages": "890", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "891", "messages": "892", "suppressedMessages": "893", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "894", "messages": "895", "suppressedMessages": "896", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "897", "messages": "898", "suppressedMessages": "899", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "900", "messages": "901", "suppressedMessages": "902", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "903", "messages": "904", "suppressedMessages": "905", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "906", "messages": "907", "suppressedMessages": "908", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "909", "messages": "910", "suppressedMessages": "911", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "912", "messages": "913", "suppressedMessages": "914", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "915", "messages": "916", "suppressedMessages": "917", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "918", "messages": "919", "suppressedMessages": "920", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "921", "messages": "922", "suppressedMessages": "923", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "924", "messages": "925", "suppressedMessages": "926", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "927", "messages": "928", "suppressedMessages": "929", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "930", "messages": "931", "suppressedMessages": "932", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "933", "messages": "934", "suppressedMessages": "935", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "936", "messages": "937", "suppressedMessages": "938", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "939", "messages": "940", "suppressedMessages": "941", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "942", "messages": "943", "suppressedMessages": "944", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "945", "messages": "946", "suppressedMessages": "947", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "948", "messages": "949", "suppressedMessages": "950", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "951", "messages": "952", "suppressedMessages": "953", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "954", "messages": "955", "suppressedMessages": "956", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "957", "messages": "958", "suppressedMessages": "959", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "960", "messages": "961", "suppressedMessages": "962", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "963", "messages": "964", "suppressedMessages": "965", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "966", "messages": "967", "suppressedMessages": "968", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "969", "messages": "970", "suppressedMessages": "971", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "972", "messages": "973", "suppressedMessages": "974", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "975", "messages": "976", "suppressedMessages": "977", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "978", "messages": "979", "suppressedMessages": "980", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "981", "messages": "982", "suppressedMessages": "983", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "984", "messages": "985", "suppressedMessages": "986", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "987", "messages": "988", "suppressedMessages": "989", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "990", "messages": "991", "suppressedMessages": "992", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "993", "messages": "994", "suppressedMessages": "995", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "996", "messages": "997", "suppressedMessages": "998", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "999", "messages": "1000", "suppressedMessages": "1001", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1002", "messages": "1003", "suppressedMessages": "1004", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1005", "messages": "1006", "suppressedMessages": "1007", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1008", "messages": "1009", "suppressedMessages": "1010", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1011", "messages": "1012", "suppressedMessages": "1013", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1014", "messages": "1015", "suppressedMessages": "1016", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1017", "messages": "1018", "suppressedMessages": "1019", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1020", "messages": "1021", "suppressedMessages": "1022", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1023", "messages": "1024", "suppressedMessages": "1025", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1026", "messages": "1027", "suppressedMessages": "1028", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1029", "messages": "1030", "suppressedMessages": "1031", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1032", "messages": "1033", "suppressedMessages": "1034", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1035", "messages": "1036", "suppressedMessages": "1037", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1038", "messages": "1039", "suppressedMessages": "1040", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1041", "messages": "1042", "suppressedMessages": "1043", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1044", "messages": "1045", "suppressedMessages": "1046", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1047", "messages": "1048", "suppressedMessages": "1049", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1050", "messages": "1051", "suppressedMessages": "1052", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1053", "messages": "1054", "suppressedMessages": "1055", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1056", "messages": "1057", "suppressedMessages": "1058", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1059", "messages": "1060", "suppressedMessages": "1061", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1062", "messages": "1063", "suppressedMessages": "1064", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1065", "messages": "1066", "suppressedMessages": "1067", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1068", "messages": "1069", "suppressedMessages": "1070", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1071", "messages": "1072", "suppressedMessages": "1073", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1074", "messages": "1075", "suppressedMessages": "1076", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1077", "messages": "1078", "suppressedMessages": "1079", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1080", "messages": "1081", "suppressedMessages": "1082", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1083", "messages": "1084", "suppressedMessages": "1085", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1086", "messages": "1087", "suppressedMessages": "1088", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1089", "messages": "1090", "suppressedMessages": "1091", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1092", "messages": "1093", "suppressedMessages": "1094", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1095", "messages": "1096", "suppressedMessages": "1097", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1098", "messages": "1099", "suppressedMessages": "1100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1101", "messages": "1102", "suppressedMessages": "1103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1104", "messages": "1105", "suppressedMessages": "1106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1107", "messages": "1108", "suppressedMessages": "1109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1110", "messages": "1111", "suppressedMessages": "1112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1113", "messages": "1114", "suppressedMessages": "1115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1116", "messages": "1117", "suppressedMessages": "1118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1119", "messages": "1120", "suppressedMessages": "1121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1122", "messages": "1123", "suppressedMessages": "1124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1125", "messages": "1126", "suppressedMessages": "1127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1128", "messages": "1129", "suppressedMessages": "1130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1131", "messages": "1132", "suppressedMessages": "1133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1134", "messages": "1135", "suppressedMessages": "1136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1137", "messages": "1138", "suppressedMessages": "1139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1140", "messages": "1141", "suppressedMessages": "1142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1143", "messages": "1144", "suppressedMessages": "1145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1146", "messages": "1147", "suppressedMessages": "1148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1149", "messages": "1150", "suppressedMessages": "1151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1152", "messages": "1153", "suppressedMessages": "1154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1155", "messages": "1156", "suppressedMessages": "1157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1158", "messages": "1159", "suppressedMessages": "1160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1161", "messages": "1162", "suppressedMessages": "1163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1164", "messages": "1165", "suppressedMessages": "1166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1167", "messages": "1168", "suppressedMessages": "1169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1170", "messages": "1171", "suppressedMessages": "1172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1173", "messages": "1174", "suppressedMessages": "1175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1176", "messages": "1177", "suppressedMessages": "1178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1179", "messages": "1180", "suppressedMessages": "1181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1182", "messages": "1183", "suppressedMessages": "1184", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "eb6e59", {"filePath": "1185", "messages": "1186", "suppressedMessages": "1187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1188", "messages": "1189", "suppressedMessages": "1190", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1191", "messages": "1192", "suppressedMessages": "1193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1194", "messages": "1195", "suppressedMessages": "1196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1197", "messages": "1198", "suppressedMessages": "1199", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1200", "messages": "1201", "suppressedMessages": "1202", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1203", "messages": "1204", "suppressedMessages": "1205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1206", "messages": "1207", "suppressedMessages": "1208", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1209", "messages": "1210", "suppressedMessages": "1211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1212", "messages": "1213", "suppressedMessages": "1214", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1215", "messages": "1216", "suppressedMessages": "1217", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1218", "messages": "1219", "suppressedMessages": "1220", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1221", "messages": "1222", "suppressedMessages": "1223", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1224", "messages": "1225", "suppressedMessages": "1226", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1227", "messages": "1228", "suppressedMessages": "1229", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1230", "messages": "1231", "suppressedMessages": "1232", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1233", "messages": "1234", "suppressedMessages": "1235", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1236", "messages": "1237", "suppressedMessages": "1238", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1239", "messages": "1240", "suppressedMessages": "1241", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1242", "messages": "1243", "suppressedMessages": "1244", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1245", "messages": "1246", "suppressedMessages": "1247", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\index.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\App.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\app\\store.js", ["1248"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\containers\\SuspenseContent.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Context\\Context.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\app\\auth.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\app\\init.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\containers\\Layout.jsx", ["1249", "1250", "1251", "1252"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\pages\\ForgotPassword.js", ["1253", "1254", "1255", "1256"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\pages\\Documentation.js", ["1257"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\pages\\Register.js", ["1258", "1259", "1260"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\pages\\Login.js", ["1261", "1262", "1263"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\common\\modalSlice.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\common\\headerSlice.js", ["1264"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\common\\userSlice.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\common\\debounceSlice.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\common\\SbStateSlice.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\common\\resourceSlice.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\common\\rightDrawerSlice.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\common\\routeLists.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\common\\InitialContentSlice.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\common\\homeContentSlice.js", ["1265"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\common\\platformSlice.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\common\\navbarSlice.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\common\\fontStyle.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\common\\ImagesSlice.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\leadSlice.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\components\\fallbackLoader\\FallbackLoader.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\Socket\\socket.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\app\\fetch.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\containers\\LeftSidebar.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\containers\\RightSidebar.jsx", ["1266", "1267", "1268", "1269", "1270", "1271", "1272"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\containers\\ModalLayout.jsx", ["1273"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\containers\\PageContent.jsx", ["1274", "1275", "1276", "1277"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\utils\\globalConstantUtil.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\app\\checkUser.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\user\\Register.js", ["1278"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\components\\Typography\\Title.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\user\\Login.jsx", ["1279", "1280", "1281", "1282", "1283", "1284", "1285"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\user\\ForgotPassword.jsx", ["1286", "1287", "1288", "1289", "1290"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\documentation\\components\\FeaturesNav.js", ["1291"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\documentation\\components\\GettingStartedNav.js", ["1292"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\documentation\\components\\GettingStartedContent.js", ["1293", "1294", "1295", "1296", "1297", "1298", "1299", "1300", "1301", "1302", "1303", "1304", "1305", "1306", "1307", "1308", "1309"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\documentation\\components\\DocComponentsContent.js", ["1310", "1311", "1312", "1313", "1314"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\documentation\\components\\DocComponentsNav.js", ["1315"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\documentation\\components\\FeaturesContent.js", ["1316", "1317", "1318", "1319"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\VersionDetails.jsx", ["1320", "1321", "1322", "1323", "1324", "1325"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Component\\ToastPlacer.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Requests\\RequestDetails.jsx", ["1326", "1327", "1328", "1329", "1330"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\app\\emailregex.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\app\\toastify.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\app\\deviceId.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\app\\valid.js", ["1331"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\user\\ResetPasswordModalBody.jsx", ["1332"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\containers\\Header.jsx", ["1333", "1334", "1335", "1336"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\routes\\backend.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\routes\\sidebar.js", ["1337"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\common\\components\\NotificationBodyRightDrawer.js", ["1338", "1339", "1340", "1341"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\common\\components\\ConfirmationModalBody.js", ["1342", "1343", "1344", "1345"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\pages\\protected\\404.js", ["1346", "1347"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\calendar\\CalendarEventsBodyRightDrawer.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\user\\LandingIntro.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\components\\Typography\\ErrorText.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\components\\Typography\\Subtitle.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\components\\Typography\\HelperText.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\components\\Input\\InputText.jsx", ["1348", "1349"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\user\\components\\OTP.jsx", ["1350", "1351", "1352"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\user\\components\\BackroundImg.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\user\\components\\PasswordValidation.jsx", ["1353"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\user\\components\\UpdatePassword.jsx", ["1354"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\components\\Button\\Button.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\routes\\index.jsx", ["1355", "1356", "1357", "1358", "1359", "1360", "1361", "1362"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\components\\Cards\\TitleCard.jsx", ["1363"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\app\\TimeFormat.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\app\\capitalizeword.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Context\\CustomContext.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\components\\Input\\SearchBar.jsx", ["1364"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Requests\\Comments.jsx", ["1365", "1366"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Component\\Toaster.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Component\\Paginations.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\components\\CalendarView\\util.js", ["1367"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\app\\OTP.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\pages\\DocFeatures.js", ["1368", "1369", "1370"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\pages\\DocComponents.js", ["1371", "1372", "1373"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\pages\\protected\\ProfileSettings.js", ["1374"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\pages\\protected\\Reminders.js", ["1375"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\pages\\protected\\Roles.js", ["1376"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\pages\\protected\\Calendar.js", ["1377"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\pages\\protected\\Integration.js", ["1378"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\pages\\protected\\Bills.js", ["1379"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\pages\\protected\\Requests.js", ["1380"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\pages\\protected\\Logs.js", ["1381"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\pages\\protected\\Resources.js", ["1382"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\pages\\protected\\Team.js", ["1383"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\pages\\protected\\Blank.js", ["1384"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\pages\\GettingStarted.js", ["1385", "1386", "1387"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\pages\\protected\\Users.js", ["1388"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\pages\\protected\\Dashboard.js", ["1389"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\pages\\protected\\Welcome.js", ["1390", "1391"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\documentation\\DocComponents.js", ["1392", "1393", "1394", "1395", "1396", "1397", "1398"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\documentation\\DocFeatures.js", ["1399", "1400", "1401", "1402", "1403", "1404", "1405"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\documentation\\DocGettingStarted.js", ["1406", "1407", "1408", "1409", "1410"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\index.jsx", ["1411"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\calendar\\index.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\user\\components\\TemplatePointers.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Requests\\index.jsx", ["1412", "1413", "1414", "1415", "1416", "1417", "1418", "1419", "1420", "1421", "1422", "1423", "1424"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\dashboard\\index.js", ["1425", "1426", "1427", "1428", "1429", "1430", "1431", "1432", "1433", "1434", "1435"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\logstable\\index.jsx", ["1436", "1437", "1438", "1439", "1440", "1441", "1442", "1443", "1444", "1445", "1446", "1447", "1448"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Reminders\\index.jsx", ["1449", "1450", "1451", "1452", "1453"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Roles\\index.jsx", ["1454", "1455", "1456", "1457", "1458", "1459", "1460"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\charts\\index.jsx", ["1461", "1462", "1463", "1464", "1465", "1466"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\integration\\index.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\settings\\team\\index.js", ["1467", "1468", "1469"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\settings\\billing\\index.js", ["1470", "1471", "1472", "1473", "1474"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\settings\\profilesettings\\index.jsx", ["1475", "1476", "1477", "1478", "1479", "1480", "1481", "1482"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Requests\\Showdifference.jsx", ["1483", "1484", "1485", "1486", "1487", "1488"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\utils\\dummyData.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Requests\\ShowVerifierTooltip.jsx", ["1489"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Requests\\ShowPDF.jsx", ["1490", "1491"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\Resources.jsx", ["1492", "1493", "1494", "1495", "1496", "1497", "1498", "1499", "1500", "1501", "1502", "1503", "1504", "1505", "1506"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\EditPage.jsx", ["1507", "1508", "1509", "1510"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\VersionTable.jsx", ["1511", "1512", "1513", "1514", "1515", "1516", "1517", "1518", "1519", "1520", "1521", "1522", "1523", "1524", "1525", "1526", "1527", "1528", "1529", "1530", "1531", "1532", "1533", "1534", "1535"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\RequestTable.jsx", ["1536", "1537", "1538", "1539", "1540", "1541", "1542"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\components\\Input\\ToogleInput.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\components\\CalendarView\\index.js", ["1543", "1544", "1545"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\components\\Toggle\\Toggle.jsx", ["1546"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\components\\Input\\Select.jsx", ["1547", "1548"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\components\\Input\\TextAreaInput.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\logstable\\ShowLog.jsx", ["1549", "1550", "1551", "1552", "1553", "1554", "1555", "1556"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\dashboard\\components\\DashboardStats.js", ["1557"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\dashboard\\components\\AmountStats.js", ["1558"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\dashboard\\components\\PageStats.js", ["1559"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\dashboard\\components\\LineChart.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\dashboard\\components\\DashboardTopBar.js", ["1560", "1561", "1562"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\dashboard\\components\\BarChart.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\dashboard\\components\\UserChannels.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\dashboard\\components\\DoughnutChart.js", ["1563", "1564"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Roles\\AddRole.jsx", ["1565", "1566", "1567", "1568", "1569", "1570", "1571", "1572", "1573", "1574", "1575"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Roles\\ShowRole.jsx", ["1576", "1577", "1578"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\charts\\ShowRole.jsx", ["1579", "1580"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\charts\\AddUser.jsx", ["1581", "1582", "1583", "1584", "1585", "1586", "1587", "1588", "1589", "1590", "1591"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\components\\Input\\DirectInputFile.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\defineContent.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Requests\\RejectPopup.jsx", ["1592"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Requests\\DateTime.jsx", ["1593", "1594", "1595"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\containers\\Navbar.jsx", ["1596", "1597"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\ResourceCard.jsx", ["1598", "1599", "1600", "1601", "1602", "1603"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\components\\Button\\CloseButton.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\NewProjectDialog.jsx", ["1604", "1605"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\breakUI\\Popups.jsx", ["1606"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\AllForOne.jsx", ["1607", "1608", "1609", "1610", "1611", "1612"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\breakUI\\SwitchLang.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\breakUI\\ConfigBar.jsx", ["1613", "1614"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\AllForOneManager.jsx", ["1615", "1616"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\breakUI\\PageDetails.jsx", ["1617", "1618"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\breakUI\\ContentTopBar.jsx", ["1619", "1620", "1621"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\components\\Input\\InputFileForm.jsx", ["1622", "1623", "1624", "1625", "1626"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\structures\\PageStructure.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\components\\Loader\\SkeletonLoader.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\resourcedata.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\breakUI\\DropDownMenu.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\Home.jsx", ["1627", "1628", "1629", "1630", "1631"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\About.jsx", ["1632", "1633", "1634", "1635", "1636", "1637"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\Projects.jsx", ["1638", "1639", "1640", "1641", "1642", "1643", "1644", "1645"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\Solutions.jsx", ["1646"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\Market.jsx", ["1647", "1648", "1649", "1650", "1651", "1652", "1653", "1654"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\CareersPage.jsx", ["1655", "1656", "1657", "1658", "1659", "1660"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\NewsPage.jsx", ["1661", "1662", "1663", "1664", "1665", "1666", "1667"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\Service.jsx", ["1668"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\HistoryPage.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\SafetyAndResponsibility.jsx", ["1669", "1670"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\VisionPage.jsx", ["1671", "1672", "1673"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\HSE.jsx", ["1674", "1675", "1676", "1677"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\Affiliates.jsx", ["1678", "1679", "1680"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\subparts\\Footerweb.jsx", ["1681"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\subparts\\Testimonials.jsx", ["1682", "1683", "1684", "1685", "1686", "1687", "1688", "1689", "1690", "1691", "1692"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\subparts\\ContactUsModal.jsx", ["1693", "1694", "1695", "1696"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\subparts\\Headerweb.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\detailspages\\ProjectDetails.jsx", ["1697", "1698", "1699", "1700", "1701", "1702"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\detailspages\\ServiceDetails.jsx", ["1703", "1704", "1705", "1706"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\detailspages\\CareersDetails.jsx", ["1707", "1708", "1709", "1710", "1711", "1712", "1713"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\detailspages\\SnRPolicies.jsx", ["1714", "1715", "1716", "1717", "1718"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\detailspages\\NewsDetails.jsx", ["1719", "1720", "1721", "1722", "1723", "1724", "1725", "1726", "1727"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\detailspages\\MarketDetails.jsx", ["1728", "1729", "1730"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\Organizational.jsx", ["1731", "1732", "1733", "1734"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\TemplateOne.jsx", ["1735", "1736", "1737", "1738"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\TemplateTwo.jsx", ["1739", "1740"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\app\\convertContent.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\TempThree.jsx", ["1741", "1742", "1743", "1744"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\breakUI\\SelectorAccordion.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\breakUI\\Statusbar.jsx", ["1745", "1746"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\TemplateFour.jsx", ["1747"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\components\\Input\\ImageSelector.jsx", ["1748", "1749", "1750"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\subDetailsPages\\SubServiceDetails.jsx", ["1751", "1752", "1753"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\contentmanager\\SolutionManager.jsx", ["1754", "1755", "1756"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\contentmanager\\HomeManager.jsx", ["1757", "1758", "1759", "1760", "1761", "1762"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\contentmanager\\MarketManager.jsx", ["1763", "1764", "1765", "1766", "1767"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\contentmanager\\AboutManager.jsx", ["1768", "1769", "1770", "1771", "1772", "1773", "1774", "1775", "1776"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\contentmanager\\ProjectContentManager.jsx", ["1777", "1778", "1779"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\contentmanager\\NewsManager.jsx", [], ["1780"], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\contentmanager\\HistoryManager.jsx", ["1781", "1782"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\contentmanager\\ServiceManager.jsx", ["1783", "1784", "1785"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\contentmanager\\SnRPolicyManager.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\contentmanager\\CareersManager.jsx", ["1786"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\contentmanager\\SnRManager.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\contentmanager\\AffiliatesManager.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\contentmanager\\VisionManager.jsx", ["1787", "1788", "1789"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\contentmanager\\OrganizationManager.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\contentmanager\\TemplateOneManager.jsx", ["1790", "1791"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\contentmanager\\HSnEManager.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\contentmanager\\TemplateThreeManager.jsx", ["1792", "1793"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\contentmanager\\TemplateFourManager.jsx", ["1794", "1795"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\contentmanager\\CMforDetails\\CareerDetailManager.jsx", ["1796", "1797", "1798", "1799", "1800", "1801", "1802", "1803"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\contentmanager\\CMforDetails\\ProjectDetailManager.jsx", ["1804", "1805", "1806", "1807", "1808"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\contentmanager\\CMforDetails\\MarketDetailsManager.jsx", ["1809", "1810", "1811"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\contentmanager\\CMforDetails\\NewsDetailsManager.jsx", ["1812", "1813", "1814", "1815", "1816", "1817"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\contentmanager\\CMforDetails\\ServiceDetailsManager.jsx", ["1818", "1819"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\contentmanager\\CMforSubParts\\HeaderManager.jsx", ["1820", "1821"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\contentmanager\\CMforSubParts\\TestimonyManager.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\contentmanager\\CMforSubParts\\FooterManager.jsx", [], ["1822"], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\contentmanager\\subDetailsManagement\\SubServiceDetailManagement.jsx", ["1823", "1824", "1825"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\contentmanager\\TemplateTwoManager.jsx", ["1826", "1827"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\app\\fontSizes.js", ["1828"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\assets\\index.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\subparts\\Pagination.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\subparts\\ModalPortal.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\components\\Input\\useImageUpload.js", ["1829"], ["1830", "1831", "1832", "1833", "1834", "1835", "1836", "1837", "1838", "1839", "1840", "1841", "1842", "1843"], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\breakUI\\ContentSections.jsx", ["1844", "1845", "1846", "1847", "1848"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\breakUI\\MultiSelect.jsx", ["1849", "1850", "1851", "1852"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\components\\Input\\InputFileUploader.jsx", ["1853", "1854", "1855", "1856", "1857", "1858", "1859"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\breakUI\\MultiSelectPro.jsx", ["1860", "1861", "1862", "1863"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\breakUI\\DynamicContentSection.jsx", ["1864", "1865"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\breakUI\\MultiSelectSM.jsx", ["1866", "1867", "1868", "1869"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\components\\Input\\InputFile.jsx", ["1870", "1871", "1872", "1873", "1874"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\common\\thunk\\smsThunk.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\pages\\protected\\ContactQueries.js", ["1875"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\ContactQueries\\index.jsx", ["1876", "1877", "1878", "1879", "1880", "1881"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\components\\TableStates\\TableSkeleton.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\components\\TableStates\\ErrorState.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\components\\TableStates\\EmptyState.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\breakUI\\ScrollableParagraph.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\breakUI\\ScrollableHtml.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\components\\Input\\UploadProgress.jsx", ["1882"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\components\\Input\\useBatchUpload.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\ContactUsPage.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\contentmanager\\ContactUsManager.jsx", ["1883"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\contentmanager\\CareerManager.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Akshay\\LOOP_PROJECTS\\shade_cms\\dashboard\\src\\features\\Resources\\components\\websiteComponent\\CareerPageEditor.jsx", [], [], {"ruleId": "1884", "severity": 1, "message": "1885", "line": 9, "column": 8, "nodeType": "1886", "messageId": "1887", "endLine": 9, "endColumn": 21}, {"ruleId": "1884", "severity": 1, "message": "1888", "line": 12, "column": 10, "nodeType": "1886", "messageId": "1887", "endLine": 12, "endColumn": 15}, {"ruleId": "1884", "severity": 1, "message": "1889", "line": 13, "column": 10, "nodeType": "1886", "messageId": "1887", "endLine": 13, "endColumn": 30}, {"ruleId": "1890", "severity": 1, "message": "1891", "line": 34, "column": 6, "nodeType": "1892", "endLine": 34, "endColumn": 30, "suggestions": "1893"}, {"ruleId": "1890", "severity": 1, "message": "1894", "line": 45, "column": 6, "nodeType": "1892", "endLine": 45, "endColumn": 8, "suggestions": "1895"}, {"ruleId": "1884", "severity": 1, "message": "1896", "line": 1, "column": 9, "nodeType": "1886", "messageId": "1887", "endLine": 1, "endColumn": 17}, {"ruleId": "1884", "severity": 1, "message": "1897", "line": 1, "column": 19, "nodeType": "1886", "messageId": "1887", "endLine": 1, "endColumn": 25}, {"ruleId": "1884", "severity": 1, "message": "1898", "line": 2, "column": 9, "nodeType": "1886", "messageId": "1887", "endLine": 2, "endColumn": 13}, {"ruleId": "1884", "severity": 1, "message": "1899", "line": 4, "column": 8, "nodeType": "1886", "messageId": "1887", "endLine": 4, "endColumn": 13}, {"ruleId": "1884", "severity": 1, "message": "1900", "line": 5, "column": 8, "nodeType": "1886", "messageId": "1887", "endLine": 5, "endColumn": 13}, {"ruleId": "1884", "severity": 1, "message": "1896", "line": 1, "column": 9, "nodeType": "1886", "messageId": "1887", "endLine": 1, "endColumn": 17}, {"ruleId": "1884", "severity": 1, "message": "1897", "line": 1, "column": 19, "nodeType": "1886", "messageId": "1887", "endLine": 1, "endColumn": 25}, {"ruleId": "1884", "severity": 1, "message": "1898", "line": 2, "column": 9, "nodeType": "1886", "messageId": "1887", "endLine": 2, "endColumn": 13}, {"ruleId": "1884", "severity": 1, "message": "1896", "line": 1, "column": 9, "nodeType": "1886", "messageId": "1887", "endLine": 1, "endColumn": 17}, {"ruleId": "1884", "severity": 1, "message": "1897", "line": 1, "column": 19, "nodeType": "1886", "messageId": "1887", "endLine": 1, "endColumn": 25}, {"ruleId": "1884", "severity": 1, "message": "1898", "line": 2, "column": 9, "nodeType": "1886", "messageId": "1887", "endLine": 2, "endColumn": 13}, {"ruleId": "1884", "severity": 1, "message": "1901", "line": 30, "column": 11, "nodeType": "1886", "messageId": "1887", "endLine": 30, "endColumn": 16}, {"ruleId": "1884", "severity": 1, "message": "1902", "line": 14, "column": 10, "nodeType": "1886", "messageId": "1887", "endLine": 14, "endColumn": 21}, {"ruleId": "1884", "severity": 1, "message": "1903", "line": 6, "column": 3, "nodeType": "1886", "messageId": "1887", "endLine": 6, "endColumn": 18}, {"ruleId": "1884", "severity": 1, "message": "1904", "line": 10, "column": 9, "nodeType": "1886", "messageId": "1887", "endLine": 10, "endColumn": 20}, {"ruleId": "1884", "severity": 1, "message": "1905", "line": 10, "column": 22, "nodeType": "1886", "messageId": "1887", "endLine": 10, "endColumn": 31}, {"ruleId": "1884", "severity": 1, "message": "1906", "line": 18, "column": 3, "nodeType": "1886", "messageId": "1887", "endLine": 18, "endColumn": 29}, {"ruleId": "1884", "severity": 1, "message": "1907", "line": 20, "column": 8, "nodeType": "1886", "messageId": "1887", "endLine": 20, "endColumn": 14}, {"ruleId": "1884", "severity": 1, "message": "1908", "line": 23, "column": 9, "nodeType": "1886", "messageId": "1887", "endLine": 23, "endColumn": 23}, {"ruleId": "1884", "severity": 1, "message": "1888", "line": 25, "column": 9, "nodeType": "1886", "messageId": "1887", "endLine": 25, "endColumn": 14}, {"ruleId": "1884", "severity": 1, "message": "1909", "line": 8, "column": 10, "nodeType": "1886", "messageId": "1887", "endLine": 8, "endColumn": 11}, {"ruleId": "1884", "severity": 1, "message": "1910", "line": 6, "column": 10, "nodeType": "1886", "messageId": "1887", "endLine": 6, "endColumn": 21}, {"ruleId": "1884", "severity": 1, "message": "1897", "line": 7, "column": 21, "nodeType": "1886", "messageId": "1887", "endLine": 7, "endColumn": 27}, {"ruleId": "1890", "severity": 1, "message": "1911", "line": 33, "column": 8, "nodeType": "1892", "endLine": 33, "endColumn": 19, "suggestions": "1912"}, {"ruleId": "1890", "severity": 1, "message": "1913", "line": 42, "column": 8, "nodeType": "1892", "endLine": 42, "endColumn": 10, "suggestions": "1914"}, {"ruleId": "1884", "severity": 1, "message": "1897", "line": 1, "column": 19, "nodeType": "1886", "messageId": "1887", "endLine": 1, "endColumn": 25}, {"ruleId": "1884", "severity": 1, "message": "1908", "line": 7, "column": 10, "nodeType": "1886", "messageId": "1887", "endLine": 7, "endColumn": 24}, {"ruleId": "1884", "severity": 1, "message": "1888", "line": 7, "column": 26, "nodeType": "1886", "messageId": "1887", "endLine": 7, "endColumn": 31}, {"ruleId": "1884", "severity": 1, "message": "1915", "line": 11, "column": 8, "nodeType": "1886", "messageId": "1887", "endLine": 11, "endColumn": 21}, {"ruleId": "1884", "severity": 1, "message": "1907", "line": 16, "column": 8, "nodeType": "1886", "messageId": "1887", "endLine": 16, "endColumn": 14}, {"ruleId": "1884", "severity": 1, "message": "1916", "line": 17, "column": 10, "nodeType": "1886", "messageId": "1887", "endLine": 17, "endColumn": 20}, {"ruleId": "1890", "severity": 1, "message": "1917", "line": 152, "column": 6, "nodeType": "1892", "endLine": 152, "endColumn": 8, "suggestions": "1918"}, {"ruleId": "1890", "severity": 1, "message": "1919", "line": 169, "column": 6, "nodeType": "1892", "endLine": 169, "endColumn": 8, "suggestions": "1920"}, {"ruleId": "1884", "severity": 1, "message": "1915", "line": 11, "column": 8, "nodeType": "1886", "messageId": "1887", "endLine": 11, "endColumn": 21}, {"ruleId": "1884", "severity": 1, "message": "1888", "line": 12, "column": 10, "nodeType": "1886", "messageId": "1887", "endLine": 12, "endColumn": 15}, {"ruleId": "1884", "severity": 1, "message": "1908", "line": 12, "column": 17, "nodeType": "1886", "messageId": "1887", "endLine": 12, "endColumn": 31}, {"ruleId": "1890", "severity": 1, "message": "1921", "line": 66, "column": 8, "nodeType": "1892", "endLine": 66, "endColumn": 10, "suggestions": "1922"}, {"ruleId": "1890", "severity": 1, "message": "1923", "line": 76, "column": 8, "nodeType": "1892", "endLine": 76, "endColumn": 10, "suggestions": "1924"}, {"ruleId": "1925", "severity": 1, "message": "1926", "line": 31, "column": 113, "nodeType": "1927", "endLine": 31, "endColumn": 116}, {"ruleId": "1925", "severity": 1, "message": "1926", "line": 32, "column": 113, "nodeType": "1927", "endLine": 32, "endColumn": 116}, {"ruleId": "1884", "severity": 1, "message": "1905", "line": 1, "column": 10, "nodeType": "1886", "messageId": "1887", "endLine": 1, "endColumn": 19}, {"ruleId": "1884", "severity": 1, "message": "1928", "line": 3, "column": 8, "nodeType": "1886", "messageId": "1887", "endLine": 3, "endColumn": 16}, {"ruleId": "1884", "severity": 1, "message": "1929", "line": 4, "column": 10, "nodeType": "1886", "messageId": "1887", "endLine": 4, "endColumn": 22}, {"ruleId": "1884", "severity": 1, "message": "1930", "line": 8, "column": 11, "nodeType": "1886", "messageId": "1887", "endLine": 8, "endColumn": 19}, {"ruleId": "1931", "severity": 1, "message": "1932", "line": 24, "column": 23, "nodeType": "1927", "messageId": "1933", "endLine": 24, "endColumn": 70, "fix": "1934"}, {"ruleId": "1931", "severity": 1, "message": "1932", "line": 25, "column": 23, "nodeType": "1927", "messageId": "1933", "endLine": 25, "endColumn": 81, "fix": "1935"}, {"ruleId": "1931", "severity": 1, "message": "1932", "line": 26, "column": 23, "nodeType": "1927", "messageId": "1933", "endLine": 26, "endColumn": 74, "fix": "1936"}, {"ruleId": "1931", "severity": 1, "message": "1932", "line": 27, "column": 23, "nodeType": "1927", "messageId": "1933", "endLine": 27, "endColumn": 70, "fix": "1937"}, {"ruleId": "1931", "severity": 1, "message": "1932", "line": 28, "column": 23, "nodeType": "1927", "messageId": "1933", "endLine": 28, "endColumn": 72, "fix": "1938"}, {"ruleId": "1931", "severity": 1, "message": "1932", "line": 29, "column": 23, "nodeType": "1927", "messageId": "1933", "endLine": 29, "endColumn": 79, "fix": "1939"}, {"ruleId": "1931", "severity": 1, "message": "1932", "line": 30, "column": 23, "nodeType": "1927", "messageId": "1933", "endLine": 30, "endColumn": 81, "fix": "1940"}, {"ruleId": "1931", "severity": 1, "message": "1932", "line": 52, "column": 21, "nodeType": "1927", "messageId": "1933", "endLine": 52, "endColumn": 143, "fix": "1941"}, {"ruleId": "1931", "severity": 1, "message": "1932", "line": 68, "column": 18, "nodeType": "1927", "messageId": "1933", "endLine": 68, "endColumn": 94, "fix": "1942"}, {"ruleId": "1931", "severity": 1, "message": "1932", "line": 70, "column": 174, "nodeType": "1927", "messageId": "1933", "endLine": 70, "endColumn": 260, "fix": "1943"}, {"ruleId": "1931", "severity": 1, "message": "1932", "line": 101, "column": 102, "nodeType": "1927", "messageId": "1933", "endLine": 101, "endColumn": 160, "fix": "1944"}, {"ruleId": "1931", "severity": 1, "message": "1932", "line": 119, "column": 20, "nodeType": "1927", "messageId": "1933", "endLine": 119, "endColumn": 98, "fix": "1945"}, {"ruleId": "1931", "severity": 1, "message": "1932", "line": 121, "column": 104, "nodeType": "1927", "messageId": "1933", "endLine": 121, "endColumn": 193, "fix": "1946"}, {"ruleId": "1884", "severity": 1, "message": "1905", "line": 1, "column": 10, "nodeType": "1886", "messageId": "1887", "endLine": 1, "endColumn": 19}, {"ruleId": "1884", "severity": 1, "message": "1929", "line": 9, "column": 10, "nodeType": "1886", "messageId": "1887", "endLine": 9, "endColumn": 22}, {"ruleId": "1884", "severity": 1, "message": "1947", "line": 9, "column": 24, "nodeType": "1886", "messageId": "1887", "endLine": 9, "endColumn": 40}, {"ruleId": "1884", "severity": 1, "message": "1930", "line": 14, "column": 11, "nodeType": "1886", "messageId": "1887", "endLine": 14, "endColumn": 19}, {"ruleId": "1931", "severity": 1, "message": "1932", "line": 77, "column": 21, "nodeType": "1927", "messageId": "1933", "endLine": 77, "endColumn": 84, "fix": "1948"}, {"ruleId": "1925", "severity": 1, "message": "1926", "line": 27, "column": 113, "nodeType": "1927", "endLine": 27, "endColumn": 116}, {"ruleId": "1884", "severity": 1, "message": "1905", "line": 1, "column": 10, "nodeType": "1886", "messageId": "1887", "endLine": 1, "endColumn": 19}, {"ruleId": "1884", "severity": 1, "message": "1928", "line": 3, "column": 8, "nodeType": "1886", "messageId": "1887", "endLine": 3, "endColumn": 16}, {"ruleId": "1884", "severity": 1, "message": "1929", "line": 4, "column": 10, "nodeType": "1886", "messageId": "1887", "endLine": 4, "endColumn": 22}, {"ruleId": "1931", "severity": 1, "message": "1932", "line": 84, "column": 407, "nodeType": "1927", "messageId": "1933", "endLine": 84, "endColumn": 466, "fix": "1949"}, {"ruleId": "1884", "severity": 1, "message": "1888", "line": 9, "column": 9, "nodeType": "1886", "messageId": "1887", "endLine": 9, "endColumn": 14}, {"ruleId": "1884", "severity": 1, "message": "1908", "line": 9, "column": 16, "nodeType": "1886", "messageId": "1887", "endLine": 9, "endColumn": 30}, {"ruleId": "1884", "severity": 1, "message": "1950", "line": 12, "column": 8, "nodeType": "1886", "messageId": "1887", "endLine": 12, "endColumn": 19}, {"ruleId": "1884", "severity": 1, "message": "1951", "line": 14, "column": 7, "nodeType": "1886", "messageId": "1887", "endLine": 14, "endColumn": 11}, {"ruleId": "1884", "severity": 1, "message": "1952", "line": 53, "column": 7, "nodeType": "1886", "messageId": "1887", "endLine": 53, "endColumn": 18}, {"ruleId": "1884", "severity": 1, "message": "1953", "line": 65, "column": 10, "nodeType": "1886", "messageId": "1887", "endLine": 65, "endColumn": 18}, {"ruleId": "1884", "severity": 1, "message": "1954", "line": 6, "column": 27, "nodeType": "1886", "messageId": "1887", "endLine": 6, "endColumn": 39}, {"ruleId": "1884", "severity": 1, "message": "1955", "line": 9, "column": 10, "nodeType": "1886", "messageId": "1887", "endLine": 9, "endColumn": 15}, {"ruleId": "1884", "severity": 1, "message": "1951", "line": 13, "column": 7, "nodeType": "1886", "messageId": "1887", "endLine": 13, "endColumn": 11}, {"ruleId": "1884", "severity": 1, "message": "1956", "line": 55, "column": 10, "nodeType": "1886", "messageId": "1887", "endLine": 55, "endColumn": 19}, {"ruleId": "1884", "severity": 1, "message": "1957", "line": 55, "column": 21, "nodeType": "1886", "messageId": "1887", "endLine": 55, "endColumn": 33}, {"ruleId": "1884", "severity": 1, "message": "1958", "line": 26, "column": 19, "nodeType": "1886", "messageId": "1887", "endLine": 26, "endColumn": 26}, {"ruleId": "1890", "severity": 1, "message": "1959", "line": 94, "column": 6, "nodeType": "1892", "endLine": 94, "endColumn": 8, "suggestions": "1960"}, {"ruleId": "1884", "severity": 1, "message": "1961", "line": 13, "column": 8, "nodeType": "1886", "messageId": "1887", "endLine": 13, "endColumn": 17}, {"ruleId": "1884", "severity": 1, "message": "1962", "line": 43, "column": 9, "nodeType": "1886", "messageId": "1887", "endLine": 43, "endColumn": 17}, {"ruleId": "1884", "severity": 1, "message": "1963", "line": 139, "column": 11, "nodeType": "1886", "messageId": "1887", "endLine": 139, "endColumn": 20}, {"ruleId": "1925", "severity": 1, "message": "1926", "line": 345, "column": 17, "nodeType": "1927", "endLine": 345, "endColumn": 41}, {"ruleId": "1884", "severity": 1, "message": "1964", "line": 28, "column": 7, "nodeType": "1886", "messageId": "1887", "endLine": 28, "endColumn": 25}, {"ruleId": "1884", "severity": 1, "message": "1965", "line": 3, "column": 3, "nodeType": "1886", "messageId": "1887", "endLine": 3, "endColumn": 23}, {"ruleId": "1884", "severity": 1, "message": "1966", "line": 4, "column": 3, "nodeType": "1886", "messageId": "1887", "endLine": 4, "endColumn": 28}, {"ruleId": "1884", "severity": 1, "message": "1889", "line": 6, "column": 10, "nodeType": "1886", "messageId": "1887", "endLine": 6, "endColumn": 30}, {"ruleId": "1884", "severity": 1, "message": "1910", "line": 7, "column": 10, "nodeType": "1886", "messageId": "1887", "endLine": 7, "endColumn": 21}, {"ruleId": "1884", "severity": 1, "message": "1967", "line": 1, "column": 22, "nodeType": "1886", "messageId": "1887", "endLine": 1, "endColumn": 33}, {"ruleId": "1884", "severity": 1, "message": "1968", "line": 2, "column": 8, "nodeType": "1886", "messageId": "1887", "endLine": 2, "endColumn": 13}, {"ruleId": "1884", "severity": 1, "message": "1969", "line": 3, "column": 42, "nodeType": "1886", "messageId": "1887", "endLine": 3, "endColumn": 59}, {"ruleId": "1884", "severity": 1, "message": "1970", "line": 11, "column": 28, "nodeType": "1886", "messageId": "1887", "endLine": 11, "endColumn": 31}, {"ruleId": "1884", "severity": 1, "message": "1971", "line": 4, "column": 8, "nodeType": "1886", "messageId": "1887", "endLine": 4, "endColumn": 21}, {"ruleId": "1890", "severity": 1, "message": "1894", "line": 12, "column": 8, "nodeType": "1892", "endLine": 12, "endColumn": 10, "suggestions": "1972"}, {"ruleId": "1884", "severity": 1, "message": "1973", "line": 2, "column": 23, "nodeType": "1886", "messageId": "1887", "endLine": 2, "endColumn": 31}, {"ruleId": "1884", "severity": 1, "message": "1974", "line": 4, "column": 8, "nodeType": "1886", "messageId": "1887", "endLine": 4, "endColumn": 16}, {"ruleId": "1884", "severity": 1, "message": "1888", "line": 8, "column": 10, "nodeType": "1886", "messageId": "1887", "endLine": 8, "endColumn": 15}, {"ruleId": "1884", "severity": 1, "message": "1915", "line": 10, "column": 8, "nodeType": "1886", "messageId": "1887", "endLine": 10, "endColumn": 21}, {"ruleId": "1890", "severity": 1, "message": "1975", "line": 33, "column": 8, "nodeType": "1892", "endLine": 33, "endColumn": 10, "suggestions": "1976"}, {"ruleId": "1977", "severity": 1, "message": "1978", "line": 46, "column": 21, "nodeType": "1927", "endLine": 46, "endColumn": 112}, {"ruleId": "1884", "severity": 1, "message": "1888", "line": 7, "column": 10, "nodeType": "1886", "messageId": "1887", "endLine": 7, "endColumn": 15}, {"ruleId": "1884", "severity": 1, "message": "1979", "line": 8, "column": 7, "nodeType": "1886", "messageId": "1887", "endLine": 8, "endColumn": 12}, {"ruleId": "1884", "severity": 1, "message": "1980", "line": 12, "column": 7, "nodeType": "1886", "messageId": "1887", "endLine": 12, "endColumn": 18}, {"ruleId": "1884", "severity": 1, "message": "1981", "line": 13, "column": 7, "nodeType": "1886", "messageId": "1887", "endLine": 13, "endColumn": 15}, {"ruleId": "1884", "severity": 1, "message": "1982", "line": 14, "column": 7, "nodeType": "1886", "messageId": "1887", "endLine": 14, "endColumn": 11}, {"ruleId": "1884", "severity": 1, "message": "1983", "line": 17, "column": 7, "nodeType": "1886", "messageId": "1887", "endLine": 17, "endColumn": 12}, {"ruleId": "1884", "severity": 1, "message": "1984", "line": 19, "column": 7, "nodeType": "1886", "messageId": "1887", "endLine": 19, "endColumn": 21}, {"ruleId": "1884", "severity": 1, "message": "1985", "line": 20, "column": 7, "nodeType": "1886", "messageId": "1887", "endLine": 20, "endColumn": 18}, {"ruleId": "1884", "severity": 1, "message": "1986", "line": 21, "column": 7, "nodeType": "1886", "messageId": "1887", "endLine": 21, "endColumn": 20}, {"ruleId": "1884", "severity": 1, "message": "1987", "line": 1, "column": 10, "nodeType": "1886", "messageId": "1887", "endLine": 1, "endColumn": 31}, {"ruleId": "1884", "severity": 1, "message": "1905", "line": 1, "column": 17, "nodeType": "1886", "messageId": "1887", "endLine": 1, "endColumn": 26}, {"ruleId": "1884", "severity": 1, "message": "1954", "line": 2, "column": 10, "nodeType": "1886", "messageId": "1887", "endLine": 2, "endColumn": 22}, {"ruleId": "1884", "severity": 1, "message": "1955", "line": 3, "column": 10, "nodeType": "1886", "messageId": "1887", "endLine": 3, "endColumn": 15}, {"ruleId": "1884", "severity": 1, "message": "1988", "line": 1, "column": 7, "nodeType": "1886", "messageId": "1887", "endLine": 1, "endColumn": 13}, {"ruleId": "1884", "severity": 1, "message": "1896", "line": 1, "column": 9, "nodeType": "1886", "messageId": "1887", "endLine": 1, "endColumn": 17}, {"ruleId": "1884", "severity": 1, "message": "1897", "line": 1, "column": 19, "nodeType": "1886", "messageId": "1887", "endLine": 1, "endColumn": 25}, {"ruleId": "1884", "severity": 1, "message": "1898", "line": 2, "column": 9, "nodeType": "1886", "messageId": "1887", "endLine": 2, "endColumn": 13}, {"ruleId": "1884", "severity": 1, "message": "1896", "line": 1, "column": 9, "nodeType": "1886", "messageId": "1887", "endLine": 1, "endColumn": 17}, {"ruleId": "1884", "severity": 1, "message": "1897", "line": 1, "column": 19, "nodeType": "1886", "messageId": "1887", "endLine": 1, "endColumn": 25}, {"ruleId": "1884", "severity": 1, "message": "1898", "line": 2, "column": 9, "nodeType": "1886", "messageId": "1887", "endLine": 2, "endColumn": 13}, {"ruleId": "1890", "severity": 1, "message": "1894", "line": 11, "column": 10, "nodeType": "1892", "endLine": 11, "endColumn": 12, "suggestions": "1989"}, {"ruleId": "1890", "severity": 1, "message": "1894", "line": 11, "column": 10, "nodeType": "1892", "endLine": 11, "endColumn": 12, "suggestions": "1990"}, {"ruleId": "1890", "severity": 1, "message": "1894", "line": 11, "column": 10, "nodeType": "1892", "endLine": 11, "endColumn": 12, "suggestions": "1991"}, {"ruleId": "1890", "severity": 1, "message": "1894", "line": 11, "column": 10, "nodeType": "1892", "endLine": 11, "endColumn": 12, "suggestions": "1992"}, {"ruleId": "1890", "severity": 1, "message": "1894", "line": 12, "column": 10, "nodeType": "1892", "endLine": 12, "endColumn": 12, "suggestions": "1993"}, {"ruleId": "1890", "severity": 1, "message": "1894", "line": 11, "column": 10, "nodeType": "1892", "endLine": 11, "endColumn": 12, "suggestions": "1994"}, {"ruleId": "1890", "severity": 1, "message": "1894", "line": 12, "column": 8, "nodeType": "1892", "endLine": 12, "endColumn": 10, "suggestions": "1995"}, {"ruleId": "1890", "severity": 1, "message": "1894", "line": 11, "column": 10, "nodeType": "1892", "endLine": 11, "endColumn": 12, "suggestions": "1996"}, {"ruleId": "1890", "severity": 1, "message": "1894", "line": 12, "column": 8, "nodeType": "1892", "endLine": 12, "endColumn": 10, "suggestions": "1997"}, {"ruleId": "1890", "severity": 1, "message": "1894", "line": 11, "column": 10, "nodeType": "1892", "endLine": 11, "endColumn": 12, "suggestions": "1998"}, {"ruleId": "1890", "severity": 1, "message": "1894", "line": 13, "column": 10, "nodeType": "1892", "endLine": 13, "endColumn": 12, "suggestions": "1999"}, {"ruleId": "1884", "severity": 1, "message": "1896", "line": 1, "column": 9, "nodeType": "1886", "messageId": "1887", "endLine": 1, "endColumn": 17}, {"ruleId": "1884", "severity": 1, "message": "1897", "line": 1, "column": 19, "nodeType": "1886", "messageId": "1887", "endLine": 1, "endColumn": 25}, {"ruleId": "1884", "severity": 1, "message": "1898", "line": 2, "column": 9, "nodeType": "1886", "messageId": "1887", "endLine": 2, "endColumn": 13}, {"ruleId": "1890", "severity": 1, "message": "1894", "line": 11, "column": 10, "nodeType": "1892", "endLine": 11, "endColumn": 12, "suggestions": "2000"}, {"ruleId": "1890", "severity": 1, "message": "1894", "line": 11, "column": 10, "nodeType": "1892", "endLine": 11, "endColumn": 12, "suggestions": "2001"}, {"ruleId": "1884", "severity": 1, "message": "1898", "line": 4, "column": 9, "nodeType": "1886", "messageId": "1887", "endLine": 4, "endColumn": 13}, {"ruleId": "1890", "severity": 1, "message": "1894", "line": 12, "column": 6, "nodeType": "1892", "endLine": 12, "endColumn": 8, "suggestions": "2002"}, {"ruleId": "1884", "severity": 1, "message": "1896", "line": 1, "column": 21, "nodeType": "1886", "messageId": "1887", "endLine": 1, "endColumn": 29}, {"ruleId": "1884", "severity": 1, "message": "2003", "line": 3, "column": 8, "nodeType": "1886", "messageId": "1887", "endLine": 3, "endColumn": 17}, {"ruleId": "1884", "severity": 1, "message": "1947", "line": 4, "column": 24, "nodeType": "1886", "messageId": "1887", "endLine": 4, "endColumn": 40}, {"ruleId": "1884", "severity": 1, "message": "2004", "line": 6, "column": 8, "nodeType": "1886", "messageId": "1887", "endLine": 6, "endColumn": 14}, {"ruleId": "1884", "severity": 1, "message": "2005", "line": 8, "column": 8, "nodeType": "1886", "messageId": "1887", "endLine": 8, "endColumn": 19}, {"ruleId": "1884", "severity": 1, "message": "2006", "line": 9, "column": 8, "nodeType": "1886", "messageId": "1887", "endLine": 9, "endColumn": 23}, {"ruleId": "1890", "severity": 1, "message": "1894", "line": 19, "column": 10, "nodeType": "1892", "endLine": 19, "endColumn": 12, "suggestions": "2007"}, {"ruleId": "1884", "severity": 1, "message": "1896", "line": 1, "column": 21, "nodeType": "1886", "messageId": "1887", "endLine": 1, "endColumn": 29}, {"ruleId": "1884", "severity": 1, "message": "2003", "line": 3, "column": 8, "nodeType": "1886", "messageId": "1887", "endLine": 3, "endColumn": 17}, {"ruleId": "1884", "severity": 1, "message": "1947", "line": 4, "column": 24, "nodeType": "1886", "messageId": "1887", "endLine": 4, "endColumn": 40}, {"ruleId": "1884", "severity": 1, "message": "2008", "line": 5, "column": 8, "nodeType": "1886", "messageId": "1887", "endLine": 5, "endColumn": 25}, {"ruleId": "1884", "severity": 1, "message": "2004", "line": 6, "column": 8, "nodeType": "1886", "messageId": "1887", "endLine": 6, "endColumn": 14}, {"ruleId": "1884", "severity": 1, "message": "2009", "line": 7, "column": 8, "nodeType": "1886", "messageId": "1887", "endLine": 7, "endColumn": 29}, {"ruleId": "1890", "severity": 1, "message": "1894", "line": 19, "column": 10, "nodeType": "1892", "endLine": 19, "endColumn": 12, "suggestions": "2010"}, {"ruleId": "1884", "severity": 1, "message": "1896", "line": 1, "column": 21, "nodeType": "1886", "messageId": "1887", "endLine": 1, "endColumn": 29}, {"ruleId": "1884", "severity": 1, "message": "2003", "line": 3, "column": 8, "nodeType": "1886", "messageId": "1887", "endLine": 3, "endColumn": 17}, {"ruleId": "1884", "severity": 1, "message": "1947", "line": 4, "column": 24, "nodeType": "1886", "messageId": "1887", "endLine": 4, "endColumn": 40}, {"ruleId": "1884", "severity": 1, "message": "2004", "line": 6, "column": 8, "nodeType": "1886", "messageId": "1887", "endLine": 6, "endColumn": 14}, {"ruleId": "1890", "severity": 1, "message": "1894", "line": 17, "column": 10, "nodeType": "1892", "endLine": 17, "endColumn": 12, "suggestions": "2011"}, {"ruleId": "1890", "severity": 1, "message": "1921", "line": 31, "column": 8, "nodeType": "1892", "endLine": 31, "endColumn": 29, "suggestions": "2012"}, {"ruleId": "1884", "severity": 1, "message": "1908", "line": 3, "column": 10, "nodeType": "1886", "messageId": "1887", "endLine": 3, "endColumn": 24}, {"ruleId": "1890", "severity": 1, "message": "2013", "line": 60, "column": 8, "nodeType": "1892", "endLine": 60, "endColumn": 20, "suggestions": "2014"}, {"ruleId": "1925", "severity": 1, "message": "1926", "line": 95, "column": 17, "nodeType": "1927", "endLine": 99, "endColumn": 18}, {"ruleId": "1925", "severity": 1, "message": "1926", "line": 106, "column": 15, "nodeType": "1927", "endLine": 109, "endColumn": 16}, {"ruleId": "1890", "severity": 1, "message": "2015", "line": 195, "column": 5, "nodeType": "1892", "endLine": 195, "endColumn": 15, "suggestions": "2016"}, {"ruleId": "2017", "severity": 1, "message": "2018", "line": 205, "column": 5, "nodeType": "2019", "messageId": "2020", "endLine": 224, "endColumn": 6}, {"ruleId": "1890", "severity": 1, "message": "2021", "line": 305, "column": 6, "nodeType": "1892", "endLine": 305, "endColumn": 78, "suggestions": "2022"}, {"ruleId": "1890", "severity": 1, "message": "2023", "line": 311, "column": 6, "nodeType": "1892", "endLine": 311, "endColumn": 22, "suggestions": "2024"}, {"ruleId": "1890", "severity": 1, "message": "1921", "line": 317, "column": 6, "nodeType": "1892", "endLine": 317, "endColumn": 18, "suggestions": "2025"}, {"ruleId": "1890", "severity": 1, "message": "2026", "line": 326, "column": 6, "nodeType": "1892", "endLine": 326, "endColumn": 18, "suggestions": "2027"}, {"ruleId": "1884", "severity": 1, "message": "2028", "line": 599, "column": 39, "nodeType": "1886", "messageId": "1887", "endLine": 599, "endColumn": 51}, {"ruleId": "1884", "severity": 1, "message": "2029", "line": 601, "column": 39, "nodeType": "1886", "messageId": "1887", "endLine": 601, "endColumn": 46}, {"ruleId": "1884", "severity": 1, "message": "2030", "line": 602, "column": 39, "nodeType": "1886", "messageId": "1887", "endLine": 602, "endColumn": 51}, {"ruleId": "1884", "severity": 1, "message": "2031", "line": 2, "column": 8, "nodeType": "1886", "messageId": "1887", "endLine": 2, "endColumn": 19}, {"ruleId": "1884", "severity": 1, "message": "2032", "line": 3, "column": 8, "nodeType": "1886", "messageId": "1887", "endLine": 3, "endColumn": 17}, {"ruleId": "1884", "severity": 1, "message": "2033", "line": 10, "column": 8, "nodeType": "1886", "messageId": "1887", "endLine": 10, "endColumn": 17}, {"ruleId": "1884", "severity": 1, "message": "2034", "line": 11, "column": 8, "nodeType": "1886", "messageId": "1887", "endLine": 11, "endColumn": 23}, {"ruleId": "1884", "severity": 1, "message": "2035", "line": 12, "column": 8, "nodeType": "1886", "messageId": "1887", "endLine": 12, "endColumn": 22}, {"ruleId": "1884", "severity": 1, "message": "2036", "line": 13, "column": 8, "nodeType": "1886", "messageId": "1887", "endLine": 13, "endColumn": 20}, {"ruleId": "1884", "severity": 1, "message": "2037", "line": 14, "column": 8, "nodeType": "1886", "messageId": "1887", "endLine": 14, "endColumn": 17}, {"ruleId": "1884", "severity": 1, "message": "2038", "line": 15, "column": 8, "nodeType": "1886", "messageId": "1887", "endLine": 15, "endColumn": 16}, {"ruleId": "1884", "severity": 1, "message": "2039", "line": 16, "column": 8, "nodeType": "1886", "messageId": "1887", "endLine": 16, "endColumn": 23}, {"ruleId": "1884", "severity": 1, "message": "2040", "line": 19, "column": 8, "nodeType": "1886", "messageId": "1887", "endLine": 19, "endColumn": 21}, {"ruleId": "1884", "severity": 1, "message": "2041", "line": 113, "column": 9, "nodeType": "1886", "messageId": "1887", "endLine": 113, "endColumn": 30}, {"ruleId": "1884", "severity": 1, "message": "1897", "line": 2, "column": 38, "nodeType": "1886", "messageId": "1887", "endLine": 2, "endColumn": 44}, {"ruleId": "1884", "severity": 1, "message": "1968", "line": 17, "column": 8, "nodeType": "1886", "messageId": "1887", "endLine": 17, "endColumn": 13}, {"ruleId": "2042", "severity": 1, "message": "2043", "line": 43, "column": 46, "nodeType": "2044", "messageId": "2045", "endLine": 43, "endColumn": 47, "suggestions": "2046"}, {"ruleId": "2047", "severity": 1, "message": "2048", "line": 52, "column": 28, "nodeType": "2049", "messageId": "2050", "endLine": 52, "endColumn": 30}, {"ruleId": "2047", "severity": 1, "message": "2048", "line": 52, "column": 54, "nodeType": "2049", "messageId": "2050", "endLine": 52, "endColumn": 56}, {"ruleId": "2047", "severity": 1, "message": "2048", "line": 52, "column": 89, "nodeType": "2049", "messageId": "2050", "endLine": 52, "endColumn": 91}, {"ruleId": "2047", "severity": 1, "message": "2048", "line": 62, "column": 28, "nodeType": "2049", "messageId": "2050", "endLine": 62, "endColumn": 30}, {"ruleId": "2047", "severity": 1, "message": "2048", "line": 62, "column": 54, "nodeType": "2049", "messageId": "2050", "endLine": 62, "endColumn": 56}, {"ruleId": "2047", "severity": 1, "message": "2048", "line": 62, "column": 89, "nodeType": "2049", "messageId": "2050", "endLine": 62, "endColumn": 91}, {"ruleId": "1884", "severity": 1, "message": "2051", "line": 437, "column": 10, "nodeType": "1886", "messageId": "1887", "endLine": 437, "endColumn": 22}, {"ruleId": "1884", "severity": 1, "message": "2052", "line": 443, "column": 10, "nodeType": "1886", "messageId": "1887", "endLine": 443, "endColumn": 22}, {"ruleId": "1884", "severity": 1, "message": "2053", "line": 483, "column": 12, "nodeType": "1886", "messageId": "1887", "endLine": 483, "endColumn": 29}, {"ruleId": "1890", "severity": 1, "message": "2054", "line": 536, "column": 6, "nodeType": "1892", "endLine": 542, "endColumn": 4, "suggestions": "2055"}, {"ruleId": "1884", "severity": 1, "message": "2056", "line": 6, "column": 8, "nodeType": "1886", "messageId": "1887", "endLine": 6, "endColumn": 17}, {"ruleId": "1884", "severity": 1, "message": "2057", "line": 7, "column": 8, "nodeType": "1886", "messageId": "1887", "endLine": 7, "endColumn": 21}, {"ruleId": "1884", "severity": 1, "message": "2058", "line": 9, "column": 8, "nodeType": "1886", "messageId": "1887", "endLine": 9, "endColumn": 20}, {"ruleId": "1884", "severity": 1, "message": "2059", "line": 68, "column": 9, "nodeType": "1886", "messageId": "1887", "endLine": 68, "endColumn": 26}, {"ruleId": "1884", "severity": 1, "message": "2060", "line": 72, "column": 9, "nodeType": "1886", "messageId": "1887", "endLine": 72, "endColumn": 21}, {"ruleId": "1884", "severity": 1, "message": "1888", "line": 4, "column": 10, "nodeType": "1886", "messageId": "1887", "endLine": 4, "endColumn": 15}, {"ruleId": "1884", "severity": 1, "message": "1908", "line": 4, "column": 17, "nodeType": "1886", "messageId": "1887", "endLine": 4, "endColumn": 31}, {"ruleId": "1884", "severity": 1, "message": "1915", "line": 18, "column": 8, "nodeType": "1886", "messageId": "1887", "endLine": 18, "endColumn": 21}, {"ruleId": "1884", "severity": 1, "message": "2061", "line": 22, "column": 8, "nodeType": "1886", "messageId": "1887", "endLine": 22, "endColumn": 22}, {"ruleId": "1890", "severity": 1, "message": "2062", "line": 50, "column": 6, "nodeType": "1892", "endLine": 50, "endColumn": 18, "suggestions": "2063"}, {"ruleId": "1925", "severity": 1, "message": "1926", "line": 88, "column": 15, "nodeType": "1927", "endLine": 92, "endColumn": 16}, {"ruleId": "1925", "severity": 1, "message": "1926", "line": 99, "column": 13, "nodeType": "1927", "endLine": 99, "endColumn": 85}, {"ruleId": "1884", "severity": 1, "message": "1888", "line": 4, "column": 10, "nodeType": "1886", "messageId": "1887", "endLine": 4, "endColumn": 15}, {"ruleId": "1884", "severity": 1, "message": "1908", "line": 4, "column": 17, "nodeType": "1886", "messageId": "1887", "endLine": 4, "endColumn": 31}, {"ruleId": "1884", "severity": 1, "message": "2064", "line": 17, "column": 8, "nodeType": "1886", "messageId": "1887", "endLine": 17, "endColumn": 22}, {"ruleId": "1890", "severity": 1, "message": "2062", "line": 51, "column": 6, "nodeType": "1892", "endLine": 51, "endColumn": 18, "suggestions": "2065"}, {"ruleId": "1925", "severity": 1, "message": "1926", "line": 91, "column": 15, "nodeType": "1927", "endLine": 95, "endColumn": 16}, {"ruleId": "1925", "severity": 1, "message": "1926", "line": 102, "column": 13, "nodeType": "1927", "endLine": 105, "endColumn": 14}, {"ruleId": "1884", "severity": 1, "message": "1905", "line": 2, "column": 10, "nodeType": "1886", "messageId": "1887", "endLine": 2, "endColumn": 19}, {"ruleId": "1884", "severity": 1, "message": "1967", "line": 3, "column": 23, "nodeType": "1886", "messageId": "1887", "endLine": 3, "endColumn": 34}, {"ruleId": "1884", "severity": 1, "message": "2066", "line": 36, "column": 21, "nodeType": "1886", "messageId": "1887", "endLine": 36, "endColumn": 31}, {"ruleId": "1884", "severity": 1, "message": "1905", "line": 2, "column": 10, "nodeType": "1886", "messageId": "1887", "endLine": 2, "endColumn": 19}, {"ruleId": "1884", "severity": 1, "message": "1910", "line": 3, "column": 10, "nodeType": "1886", "messageId": "1887", "endLine": 3, "endColumn": 21}, {"ruleId": "1884", "severity": 1, "message": "1967", "line": 3, "column": 23, "nodeType": "1886", "messageId": "1887", "endLine": 3, "endColumn": 34}, {"ruleId": "1884", "severity": 1, "message": "1947", "line": 5, "column": 10, "nodeType": "1886", "messageId": "1887", "endLine": 5, "endColumn": 26}, {"ruleId": "1884", "severity": 1, "message": "2067", "line": 32, "column": 19, "nodeType": "1886", "messageId": "1887", "endLine": 32, "endColumn": 27}, {"ruleId": "1884", "severity": 1, "message": "1947", "line": 3, "column": 10, "nodeType": "1886", "messageId": "1887", "endLine": 3, "endColumn": 26}, {"ruleId": "1884", "severity": 1, "message": "2068", "line": 4, "column": 10, "nodeType": "1886", "messageId": "1887", "endLine": 4, "endColumn": 21}, {"ruleId": "1884", "severity": 1, "message": "2069", "line": 7, "column": 8, "nodeType": "1886", "messageId": "1887", "endLine": 7, "endColumn": 23}, {"ruleId": "1884", "severity": 1, "message": "1908", "line": 11, "column": 17, "nodeType": "1886", "messageId": "1887", "endLine": 11, "endColumn": 31}, {"ruleId": "1884", "severity": 1, "message": "2070", "line": 27, "column": 12, "nodeType": "1886", "messageId": "1887", "endLine": 27, "endColumn": 23}, {"ruleId": "1884", "severity": 1, "message": "2071", "line": 44, "column": 11, "nodeType": "1886", "messageId": "1887", "endLine": 44, "endColumn": 22}, {"ruleId": "1890", "severity": 1, "message": "2072", "line": 86, "column": 8, "nodeType": "1892", "endLine": 86, "endColumn": 14, "suggestions": "2073"}, {"ruleId": "2074", "severity": 1, "message": "2075", "line": 95, "column": 21, "nodeType": "2076", "messageId": "2077", "endLine": 95, "endColumn": 96}, {"ruleId": "1884", "severity": 1, "message": "2078", "line": 2, "column": 18, "nodeType": "1886", "messageId": "1887", "endLine": 2, "endColumn": 24}, {"ruleId": "1884", "severity": 1, "message": "1888", "line": 23, "column": 10, "nodeType": "1886", "messageId": "1887", "endLine": 23, "endColumn": 15}, {"ruleId": "1884", "severity": 1, "message": "2079", "line": 43, "column": 11, "nodeType": "1886", "messageId": "1887", "endLine": 43, "endColumn": 21}, {"ruleId": "1890", "severity": 1, "message": "2080", "line": 131, "column": 8, "nodeType": "1892", "endLine": 131, "endColumn": 10, "suggestions": "2081"}, {"ruleId": "1890", "severity": 1, "message": "2082", "line": 160, "column": 8, "nodeType": "1892", "endLine": 160, "endColumn": 10, "suggestions": "2083"}, {"ruleId": "1890", "severity": 1, "message": "2084", "line": 179, "column": 43, "nodeType": "1886", "endLine": 179, "endColumn": 50}, {"ruleId": "1890", "severity": 1, "message": "2085", "line": 16, "column": 6, "nodeType": "1892", "endLine": 16, "endColumn": 8, "suggestions": "2086"}, {"ruleId": "1884", "severity": 1, "message": "1905", "line": 2, "column": 10, "nodeType": "1886", "messageId": "1887", "endLine": 2, "endColumn": 19}, {"ruleId": "1884", "severity": 1, "message": "1897", "line": 2, "column": 21, "nodeType": "1886", "messageId": "1887", "endLine": 2, "endColumn": 27}, {"ruleId": "1884", "severity": 1, "message": "1954", "line": 13, "column": 27, "nodeType": "1886", "messageId": "1887", "endLine": 13, "endColumn": 39}, {"ruleId": "1884", "severity": 1, "message": "2087", "line": 23, "column": 23, "nodeType": "1886", "messageId": "1887", "endLine": 23, "endColumn": 31}, {"ruleId": "1884", "severity": 1, "message": "2088", "line": 42, "column": 10, "nodeType": "1886", "messageId": "1887", "endLine": 42, "endColumn": 16}, {"ruleId": "1884", "severity": 1, "message": "2089", "line": 43, "column": 10, "nodeType": "1886", "messageId": "1887", "endLine": 43, "endColumn": 21}, {"ruleId": "1884", "severity": 1, "message": "2090", "line": 44, "column": 10, "nodeType": "1886", "messageId": "1887", "endLine": 44, "endColumn": 17}, {"ruleId": "1884", "severity": 1, "message": "2091", "line": 59, "column": 11, "nodeType": "1886", "messageId": "1887", "endLine": 59, "endColumn": 23}, {"ruleId": "1890", "severity": 1, "message": "2015", "line": 89, "column": 5, "nodeType": "1892", "endLine": 89, "endColumn": 15, "suggestions": "2092"}, {"ruleId": "2093", "severity": 1, "message": "2094", "line": 115, "column": 64, "nodeType": "2095", "messageId": "2096", "endLine": 115, "endColumn": 66}, {"ruleId": "1890", "severity": 1, "message": "2097", "line": 151, "column": 6, "nodeType": "1892", "endLine": 151, "endColumn": 61, "suggestions": "2098"}, {"ruleId": "1890", "severity": 1, "message": "2099", "line": 192, "column": 6, "nodeType": "1892", "endLine": 192, "endColumn": 25, "suggestions": "2100"}, {"ruleId": "1884", "severity": 1, "message": "2029", "line": 216, "column": 44, "nodeType": "1886", "messageId": "1887", "endLine": 216, "endColumn": 51}, {"ruleId": "1884", "severity": 1, "message": "2030", "line": 216, "column": 53, "nodeType": "1886", "messageId": "1887", "endLine": 216, "endColumn": 65}, {"ruleId": "1884", "severity": 1, "message": "2028", "line": 235, "column": 17, "nodeType": "1886", "messageId": "1887", "endLine": 235, "endColumn": 29}, {"ruleId": "1884", "severity": 1, "message": "2029", "line": 235, "column": 44, "nodeType": "1886", "messageId": "1887", "endLine": 235, "endColumn": 51}, {"ruleId": "1884", "severity": 1, "message": "2030", "line": 235, "column": 53, "nodeType": "1886", "messageId": "1887", "endLine": 235, "endColumn": 65}, {"ruleId": "1884", "severity": 1, "message": "2101", "line": 11, "column": 8, "nodeType": "1886", "messageId": "1887", "endLine": 11, "endColumn": 14}, {"ruleId": "1884", "severity": 1, "message": "2102", "line": 40, "column": 12, "nodeType": "1886", "messageId": "1887", "endLine": 40, "endColumn": 25}, {"ruleId": "1890", "severity": 1, "message": "1894", "line": 74, "column": 8, "nodeType": "1892", "endLine": 74, "endColumn": 10, "suggestions": "2103"}, {"ruleId": "1890", "severity": 1, "message": "2104", "line": 151, "column": 8, "nodeType": "1892", "endLine": 151, "endColumn": 43, "suggestions": "2105"}, {"ruleId": "1884", "severity": 1, "message": "1908", "line": 3, "column": 10, "nodeType": "1886", "messageId": "1887", "endLine": 3, "endColumn": 24}, {"ruleId": "1884", "severity": 1, "message": "1961", "line": 9, "column": 8, "nodeType": "1886", "messageId": "1887", "endLine": 9, "endColumn": 17}, {"ruleId": "1884", "severity": 1, "message": "1954", "line": 11, "column": 27, "nodeType": "1886", "messageId": "1887", "endLine": 11, "endColumn": 39}, {"ruleId": "1884", "severity": 1, "message": "2058", "line": 15, "column": 8, "nodeType": "1886", "messageId": "1887", "endLine": 15, "endColumn": 20}, {"ruleId": "1884", "severity": 1, "message": "2106", "line": 22, "column": 10, "nodeType": "1886", "messageId": "1887", "endLine": 22, "endColumn": 20}, {"ruleId": "1884", "severity": 1, "message": "2078", "line": 23, "column": 10, "nodeType": "1886", "messageId": "1887", "endLine": 23, "endColumn": 16}, {"ruleId": "1884", "severity": 1, "message": "2107", "line": 31, "column": 10, "nodeType": "1886", "messageId": "1887", "endLine": 31, "endColumn": 21}, {"ruleId": "1890", "severity": 1, "message": "2108", "line": 68, "column": 8, "nodeType": "1892", "endLine": 68, "endColumn": 20, "suggestions": "2109"}, {"ruleId": "1925", "severity": 1, "message": "1926", "line": 103, "column": 17, "nodeType": "1927", "endLine": 107, "endColumn": 18}, {"ruleId": "1925", "severity": 1, "message": "1926", "line": 114, "column": 15, "nodeType": "1927", "endLine": 117, "endColumn": 16}, {"ruleId": "1884", "severity": 1, "message": "2110", "line": 137, "column": 9, "nodeType": "1886", "messageId": "1887", "endLine": 137, "endColumn": 27}, {"ruleId": "1884", "severity": 1, "message": "2111", "line": 140, "column": 10, "nodeType": "1886", "messageId": "1887", "endLine": 140, "endColumn": 26}, {"ruleId": "1884", "severity": 1, "message": "2112", "line": 142, "column": 10, "nodeType": "1886", "messageId": "1887", "endLine": 142, "endColumn": 26}, {"ruleId": "1884", "severity": 1, "message": "2113", "line": 142, "column": 28, "nodeType": "1886", "messageId": "1887", "endLine": 142, "endColumn": 47}, {"ruleId": "1884", "severity": 1, "message": "2114", "line": 151, "column": 10, "nodeType": "1886", "messageId": "1887", "endLine": 151, "endColumn": 21}, {"ruleId": "1884", "severity": 1, "message": "2115", "line": 151, "column": 23, "nodeType": "1886", "messageId": "1887", "endLine": 151, "endColumn": 37}, {"ruleId": "1884", "severity": 1, "message": "2116", "line": 152, "column": 10, "nodeType": "1886", "messageId": "1887", "endLine": 152, "endColumn": 29}, {"ruleId": "1884", "severity": 1, "message": "2117", "line": 152, "column": 31, "nodeType": "1886", "messageId": "1887", "endLine": 152, "endColumn": 47}, {"ruleId": "1884", "severity": 1, "message": "2118", "line": 171, "column": 11, "nodeType": "1886", "messageId": "1887", "endLine": 171, "endColumn": 20}, {"ruleId": "1884", "severity": 1, "message": "1962", "line": 174, "column": 9, "nodeType": "1886", "messageId": "1887", "endLine": 174, "endColumn": 17}, {"ruleId": "1884", "severity": 1, "message": "2028", "line": 210, "column": 7, "nodeType": "1886", "messageId": "1887", "endLine": 210, "endColumn": 19}, {"ruleId": "1884", "severity": 1, "message": "2029", "line": 213, "column": 7, "nodeType": "1886", "messageId": "1887", "endLine": 213, "endColumn": 14}, {"ruleId": "1884", "severity": 1, "message": "2030", "line": 214, "column": 7, "nodeType": "1886", "messageId": "1887", "endLine": 214, "endColumn": 19}, {"ruleId": "1890", "severity": 1, "message": "2119", "line": 273, "column": 6, "nodeType": "1892", "endLine": 273, "endColumn": 8, "suggestions": "2120"}, {"ruleId": "1890", "severity": 1, "message": "1894", "line": 321, "column": 6, "nodeType": "1892", "endLine": 321, "endColumn": 34, "suggestions": "2121"}, {"ruleId": "1890", "severity": 1, "message": "2013", "line": 56, "column": 8, "nodeType": "1892", "endLine": 56, "endColumn": 20, "suggestions": "2122"}, {"ruleId": "1884", "severity": 1, "message": "2123", "line": 133, "column": 11, "nodeType": "1886", "messageId": "1887", "endLine": 133, "endColumn": 19}, {"ruleId": "2017", "severity": 1, "message": "2018", "line": 166, "column": 9, "nodeType": "2019", "messageId": "2020", "endLine": 185, "endColumn": 10}, {"ruleId": "1890", "severity": 1, "message": "2021", "line": 260, "column": 8, "nodeType": "1892", "endLine": 260, "endColumn": 47, "suggestions": "2124"}, {"ruleId": "1890", "severity": 1, "message": "2125", "line": 266, "column": 8, "nodeType": "1892", "endLine": 266, "endColumn": 24, "suggestions": "2126"}, {"ruleId": "1890", "severity": 1, "message": "1921", "line": 272, "column": 8, "nodeType": "1892", "endLine": 272, "endColumn": 20, "suggestions": "2127"}, {"ruleId": "1890", "severity": 1, "message": "2026", "line": 281, "column": 8, "nodeType": "1892", "endLine": 281, "endColumn": 20, "suggestions": "2128"}, {"ruleId": "1884", "severity": 1, "message": "2129", "line": 25, "column": 12, "nodeType": "1886", "messageId": "1887", "endLine": 25, "endColumn": 21}, {"ruleId": "2047", "severity": 1, "message": "2130", "line": 55, "column": 18, "nodeType": "2049", "messageId": "2050", "endLine": 55, "endColumn": 20}, {"ruleId": "2047", "severity": 1, "message": "2130", "line": 65, "column": 37, "nodeType": "2049", "messageId": "2050", "endLine": 65, "endColumn": 39}, {"ruleId": "1890", "severity": 1, "message": "2131", "line": 24, "column": 8, "nodeType": "1892", "endLine": 24, "endColumn": 10, "suggestions": "2132"}, {"ruleId": "1884", "severity": 1, "message": "1888", "line": 3, "column": 10, "nodeType": "1886", "messageId": "1887", "endLine": 3, "endColumn": 15}, {"ruleId": "1884", "severity": 1, "message": "2133", "line": 39, "column": 11, "nodeType": "1886", "messageId": "1887", "endLine": 39, "endColumn": 19}, {"ruleId": "1884", "severity": 1, "message": "2134", "line": 6, "column": 10, "nodeType": "1886", "messageId": "1887", "endLine": 6, "endColumn": 21}, {"ruleId": "1884", "severity": 1, "message": "2135", "line": 8, "column": 8, "nodeType": "1886", "messageId": "1887", "endLine": 8, "endColumn": 22}, {"ruleId": "1884", "severity": 1, "message": "2136", "line": 13, "column": 10, "nodeType": "1886", "messageId": "1887", "endLine": 13, "endColumn": 21}, {"ruleId": "1884", "severity": 1, "message": "2137", "line": 13, "column": 23, "nodeType": "1886", "messageId": "1887", "endLine": 13, "endColumn": 37}, {"ruleId": "1884", "severity": 1, "message": "2138", "line": 16, "column": 9, "nodeType": "1886", "messageId": "1887", "endLine": 16, "endColumn": 22}, {"ruleId": "1884", "severity": 1, "message": "2139", "line": 50, "column": 12, "nodeType": "1886", "messageId": "1887", "endLine": 50, "endColumn": 27}, {"ruleId": "1884", "severity": 1, "message": "1951", "line": 55, "column": 9, "nodeType": "1886", "messageId": "1887", "endLine": 55, "endColumn": 13}, {"ruleId": "1890", "severity": 1, "message": "2080", "line": 83, "column": 6, "nodeType": "1892", "endLine": 83, "endColumn": 8, "suggestions": "2140"}, {"ruleId": "1884", "severity": 1, "message": "2141", "line": 15, "column": 9, "nodeType": "1886", "messageId": "1887", "endLine": 15, "endColumn": 21}, {"ruleId": "2142", "severity": 1, "message": "2143", "line": 3, "column": 22, "nodeType": "2144", "messageId": "2050", "endLine": 3, "endColumn": 24}, {"ruleId": "2142", "severity": 1, "message": "2143", "line": 5, "column": 20, "nodeType": "2144", "messageId": "2050", "endLine": 5, "endColumn": 22}, {"ruleId": "1884", "severity": 1, "message": "2145", "line": 11, "column": 7, "nodeType": "1886", "messageId": "1887", "endLine": 11, "endColumn": 20}, {"ruleId": "1925", "severity": 1, "message": "1926", "line": 65, "column": 29, "nodeType": "1927", "endLine": 65, "endColumn": 32}, {"ruleId": "1925", "severity": 1, "message": "1926", "line": 66, "column": 29, "nodeType": "1927", "endLine": 66, "endColumn": 32}, {"ruleId": "1884", "severity": 1, "message": "1900", "line": 5, "column": 3, "nodeType": "1886", "messageId": "1887", "endLine": 5, "endColumn": 8}, {"ruleId": "1884", "severity": 1, "message": "1928", "line": 11, "column": 8, "nodeType": "1886", "messageId": "1887", "endLine": 11, "endColumn": 16}, {"ruleId": "1884", "severity": 1, "message": "1888", "line": 10, "column": 10, "nodeType": "1886", "messageId": "1887", "endLine": 10, "endColumn": 15}, {"ruleId": "1884", "severity": 1, "message": "1908", "line": 10, "column": 17, "nodeType": "1886", "messageId": "1887", "endLine": 10, "endColumn": 31}, {"ruleId": "1884", "severity": 1, "message": "1915", "line": 12, "column": 8, "nodeType": "1886", "messageId": "1887", "endLine": 12, "endColumn": 21}, {"ruleId": "1884", "severity": 1, "message": "2146", "line": 32, "column": 10, "nodeType": "1886", "messageId": "1887", "endLine": 32, "endColumn": 27}, {"ruleId": "1884", "severity": 1, "message": "2147", "line": 32, "column": 29, "nodeType": "1886", "messageId": "1887", "endLine": 32, "endColumn": 49}, {"ruleId": "1890", "severity": 1, "message": "2148", "line": 133, "column": 6, "nodeType": "1892", "endLine": 133, "endColumn": 33, "suggestions": "2149"}, {"ruleId": "2150", "severity": 1, "message": "2151", "line": 218, "column": 9, "nodeType": "2152", "messageId": "2050", "endLine": 218, "endColumn": 13}, {"ruleId": "2150", "severity": 1, "message": "2153", "line": 225, "column": 9, "nodeType": "2152", "messageId": "2050", "endLine": 225, "endColumn": 25}, {"ruleId": "1890", "severity": 1, "message": "2154", "line": 230, "column": 6, "nodeType": "1892", "endLine": 230, "endColumn": 18, "suggestions": "2155"}, {"ruleId": "1890", "severity": 1, "message": "2156", "line": 242, "column": 6, "nodeType": "1892", "endLine": 242, "endColumn": 34, "suggestions": "2157"}, {"ruleId": "1890", "severity": 1, "message": "2158", "line": 258, "column": 6, "nodeType": "1892", "endLine": 258, "endColumn": 8, "suggestions": "2159"}, {"ruleId": "1884", "severity": 1, "message": "2160", "line": 4, "column": 8, "nodeType": "1886", "messageId": "1887", "endLine": 4, "endColumn": 16}, {"ruleId": "1890", "severity": 1, "message": "2080", "line": 40, "column": 8, "nodeType": "1892", "endLine": 40, "endColumn": 10, "suggestions": "2161"}, {"ruleId": "2074", "severity": 1, "message": "2075", "line": 49, "column": 21, "nodeType": "2076", "messageId": "2077", "endLine": 49, "endColumn": 97}, {"ruleId": "1890", "severity": 1, "message": "2080", "line": 44, "column": 6, "nodeType": "1892", "endLine": 44, "endColumn": 8, "suggestions": "2162"}, {"ruleId": "2074", "severity": 1, "message": "2075", "line": 53, "column": 11, "nodeType": "2076", "messageId": "2077", "endLine": 53, "endColumn": 86}, {"ruleId": "1884", "severity": 1, "message": "1888", "line": 4, "column": 10, "nodeType": "1886", "messageId": "1887", "endLine": 4, "endColumn": 15}, {"ruleId": "1884", "severity": 1, "message": "1908", "line": 4, "column": 17, "nodeType": "1886", "messageId": "1887", "endLine": 4, "endColumn": 31}, {"ruleId": "1884", "severity": 1, "message": "2064", "line": 6, "column": 8, "nodeType": "1886", "messageId": "1887", "endLine": 6, "endColumn": 22}, {"ruleId": "1884", "severity": 1, "message": "2163", "line": 7, "column": 8, "nodeType": "1886", "messageId": "1887", "endLine": 7, "endColumn": 21}, {"ruleId": "1884", "severity": 1, "message": "2164", "line": 8, "column": 8, "nodeType": "1886", "messageId": "1887", "endLine": 8, "endColumn": 13}, {"ruleId": "1884", "severity": 1, "message": "1909", "line": 9, "column": 10, "nodeType": "1886", "messageId": "1887", "endLine": 9, "endColumn": 11}, {"ruleId": "1884", "severity": 1, "message": "1950", "line": 16, "column": 8, "nodeType": "1886", "messageId": "1887", "endLine": 16, "endColumn": 19}, {"ruleId": "1884", "severity": 1, "message": "2165", "line": 24, "column": 10, "nodeType": "1886", "messageId": "1887", "endLine": 24, "endColumn": 27}, {"ruleId": "1884", "severity": 1, "message": "2166", "line": 26, "column": 10, "nodeType": "1886", "messageId": "1887", "endLine": 26, "endColumn": 21}, {"ruleId": "1890", "severity": 1, "message": "2167", "line": 135, "column": 6, "nodeType": "1892", "endLine": 135, "endColumn": 12, "suggestions": "2168"}, {"ruleId": "1890", "severity": 1, "message": "2169", "line": 173, "column": 6, "nodeType": "1892", "endLine": 173, "endColumn": 15, "suggestions": "2170"}, {"ruleId": "1890", "severity": 1, "message": "2171", "line": 41, "column": 6, "nodeType": "1892", "endLine": 41, "endColumn": 8, "suggestions": "2172"}, {"ruleId": "1890", "severity": 1, "message": "2173", "line": 97, "column": 8, "nodeType": "1892", "endLine": 97, "endColumn": 17, "suggestions": "2174"}, {"ruleId": "1884", "severity": 1, "message": "2175", "line": 168, "column": 55, "nodeType": "1886", "messageId": "1887", "endLine": 168, "endColumn": 62}, {"ruleId": "1884", "severity": 1, "message": "2175", "line": 187, "column": 55, "nodeType": "1886", "messageId": "1887", "endLine": 187, "endColumn": 62}, {"ruleId": "1884", "severity": 1, "message": "1896", "line": 4, "column": 21, "nodeType": "1886", "messageId": "1887", "endLine": 4, "endColumn": 29}, {"ruleId": "1890", "severity": 1, "message": "1894", "line": 51, "column": 8, "nodeType": "1892", "endLine": 51, "endColumn": 10, "suggestions": "2176"}, {"ruleId": "1884", "severity": 1, "message": "2177", "line": 2, "column": 36, "nodeType": "1886", "messageId": "1887", "endLine": 2, "endColumn": 41}, {"ruleId": "1884", "severity": 1, "message": "2178", "line": 2, "column": 43, "nodeType": "1886", "messageId": "1887", "endLine": 2, "endColumn": 51}, {"ruleId": "1884", "severity": 1, "message": "2179", "line": 4, "column": 8, "nodeType": "1886", "messageId": "1887", "endLine": 4, "endColumn": 12}, {"ruleId": "1884", "severity": 1, "message": "1905", "line": 6, "column": 10, "nodeType": "1886", "messageId": "1887", "endLine": 6, "endColumn": 19}, {"ruleId": "1884", "severity": 1, "message": "1897", "line": 6, "column": 21, "nodeType": "1886", "messageId": "1887", "endLine": 6, "endColumn": 27}, {"ruleId": "1884", "severity": 1, "message": "1896", "line": 6, "column": 29, "nodeType": "1886", "messageId": "1887", "endLine": 6, "endColumn": 37}, {"ruleId": "2093", "severity": 1, "message": "2094", "line": 11, "column": 46, "nodeType": "2095", "messageId": "2096", "endLine": 11, "endColumn": 48}, {"ruleId": "2047", "severity": 1, "message": "2048", "line": 54, "column": 113, "nodeType": "2049", "messageId": "2050", "endLine": 54, "endColumn": 115}, {"ruleId": "1890", "severity": 1, "message": "2180", "line": 26, "column": 8, "nodeType": "1892", "endLine": 26, "endColumn": 10, "suggestions": "2181"}, {"ruleId": "1884", "severity": 1, "message": "2182", "line": 18, "column": 8, "nodeType": "1886", "messageId": "1887", "endLine": 18, "endColumn": 19}, {"ruleId": "1884", "severity": 1, "message": "2183", "line": 21, "column": 10, "nodeType": "1886", "messageId": "1887", "endLine": 21, "endColumn": 27}, {"ruleId": "1884", "severity": 1, "message": "2184", "line": 48, "column": 11, "nodeType": "1886", "messageId": "1887", "endLine": 48, "endColumn": 19}, {"ruleId": "1884", "severity": 1, "message": "2185", "line": 49, "column": 11, "nodeType": "1886", "messageId": "1887", "endLine": 49, "endColumn": 18}, {"ruleId": "1884", "severity": 1, "message": "1930", "line": 50, "column": 11, "nodeType": "1886", "messageId": "1887", "endLine": 50, "endColumn": 19}, {"ruleId": "1890", "severity": 1, "message": "2084", "line": 64, "column": 59, "nodeType": "1886", "endLine": 64, "endColumn": 66}, {"ruleId": "1890", "severity": 1, "message": "2186", "line": 210, "column": 6, "nodeType": "1892", "endLine": 210, "endColumn": 22, "suggestions": "2187"}, {"ruleId": "1890", "severity": 1, "message": "2082", "line": 229, "column": 6, "nodeType": "1892", "endLine": 229, "endColumn": 24, "suggestions": "2188"}, {"ruleId": "2189", "severity": 1, "message": "2190", "line": 62, "column": 9, "nodeType": "2191", "messageId": "2050", "endLine": 65, "endColumn": 19}, {"ruleId": "1890", "severity": 1, "message": "1894", "line": 163, "column": 8, "nodeType": "1892", "endLine": 163, "endColumn": 10, "suggestions": "2192"}, {"ruleId": "1884", "severity": 1, "message": "2193", "line": 21, "column": 10, "nodeType": "1886", "messageId": "1887", "endLine": 21, "endColumn": 15}, {"ruleId": "1884", "severity": 1, "message": "2194", "line": 24, "column": 9, "nodeType": "1886", "messageId": "1887", "endLine": 24, "endColumn": 19}, {"ruleId": "1884", "severity": 1, "message": "1888", "line": 4, "column": 10, "nodeType": "1886", "messageId": "1887", "endLine": 4, "endColumn": 15}, {"ruleId": "1890", "severity": 1, "message": "2195", "line": 367, "column": 8, "nodeType": "1892", "endLine": 367, "endColumn": 30, "suggestions": "2196"}, {"ruleId": "1890", "severity": 1, "message": "2197", "line": 377, "column": 8, "nodeType": "1892", "endLine": 377, "endColumn": 37, "suggestions": "2198"}, {"ruleId": "1884", "severity": 1, "message": "1897", "line": 1, "column": 21, "nodeType": "1886", "messageId": "1887", "endLine": 1, "endColumn": 27}, {"ruleId": "1884", "severity": 1, "message": "1909", "line": 2, "column": 18, "nodeType": "1886", "messageId": "1887", "endLine": 2, "endColumn": 19}, {"ruleId": "1884", "severity": 1, "message": "2199", "line": 4, "column": 10, "nodeType": "1886", "messageId": "1887", "endLine": 4, "endColumn": 22}, {"ruleId": "1884", "severity": 1, "message": "2200", "line": 4, "column": 52, "nodeType": "1886", "messageId": "1887", "endLine": 4, "endColumn": 73}, {"ruleId": "1884", "severity": 1, "message": "2201", "line": 13, "column": 10, "nodeType": "1886", "messageId": "1887", "endLine": 13, "endColumn": 17}, {"ruleId": "1884", "severity": 1, "message": "2202", "line": 28, "column": 8, "nodeType": "1886", "messageId": "1887", "endLine": 28, "endColumn": 27}, {"ruleId": "1884", "severity": 1, "message": "2203", "line": 74, "column": 11, "nodeType": "1886", "messageId": "1887", "endLine": 74, "endColumn": 24}, {"ruleId": "1890", "severity": 1, "message": "2204", "line": 86, "column": 8, "nodeType": "1892", "endLine": 86, "endColumn": 18, "suggestions": "2205"}, {"ruleId": "1884", "severity": 1, "message": "2206", "line": 119, "column": 11, "nodeType": "1886", "messageId": "1887", "endLine": 119, "endColumn": 27}, {"ruleId": "2207", "severity": 1, "message": "2208", "line": 319, "column": 86, "nodeType": "2209", "messageId": "2210", "endLine": 319, "endColumn": 87}, {"ruleId": "1884", "severity": 1, "message": "1905", "line": 1, "column": 10, "nodeType": "1886", "messageId": "1887", "endLine": 1, "endColumn": 19}, {"ruleId": "1884", "severity": 1, "message": "1897", "line": 1, "column": 21, "nodeType": "1886", "messageId": "1887", "endLine": 1, "endColumn": 27}, {"ruleId": "1884", "severity": 1, "message": "1896", "line": 1, "column": 29, "nodeType": "1886", "messageId": "1887", "endLine": 1, "endColumn": 37}, {"ruleId": "1884", "severity": 1, "message": "1910", "line": 2, "column": 10, "nodeType": "1886", "messageId": "1887", "endLine": 2, "endColumn": 21}, {"ruleId": "1884", "severity": 1, "message": "2202", "line": 6, "column": 8, "nodeType": "1886", "messageId": "1887", "endLine": 6, "endColumn": 27}, {"ruleId": "1884", "severity": 1, "message": "2211", "line": 7, "column": 8, "nodeType": "1886", "messageId": "1887", "endLine": 7, "endColumn": 22}, {"ruleId": "1884", "severity": 1, "message": "2212", "line": 5, "column": 10, "nodeType": "1886", "messageId": "1887", "endLine": 5, "endColumn": 25}, {"ruleId": "1884", "severity": 1, "message": "2202", "line": 9, "column": 8, "nodeType": "1886", "messageId": "1887", "endLine": 9, "endColumn": 27}, {"ruleId": "1884", "severity": 1, "message": "1930", "line": 17, "column": 11, "nodeType": "1886", "messageId": "1887", "endLine": 17, "endColumn": 19}, {"ruleId": "1884", "severity": 1, "message": "2213", "line": 31, "column": 11, "nodeType": "1886", "messageId": "1887", "endLine": 31, "endColumn": 20}, {"ruleId": "2047", "severity": 1, "message": "2048", "line": 32, "column": 77, "nodeType": "2049", "messageId": "2050", "endLine": 32, "endColumn": 79}, {"ruleId": "1890", "severity": 1, "message": "2214", "line": 39, "column": 8, "nodeType": "1892", "endLine": 39, "endColumn": 35, "suggestions": "2215"}, {"ruleId": "1890", "severity": 1, "message": "2084", "line": 55, "column": 43, "nodeType": "1886", "endLine": 55, "endColumn": 50}, {"ruleId": "2047", "severity": 1, "message": "2048", "line": 136, "column": 53, "nodeType": "2049", "messageId": "2050", "endLine": 136, "endColumn": 55}, {"ruleId": "2216", "severity": 1, "message": "2217", "line": 179, "column": 33, "nodeType": "1927", "endLine": 185, "endColumn": 35}, {"ruleId": "1884", "severity": 1, "message": "1910", "line": 6, "column": 10, "nodeType": "1886", "messageId": "1887", "endLine": 6, "endColumn": 21}, {"ruleId": "1884", "severity": 1, "message": "2202", "line": 23, "column": 8, "nodeType": "1886", "messageId": "1887", "endLine": 23, "endColumn": 27}, {"ruleId": "1884", "severity": 1, "message": "2211", "line": 24, "column": 8, "nodeType": "1886", "messageId": "1887", "endLine": 24, "endColumn": 22}, {"ruleId": "1884", "severity": 1, "message": "2218", "line": 32, "column": 23, "nodeType": "1886", "messageId": "1887", "endLine": 32, "endColumn": 35}, {"ruleId": "1884", "severity": 1, "message": "2219", "line": 34, "column": 12, "nodeType": "1886", "messageId": "1887", "endLine": 34, "endColumn": 29}, {"ruleId": "1884", "severity": 1, "message": "2220", "line": 36, "column": 12, "nodeType": "1886", "messageId": "1887", "endLine": 36, "endColumn": 35}, {"ruleId": "1890", "severity": 1, "message": "2084", "line": 75, "column": 43, "nodeType": "1886", "endLine": 75, "endColumn": 50}, {"ruleId": "1890", "severity": 1, "message": "2204", "line": 89, "column": 8, "nodeType": "1892", "endLine": 89, "endColumn": 18, "suggestions": "2221"}, {"ruleId": "1884", "severity": 1, "message": "2222", "line": 10, "column": 8, "nodeType": "1886", "messageId": "1887", "endLine": 10, "endColumn": 13}, {"ruleId": "1884", "severity": 1, "message": "2223", "line": 23, "column": 12, "nodeType": "1886", "messageId": "1887", "endLine": 23, "endColumn": 19}, {"ruleId": "1884", "severity": 1, "message": "2224", "line": 24, "column": 12, "nodeType": "1886", "messageId": "1887", "endLine": 24, "endColumn": 23}, {"ruleId": "1884", "severity": 1, "message": "2225", "line": 28, "column": 12, "nodeType": "1886", "messageId": "1887", "endLine": 28, "endColumn": 22}, {"ruleId": "1884", "severity": 1, "message": "2226", "line": 77, "column": 13, "nodeType": "1886", "messageId": "1887", "endLine": 77, "endColumn": 22}, {"ruleId": "1890", "severity": 1, "message": "1894", "line": 84, "column": 8, "nodeType": "1892", "endLine": 84, "endColumn": 10, "suggestions": "2227"}, {"ruleId": "1884", "severity": 1, "message": "2228", "line": 1, "column": 10, "nodeType": "1886", "messageId": "1887", "endLine": 1, "endColumn": 19}, {"ruleId": "1884", "severity": 1, "message": "2202", "line": 7, "column": 8, "nodeType": "1886", "messageId": "1887", "endLine": 7, "endColumn": 27}, {"ruleId": "2047", "severity": 1, "message": "2048", "line": 186, "column": 158, "nodeType": "2049", "messageId": "2050", "endLine": 186, "endColumn": 160}, {"ruleId": "2047", "severity": 1, "message": "2048", "line": 191, "column": 63, "nodeType": "2049", "messageId": "2050", "endLine": 191, "endColumn": 65}, {"ruleId": "2216", "severity": 1, "message": "2217", "line": 256, "column": 33, "nodeType": "1927", "endLine": 260, "endColumn": 35}, {"ruleId": "2229", "severity": 1, "message": "2230", "line": 259, "column": 155, "nodeType": "2231", "messageId": "2232", "endLine": 259, "endColumn": 157}, {"ruleId": "2229", "severity": 1, "message": "2230", "line": 259, "column": 166, "nodeType": "2231", "messageId": "2232", "endLine": 259, "endColumn": 168}, {"ruleId": "1884", "severity": 1, "message": "2212", "line": 2, "column": 10, "nodeType": "1886", "messageId": "1887", "endLine": 2, "endColumn": 25}, {"ruleId": "1884", "severity": 1, "message": "2222", "line": 2, "column": 8, "nodeType": "1886", "messageId": "1887", "endLine": 2, "endColumn": 13}, {"ruleId": "1884", "severity": 1, "message": "1930", "line": 19, "column": 11, "nodeType": "1886", "messageId": "1887", "endLine": 19, "endColumn": 19}, {"ruleId": "1884", "severity": 1, "message": "2233", "line": 5, "column": 10, "nodeType": "1886", "messageId": "1887", "endLine": 5, "endColumn": 18}, {"ruleId": "1884", "severity": 1, "message": "2212", "line": 5, "column": 20, "nodeType": "1886", "messageId": "1887", "endLine": 5, "endColumn": 35}, {"ruleId": "1884", "severity": 1, "message": "2133", "line": 30, "column": 11, "nodeType": "1886", "messageId": "1887", "endLine": 30, "endColumn": 19}, {"ruleId": "1884", "severity": 1, "message": "2212", "line": 6, "column": 10, "nodeType": "1886", "messageId": "1887", "endLine": 6, "endColumn": 25}, {"ruleId": "1884", "severity": 1, "message": "2202", "line": 7, "column": 8, "nodeType": "1886", "messageId": "1887", "endLine": 7, "endColumn": 27}, {"ruleId": "1884", "severity": 1, "message": "2211", "line": 8, "column": 8, "nodeType": "1886", "messageId": "1887", "endLine": 8, "endColumn": 22}, {"ruleId": "1884", "severity": 1, "message": "2133", "line": 18, "column": 11, "nodeType": "1886", "messageId": "1887", "endLine": 18, "endColumn": 19}, {"ruleId": "1884", "severity": 1, "message": "2212", "line": 3, "column": 10, "nodeType": "1886", "messageId": "1887", "endLine": 3, "endColumn": 25}, {"ruleId": "1884", "severity": 1, "message": "2202", "line": 5, "column": 8, "nodeType": "1886", "messageId": "1887", "endLine": 5, "endColumn": 27}, {"ruleId": "1884", "severity": 1, "message": "2133", "line": 16, "column": 11, "nodeType": "1886", "messageId": "1887", "endLine": 16, "endColumn": 19}, {"ruleId": "1890", "severity": 1, "message": "2084", "line": 43, "column": 43, "nodeType": "1886", "endLine": 43, "endColumn": 50}, {"ruleId": "1884", "severity": 1, "message": "1905", "line": 2, "column": 10, "nodeType": "1886", "messageId": "1887", "endLine": 2, "endColumn": 19}, {"ruleId": "1884", "severity": 1, "message": "1967", "line": 3, "column": 10, "nodeType": "1886", "messageId": "1887", "endLine": 3, "endColumn": 21}, {"ruleId": "1884", "severity": 1, "message": "1910", "line": 3, "column": 23, "nodeType": "1886", "messageId": "1887", "endLine": 3, "endColumn": 34}, {"ruleId": "1884", "severity": 1, "message": "2234", "line": 5, "column": 8, "nodeType": "1886", "messageId": "1887", "endLine": 5, "endColumn": 15}, {"ruleId": "1884", "severity": 1, "message": "2183", "line": 6, "column": 10, "nodeType": "1886", "messageId": "1887", "endLine": 6, "endColumn": 27}, {"ruleId": "1884", "severity": 1, "message": "2235", "line": 7, "column": 10, "nodeType": "1886", "messageId": "1887", "endLine": 7, "endColumn": 22}, {"ruleId": "1884", "severity": 1, "message": "2160", "line": 8, "column": 8, "nodeType": "1886", "messageId": "1887", "endLine": 8, "endColumn": 16}, {"ruleId": "1884", "severity": 1, "message": "2236", "line": 9, "column": 8, "nodeType": "1886", "messageId": "1887", "endLine": 9, "endColumn": 28}, {"ruleId": "1884", "severity": 1, "message": "2237", "line": 17, "column": 11, "nodeType": "1886", "messageId": "1887", "endLine": 17, "endColumn": 21}, {"ruleId": "1884", "severity": 1, "message": "2184", "line": 18, "column": 11, "nodeType": "1886", "messageId": "1887", "endLine": 18, "endColumn": 19}, {"ruleId": "1884", "severity": 1, "message": "2185", "line": 19, "column": 11, "nodeType": "1886", "messageId": "1887", "endLine": 19, "endColumn": 18}, {"ruleId": "1884", "severity": 1, "message": "2238", "line": 1, "column": 8, "nodeType": "1886", "messageId": "1887", "endLine": 1, "endColumn": 19}, {"ruleId": "1884", "severity": 1, "message": "1967", "line": 5, "column": 23, "nodeType": "1886", "messageId": "1887", "endLine": 5, "endColumn": 34}, {"ruleId": "1884", "severity": 1, "message": "2239", "line": 28, "column": 11, "nodeType": "1886", "messageId": "1887", "endLine": 28, "endColumn": 27}, {"ruleId": "1890", "severity": 1, "message": "1894", "line": 38, "column": 8, "nodeType": "1892", "endLine": 38, "endColumn": 10, "suggestions": "2240"}, {"ruleId": "1884", "severity": 1, "message": "2212", "line": 3, "column": 10, "nodeType": "1886", "messageId": "1887", "endLine": 3, "endColumn": 25}, {"ruleId": "1884", "severity": 1, "message": "2202", "line": 18, "column": 8, "nodeType": "1886", "messageId": "1887", "endLine": 18, "endColumn": 27}, {"ruleId": "1884", "severity": 1, "message": "1930", "line": 27, "column": 11, "nodeType": "1886", "messageId": "1887", "endLine": 27, "endColumn": 19}, {"ruleId": "1884", "severity": 1, "message": "2241", "line": 40, "column": 11, "nodeType": "1886", "messageId": "1887", "endLine": 40, "endColumn": 21}, {"ruleId": "1884", "severity": 1, "message": "2242", "line": 47, "column": 11, "nodeType": "1886", "messageId": "1887", "endLine": 47, "endColumn": 25}, {"ruleId": "1884", "severity": 1, "message": "2243", "line": 49, "column": 11, "nodeType": "1886", "messageId": "1887", "endLine": 49, "endColumn": 33}, {"ruleId": "1884", "severity": 1, "message": "2212", "line": 2, "column": 10, "nodeType": "1886", "messageId": "1887", "endLine": 2, "endColumn": 25}, {"ruleId": "1884", "severity": 1, "message": "1954", "line": 3, "column": 10, "nodeType": "1886", "messageId": "1887", "endLine": 3, "endColumn": 22}, {"ruleId": "1884", "severity": 1, "message": "2202", "line": 6, "column": 8, "nodeType": "1886", "messageId": "1887", "endLine": 6, "endColumn": 27}, {"ruleId": "1884", "severity": 1, "message": "2211", "line": 7, "column": 8, "nodeType": "1886", "messageId": "1887", "endLine": 7, "endColumn": 22}, {"ruleId": "1884", "severity": 1, "message": "1905", "line": 1, "column": 17, "nodeType": "1886", "messageId": "1887", "endLine": 1, "endColumn": 26}, {"ruleId": "1884", "severity": 1, "message": "1896", "line": 1, "column": 28, "nodeType": "1886", "messageId": "1887", "endLine": 1, "endColumn": 36}, {"ruleId": "1884", "severity": 1, "message": "2183", "line": 4, "column": 10, "nodeType": "1886", "messageId": "1887", "endLine": 4, "endColumn": 27}, {"ruleId": "1884", "severity": 1, "message": "2234", "line": 5, "column": 8, "nodeType": "1886", "messageId": "1887", "endLine": 5, "endColumn": 15}, {"ruleId": "1884", "severity": 1, "message": "2237", "line": 22, "column": 11, "nodeType": "1886", "messageId": "1887", "endLine": 22, "endColumn": 21}, {"ruleId": "1884", "severity": 1, "message": "1930", "line": 25, "column": 11, "nodeType": "1886", "messageId": "1887", "endLine": 25, "endColumn": 19}, {"ruleId": "2047", "severity": 1, "message": "2048", "line": 31, "column": 28, "nodeType": "2049", "messageId": "2050", "endLine": 31, "endColumn": 30}, {"ruleId": "1884", "severity": 1, "message": "1954", "line": 5, "column": 10, "nodeType": "1886", "messageId": "1887", "endLine": 5, "endColumn": 22}, {"ruleId": "1884", "severity": 1, "message": "2244", "line": 8, "column": 8, "nodeType": "1886", "messageId": "1887", "endLine": 8, "endColumn": 21}, {"ruleId": "1884", "severity": 1, "message": "2202", "line": 9, "column": 8, "nodeType": "1886", "messageId": "1887", "endLine": 9, "endColumn": 27}, {"ruleId": "1884", "severity": 1, "message": "1930", "line": 16, "column": 11, "nodeType": "1886", "messageId": "1887", "endLine": 16, "endColumn": 19}, {"ruleId": "1884", "severity": 1, "message": "2133", "line": 19, "column": 11, "nodeType": "1886", "messageId": "1887", "endLine": 19, "endColumn": 19}, {"ruleId": "1884", "severity": 1, "message": "1905", "line": 1, "column": 17, "nodeType": "1886", "messageId": "1887", "endLine": 1, "endColumn": 26}, {"ruleId": "1884", "severity": 1, "message": "2234", "line": 3, "column": 8, "nodeType": "1886", "messageId": "1887", "endLine": 3, "endColumn": 15}, {"ruleId": "1884", "severity": 1, "message": "2183", "line": 5, "column": 10, "nodeType": "1886", "messageId": "1887", "endLine": 5, "endColumn": 27}, {"ruleId": "1884", "severity": 1, "message": "1910", "line": 6, "column": 10, "nodeType": "1886", "messageId": "1887", "endLine": 6, "endColumn": 21}, {"ruleId": "1884", "severity": 1, "message": "2245", "line": 7, "column": 8, "nodeType": "1886", "messageId": "1887", "endLine": 7, "endColumn": 30}, {"ruleId": "1884", "severity": 1, "message": "2202", "line": 10, "column": 8, "nodeType": "1886", "messageId": "1887", "endLine": 10, "endColumn": 27}, {"ruleId": "1884", "severity": 1, "message": "2211", "line": 11, "column": 8, "nodeType": "1886", "messageId": "1887", "endLine": 11, "endColumn": 22}, {"ruleId": "2047", "severity": 1, "message": "2048", "line": 174, "column": 158, "nodeType": "2049", "messageId": "2050", "endLine": 174, "endColumn": 160}, {"ruleId": "2047", "severity": 1, "message": "2048", "line": 179, "column": 63, "nodeType": "2049", "messageId": "2050", "endLine": 179, "endColumn": 65}, {"ruleId": "1884", "severity": 1, "message": "2246", "line": 4, "column": 8, "nodeType": "1886", "messageId": "1887", "endLine": 4, "endColumn": 25}, {"ruleId": "1884", "severity": 1, "message": "2202", "line": 7, "column": 8, "nodeType": "1886", "messageId": "1887", "endLine": 7, "endColumn": 27}, {"ruleId": "1884", "severity": 1, "message": "2211", "line": 8, "column": 8, "nodeType": "1886", "messageId": "1887", "endLine": 8, "endColumn": 22}, {"ruleId": "1884", "severity": 1, "message": "2247", "line": 4, "column": 8, "nodeType": "1886", "messageId": "1887", "endLine": 4, "endColumn": 17}, {"ruleId": "1884", "severity": 1, "message": "2202", "line": 5, "column": 8, "nodeType": "1886", "messageId": "1887", "endLine": 5, "endColumn": 27}, {"ruleId": "1884", "severity": 1, "message": "2248", "line": 13, "column": 11, "nodeType": "1886", "messageId": "1887", "endLine": 13, "endColumn": 15}, {"ruleId": "1884", "severity": 1, "message": "2133", "line": 16, "column": 11, "nodeType": "1886", "messageId": "1887", "endLine": 16, "endColumn": 19}, {"ruleId": "1884", "severity": 1, "message": "2249", "line": 4, "column": 10, "nodeType": "1886", "messageId": "1887", "endLine": 4, "endColumn": 22}, {"ruleId": "1884", "severity": 1, "message": "2212", "line": 4, "column": 24, "nodeType": "1886", "messageId": "1887", "endLine": 4, "endColumn": 39}, {"ruleId": "1884", "severity": 1, "message": "2233", "line": 5, "column": 10, "nodeType": "1886", "messageId": "1887", "endLine": 5, "endColumn": 18}, {"ruleId": "1884", "severity": 1, "message": "2250", "line": 20, "column": 11, "nodeType": "1886", "messageId": "1887", "endLine": 20, "endColumn": 18}, {"ruleId": "1884", "severity": 1, "message": "2233", "line": 3, "column": 27, "nodeType": "1886", "messageId": "1887", "endLine": 3, "endColumn": 35}, {"ruleId": "1884", "severity": 1, "message": "2133", "line": 14, "column": 11, "nodeType": "1886", "messageId": "1887", "endLine": 14, "endColumn": 19}, {"ruleId": "1884", "severity": 1, "message": "2249", "line": 4, "column": 10, "nodeType": "1886", "messageId": "1887", "endLine": 4, "endColumn": 22}, {"ruleId": "1884", "severity": 1, "message": "2233", "line": 5, "column": 10, "nodeType": "1886", "messageId": "1887", "endLine": 5, "endColumn": 18}, {"ruleId": "1884", "severity": 1, "message": "2133", "line": 15, "column": 11, "nodeType": "1886", "messageId": "1887", "endLine": 15, "endColumn": 19}, {"ruleId": "1884", "severity": 1, "message": "2250", "line": 21, "column": 11, "nodeType": "1886", "messageId": "1887", "endLine": 21, "endColumn": 18}, {"ruleId": "1884", "severity": 1, "message": "1896", "line": 1, "column": 10, "nodeType": "1886", "messageId": "1887", "endLine": 1, "endColumn": 18}, {"ruleId": "1884", "severity": 1, "message": "1905", "line": 1, "column": 20, "nodeType": "1886", "messageId": "1887", "endLine": 1, "endColumn": 29}, {"ruleId": "1884", "severity": 1, "message": "2249", "line": 4, "column": 10, "nodeType": "1886", "messageId": "1887", "endLine": 4, "endColumn": 22}, {"ruleId": "1884", "severity": 1, "message": "1967", "line": 8, "column": 10, "nodeType": "1886", "messageId": "1887", "endLine": 8, "endColumn": 21}, {"ruleId": "1890", "severity": 1, "message": "2251", "line": 198, "column": 8, "nodeType": "1892", "endLine": 198, "endColumn": 46, "suggestions": "2252"}, {"ruleId": "2216", "severity": 1, "message": "2217", "line": 378, "column": 57, "nodeType": "1927", "endLine": 387, "endColumn": 59}, {"ruleId": "1884", "severity": 1, "message": "2253", "line": 7, "column": 5, "nodeType": "1886", "messageId": "1887", "endLine": 7, "endColumn": 15}, {"ruleId": "1884", "severity": 1, "message": "2211", "line": 16, "column": 8, "nodeType": "1886", "messageId": "1887", "endLine": 16, "endColumn": 22}, {"ruleId": "1884", "severity": 1, "message": "2202", "line": 17, "column": 8, "nodeType": "1886", "messageId": "1887", "endLine": 17, "endColumn": 27}, {"ruleId": "1884", "severity": 1, "message": "2254", "line": 13, "column": 12, "nodeType": "1886", "messageId": "1887", "endLine": 13, "endColumn": 24}, {"ruleId": "1890", "severity": 1, "message": "2255", "line": 110, "column": 8, "nodeType": "1892", "endLine": 110, "endColumn": 34, "suggestions": "2256"}, {"ruleId": "1884", "severity": 1, "message": "2257", "line": 160, "column": 31, "nodeType": "1886", "messageId": "1887", "endLine": 160, "endColumn": 37}, {"ruleId": "1884", "severity": 1, "message": "2183", "line": 6, "column": 10, "nodeType": "1886", "messageId": "1887", "endLine": 6, "endColumn": 27}, {"ruleId": "1884", "severity": 1, "message": "2258", "line": 14, "column": 23, "nodeType": "1886", "messageId": "1887", "endLine": 14, "endColumn": 35}, {"ruleId": "1884", "severity": 1, "message": "2254", "line": 24, "column": 12, "nodeType": "1886", "messageId": "1887", "endLine": 24, "endColumn": 24}, {"ruleId": "1884", "severity": 1, "message": "2259", "line": 24, "column": 26, "nodeType": "1886", "messageId": "1887", "endLine": 24, "endColumn": 41}, {"ruleId": "1884", "severity": 1, "message": "1930", "line": 39, "column": 11, "nodeType": "1886", "messageId": "1887", "endLine": 39, "endColumn": 19}, {"ruleId": "1890", "severity": 1, "message": "2255", "line": 338, "column": 8, "nodeType": "1892", "endLine": 338, "endColumn": 27, "suggestions": "2260"}, {"ruleId": "1884", "severity": 1, "message": "2261", "line": 2, "column": 8, "nodeType": "1886", "messageId": "1887", "endLine": 2, "endColumn": 22}, {"ruleId": "1884", "severity": 1, "message": "2183", "line": 6, "column": 10, "nodeType": "1886", "messageId": "1887", "endLine": 6, "endColumn": 27}, {"ruleId": "1884", "severity": 1, "message": "1930", "line": 19, "column": 9, "nodeType": "1886", "messageId": "1887", "endLine": 19, "endColumn": 17}, {"ruleId": "1884", "severity": 1, "message": "2254", "line": 26, "column": 10, "nodeType": "1886", "messageId": "1887", "endLine": 26, "endColumn": 22}, {"ruleId": "1890", "severity": 1, "message": "2255", "line": 152, "column": 6, "nodeType": "1892", "endLine": 152, "endColumn": 32, "suggestions": "2262"}, {"ruleId": "1884", "severity": 1, "message": "2183", "line": 7, "column": 10, "nodeType": "1886", "messageId": "1887", "endLine": 7, "endColumn": 27}, {"ruleId": "1884", "severity": 1, "message": "2106", "line": 10, "column": 10, "nodeType": "1886", "messageId": "1887", "endLine": 10, "endColumn": 20}, {"ruleId": "1884", "severity": 1, "message": "1930", "line": 13, "column": 11, "nodeType": "1886", "messageId": "1887", "endLine": 13, "endColumn": 19}, {"ruleId": "1884", "severity": 1, "message": "2263", "line": 14, "column": 12, "nodeType": "1886", "messageId": "1887", "endLine": 14, "endColumn": 21}, {"ruleId": "1884", "severity": 1, "message": "2118", "line": 16, "column": 13, "nodeType": "1886", "messageId": "1887", "endLine": 16, "endColumn": 22}, {"ruleId": "1884", "severity": 1, "message": "2264", "line": 16, "column": 24, "nodeType": "1886", "messageId": "1887", "endLine": 16, "endColumn": 32}, {"ruleId": "1884", "severity": 1, "message": "2254", "line": 21, "column": 12, "nodeType": "1886", "messageId": "1887", "endLine": 21, "endColumn": 24}, {"ruleId": "1884", "severity": 1, "message": "2265", "line": 105, "column": 15, "nodeType": "1886", "messageId": "1887", "endLine": 105, "endColumn": 24}, {"ruleId": "1890", "severity": 1, "message": "2255", "line": 134, "column": 8, "nodeType": "1892", "endLine": 134, "endColumn": 27, "suggestions": "2266"}, {"ruleId": "1884", "severity": 1, "message": "2254", "line": 19, "column": 12, "nodeType": "1886", "messageId": "1887", "endLine": 19, "endColumn": 24}, {"ruleId": "1890", "severity": 1, "message": "2255", "line": 99, "column": 8, "nodeType": "1892", "endLine": 99, "endColumn": 34, "suggestions": "2267"}, {"ruleId": "1890", "severity": 1, "message": "1894", "line": 145, "column": 8, "nodeType": "1892", "endLine": 145, "endColumn": 10, "suggestions": "2268"}, {"ruleId": "1884", "severity": 1, "message": "2254", "line": 15, "column": 12, "nodeType": "1886", "messageId": "1887", "endLine": 15, "endColumn": 24, "suppressions": "2269"}, {"ruleId": "1884", "severity": 1, "message": "2254", "line": 12, "column": 12, "nodeType": "1886", "messageId": "1887", "endLine": 12, "endColumn": 24}, {"ruleId": "1890", "severity": 1, "message": "2255", "line": 87, "column": 8, "nodeType": "1892", "endLine": 87, "endColumn": 27, "suggestions": "2270"}, {"ruleId": "1884", "severity": 1, "message": "1967", "line": 6, "column": 10, "nodeType": "1886", "messageId": "1887", "endLine": 6, "endColumn": 21}, {"ruleId": "1884", "severity": 1, "message": "2254", "line": 13, "column": 12, "nodeType": "1886", "messageId": "1887", "endLine": 13, "endColumn": 24}, {"ruleId": "1890", "severity": 1, "message": "2255", "line": 95, "column": 8, "nodeType": "1892", "endLine": 95, "endColumn": 34, "suggestions": "2271"}, {"ruleId": "1890", "severity": 1, "message": "1894", "line": 15, "column": 8, "nodeType": "1892", "endLine": 15, "endColumn": 10, "suggestions": "2272"}, {"ruleId": "1884", "severity": 1, "message": "2273", "line": 2, "column": 10, "nodeType": "1886", "messageId": "1887", "endLine": 2, "endColumn": 22}, {"ruleId": "1884", "severity": 1, "message": "2254", "line": 15, "column": 12, "nodeType": "1886", "messageId": "1887", "endLine": 15, "endColumn": 24}, {"ruleId": "1890", "severity": 1, "message": "2255", "line": 128, "column": 8, "nodeType": "1892", "endLine": 128, "endColumn": 27, "suggestions": "2274"}, {"ruleId": "1884", "severity": 1, "message": "2254", "line": 69, "column": 12, "nodeType": "1886", "messageId": "1887", "endLine": 69, "endColumn": 24}, {"ruleId": "1890", "severity": 1, "message": "2255", "line": 208, "column": 8, "nodeType": "1892", "endLine": 208, "endColumn": 36, "suggestions": "2275"}, {"ruleId": "1884", "severity": 1, "message": "2254", "line": 51, "column": 10, "nodeType": "1886", "messageId": "1887", "endLine": 51, "endColumn": 22}, {"ruleId": "1890", "severity": 1, "message": "2255", "line": 235, "column": 6, "nodeType": "1892", "endLine": 235, "endColumn": 34, "suggestions": "2276"}, {"ruleId": "1884", "severity": 1, "message": "2254", "line": 39, "column": 12, "nodeType": "1886", "messageId": "1887", "endLine": 39, "endColumn": 24}, {"ruleId": "1890", "severity": 1, "message": "2255", "line": 163, "column": 8, "nodeType": "1892", "endLine": 163, "endColumn": 36, "suggestions": "2277"}, {"ruleId": "1884", "severity": 1, "message": "2278", "line": 5, "column": 29, "nodeType": "1886", "messageId": "1887", "endLine": 5, "endColumn": 56}, {"ruleId": "2047", "severity": 1, "message": "2048", "line": 11, "column": 61, "nodeType": "2049", "messageId": "2050", "endLine": 11, "endColumn": 63}, {"ruleId": "1884", "severity": 1, "message": "2254", "line": 15, "column": 12, "nodeType": "1886", "messageId": "1887", "endLine": 15, "endColumn": 24}, {"ruleId": "1884", "severity": 1, "message": "2279", "line": 41, "column": 11, "nodeType": "1886", "messageId": "1887", "endLine": 41, "endColumn": 24}, {"ruleId": "1890", "severity": 1, "message": "2255", "line": 143, "column": 8, "nodeType": "1892", "endLine": 143, "endColumn": 47, "suggestions": "2280"}, {"ruleId": "1890", "severity": 1, "message": "1894", "line": 168, "column": 8, "nodeType": "1892", "endLine": 168, "endColumn": 10, "suggestions": "2281"}, {"ruleId": "1884", "severity": 1, "message": "2257", "line": 195, "column": 31, "nodeType": "1886", "messageId": "1887", "endLine": 195, "endColumn": 37}, {"ruleId": "1884", "severity": 1, "message": "2257", "line": 248, "column": 31, "nodeType": "1886", "messageId": "1887", "endLine": 248, "endColumn": 37}, {"ruleId": "1890", "severity": 1, "message": "2282", "line": 36, "column": 11, "nodeType": "2283", "endLine": 36, "endColumn": 57}, {"ruleId": "1890", "severity": 1, "message": "2284", "line": 37, "column": 11, "nodeType": "2283", "endLine": 37, "endColumn": 59}, {"ruleId": "2093", "severity": 1, "message": "2094", "line": 212, "column": 61, "nodeType": "2095", "messageId": "2096", "endLine": 212, "endColumn": 63}, {"ruleId": "1890", "severity": 1, "message": "2285", "line": 272, "column": 8, "nodeType": "1892", "endLine": 272, "endColumn": 10, "suggestions": "2286"}, {"ruleId": "1884", "severity": 1, "message": "2257", "line": 368, "column": 31, "nodeType": "1886", "messageId": "1887", "endLine": 368, "endColumn": 37}, {"ruleId": "1884", "severity": 1, "message": "2254", "line": 21, "column": 12, "nodeType": "1886", "messageId": "1887", "endLine": 21, "endColumn": 24}, {"ruleId": "1890", "severity": 1, "message": "2285", "line": 192, "column": 8, "nodeType": "1892", "endLine": 192, "endColumn": 10, "suggestions": "2287"}, {"ruleId": "2150", "severity": 1, "message": "2288", "line": 272, "column": 142, "nodeType": "2152", "messageId": "2050", "endLine": 272, "endColumn": 151}, {"ruleId": "1884", "severity": 1, "message": "2278", "line": 5, "column": 35, "nodeType": "1886", "messageId": "1887", "endLine": 5, "endColumn": 62}, {"ruleId": "1884", "severity": 1, "message": "2254", "line": 21, "column": 12, "nodeType": "1886", "messageId": "1887", "endLine": 21, "endColumn": 24}, {"ruleId": "1890", "severity": 1, "message": "2255", "line": 136, "column": 8, "nodeType": "1892", "endLine": 136, "endColumn": 36, "suggestions": "2289"}, {"ruleId": "1884", "severity": 1, "message": "2290", "line": 162, "column": 11, "nodeType": "1886", "messageId": "1887", "endLine": 162, "endColumn": 21}, {"ruleId": "2150", "severity": 1, "message": "2288", "line": 246, "column": 129, "nodeType": "2152", "messageId": "2050", "endLine": 246, "endColumn": 138}, {"ruleId": "2150", "severity": 1, "message": "2288", "line": 247, "column": 153, "nodeType": "2152", "messageId": "2050", "endLine": 247, "endColumn": 162}, {"ruleId": "1884", "severity": 1, "message": "2254", "line": 21, "column": 12, "nodeType": "1886", "messageId": "1887", "endLine": 21, "endColumn": 24}, {"ruleId": "1890", "severity": 1, "message": "2285", "line": 159, "column": 8, "nodeType": "1892", "endLine": 159, "endColumn": 12, "suggestions": "2291"}, {"ruleId": "1884", "severity": 1, "message": "2254", "line": 17, "column": 12, "nodeType": "1886", "messageId": "1887", "endLine": 17, "endColumn": 24}, {"ruleId": "1890", "severity": 1, "message": "2255", "line": 87, "column": 8, "nodeType": "1892", "endLine": 87, "endColumn": 34, "suggestions": "2292"}, {"ruleId": "1884", "severity": 1, "message": "2254", "line": 21, "column": 12, "nodeType": "1886", "messageId": "1887", "endLine": 21, "endColumn": 24, "suppressions": "2293"}, {"ruleId": "1890", "severity": 1, "message": "2294", "line": 13, "column": 11, "nodeType": "2283", "endLine": 13, "endColumn": 84}, {"ruleId": "1884", "severity": 1, "message": "2295", "line": 15, "column": 11, "nodeType": "1886", "messageId": "1887", "endLine": 15, "endColumn": 20}, {"ruleId": "1884", "severity": 1, "message": "2254", "line": 20, "column": 12, "nodeType": "1886", "messageId": "1887", "endLine": 20, "endColumn": 24}, {"ruleId": "1884", "severity": 1, "message": "2254", "line": 72, "column": 10, "nodeType": "1886", "messageId": "1887", "endLine": 72, "endColumn": 22}, {"ruleId": "1890", "severity": 1, "message": "2255", "line": 248, "column": 6, "nodeType": "1892", "endLine": 248, "endColumn": 34, "suggestions": "2296"}, {"ruleId": "1884", "severity": 1, "message": "2297", "line": 1, "column": 10, "nodeType": "1886", "messageId": "1887", "endLine": 1, "endColumn": 14}, {"ruleId": "1884", "severity": 1, "message": "2193", "line": 8, "column": 12, "nodeType": "1886", "messageId": "1887", "endLine": 8, "endColumn": 17}, {"ruleId": "2298", "severity": 1, "message": "2299", "line": 44, "column": 13, "nodeType": "2300", "messageId": "2301", "endLine": 44, "endColumn": 25, "suppressions": "2302"}, {"ruleId": "2303", "severity": 1, "message": "2304", "line": 52, "column": 100, "nodeType": "1886", "messageId": "2050", "endLine": 52, "endColumn": 104, "suppressions": "2305"}, {"ruleId": "2303", "severity": 1, "message": "2304", "line": 52, "column": 141, "nodeType": "1886", "messageId": "2050", "endLine": 52, "endColumn": 145, "suppressions": "2306"}, {"ruleId": "1884", "severity": 1, "message": "2307", "line": 52, "column": 43925, "nodeType": "1886", "messageId": "1887", "endLine": 52, "endColumn": 43930, "suppressions": "2308"}, {"ruleId": "1884", "severity": 1, "message": "2309", "line": 52, "column": 44059, "nodeType": "1886", "messageId": "1887", "endLine": 52, "endColumn": 44064, "suppressions": "2310"}, {"ruleId": "1884", "severity": 1, "message": "2311", "line": 52, "column": 44193, "nodeType": "1886", "messageId": "1887", "endLine": 52, "endColumn": 44198, "suppressions": "2312"}, {"ruleId": "1884", "severity": 1, "message": "2313", "line": 52, "column": 44303, "nodeType": "1886", "messageId": "1887", "endLine": 52, "endColumn": 44308, "suppressions": "2314"}, {"ruleId": "2315", "message": "2316", "line": 52, "column": 44403, "endLine": 52, "endColumn": 44670, "severity": 2, "nodeType": null, "suppressions": "2317"}, {"ruleId": "2318", "message": "2319", "line": 52, "column": 44403, "endLine": 52, "endColumn": 44670, "severity": 2, "nodeType": null, "suppressions": "2320"}, {"ruleId": "2321", "message": "2322", "line": 52, "column": 44403, "endLine": 52, "endColumn": 44670, "severity": 2, "nodeType": null, "suppressions": "2323"}, {"ruleId": "2324", "message": "2325", "line": 52, "column": 44403, "endLine": 52, "endColumn": 44670, "severity": 2, "nodeType": null, "suppressions": "2326"}, {"ruleId": "2327", "message": "2328", "line": 52, "column": 44403, "endLine": 52, "endColumn": 44670, "severity": 2, "nodeType": null, "suppressions": "2329"}, {"ruleId": "2330", "message": "2331", "line": 52, "column": 44403, "endLine": 52, "endColumn": 44670, "severity": 2, "nodeType": null, "suppressions": "2332"}, {"ruleId": "2333", "message": "2334", "line": 52, "column": 44403, "endLine": 52, "endColumn": 44670, "severity": 2, "nodeType": null, "suppressions": "2335"}, {"ruleId": "1884", "severity": 1, "message": "1896", "line": 1, "column": 27, "nodeType": "1886", "messageId": "1887", "endLine": 1, "endColumn": 35}, {"ruleId": "1884", "severity": 1, "message": "2336", "line": 9, "column": 3, "nodeType": "1886", "messageId": "1887", "endLine": 9, "endColumn": 15}, {"ruleId": "1884", "severity": 1, "message": "2337", "line": 10, "column": 3, "nodeType": "1886", "messageId": "1887", "endLine": 10, "endColumn": 14}, {"ruleId": "1884", "severity": 1, "message": "2338", "line": 15, "column": 3, "nodeType": "1886", "messageId": "1887", "endLine": 15, "endColumn": 37}, {"ruleId": "1890", "severity": 1, "message": "2339", "line": 349, "column": 5, "nodeType": "1892", "endLine": 349, "endColumn": 19, "suggestions": "2340"}, {"ruleId": "1884", "severity": 1, "message": "2234", "line": 3, "column": 8, "nodeType": "1886", "messageId": "1887", "endLine": 3, "endColumn": 15}, {"ruleId": "1884", "severity": 1, "message": "2341", "line": 71, "column": 7, "nodeType": "1886", "messageId": "1887", "endLine": 71, "endColumn": 16}, {"ruleId": "1890", "severity": 1, "message": "2342", "line": 161, "column": 6, "nodeType": "1892", "endLine": 161, "endColumn": 14, "suggestions": "2343"}, {"ruleId": "1890", "severity": 1, "message": "2344", "line": 170, "column": 6, "nodeType": "1892", "endLine": 170, "endColumn": 15, "suggestions": "2345"}, {"ruleId": "1884", "severity": 1, "message": "1905", "line": 1, "column": 10, "nodeType": "1886", "messageId": "1887", "endLine": 1, "endColumn": 19}, {"ruleId": "1884", "severity": 1, "message": "2346", "line": 2, "column": 10, "nodeType": "1886", "messageId": "1887", "endLine": 2, "endColumn": 16}, {"ruleId": "1884", "severity": 1, "message": "1909", "line": 2, "column": 18, "nodeType": "1886", "messageId": "1887", "endLine": 2, "endColumn": 19}, {"ruleId": "1884", "severity": 1, "message": "2347", "line": 2, "column": 21, "nodeType": "1886", "messageId": "1887", "endLine": 2, "endColumn": 29}, {"ruleId": "1884", "severity": 1, "message": "2348", "line": 12, "column": 12, "nodeType": "1886", "messageId": "1887", "endLine": 12, "endColumn": 20}, {"ruleId": "1884", "severity": 1, "message": "2201", "line": 13, "column": 12, "nodeType": "1886", "messageId": "1887", "endLine": 13, "endColumn": 19}, {"ruleId": "1884", "severity": 1, "message": "2349", "line": 28, "column": 11, "nodeType": "1886", "messageId": "1887", "endLine": 28, "endColumn": 20}, {"ruleId": "1884", "severity": 1, "message": "2234", "line": 3, "column": 8, "nodeType": "1886", "messageId": "1887", "endLine": 3, "endColumn": 15}, {"ruleId": "1884", "severity": 1, "message": "2341", "line": 72, "column": 9, "nodeType": "1886", "messageId": "1887", "endLine": 72, "endColumn": 18}, {"ruleId": "1890", "severity": 1, "message": "2342", "line": 163, "column": 8, "nodeType": "1892", "endLine": 163, "endColumn": 16, "suggestions": "2350"}, {"ruleId": "1890", "severity": 1, "message": "2344", "line": 172, "column": 8, "nodeType": "1892", "endLine": 172, "endColumn": 17, "suggestions": "2351"}, {"ruleId": "1884", "severity": 1, "message": "2278", "line": 13, "column": 3, "nodeType": "1886", "messageId": "1887", "endLine": 13, "endColumn": 30}, {"ruleId": "1890", "severity": 1, "message": "2339", "line": 331, "column": 5, "nodeType": "1892", "endLine": 331, "endColumn": 19, "suggestions": "2352"}, {"ruleId": "1884", "severity": 1, "message": "2234", "line": 3, "column": 8, "nodeType": "1886", "messageId": "1887", "endLine": 3, "endColumn": 15}, {"ruleId": "1884", "severity": 1, "message": "2341", "line": 72, "column": 7, "nodeType": "1886", "messageId": "1887", "endLine": 72, "endColumn": 16}, {"ruleId": "1890", "severity": 1, "message": "2353", "line": 147, "column": 6, "nodeType": "1892", "endLine": 147, "endColumn": 14, "suggestions": "2354"}, {"ruleId": "1890", "severity": 1, "message": "2344", "line": 154, "column": 6, "nodeType": "1892", "endLine": 154, "endColumn": 15, "suggestions": "2355"}, {"ruleId": "1884", "severity": 1, "message": "1909", "line": 2, "column": 18, "nodeType": "1886", "messageId": "1887", "endLine": 2, "endColumn": 19}, {"ruleId": "1884", "severity": 1, "message": "1967", "line": 3, "column": 23, "nodeType": "1886", "messageId": "1887", "endLine": 3, "endColumn": 34}, {"ruleId": "1884", "severity": 1, "message": "2199", "line": 4, "column": 10, "nodeType": "1886", "messageId": "1887", "endLine": 4, "endColumn": 22}, {"ruleId": "1884", "severity": 1, "message": "2201", "line": 11, "column": 10, "nodeType": "1886", "messageId": "1887", "endLine": 11, "endColumn": 17}, {"ruleId": "1884", "severity": 1, "message": "2356", "line": 11, "column": 19, "nodeType": "1886", "messageId": "1887", "endLine": 11, "endColumn": 29}, {"ruleId": "1890", "severity": 1, "message": "1894", "line": 11, "column": 10, "nodeType": "1892", "endLine": 11, "endColumn": 12, "suggestions": "2357"}, {"ruleId": "1884", "severity": 1, "message": "1888", "line": 3, "column": 10, "nodeType": "1886", "messageId": "1887", "endLine": 3, "endColumn": 15}, {"ruleId": "1884", "severity": 1, "message": "2358", "line": 8, "column": 17, "nodeType": "1886", "messageId": "1887", "endLine": 8, "endColumn": 23}, {"ruleId": "1884", "severity": 1, "message": "2359", "line": 10, "column": 10, "nodeType": "1886", "messageId": "1887", "endLine": 10, "endColumn": 27}, {"ruleId": "1890", "severity": 1, "message": "2062", "line": 50, "column": 6, "nodeType": "1892", "endLine": 50, "endColumn": 18, "suggestions": "2360"}, {"ruleId": "1925", "severity": 1, "message": "1926", "line": 87, "column": 15, "nodeType": "1927", "endLine": 91, "endColumn": 16}, {"ruleId": "1925", "severity": 1, "message": "1926", "line": 98, "column": 13, "nodeType": "1927", "endLine": 98, "endColumn": 85}, {"ruleId": "1884", "severity": 1, "message": "2361", "line": 5, "column": 13, "nodeType": "1886", "messageId": "1887", "endLine": 5, "endColumn": 20}, {"ruleId": "1890", "severity": 1, "message": "2255", "line": 154, "column": 6, "nodeType": "1892", "endLine": 154, "endColumn": 25, "suggestions": "2362"}, "no-unused-vars", "'imagesReducer' is defined but never used.", "Identifier", "unusedVar", "'toast' is defined but never used.", "'setNotificationCount' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'dispatch' and 'newNotificationStatus'. Either include them or remove the dependency array.", "ArrayExpression", ["2363"], "React Hook useEffect has a missing dependency: 'dispatch'. Either include it or remove the dependency array.", ["2364"], "'useState' is defined but never used.", "'useRef' is defined but never used.", "'Link' is defined but never used.", "'Login' is defined but never used.", "'Title' is defined but never used.", "'count' is assigned a value but never used.", "'jsonConsole' is defined but never used.", "'openRightDrawer' is defined but never used.", "'useCallback' is defined but never used.", "'useEffect' is defined but never used.", "'incrementNotificationCount' is defined but never used.", "'socket' is defined but never used.", "'ToastContainer' is defined but never used.", "'X' is defined but never used.", "'useDispatch' is defined but never used.", "React Hook useEffect has a missing dependency: 'scrollContainerRef'. Either include it or remove the dependency array.", ["2365"], "React Hook useEffect has missing dependencies: 'location.pathname' and 'navigate'. Either include them or remove the dependency array.", ["2366"], "'updateToasify' is defined but never used.", "'CodeSquare' is defined but never used.", "React Hook useEffect has missing dependencies: 'formObj' and 'navigate'. Either include them or remove the dependency array. You can also do a functional update 'setFormObj(f => ...)' if you only need 'formObj' in the 'setFormObj' call.", ["2367"], "React Hook useEffect has a missing dependency: 'formObj.otpOrigin'. Either include it or remove the dependency array.", ["2368"], "React Hook useEffect has a missing dependency: 'navigate'. Either include it or remove the dependency array.", ["2369"], "React Hook useEffect has a missing dependency: 'formObj'. Either include it or remove the dependency array. You can also do a functional update 'setFormObj(f => ...)' if you only need 'formObj' in the 'setFormObj' call.", ["2370"], "jsx-a11y/anchor-is-valid", "The href attribute is required for an anchor to be keyboard accessible. Provide a valid, navigable address as the href value. If you cannot provide an href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", "'Subtitle' is defined but never used.", "'setPageTitle' is defined but never used.", "'dispatch' is assigned a value but never used.", "react/jsx-no-target-blank", "Using target=\"_blank\" without rel=\"noreferrer\" (which implies rel=\"noopener\") is a security risk in older browsers: see https://mathiasbynens.github.io/rel-noopener/#recommendations", "noTargetBlankWithoutNoreferrer", {"range": "2371", "text": "2372"}, {"range": "2373", "text": "2372"}, {"range": "2374", "text": "2372"}, {"range": "2375", "text": "2372"}, {"range": "2376", "text": "2372"}, {"range": "2377", "text": "2372"}, {"range": "2378", "text": "2372"}, {"range": "2379", "text": "2372"}, {"range": "2380", "text": "2372"}, {"range": "2381", "text": "2372"}, {"range": "2382", "text": "2372"}, {"range": "2383", "text": "2372"}, {"range": "2384", "text": "2372"}, "'showNotification' is defined but never used.", {"range": "2385", "text": "2372"}, {"range": "2386", "text": "2372"}, "'ToastPlacer' is defined but never used.", "'data' is assigned a value but never used.", "'toastObject' is assigned a value but never used.", "'resource' is assigned a value but never used.", "'TruncateText' is defined but never used.", "'FiEye' is defined but never used.", "'commentOn' is assigned a value but never used.", "'setCommentOn' is assigned a value but never used.", "'keyName' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'close'. Either include it or remove the dependency array. If 'close' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["2387"], "'SearchBar' is defined but never used.", "'navigate' is assigned a value but never used.", "'handleNew' is assigned a value but never used.", "'submenuIconClasses' is assigned a value but never used.", "'getNotificationsbyId' is defined but never used.", "'markAllNotificationAsRead' is defined but never used.", "'useSelector' is defined but never used.", "'axios' is defined but never used.", "'MODAL_CLOSE_TYPES' is defined but never used.", "'_id' is assigned a value but never used.", "'FaceFrownIcon' is defined but never used.", ["2388"], "'UndoIcon' is defined but never used.", "'greenDot' is defined but never used.", "React Hook useEffect has a missing dependency: 'formObj.otpOrigin'. Either include it or remove the dependency array. If 'setPayload' needs the current value of 'formObj.otpOrigin', you can also switch to useReducer instead of useState and read 'formObj.otpOrigin' in the reducer.", ["2389"], "jsx-a11y/alt-text", "img elements must have an alt prop, either with meaningful text, or an empty string for decorative images.", "'Blank' is assigned a value but never used.", "'Integration' is assigned a value but never used.", "'Calendar' is assigned a value but never used.", "'Team' is assigned a value but never used.", "'Bills' is assigned a value but never used.", "'GettingStarted' is assigned a value but never used.", "'DocFeatures' is assigned a value but never used.", "'DocComponents' is assigned a value but never used.", "'RxQuestionMarkCircled' is defined but never used.", "'moment' is assigned a value but never used.", ["2390"], ["2391"], ["2392"], ["2393"], ["2394"], ["2395"], ["2396"], ["2397"], ["2398"], ["2399"], ["2400"], ["2401"], ["2402"], ["2403"], "'TitleCard' is defined but never used.", "'ReadMe' is defined but never used.", "'FeaturesNav' is defined but never used.", "'FeaturesContent' is defined but never used.", ["2404"], "'GettingStartedNav' is defined but never used.", "'GettingStartedContent' is defined but never used.", ["2405"], ["2406"], ["2407"], "React Hook useEffect has missing dependencies: 'applySearch' and 'removeAppliedFilter'. Either include them or remove the dependency array. If 'applySearch' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["2408"], "React Hook useCallback has an unnecessary dependency: 'navigate'. Either exclude it or remove the dependency array.", ["2409"], "default-case", "Expected a default case.", "SwitchStatement", "missingDefaultCase", "React Hook useEffect has missing dependencies: 'RoleTypeIsUser', 'activeRole?.permissions', and 'roleId'. Either include them or remove the dependency array.", ["2410"], "React Hook useEffect has missing dependencies: 'isEditor', 'isManager', 'isPublisher', and 'isVerifier'. Either include them or remove the dependency array.", ["2411"], ["2412"], "React Hook useEffect has a missing dependency: 'RoleTypeIsUser'. Either include it or remove the dependency array.", ["2413"], "'relationType' is assigned a value but never used.", "'subPage' is assigned a value but never used.", "'subOfSubPage' is assigned a value but never used.", "'AmountStats' is defined but never used.", "'PageStats' is defined but never used.", "'UsersIcon' is defined but never used.", "'CircleStackIcon' is defined but never used.", "'CreditCardIcon' is defined but never used.", "'UserChannels' is defined but never used.", "'LineChart' is defined but never used.", "'BarChart' is defined but never used.", "'DashboardTopBar' is defined but never used.", "'DoughnutChart' is defined but never used.", "'updateDashboardPeriod' is assigned a value but never used.", "no-useless-escape", "Unnecessary escape character: \\/.", "Literal", "unnecessaryEscape", ["2414", "2415"], "eqeqeq", "Expected '===' and instead saw '=='.", "BinaryExpression", "unexpected", "'originalLogs' is assigned a value but never used.", "'filteredLogs' is assigned a value but never used.", "'handleSearchInput' is defined but never used.", "React Hook useEffect has missing dependencies: 'endDate' and 'startDate'. Either include them or remove the dependency array.", ["2416"], "'InputText' is defined but never used.", "'TextAreaInput' is defined but never used.", "'ToggleSwitch' is defined but never used.", "'handleEmailToggle' is assigned a value but never used.", "'handleDelete' is assigned a value but never used.", "'FallBackLoader' is defined but never used.", "React Hook useEffect has a missing dependency: 'applySearch'. Either include it or remove the dependency array. If 'applySearch' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["2417"], "'updateToastify' is defined but never used.", ["2418"], "'setMembers' is assigned a value but never used.", "'setBills' is assigned a value but never used.", "'FaAngleDown' is defined but never used.", "'formatTimestamp' is defined but never used.", "'fetchedData' is assigned a value but never used.", "'toggleIndex' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'normalUserState'. Either include it or remove the dependency array.", ["2419"], "no-throw-literal", "Expected an error object to be thrown.", "ThrowStatement", "object", "'Switch' is defined but never used.", "'pageStages' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'onClose'. Either include it or remove the dependency array. If 'onClose' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["2420"], "React Hook useEffect has a missing dependency: 'resourceId'. Either include it or remove the dependency array.", ["2421"], "The ref value 'divRef.current' will likely have changed by the time this effect cleanup function runs. If this ref points to a node rendered by React, copy 'divRef.current' to a variable inside the effect, and use that variable in the cleanup function.", "React Hook useEffect has a missing dependency: 'setOnView'. Either include it or remove the dependency array. If 'setOnView' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["2422"], "'runToast' is defined but never used.", "'screen' is assigned a value but never used.", "'isCollapsed' is assigned a value but never used.", "'isSmall' is assigned a value but never used.", "'showVersions' is assigned a value but never used.", ["2423"], "array-callback-return", "Array.prototype.map() expects a value to be returned at the end of arrow function.", "ArrowFunctionExpression", "expectedAtEnd", "React Hook useEffect has missing dependencies: 'activeRoleId' and 'superUser'. Either include them or remove the dependency array.", ["2424"], "React Hook useEffect has missing dependencies: 'dispatch', 'isManager', and 'preview'. Either include them or remove the dependency array.", ["2425"], "'Popups' is defined but never used.", "'subRoutesList' is assigned a value but never used.", ["2426"], "React Hook useEffect has missing dependencies: 'dispatch' and 'resourceType'. Either include them or remove the dependency array.", ["2427"], "'getContent' is defined but never used.", "'setPlatform' is defined but never used.", "React Hook useEffect has a missing dependency: 'removeAppliedFilter'. Either include it or remove the dependency array.", ["2428"], "'userPermissionsSet' is assigned a value but never used.", "'originalVersions' is assigned a value but never used.", "'showDetailsModal' is assigned a value but never used.", "'setShowDetailsModal' is assigned a value but never used.", "'searchValue' is assigned a value but never used.", "'setSearchValue' is assigned a value but never used.", "'debounceSearchValue' is assigned a value but never used.", "'setDebounceValue' is assigned a value but never used.", "'isManager' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'currentResource'. Either include it or remove the dependency array. You can also do a functional update 'setCurrentResource(c => ...)' if you only need 'currentResource' in the 'setCurrentResource' call.", ["2429"], ["2430"], ["2431"], "'userRole' is assigned a value but never used.", ["2432"], "React Hook useEffect has missing dependencies: 'isManager', 'isPublisher', and 'isVerifier'. Either include them or remove the dependency array.", ["2433"], ["2434"], ["2435"], "'currMonth' is assigned a value but never used.", "Expected '!==' and instead saw '!='.", "React Hook useEffect has missing dependencies: 'options' and 'switchToggles'. Either include them or remove the dependency array. If 'switchToggles' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["2436"], "'titleLan' is assigned a value but never used.", "'getRoleById' is defined but never used.", "'SkeletonLoader' is defined but never used.", "'fetchedRole' is assigned a value but never used.", "'setFetchedRole' is assigned a value but never used.", "'emptyOfObject' is assigned a value but never used.", "'isValidDateTime' is defined but never used.", ["2437"], "'getDescStyle' is assigned a value but never used.", "no-empty-pattern", "Unexpected empty object pattern.", "ObjectPattern", "'periodOptions' is assigned a value but never used.", "'PermissionOptions' is assigned a value but never used.", "'setPermissionOptions' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'activeRole?.permissions' and 'role?.roleTypeId'. Either include them or remove the dependency array. If 'setRoleData' needs the current value of 'role.roleTypeId', you can also switch to useReducer instead of useState and read 'role.roleTypeId' in the reducer.", ["2438"], "no-dupe-keys", "Duplicate key 'name'.", "ObjectExpression", "Duplicate key 'selectedRoletype'.", "React Hook useEffect has missing dependencies: 'freshObject' and 'role'. Either include them or remove the dependency array.", ["2439"], "React Hook useEffect has missing dependencies: 'activeRole?.permissions' and 'role?.roleTypeId'. Either include them or remove the dependency array. You can also replace multiple useState variables with useReducer if 'setRoleData' needs the current value of 'activeRole.permissions'.", ["2440"], "React Hook useEffect has a missing dependency: 'modalClose'. Either include it or remove the dependency array.", ["2441"], "'userIcon' is defined but never used.", ["2442"], ["2443"], "'InputFileForm' is defined but never used.", "'dummy' is defined but never used.", "'errorMessageRoles' is assigned a value but never used.", "'fetchedUser' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'initialUserState'. Either include it or remove the dependency array.", ["2444"], "React Hook useEffect has a missing dependency: 'onCloseModal'. Either include it or remove the dependency array.", ["2445"], "React Hook useEffect has a missing dependency: 'closeThisPopup'. Either include it or remove the dependency array.", ["2446"], "React Hook useEffect has a missing dependency: 'closeModal'. Either include it or remove the dependency array.", ["2447"], "'tooPast' is assigned a value but never used.", ["2448"], "'Globe' is defined but never used.", "'Calendar' is defined but never used.", "'temp' is defined but never used.", "React Hook useEffect has a missing dependency: 'setClose'. Either include it or remove the dependency array. If 'setClose' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["2449"], "'tempContent' is defined but never used.", "'updateMainContent' is defined but never used.", "'isTablet' is assigned a value but never used.", "'isPhone' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'closeButton'. Either include it or remove the dependency array.", ["2450"], ["2451"], "no-duplicate-case", "Duplicate case label.", "SwitchCase", ["2452"], "'error' is assigned a value but never used.", "'activeRole' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'saveTheDraft'. Either include it or remove the dependency array.", ["2453"], "React Hook useEffect has a missing dependency: 'savedInitialState'. Either include it or remove the dependency array.", ["2454"], "'removeImages' is defined but never used.", "'updateSpecificContent' is defined but never used.", "'fileURL' is assigned a value but never used.", "'ScrollableParagraph' is defined but never used.", "'ProjectSlider' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'swiperInstance'. Either include it or remove the dependency array.", ["2455"], "'handleSwiperInit' is assigned a value but never used.", "no-sequences", "Unexpected use of comma operator.", "SequenceExpression", "unexpectedCommaExpression", "'ScrollableHTML' is defined but never used.", "'projectPageData' is defined but never used.", "'fontLight' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'tabIndex'. Either include it or remove the dependency array.", ["2456"], "jsx-a11y/img-redundant-alt", "Redundant alt attribute. Screen-readers already announce `img` tags as an image. You don’t need to use the words `image`, `photo,` or `picture` (or any specified custom words) in the alt prop.", "'setActiveTab' is assigned a value but never used.", "'filterMarketItems' is assigned a value but never used.", "'visibleMarketItemsCount' is assigned a value but never used.", ["2457"], "'Arrow' is defined but never used.", "'isModal' is assigned a value but never used.", "'selectedJob' is assigned a value but never used.", "'searchTerm' is assigned a value but never used.", "'isMounted' is assigned a value but never used.", ["2458"], "'newsBlogs' is defined but never used.", "no-mixed-operators", "Unexpected mix of '||' and '&&'. Use parentheses to clarify the intended order of operations.", "LogicalExpression", "unexpectedMixedOperator", "'services' is defined but never used.", "'content' is defined but never used.", "'testimonials' is defined but never used.", "'structureOfTestimony' is defined but never used.", "'isComputer' is assigned a value but never used.", "'ModalPortal' is defined but never used.", "'handleFileChange' is assigned a value but never used.", ["2459"], "'urlSection' is assigned a value but never used.", "'liveUrlSection' is assigned a value but never used.", "'liveDescriptionSection' is assigned a value but never used.", "'blueCheckIcon' is defined but never used.", "'structureOfNewsDetails' is defined but never used.", "'ProjectDetailPage' is defined but never used.", "'org_chart' is defined but never used.", "'slug' is assigned a value but never used.", "'aboutUsIcons' is defined but never used.", "'tempArr' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'type'. Either include it or remove the dependency array.", ["2460"], "'Pagination' is defined but never used.", "'isValidating' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'validateAllFields'. Either include it or remove the dependency array.", ["2461"], "'isLast' is assigned a value but never used.", "'setCurrentId' is assigned a value but never used.", "'setIsValidating' is assigned a value but never used.", ["2462"], "'MultiSelectPro' is defined but never used.", ["2463"], "'currentId' is assigned a value but never used.", "'isEditor' is assigned a value but never used.", "'mainVideo' is assigned a value but never used.", ["2464"], ["2465"], ["2466"], ["2467"], ["2468"], ["2469"], ["2470"], "'getResources' is defined but never used.", ["2471"], ["2472"], ["2473"], ["2474"], "'updateTheProjectSummaryList' is defined but never used.", "'validateArray' is assigned a value but never used.", ["2475"], ["2476"], "The 'projectInforCard' logical expression could make the dependencies of useCallback Hook (at line 173) change on every render. To fix this, wrap the initialization of 'projectInforCard' in its own useMemo() Hook.", "VariableDeclarator", "The 'descriptionSection' logical expression could make the dependencies of useCallback Hook (at line 173) change on every render. To fix this, wrap the initialization of 'descriptionSection' in its own useMemo() Hook.", "React Hook useEffect has a missing dependency: 'slug'. Either include it or remove the dependency array.", ["2477"], ["2478"], "Duplicate key 'maxLength'.", ["2479"], "'latestNews' is assigned a value but never used.", ["2480"], ["2481"], ["2482"], "The 'context' logical expression could make the dependencies of useCallback Hook (at line 157) change on every render. To fix this, wrap the initialization of 'context' in its own useMemo() Hook.", "'thumbIcon' is assigned a value but never used.", ["2483"], "'cond' is defined but never used.", "no-unreachable", "Unreachable code.", "ReturnStatement", "unreachableCode", ["2484"], "no-eval", "eval can be harmful.", ["2485", "2486"], ["2487", "2488"], "'oo_tr' is defined but never used.", ["2489", "2490"], "'oo_tx' is defined but never used.", ["2491", "2492"], "'oo_ts' is defined but never used.", ["2493", "2494"], "'oo_te' is defined but never used.", ["2495", "2496"], "unicorn/no-abusive-eslint-disable", "Definition for rule 'unicorn/no-abusive-eslint-disable' was not found.", ["2497", "2498"], "eslint-comments/disable-enable-pair", "Definition for rule 'eslint-comments/disable-enable-pair' was not found.", ["2499", "2500"], "eslint-comments/no-unlimited-disable", "Definition for rule 'eslint-comments/no-unlimited-disable' was not found.", ["2501", "2502"], "eslint-comments/no-aggregating-enable", "Definition for rule 'eslint-comments/no-aggregating-enable' was not found.", ["2503", "2504"], "eslint-comments/no-duplicate-disable", "Definition for rule 'eslint-comments/no-duplicate-disable' was not found.", ["2505", "2506"], "eslint-comments/no-unused-disable", "Definition for rule 'eslint-comments/no-unused-disable' was not found.", ["2507", "2508"], "eslint-comments/no-unused-enable", "Definition for rule 'eslint-comments/no-unused-enable' was not found.", ["2509", "2510"], "'updateImages' is defined but never used.", "'updateAList' is defined but never used.", "'updateSubServiceDetailsPointsArray' is defined but never used.", "React Hook useMemo has an unnecessary dependency: 'outOfEditing'. Either exclude it or remove the dependency array.", ["2511"], "'operation' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'currentPath', 'dispatch', 'language', 'options', 'projectId', 'referenceOriginal.dir', 'referenceOriginal.index', 'section', 'sectionIndex', 'selectedOptions', and 'titleLan'. Either include them or remove the dependency array.", ["2512"], "React Hook useEffect has a missing dependency: 'showOptions'. Either include it or remove the dependency array.", ["2513"], "'Upload' is defined but never used.", "'FileText' is defined but never used.", "'fileName' is assigned a value but never used.", "'clearFile' is assigned a value but never used.", ["2514"], ["2515"], ["2516"], "React Hook useEffect has missing dependencies: 'dispatch', 'options', 'sectionIndex', and 'selectedOptions'. Either include them or remove the dependency array.", ["2517"], ["2518"], "'setFileURL' is assigned a value but never used.", ["2519"], "'FiEdit' is defined but never used.", "'HiOutlineChatAlt2' is defined but never used.", ["2520"], "'current' is assigned a value but never used.", ["2521"], {"desc": "2522", "fix": "2523"}, {"desc": "2524", "fix": "2525"}, {"desc": "2526", "fix": "2527"}, {"desc": "2528", "fix": "2529"}, {"desc": "2530", "fix": "2531"}, {"desc": "2532", "fix": "2533"}, {"desc": "2534", "fix": "2535"}, {"desc": "2536", "fix": "2537"}, [1291, 1291], " rel=\"noreferrer\"", [1398, 1398], [1501, 1501], [1600, 1600], [1698, 1698], [1804, 1804], [1915, 1915], [3265, 3265], [3968, 3968], [4441, 4441], [6319, 6319], [7353, 7353], [7707, 7707], [4922, 4922], [6079, 6079], {"desc": "2538", "fix": "2539"}, {"desc": "2524", "fix": "2540"}, {"desc": "2532", "fix": "2541"}, {"desc": "2524", "fix": "2542"}, {"desc": "2524", "fix": "2543"}, {"desc": "2524", "fix": "2544"}, {"desc": "2524", "fix": "2545"}, {"desc": "2524", "fix": "2546"}, {"desc": "2524", "fix": "2547"}, {"desc": "2524", "fix": "2548"}, {"desc": "2524", "fix": "2549"}, {"desc": "2524", "fix": "2550"}, {"desc": "2524", "fix": "2551"}, {"desc": "2524", "fix": "2552"}, {"desc": "2524", "fix": "2553"}, {"desc": "2524", "fix": "2554"}, {"desc": "2524", "fix": "2555"}, {"desc": "2524", "fix": "2556"}, {"desc": "2524", "fix": "2557"}, {"desc": "2524", "fix": "2558"}, {"desc": "2559", "fix": "2560"}, {"desc": "2561", "fix": "2562"}, {"desc": "2563", "fix": "2564"}, {"desc": "2565", "fix": "2566"}, {"desc": "2567", "fix": "2568"}, {"desc": "2569", "fix": "2570"}, {"desc": "2571", "fix": "2572"}, {"messageId": "2573", "fix": "2574", "desc": "2575"}, {"messageId": "2576", "fix": "2577", "desc": "2578"}, {"desc": "2579", "fix": "2580"}, {"desc": "2581", "fix": "2582"}, {"desc": "2581", "fix": "2583"}, {"desc": "2584", "fix": "2585"}, {"desc": "2586", "fix": "2587"}, {"desc": "2588", "fix": "2589"}, {"desc": "2590", "fix": "2591"}, {"desc": "2563", "fix": "2592"}, {"desc": "2593", "fix": "2594"}, {"desc": "2595", "fix": "2596"}, {"desc": "2524", "fix": "2597"}, {"desc": "2598", "fix": "2599"}, {"desc": "2600", "fix": "2601"}, {"desc": "2602", "fix": "2603"}, {"desc": "2604", "fix": "2605"}, {"desc": "2561", "fix": "2606"}, {"desc": "2607", "fix": "2608"}, {"desc": "2609", "fix": "2610"}, {"desc": "2569", "fix": "2611"}, {"desc": "2571", "fix": "2612"}, {"desc": "2613", "fix": "2614"}, {"desc": "2586", "fix": "2615"}, {"desc": "2616", "fix": "2617"}, {"desc": "2618", "fix": "2619"}, {"desc": "2616", "fix": "2620"}, {"desc": "2621", "fix": "2622"}, {"desc": "2586", "fix": "2623"}, {"desc": "2586", "fix": "2624"}, {"desc": "2625", "fix": "2626"}, {"desc": "2627", "fix": "2628"}, {"desc": "2629", "fix": "2630"}, {"desc": "2631", "fix": "2632"}, {"desc": "2524", "fix": "2633"}, {"desc": "2634", "fix": "2635"}, {"desc": "2636", "fix": "2637"}, {"desc": "2638", "fix": "2639"}, {"desc": "2524", "fix": "2640"}, {"desc": "2641", "fix": "2642"}, {"desc": "2643", "fix": "2644"}, {"desc": "2645", "fix": "2646"}, {"desc": "2647", "fix": "2648"}, {"desc": "2645", "fix": "2649"}, {"desc": "2524", "fix": "2650"}, {"desc": "2524", "fix": "2651"}, {"desc": "2652", "fix": "2653"}, {"desc": "2654", "fix": "2655"}, {"desc": "2656", "fix": "2657"}, {"desc": "2654", "fix": "2658"}, {"desc": "2656", "fix": "2659"}, {"desc": "2654", "fix": "2660"}, {"desc": "2524", "fix": "2661"}, {"kind": "2662", "justification": "2663"}, {"desc": "2656", "fix": "2664"}, {"desc": "2654", "fix": "2665"}, {"desc": "2524", "fix": "2666"}, {"desc": "2656", "fix": "2667"}, {"desc": "2668", "fix": "2669"}, {"desc": "2668", "fix": "2670"}, {"desc": "2668", "fix": "2671"}, {"desc": "2672", "fix": "2673"}, {"desc": "2524", "fix": "2674"}, {"desc": "2675", "fix": "2676"}, {"desc": "2675", "fix": "2677"}, {"desc": "2668", "fix": "2678"}, {"desc": "2679", "fix": "2680"}, {"desc": "2654", "fix": "2681"}, {"kind": "2662", "justification": "2663"}, {"desc": "2668", "fix": "2682"}, {"kind": "2662", "justification": "2663"}, {"kind": "2662", "justification": "2663"}, {"kind": "2662", "justification": "2663"}, {"kind": "2662", "justification": "2663"}, {"kind": "2662", "justification": "2663"}, {"kind": "2662", "justification": "2663"}, {"kind": "2662", "justification": "2663"}, {"kind": "2662", "justification": "2663"}, {"kind": "2662", "justification": "2663"}, {"kind": "2662", "justification": "2663"}, {"kind": "2662", "justification": "2663"}, {"kind": "2662", "justification": "2663"}, {"kind": "2662", "justification": "2663"}, {"kind": "2662", "justification": "2663"}, {"kind": "2662", "justification": "2663"}, {"kind": "2662", "justification": "2663"}, {"kind": "2662", "justification": "2663"}, {"kind": "2662", "justification": "2663"}, {"kind": "2662", "justification": "2663"}, {"kind": "2662", "justification": "2663"}, {"kind": "2662", "justification": "2663"}, {"kind": "2662", "justification": "2663"}, {"kind": "2662", "justification": "2663"}, {"kind": "2662", "justification": "2663"}, {"kind": "2662", "justification": "2663"}, {"kind": "2662", "justification": "2663"}, {"kind": "2662", "justification": "2663"}, {"desc": "2563", "fix": "2683"}, {"desc": "2684", "fix": "2685"}, {"desc": "2686", "fix": "2687"}, {"desc": "2684", "fix": "2688"}, {"desc": "2686", "fix": "2689"}, {"desc": "2563", "fix": "2690"}, {"desc": "2691", "fix": "2692"}, {"desc": "2686", "fix": "2693"}, {"desc": "2524", "fix": "2694"}, {"desc": "2581", "fix": "2695"}, {"desc": "2656", "fix": "2696"}, "Update the dependencies array to be: [dispatch, newNotificationMessage, newNotificationStatus]", {"range": "2697", "text": "2698"}, "Update the dependencies array to be: [dispatch]", {"range": "2699", "text": "2700"}, "Update the dependencies array to be: [pageTitle, scrollContainerRef]", {"range": "2701", "text": "2702"}, "Update the dependencies array to be: [location.pathname, navigate]", {"range": "2703", "text": "2704"}, "Update the dependencies array to be: [formObj, navigate]", {"range": "2705", "text": "2706"}, "Update the dependencies array to be: [formObj.otpOrigin]", {"range": "2707", "text": "2708"}, "Update the dependencies array to be: [navigate]", {"range": "2709", "text": "2710"}, "Update the dependencies array to be: [formObj]", {"range": "2711", "text": "2712"}, "Update the dependencies array to be: [close]", {"range": "2713", "text": "2714"}, {"range": "2715", "text": "2700"}, {"range": "2716", "text": "2708"}, {"range": "2717", "text": "2700"}, {"range": "2718", "text": "2700"}, {"range": "2719", "text": "2700"}, {"range": "2720", "text": "2700"}, {"range": "2721", "text": "2700"}, {"range": "2722", "text": "2700"}, {"range": "2723", "text": "2700"}, {"range": "2724", "text": "2700"}, {"range": "2725", "text": "2700"}, {"range": "2726", "text": "2700"}, {"range": "2727", "text": "2700"}, {"range": "2728", "text": "2700"}, {"range": "2729", "text": "2700"}, {"range": "2730", "text": "2700"}, {"range": "2731", "text": "2700"}, {"range": "2732", "text": "2700"}, {"range": "2733", "text": "2700"}, "Update the dependencies array to be: [isEditor, isManager, navigate]", {"range": "2734", "text": "2735"}, "Update the dependencies array to be: [applySearch, removeAppliedFilter, searchText]", {"range": "2736", "text": "2737"}, "Update the dependencies array to be: []", {"range": "2738", "text": "2739"}, "Update the dependencies array to be: [activeRole?.id, permission, filter, debouncedValue, currentPage, random, activeRole?.permissions, roleId, RoleTypeIsUser]", {"range": "2740", "text": "2741"}, "Update the dependencies array to be: [activeRole.id, isEditor, isManager, isPublisher, isVerifier]", {"range": "2742", "text": "2743"}, "Update the dependencies array to be: [navigate, noneCanSee]", {"range": "2744", "text": "2745"}, "Update the dependencies array to be: [RoleTypeIsUser, activeRole]", {"range": "2746", "text": "2747"}, "removeEscape", {"range": "2748", "text": "2663"}, "Remove the `\\`. This maintains the current functionality.", "escape<PERSON><PERSON><PERSON><PERSON>", {"range": "2749", "text": "2750"}, "Replace the `\\` with `\\\\` to include the actual backslash character.", "Update the dependencies array to be: [currentPage, debouncedValue, filterParam, targetParam, dateFilterReady, startDate, endDate]", {"range": "2751", "text": "2752"}, "Update the dependencies array to be: [applySearch, searchText]", {"range": "2753", "text": "2754"}, {"range": "2755", "text": "2754"}, "Update the dependencies array to be: [normalUserState, user]", {"range": "2756", "text": "2757"}, "Update the dependencies array to be: [onClose]", {"range": "2758", "text": "2759"}, "Update the dependencies array to be: [resourceId]", {"range": "2760", "text": "2761"}, "Update the dependencies array to be: [setOnView]", {"range": "2762", "text": "2763"}, {"range": "2764", "text": "2739"}, "Update the dependencies array to be: [resourceType, resourceTag, randomRender, setRouteList, superUser, activeRoleId]", {"range": "2765", "text": "2766"}, "Update the dependencies array to be: [currentResourceId, dispatch, isManager, preview]", {"range": "2767", "text": "2768"}, {"range": "2769", "text": "2700"}, "Update the dependencies array to be: [currentId, dispatch, isManager, resourceTag, resourceType]", {"range": "2770", "text": "2771"}, "Update the dependencies array to be: [removeAppliedFilter, searchText]", {"range": "2772", "text": "2773"}, "Update the dependencies array to be: [currentResource]", {"range": "2774", "text": "2775"}, "Update the dependencies array to be: [currentResourceId, dispatch, preview]", {"range": "2776", "text": "2777"}, {"range": "2778", "text": "2737"}, "Update the dependencies array to be: [RoleTypeIsUser, activeRole?.id, activeRole?.permissions, permission, resourceId, roleId]", {"range": "2779", "text": "2780"}, "Update the dependencies array to be: [activeRole.id, isManager, isPublisher, isVerifier]", {"range": "2781", "text": "2782"}, {"range": "2783", "text": "2745"}, {"range": "2784", "text": "2747"}, "Update the dependencies array to be: [options, switchToggles]", {"range": "2785", "text": "2786"}, {"range": "2787", "text": "2759"}, "Update the dependencies array to be: [activeRole?.permissions, role?.roleTypeId, roleData?.selectedRoletype]", {"range": "2788", "text": "2789"}, "Update the dependencies array to be: [activeRole, freshObject, role]", {"range": "2790", "text": "2791"}, {"range": "2792", "text": "2789"}, "Update the dependencies array to be: [modalClose]", {"range": "2793", "text": "2794"}, {"range": "2795", "text": "2759"}, {"range": "2796", "text": "2759"}, "Update the dependencies array to be: [initialUserState, user]", {"range": "2797", "text": "2798"}, "Update the dependencies array to be: [onClose, onCloseModal]", {"range": "2799", "text": "2800"}, "Update the dependencies array to be: [closeThisPopup]", {"range": "2801", "text": "2802"}, "Update the dependencies array to be: [closeModal, display]", {"range": "2803", "text": "2804"}, {"range": "2805", "text": "2700"}, "Update the dependencies array to be: [setClose]", {"range": "2806", "text": "2807"}, "Update the dependencies array to be: [closeButton, display, setOn]", {"range": "2808", "text": "2809"}, "Update the dependencies array to be: [preAssignedUsers, resourceId]", {"range": "2810", "text": "2811"}, {"range": "2812", "text": "2700"}, "Update the dependencies array to be: [ReduxState, autoSave, saveTheDraft]", {"range": "2813", "text": "2814"}, "Update the dependencies array to be: [ReduxState.present?.content, savedInitialState]", {"range": "2815", "text": "2816"}, "Update the dependencies array to be: [language, swiperInstance]", {"range": "2817", "text": "2818"}, "Update the dependencies array to be: [activeTab, currentContent, tabIndex]", {"range": "2819", "text": "2820"}, {"range": "2821", "text": "2818"}, {"range": "2822", "text": "2700"}, {"range": "2823", "text": "2700"}, "Update the dependencies array to be: [imagesByResource, random, resourceId, type]", {"range": "2824", "text": "2825"}, "Update the dependencies array to be: [currentContent, language, validateAllFields]", {"range": "2826", "text": "2827"}, "Update the dependencies array to be: [content, language, validateAllFields]", {"range": "2828", "text": "2829"}, {"range": "2830", "text": "2827"}, {"range": "2831", "text": "2829"}, {"range": "2832", "text": "2827"}, {"range": "2833", "text": "2700"}, "directive", "", {"range": "2834", "text": "2829"}, {"range": "2835", "text": "2827"}, {"range": "2836", "text": "2700"}, {"range": "2837", "text": "2829"}, "Update the dependencies array to be: [content, language, context, validateAllFields]", {"range": "2838", "text": "2839"}, {"range": "2840", "text": "2839"}, {"range": "2841", "text": "2839"}, "Update the dependencies array to be: [currentContent, language, careerIndex, validateAllFields]", {"range": "2842", "text": "2843"}, {"range": "2844", "text": "2700"}, "Update the dependencies array to be: [slug]", {"range": "2845", "text": "2846"}, {"range": "2847", "text": "2846"}, {"range": "2848", "text": "2839"}, "Update the dependencies array to be: [id, slug]", {"range": "2849", "text": "2850"}, {"range": "2851", "text": "2827"}, {"range": "2852", "text": "2839"}, {"range": "2853", "text": "2739"}, "Update the dependencies array to be: [currentPath, dispatch, language, options, projectId, random, referenceOriginal.dir, referenceOriginal.index, section, sectionIndex, selectedOptions, titleLan]", {"range": "2854", "text": "2855"}, "Update the dependencies array to be: [options, showOptions]", {"range": "2856", "text": "2857"}, {"range": "2858", "text": "2855"}, {"range": "2859", "text": "2857"}, {"range": "2860", "text": "2739"}, "Update the dependencies array to be: [dispatch, options, random, sectionIndex, selectedOptions]", {"range": "2861", "text": "2862"}, {"range": "2863", "text": "2857"}, {"range": "2864", "text": "2700"}, {"range": "2865", "text": "2754"}, {"range": "2866", "text": "2829"}, [1679, 1703], "[dispatch, newNotificationMessage, newNotificationStatus]", [2011, 2013], "[dispatch]", [1148, 1159], "[pageTitle, scrollContainerRef]", [1376, 1378], "[location.pathname, navigate]", [5072, 5074], "[formObj, navigate]", [5510, 5512], "[formObj.otp<PERSON><PERSON>in]", [2698, 2700], "[navigate]", [3065, 3067], "[formObj]", [3857, 3859], "[close]", [419, 421], [1494, 1496], [368, 370], [349, 351], [343, 345], [345, 347], [357, 359], [349, 351], [391, 393], [338, 340], [392, 394], [350, 352], [371, 373], [341, 343], [354, 356], [396, 398], [684, 686], [683, 685], [577, 579], [1044, 1065], "[is<PERSON><PERSON><PERSON>, is<PERSON><PERSON><PERSON>, navigate]", [2308, 2320], "[apply<PERSON><PERSON><PERSON>, removeAppliedFilter, searchText]", [7276, 7286], "[]", [10276, 10348], "[activeRole?.id, permission, filter, debouncedValue, currentPage, random, activeRole?.permissions, roleId, RoleTypeIsUser]", [10566, 10582], "[activeRole.id, isE<PERSON>or, isManager, isPublisher, isVerifier]", [10679, 10691], "[navigate, noneCanSee]", [10871, 10883], "[RoleTypeIsUser, activeRole]", [1720, 1721], [1720, 1720], "\\", [19162, 19265], "[currentPage, debouncedValue, filterParam, targetParam, dateFilterReady, startDate, endDate]", [1813, 1825], "[applySearch, searchText]", [2023, 2035], [3288, 3294], "[normalUserState, user]", [5269, 5271], "[onClose]", [6521, 6523], "[resourceId]", [511, 513], "[set<PERSON>n<PERSON>iew]", [3648, 3658], [5766, 5821], "[resourceType, resourceTag, randomRender, setRouteList, superUser, activeRoleId]", [7383, 7402], "[currentResourceId, dispatch, isManager, preview]", [3360, 3362], [6507, 6542], "[currentId, dispatch, isManager, resourceTag, resourceType]", [2561, 2573], "[removeApplied<PERSON><PERSON><PERSON>, searchText]", [9602, 9604], "[currentResource]", [11134, 11162], "[currentResourceId, dispatch, preview]", [2079, 2091], [10250, 10289], "[RoleTypeIsUser, activeRole?.id, activeRole?.permissions, permission, resourceId, roleId]", [10496, 10512], "[activeRole.id, isManager, isPublisher, isVerifier]", [10625, 10637], [10846, 10858], [778, 780], "[options, switchToggles]", [2536, 2538], [4003, 4030], "[activeRole?.permissions, role?.roleTypeId, roleData?.selectedRoletype]", [6784, 6796], "[activeRole, freshObject, role]", [7091, 7119], [7565, 7567], "[modalClose]", [1343, 1345], [1497, 1499], [4402, 4408], "[initialUserState, user]", [5373, 5382], "[onClose, onCloseModal]", [1329, 1331], "[closeThis<PERSON>opup]", [3200, 3209], "[closeModal, display]", [2769, 2771], [878, 880], "[setClose]", [6972, 6988], "[closeButton, display, setOn]", [7556, 7574], "[preAssignedUsers, resourceId]", [9397, 9399], [16695, 16717], "[ReduxState, autoSave, saveTheDraft]", [17046, 17075], "[ReduxState.present?.content, savedInitialState]", [3085, 3095], "[language, swiperInstance]", [1847, 1874], "[activeTab, currentContent, tabIndex]", [3470, 3480], [3147, 3149], [1122, 1124], [7577, 7615], "[imagesByResource, random, resourceId, type]", [4707, 4733], "[currentContent, language, validateAllFields]", [14873, 14892], "[content, language, validateAllFields]", [5278, 5304], [5784, 5803], [3931, 3957], [5572, 5574], [3579, 3598], [3753, 3779], [693, 695], [5397, 5416], [7710, 7738], "[content, language, context, validateAllFields]", [7480, 7508], [6247, 6275], [6362, 6401], "[currentContent, language, careerIndex, validateAllFields]", [7095, 7097], [11340, 11342], "[slug]", [8004, 8006], [5802, 5830], [6969, 6973], "[id, slug]", [3345, 3371], [7442, 7470], [7766, 7780], [5431, 5439], "[currentPath, dispatch, language, options, projectId, random, referenceOriginal.dir, referenceOriginal.index, section, sectionIndex, selectedOptions, titleLan]", [5648, 5657], "[options, showOptions]", [5919, 5927], [6168, 6177], [7822, 7836], [4726, 4734], "[dispatch, options, random, sectionIndex, selectedOptions]", [4888, 4897], [364, 366], [1596, 1608], [4984, 5003]]