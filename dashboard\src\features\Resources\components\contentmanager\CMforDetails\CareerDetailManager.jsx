import { useEffect, useState } from "react"
import FileUploader from "../../../../../components/Input/InputFileUploader"
import ContentSection from "../../breakUI/ContentSections"
import { useDispatch } from "react-redux"
import { updateMainContent, updateTheProjectSummaryList } from "../../../../common/homeContentSlice"
import content from "../../websiteComponent/content.json"
import DynamicContentSection from "../../breakUI/DynamicContentSection"

const CareerDetailManager = ({ careerId, currentContent, currentPath, language }) => {
    const dispatch = useDispatch();
    const careerIndex = currentContent?.findIndex(e => e.id == careerId)

    // Validation states
    const [validationErrors, setValidationErrors] = useState({})
    const [isValidating, setIsValidating] = useState(false)

    // Function to clear specific validation error
    const clearValidationError = (errorKey) => {
        setValidationErrors(prev => {
            const newErrors = { ...prev }
            delete newErrors[errorKey]
            return newErrors
        })
    }

    // Validation functions
    const validateField = (value, fieldName) => {
        if (!value || (typeof value === 'string' && value.trim() === '')) {
            return "Required"
        }
        return null
    }

    const validateImage = (imageUrl, fieldName) => {
        if (!imageUrl || imageUrl.trim() === '') {
            return "Required"
        }
        return null
    }

    const validateArray = (array, fieldName, minLength = 1) => {
        if (!array || array.length < minLength) {
            return "Required"
        }
        return null
    }

    // Comprehensive validation function
    const validateAllFields = () => {
        setIsValidating(true)
        const errors = {}

        const career = currentContent?.[careerIndex]

        // Banner section validation
        const bannerTitle = career?.banner?.title?.[language]
        const bannerSubTitle = career?.banner?.subTitle?.[language]
        const bannerButton = career?.banner?.button?.[language]
        const bannerImage = career?.banner?.images?.[0]?.url

        const bannerTitleError = validateField(bannerTitle, "Banner Title")
        if (bannerTitleError) errors["banner_title"] = bannerTitleError

        const bannerSubTitleError = validateField(bannerSubTitle, "Banner Sub Title")
        if (bannerSubTitleError) errors["banner_subtitle"] = bannerSubTitleError

        const bannerButtonError = validateField(bannerButton, "Banner Button")
        if (bannerButtonError) errors["banner_button"] = bannerButtonError

        const bannerImageError = validateImage(bannerImage, "Banner Image")
        if (bannerImageError) errors["banner_image"] = bannerImageError

        // Left panel sections validation
        const leftPanelSections = career?.jobDetails?.leftPanel?.sections
        if (leftPanelSections && leftPanelSections.length > 0) {
            leftPanelSections.forEach((section, index) => {
                const sectionTitle = section?.title?.[language]
                const sectionContent = section?.content?.[language]

                const sectionTitleError = validateField(sectionTitle, `Left Panel Section ${index + 1} Title`)
                if (sectionTitleError) errors[`left_section_${index}_title`] = sectionTitleError

                const sectionContentError = validateField(sectionContent, `Left Panel Section ${index + 1} Content`)
                if (sectionContentError) errors[`left_section_${index}_content`] = sectionContentError
            })
        }

        // Right panel top section validation
        const rightPanelTitle = career?.jobDetails?.rightPanel?.title?.[language]
        const rightPanelButton = career?.jobDetails?.rightPanel?.button?.[language]

        const rightPanelTitleError = validateField(rightPanelTitle, "Right Panel Title")
        if (rightPanelTitleError) errors["right_panel_title"] = rightPanelTitleError

        const rightPanelButtonError = validateField(rightPanelButton, "Right Panel Button")
        if (rightPanelButtonError) errors["right_panel_button"] = rightPanelButtonError

        // Right panel tailwraps validation
        const tailwraps = career?.jobDetails?.rightPanel?.tailwraps
        if (tailwraps && tailwraps.length > 0) {
            tailwraps.forEach((tailwrap, index) => {
                const tailwrapTitle = tailwrap?.title?.[language]
                const tailwrapDescription = tailwrap?.description?.[language]
                const tailwrapIcon = tailwrap?.images?.[0]?.url

                const tailwrapTitleError = validateField(tailwrapTitle, `Tailwrap ${index + 1} Title`)
                if (tailwrapTitleError) errors[`tailwrap_${index}_title`] = tailwrapTitleError

                const tailwrapDescriptionError = validateField(tailwrapDescription, `Tailwrap ${index + 1} Description`)
                if (tailwrapDescriptionError) errors[`tailwrap_${index}_description`] = tailwrapDescriptionError

                const tailwrapIconError = validateImage(tailwrapIcon, `Tailwrap ${index + 1} Icon`)
                if (tailwrapIconError) errors[`tailwrap_${index}_icon`] = tailwrapIconError
            })
        }

        // Right panel view all button validation
        const viewAllText = career?.jobDetails?.rightPanel?.viewAllButton?.text?.[language]
        const viewAllLink = career?.jobDetails?.rightPanel?.viewAllButton?.link?.[language]

        const viewAllTextError = validateField(viewAllText, "View All Text")
        if (viewAllTextError) errors["view_all_text"] = viewAllTextError

        const viewAllLinkError = validateField(viewAllLink, "View All Link")
        if (viewAllLinkError) errors["view_all_link"] = viewAllLinkError

        // Bottom button validation
        const bottomButton = career?.jobDetails?.button?.[language]
        const bottomButtonError = validateField(bottomButton, "Bottom Button")
        if (bottomButtonError) errors["bottom_button"] = bottomButtonError

        setValidationErrors(errors)
        setIsValidating(false)
        return Object.keys(errors).length === 0
    }

    // Expose validation function globally
    useEffect(() => {
        window.validateCareerDetailContent = validateAllFields;
        return () => {
            delete window.validateCareerDetailContent;
        };
    }, [currentContent, language, careerIndex]);

    const addExtraSummary = () => {
        // dispatch(updateCardAndItemsArray(
        //     {
        //         insert: {
        //             title: {
        //                 ar: "",
        //                 en: ""
        //             },
        //             content: {
        //                 ar: "",
        //                 en: ""
        //             }
        //         },
        //         careerIndex,
        //         context: "careerDetails",
        //         operation: 'add'
        //     }
        // ))
    }

    useEffect(() => {

        dispatch(updateMainContent({ currentPath: "careerDetails", payload: (content?.careerDetails) }))
    }, [])
    return (
        <div>
            <FileUploader id={"CareerDetailsIDReference" + careerId} label={"Rerference doc"} fileName={"Upload your file..."} />
            {/** Hero Banner */}
            <ContentSection
                currentPath={currentPath}
                Heading={"Banner"}
                inputs={[
                    { input: "input", label: "Heading/title", updateType: "title", errorMessage: validationErrors["banner_title"], errorKey: "banner_title" },
                    { input: "input", label: "Description", updateType: "subTitle", maxLength: 23, errorMessage: validationErrors["banner_subtitle"], errorKey: "banner_subtitle" },
                    { input: "input", label: "Button Text", updateType: "button", errorMessage: validationErrors["banner_button"], errorKey: "banner_button" },
                    // { input: "input", label: "Url", updateType: "url" },
                ]}
                inputFiles={[{ label: "Backround Image", id: "careerBanner/" + (careerId), errorMessage: validationErrors["banner_image"], errorKey: "banner_image" }]}
                section={"banner"}
                language={language}
                currentContent={currentContent}
                projectId={careerIndex + 1}
                clearValidationError={clearValidationError}
            />

            {/* left panel */}
            <div className="mt-4 border-b">
                <h3 className={`font-semibold text-[1.25rem] mb-4`}>Job Details Left Panel</h3>
                {
                    currentContent?.[careerIndex]?.jobDetails?.leftPanel?.sections?.map((element, index, a) => {
                        const isLast = index === a.length - 1
                        return (
                            <DynamicContentSection key={index}
                                currentPath={currentPath}
                                subHeading={"Section " + (index + 1)}
                                inputs={[
                                    { input: "input", label: "Title", updateType: "title", errorMessage: validationErrors[`left_section_${index}_title`], errorKey: `left_section_${index}_title` },
                                    { input: "textarea", label: "Description", updateType: "content", errorMessage: validationErrors[`left_section_${index}_content`], errorKey: `left_section_${index}_content` },
                                ]}
                                section={"jobDetails"}
                                subSection={"leftPanel"}
                                subSectionsProMax={"sections"}
                                index={index}
                                language={language}
                                currentContent={currentContent}
                                projectId={careerIndex + 1}
                                careerIndex={careerIndex}
                                careerId={careerId}
                                allowRemoval={true}
                                isBorder={false}
                                clearValidationError={clearValidationError}
                            />
                        )
                    })
                }
                <button className="text-blue-500 cursor-pointer mb-3"
                    onClick={addExtraSummary}
                >Add More Section...</button>
            </div>

            {/* right panel */}
            <div className="mt-4 border-b">
                <h3 className={`font-semibold text-[1.25rem] mb-4`}>Job Details Right Panel</h3>
                <div className="mb-6">
                    <ContentSection
                        currentPath={currentPath}
                        subHeading={"Top Section"}
                        inputs={[
                            { input: "input", label: "Heading/title", updateType: "title", maxLength: 15, errorMessage: validationErrors["right_panel_title"], errorKey: "right_panel_title" },
                            { input: "input", label: "Button Text", updateType: "button", maxLength: 18, errorMessage: validationErrors["right_panel_button"], errorKey: "right_panel_button" },
                            // { input: "input", label: "Url", updateType: "url" },
                        ]}
                        section={"jobDetails"}
                        subSection={"rightPanel"}
                        language={language}
                        currentContent={currentContent}
                        projectId={careerIndex + 1}
                        careerId={careerId}
                        clearValidationError={clearValidationError}
                    />
                </div>
                {
                    currentContent?.[careerIndex]?.jobDetails?.rightPanel?.tailwraps?.map((element, index, a) => {
                        const isLast = index === a.length - 1
                        return (
                            <DynamicContentSection key={index}
                                currentPath={currentPath}
                                subHeading={"Section " + (index + 1)}
                                inputs={[
                                    { input: "input", label: "Title", updateType: "title", maxLength: 15, errorMessage: validationErrors[`tailwrap_${index}_title`], errorKey: `tailwrap_${index}_title` },
                                    { input: "input", label: "Description", updateType: "description", maxLength: 19, errorMessage: validationErrors[`tailwrap_${index}_description`], errorKey: `tailwrap_${index}_description` },
                                ]}
                                inputFiles={[{ label: "icon", id: `careerRightPanel/${careerId}/${index}`, errorMessage: validationErrors[`tailwrap_${index}_icon`], errorKey: `tailwrap_${index}_icon` }]}
                                section={"jobDetails"}
                                subSection={"rightPanel"}
                                subSectionsProMax={"tailwraps"}
                                index={index}
                                language={language}
                                currentContent={currentContent}
                                projectId={careerIndex + 1}
                                careerIndex={careerIndex}
                                careerId={careerId}
                                isBorder={false}
                                clearValidationError={clearValidationError}
                            />
                        )
                    })
                }

                {/* right panel redirection link */}
                <div>
                    <ContentSection
                        currentPath={currentPath}
                        subHeading={"Right panel redirection"}
                        inputs={[
                            { input: "input", label: "Heading/title", updateType: "text", errorMessage: validationErrors["view_all_text"], errorKey: "view_all_text" },
                            { input: "input", label: "Link", updateType: "link", errorMessage: validationErrors["view_all_link"], errorKey: "view_all_link" },
                            // { input: "input", label: "Url", updateType: "url" },
                        ]}
                        section={"jobDetails"}
                        subSection={"rightPanel"}
                        subSectionsProMax={"viewAllButton"}
                        language={language}
                        currentContent={currentContent}
                        projectId={careerIndex + 1}
                        careerId={careerId}
                        clearValidationError={clearValidationError}
                    />
                </div>
            </div>

            {/* the last button */}
            <ContentSection
                currentPath={currentPath}
                subHeading={"Bottom Button"}
                inputs={[
                    { input: "input", label: "Button", updateType: "button", errorMessage: validationErrors["bottom_button"], errorKey: "bottom_button" },
                ]}
                section={"jobDetails"}
                language={language}
                currentContent={currentContent}
                projectId={careerIndex + 1}
                careerId={careerId}
                clearValidationError={clearValidationError}
            />

        </div>
    )
}

export default CareerDetailManager