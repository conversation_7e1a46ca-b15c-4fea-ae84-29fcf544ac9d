/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["pages/404"],{

/***/ "./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[6].oneOf[10].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[6].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[6].oneOf[10].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[6].oneOf[10].use[4]!./src/styles/Error.module.scss":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[6].oneOf[10].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[6].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[6].oneOf[10].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[6].oneOf[10].use[4]!./src/styles/Error.module.scss ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("// Imports\nvar ___CSS_LOADER_API_IMPORT___ = __webpack_require__(/*! ../../node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(true);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \"/* pages/404.module.css */\\n.Error_container__JM7R_ {\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: center;\\n  align-items: center;\\n  height: 100vh;\\n  background-color: #f4f4f4;\\n  text-align: right;\\n  /* Align text to the right for Arabic */\\n  direction: rtl;\\n  /* Enable right-to-left layout */\\n}\\n.Error_container__JM7R_ .Error_title__S7_rW {\\n  font-size: 3rem;\\n  color: var(--secondary);\\n  font-weight: 300;\\n  margin-bottom: 28px;\\n}\\n.Error_container__JM7R_ .Error_description__SvO3k {\\n  margin-top: 28px;\\n  font-size: 1.5rem;\\n  color: var(--black);\\n  margin-bottom: 24px;\\n}\\n.Error_container__JM7R_ .Error_homeLink__Cv6Nd {\\n  padding: 18px 20px 10px 20px;\\n  background-color: var(--primary);\\n  color: var(--white);\\n  border-radius: 5px;\\n  text-decoration: none;\\n  font-size: 1rem;\\n  transition: all 0.3s linear;\\n}\\n.Error_container__JM7R_ .Error_homeLink__Cv6Nd.Error_enVersion__RZRcM {\\n  padding: 13px 20px;\\n}\\n.Error_container__JM7R_ .Error_homeLink__Cv6Nd:hover {\\n  background-color: var(--secondary);\\n}\", \"\",{\"version\":3,\"sources\":[\"webpack://src/styles/Error.module.scss\"],\"names\":[],\"mappings\":\"AAAA,yBAAA;AAEA;EACI,aAAA;EACA,sBAAA;EACA,uBAAA;EACA,mBAAA;EACA,aAAA;EACA,yBAAA;EACA,iBAAA;EACA,uCAAA;EACA,cAAA;EAEA,gCAAA;AADJ;AAEI;EACI,eAAA;EACA,uBAAA;EACA,gBAAA;EACA,mBAAA;AAAR;AAGI;EACI,gBAAA;EACA,iBAAA;EACA,mBAAA;EACA,mBAAA;AADR;AAII;EACI,4BAAA;EACA,gCAAA;EACA,mBAAA;EACA,kBAAA;EACA,qBAAA;EACA,eAAA;EACA,2BAAA;AAFR;AAGQ;EACI,kBAAA;AADZ;AAKI;EACI,kCAAA;AAHR\",\"sourcesContent\":[\"/* pages/404.module.css */\\r\\n\\r\\n.container {\\r\\n    display: flex;\\r\\n    flex-direction: column;\\r\\n    justify-content: center;\\r\\n    align-items: center;\\r\\n    height: 100vh;\\r\\n    background-color: #f4f4f4;\\r\\n    text-align: right;\\r\\n    /* Align text to the right for Arabic */\\r\\n    direction: rtl;\\r\\n\\r\\n    /* Enable right-to-left layout */\\r\\n    .title {\\r\\n        font-size: 3rem;\\r\\n        color: var(--secondary);\\r\\n        font-weight: 300;\\r\\n        margin-bottom: 28px;\\r\\n    }\\r\\n    \\r\\n    .description {\\r\\n        margin-top: 28px;\\r\\n        font-size: 1.5rem;\\r\\n        color: var(--black);\\r\\n        margin-bottom: 24px;\\r\\n    }\\r\\n\\r\\n    .homeLink {\\r\\n        padding: 18px 20px 10px 20px;\\r\\n        background-color: var(--primary);\\r\\n        color: var(--white);\\r\\n        border-radius: 5px;\\r\\n        text-decoration: none;\\r\\n        font-size: 1rem;\\r\\n        transition: all .3s linear;\\r\\n        &.enVersion{\\r\\n            padding: 13px 20px;\\r\\n        }\\r\\n    }\\r\\n\\r\\n    .homeLink:hover {\\r\\n        background-color: var(--secondary);\\r\\n    }\\r\\n}\"],\"sourceRoot\":\"\"}]);\n// Exports\n___CSS_LOADER_EXPORT___.locals = {\n\t\"container\": \"Error_container__JM7R_\",\n\t\"title\": \"Error_title__S7_rW\",\n\t\"description\": \"Error_description__SvO3k\",\n\t\"homeLink\": \"Error_homeLink__Cv6Nd\",\n\t\"enVersion\": \"Error_enVersion__RZRcM\"\n};\nmodule.exports = ___CSS_LOADER_EXPORT___;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[6].oneOf[10].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[6].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[6].oneOf[10].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[6].oneOf[10].use[4]!./src/styles/Error.module.scss\n"));

/***/ }),

/***/ "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5Cakshy%5COneDrive%5CDesktop%5CAkshay%5CLOOP_PROJECTS%5Cshade_cms%5Cwebsite%5Csrc%5Cpages%5C404.js&page=%2F404!":
/*!********************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5Cakshy%5COneDrive%5CDesktop%5CAkshay%5CLOOP_PROJECTS%5Cshade_cms%5Cwebsite%5Csrc%5Cpages%5C404.js&page=%2F404! ***!
  \********************************************************************************************************************************************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/404\",\n      function () {\n        return __webpack_require__(/*! ./src/pages/404.js */ \"./src/pages/404.js\");\n      }\n    ]);\n    if(true) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/404\"])\n      });\n    }\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWNsaWVudC1wYWdlcy1sb2FkZXIuanM/YWJzb2x1dGVQYWdlUGF0aD1DJTNBJTVDVXNlcnMlNUNha3NoeSU1Q09uZURyaXZlJTVDRGVza3RvcCU1Q0Frc2hheSU1Q0xPT1BfUFJPSkVDVFMlNUNzaGFkZV9jbXMlNUN3ZWJzaXRlJTVDc3JjJTVDcGFnZXMlNUM0MDQuanMmcGFnZT0lMkY0MDQhIiwibWFwcGluZ3MiOiI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLG1CQUFPLENBQUMsOENBQW9CO0FBQzNDO0FBQ0E7QUFDQSxPQUFPLElBQVU7QUFDakIsTUFBTSxVQUFVO0FBQ2hCO0FBQ0EsT0FBTztBQUNQO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLz8wYmFjIl0sInNvdXJjZXNDb250ZW50IjpbIlxuICAgICh3aW5kb3cuX19ORVhUX1AgPSB3aW5kb3cuX19ORVhUX1AgfHwgW10pLnB1c2goW1xuICAgICAgXCIvNDA0XCIsXG4gICAgICBmdW5jdGlvbiAoKSB7XG4gICAgICAgIHJldHVybiByZXF1aXJlKFwiLi9zcmMvcGFnZXMvNDA0LmpzXCIpO1xuICAgICAgfVxuICAgIF0pO1xuICAgIGlmKG1vZHVsZS5ob3QpIHtcbiAgICAgIG1vZHVsZS5ob3QuZGlzcG9zZShmdW5jdGlvbiAoKSB7XG4gICAgICAgIHdpbmRvdy5fX05FWFRfUC5wdXNoKFtcIi80MDRcIl0pXG4gICAgICB9KTtcbiAgICB9XG4gICJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5Cakshy%5COneDrive%5CDesktop%5CAkshay%5CLOOP_PROJECTS%5Cshade_cms%5Cwebsite%5Csrc%5Cpages%5C404.js&page=%2F404!\n"));

/***/ }),

/***/ "./src/styles/Error.module.scss":
/*!**************************************!*\
  !*** ./src/styles/Error.module.scss ***!
  \**************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("var api = __webpack_require__(/*! !../../node_modules/next/dist/build/webpack/loaders/next-style-loader/runtime/injectStylesIntoStyleTag.js */ \"./node_modules/next/dist/build/webpack/loaders/next-style-loader/runtime/injectStylesIntoStyleTag.js\");\n            var content = __webpack_require__(/*! !!../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[6].oneOf[10].use[1]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[6].oneOf[10].use[2]!../../node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[6].oneOf[10].use[3]!../../node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[6].oneOf[10].use[4]!./Error.module.scss */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[6].oneOf[10].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[6].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[6].oneOf[10].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[6].oneOf[10].use[4]!./src/styles/Error.module.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function(element) {\n                    // By default, style-loader injects CSS into the bottom\n                    // of <head>. This causes ordering problems between dev\n                    // and prod. To fix this, we render a <noscript> tag as\n                    // an anchor for the styles to be placed before. These\n                    // styles will be applied _before_ <style jsx global>.\n                    // These elements should always exist. If they do not,\n                    // this code should fail.\n                    var anchorElement = document.querySelector(\"#__next_css__DO_NOT_USE__\");\n                    var parentNode = anchorElement.parentNode// Normally <head>\n                    ;\n                    // Each style tag should be placed right before our\n                    // anchor. By inserting before and not after, we do not\n                    // need to track the last inserted element.\n                    parentNode.insertBefore(element, anchorElement);\n                };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\nif (true) {\n  if (!content.locals || module.hot.invalidate) {\n    var isEqualLocals = function isEqualLocals(a, b, isNamedExport) {\n    if (!a && b || a && !b) {\n        return false;\n    }\n    let p;\n    for(p in a){\n        if (isNamedExport && p === \"default\") {\n            continue;\n        }\n        if (a[p] !== b[p]) {\n            return false;\n        }\n    }\n    for(p in b){\n        if (isNamedExport && p === \"default\") {\n            continue;\n        }\n        if (!a[p]) {\n            return false;\n        }\n    }\n    return true;\n};\n    var oldLocals = content.locals;\n\n    module.hot.accept(\n      /*! !!../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[6].oneOf[10].use[1]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[6].oneOf[10].use[2]!../../node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[6].oneOf[10].use[3]!../../node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[6].oneOf[10].use[4]!./Error.module.scss */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[6].oneOf[10].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[6].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[6].oneOf[10].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[6].oneOf[10].use[4]!./src/styles/Error.module.scss\",\n      function () {\n        content = __webpack_require__(/*! !!../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[6].oneOf[10].use[1]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[6].oneOf[10].use[2]!../../node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[6].oneOf[10].use[3]!../../node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[6].oneOf[10].use[4]!./Error.module.scss */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[6].oneOf[10].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[6].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[6].oneOf[10].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[6].oneOf[10].use[4]!./src/styles/Error.module.scss\");\n\n              content = content.__esModule ? content.default : content;\n\n              if (typeof content === 'string') {\n                content = [[module.id, content, '']];\n              }\n\n              if (!isEqualLocals(oldLocals, content.locals)) {\n                module.hot.invalidate();\n\n                return;\n              }\n\n              oldLocals = content.locals;\n\n              update(content);\n      }\n    )\n  }\n\n  module.hot.dispose(function() {\n    update();\n  });\n}\n\nmodule.exports = content.locals || {};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/styles/Error.module.scss\n"));

/***/ }),

/***/ "./src/pages/404.js":
/*!**************************!*\
  !*** ./src/pages/404.js ***!
  \**************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _styles_Error_module_scss__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/styles/Error.module.scss */ \"./src/styles/Error.module.scss\");\n/* harmony import */ var _styles_Error_module_scss__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_styles_Error_module_scss__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dynamic */ \"./node_modules/next/dynamic.js\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dynamic__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _common_404_json__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/common/404.json */ \"./src/common/404.json\");\n/* harmony import */ var _contexts_GlobalContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../contexts/GlobalContext */ \"./src/contexts/GlobalContext.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n// Dynamically import Lottie with ssr: false to prevent SSR issues\nconst Lottie = next_dynamic__WEBPACK_IMPORTED_MODULE_3___default()(()=>__webpack_require__.e(/*! import() */ \"node_modules_react-lottie_dist_index_js\").then(__webpack_require__.bind(__webpack_require__, /*! react-lottie */ \"./node_modules/react-lottie/dist/index.js\")), {\n    loadableGenerated: {\n        modules: [\n            \"pages\\\\404.js -> \" + \"react-lottie\"\n        ]\n    },\n    ssr: false\n});\n_c = Lottie;\n\n\nconst Custom404 = ()=>{\n    var _currentContent_button;\n    _s();\n    const route = (0,next_router__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const globalContext = (0,_contexts_GlobalContext__WEBPACK_IMPORTED_MODULE_7__.useGlobalContext)(); // Store the context in a variable\n    const { language, content } = globalContext || {}; // Destructure safely\n    // Check if content is available\n    const currentContent = content === null || content === void 0 ? void 0 : content.notFound;\n    const defaultOptions = {\n        loop: true,\n        autoplay: true,\n        animationData: _common_404_json__WEBPACK_IMPORTED_MODULE_6__,\n        rendererSettings: {\n            preserveAspectRatio: \"xMidYMid slice\"\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)(()=>{\n        const timer = setTimeout(()=>{\n            route.push(\"/\");\n        }, 1000000);\n        // Cleanup the timeout if the component unmounts before it executes\n        return ()=>clearTimeout(timer);\n    }, [\n        route\n    ]); // Include 'route' in the dependency array\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_styles_Error_module_scss__WEBPACK_IMPORTED_MODULE_2___default().container),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Lottie, {\n                options: defaultOptions,\n                height: 200,\n                width: 500\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Akshay\\\\LOOP_PROJECTS\\\\shade_cms\\\\website\\\\src\\\\pages\\\\404.js\",\n                lineNumber: 39,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: (_styles_Error_module_scss__WEBPACK_IMPORTED_MODULE_2___default().description),\n                children: currentContent === null || currentContent === void 0 ? void 0 : currentContent.description[language]\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Akshay\\\\LOOP_PROJECTS\\\\shade_cms\\\\website\\\\src\\\\pages\\\\404.js\",\n                lineNumber: 40,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                href: \"/\",\n                className: \"\".concat((_styles_Error_module_scss__WEBPACK_IMPORTED_MODULE_2___default().homeLink), \" \").concat(language === \"en\" && (_styles_Error_module_scss__WEBPACK_IMPORTED_MODULE_2___default().enVersion)),\n                children: currentContent === null || currentContent === void 0 ? void 0 : (_currentContent_button = currentContent.button) === null || _currentContent_button === void 0 ? void 0 : _currentContent_button.text[language]\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Akshay\\\\LOOP_PROJECTS\\\\shade_cms\\\\website\\\\src\\\\pages\\\\404.js\",\n                lineNumber: 43,\n                columnNumber: 13\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Akshay\\\\LOOP_PROJECTS\\\\shade_cms\\\\website\\\\src\\\\pages\\\\404.js\",\n        lineNumber: 38,\n        columnNumber: 9\n    }, undefined);\n};\n_s(Custom404, \"yGobTMRq3Y6qvZgAgfSYt25zaZQ=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_4__.useRouter,\n        _contexts_GlobalContext__WEBPACK_IMPORTED_MODULE_7__.useGlobalContext\n    ];\n});\n_c1 = Custom404;\n/* harmony default export */ __webpack_exports__[\"default\"] = (Custom404);\nvar _c, _c1;\n$RefreshReg$(_c, \"Lottie\");\n$RefreshReg$(_c1, \"Custom404\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/404.js\n"));

/***/ }),

/***/ "./src/common/404.json":
/*!*****************************!*\
  !*** ./src/common/404.json ***!
  \*****************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

"use strict";
module.exports = /*#__PURE__*/JSON.parse('{"v":"5.1.9","fr":12,"ip":0,"op":20,"w":620,"h":250,"nm":"Comp 1","ddd":0,"assets":[],"layers":[{"ddd":0,"ind":1,"ty":4,"nm":"planet","sr":1,"ks":{"o":{"a":0,"k":100,"ix":11},"r":{"a":0,"k":0,"ix":10},"p":{"a":1,"k":[{"i":{"x":0.833,"y":0.833},"o":{"x":0.167,"y":0.167},"n":"0p833_0p833_0p167_0p167","t":0,"s":[310,136,0],"e":[310,125,0],"to":null,"ti":null},{"i":{"x":0.833,"y":0.833},"o":{"x":0.167,"y":0.167},"n":"0p833_0p833_0p167_0p167","t":10,"s":[310,125,0],"e":[310,136,0],"to":[0,0,0],"ti":[0,-1.83333337306976,0]},{"t":20}],"ix":2,"x":"var $bm_rt;\\n$bm_rt = loopOut();"},"a":{"a":0,"k":[0,0,0],"ix":1},"s":{"a":0,"k":[85,85,100],"ix":6}},"ao":0,"shapes":[{"ty":"gr","it":[{"ind":0,"ty":"sh","ix":1,"ks":{"a":0,"k":{"i":[[69.765,-32.035],[63.682,-32.103],[62.851,-36.995999999999995],[61.423,-35.059999999999995],[65.467,-29.935],[70.23700000000001,-31.895]],"o":[[66.435,-30.955],[62.53,-35.653],[62.151999999999994,-36.909],[62.713,-31.083],[69.299,-31.179],[70.366,-32.363]],"v":[[69.093,-31.816],[62.942,-34.381],[63.663,-38.096],[61.976,-33.361],[68.125,-30.796],[70.879,-32.766]],"c":true},"ix":2},"nm":"Path 1","mn":"ADBE Vector Shape - Group","hd":false,"_render":true},{"ty":"fl","c":{"a":0,"k":[0.1608,0.1804,0.2392,1],"ix":4},"o":{"a":0,"k":100,"ix":5},"r":1,"nm":"Fill 1","mn":"ADBE Vector Graphic - Fill","hd":false,"_render":true},{"ty":"tr","p":{"a":0,"k":[0,0],"ix":2},"a":{"a":0,"k":[0,0],"ix":1},"s":{"a":0,"k":[100,100],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":0,"k":15,"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"Transform","_render":true}],"nm":"Group 1","np":2,"cix":2,"ix":1,"mn":"ADBE Vector Group","hd":false,"_render":true},{"ty":"gr","it":[{"ty":"gr","it":[{"ind":0,"ty":"sh","ix":1,"ks":{"a":0,"k":{"i":[[-84.245,48.555],[-87.34700000000001,42.105000000000004],[-84.864,50.457],[-90.06599999999999,48.534],[-92.045,44.972],[-85.998,54.789]],"o":[[-87.085,42.605000000000004],[-85.561,46.383],[-87.337,51.639],[-92.271,44.455999999999996],[-89.206,50.924],[-83.257,53.48]],"v":[[-86.833,43.133],[-87.608,41.609],[-86.102,51.049],[-92.49,43.94],[-91.791,45.502],[-84.628,54.135]],"c":true},"ix":2},"nm":"Path 1","mn":"ADBE Vector Shape - Group","hd":false,"_render":true},{"ty":"fl","c":{"a":0,"k":[0.1608,0.1804,0.2392,1],"ix":4},"o":{"a":0,"k":100,"ix":5},"r":1,"nm":"Fill 1","mn":"ADBE Vector Graphic - Fill","hd":false,"_render":true},{"ty":"tr","p":{"a":0,"k":[0,0],"ix":2},"a":{"a":0,"k":[0,0],"ix":1},"s":{"a":0,"k":[100,100],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":0,"k":15,"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"Transform","_render":true}],"nm":"Group 1","np":2,"cix":2,"ix":1,"mn":"ADBE Vector Group","hd":false,"_render":true},{"ty":"gr","it":[{"ind":0,"ty":"sh","ix":1,"ks":{"a":0,"k":{"i":[[-89.42099999999999,37.712],[-83.258,53.482],[-89.20700000000001,50.924],[-95.368,35.153999999999996]],"o":[[-84.245,48.555],[-85.998,54.789],[-94.38300000000001,40.079],[-92.62899999999999,33.846999999999994]],"v":[[-86.833,43.133],[-84.628,54.135],[-91.793,45.502],[-93.999,34.501]],"c":true},"ix":2},"nm":"Path 1","mn":"ADBE Vector Shape - Group","hd":false,"_render":true},{"ty":"fl","c":{"a":0,"k":[0,0.7255,0.949,1],"ix":4},"o":{"a":0,"k":100,"ix":5},"r":1,"nm":"Fill 1","mn":"ADBE Vector Graphic - Fill","hd":false,"_render":true},{"ty":"tr","p":{"a":0,"k":[0,0],"ix":2},"a":{"a":0,"k":[0,0],"ix":1},"s":{"a":0,"k":[100,100],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":0,"k":100,"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"Transform","_render":true}],"nm":"Group 2","np":2,"cix":2,"ix":2,"mn":"ADBE Vector Group","hd":false,"_render":true},{"ty":"gr","it":[{"ty":"gr","it":[{"ind":0,"ty":"sh","ix":1,"ks":{"a":0,"k":{"i":[[-81.365,47.41],[-90.86,32.594],[-93.954,34.461],[-89.157,38.264],[-84.817,52.415],[-83.667,54.079]],"o":[[-86.70700000000001,36.214999999999996],[-93.77,34.022],[-92.434,35.078],[-84.515,47.987],[-84.372,54.471000000000004],[-81.172,52.89]],"v":[[-84.036,41.812],[-93.28,33.783],[-94.047,34.524],[-86.791,43.219],[-85.252,54.164],[-83.607,54.051]],"c":true},"ix":2},"nm":"Path 1","mn":"ADBE Vector Shape - Group","hd":false,"_render":true},{"ty":"fl","c":{"a":0,"k":[0.1608,0.1804,0.2392,1],"ix":4},"o":{"a":0,"k":100,"ix":5},"r":1,"nm":"Fill 1","mn":"ADBE Vector Graphic - Fill","hd":false,"_render":true},{"ty":"tr","p":{"a":0,"k":[0,0],"ix":2},"a":{"a":0,"k":[0,0],"ix":1},"s":{"a":0,"k":[100,100],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":0,"k":100,"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"Transform","_render":true}],"nm":"Group 1","np":2,"cix":2,"ix":1,"mn":"ADBE Vector Group","hd":false,"_render":true},{"ty":"tr","p":{"a":0,"k":[0,0],"ix":2},"a":{"a":0,"k":[0,0],"ix":1},"s":{"a":0,"k":[100,100],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":0,"k":100,"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"Transform","_render":true}],"nm":"Group 3","np":1,"cix":2,"ix":3,"mn":"ADBE Vector Group","hd":false,"_render":true},{"ty":"tr","p":{"a":0,"k":[0,0],"ix":2},"a":{"a":0,"k":[0,0],"ix":1},"s":{"a":0,"k":[100,100],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":0,"k":100,"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"Transform","_render":true}],"nm":"Group 2","np":3,"cix":2,"ix":2,"mn":"ADBE Vector Group","hd":false,"_render":true},{"ty":"gr","it":[{"ind":0,"ty":"sh","ix":1,"ks":{"a":0,"k":{"i":[[32.054,-66.726],[35.842,-70.473],[41.272,-69.23599999999999],[39.257,-71.016],[30.253999999999998,-71.188],[30.871999999999996,-66.21499999999999]],"o":[[32.613,-69.46799999999999],[40.389,-69.54400000000001],[41.065000000000005,-70.017],[33.483,-72.195],[29.557,-67.769],[32.366,-65.928]],"v":[[32.21,-67.493],[39.423,-69.742],[42.051,-68.859],[37.064,-71.464],[29.851,-69.214],[32.995,-65.195]],"c":true},"ix":2},"nm":"Path 1","mn":"ADBE Vector Shape - Group","hd":false,"_render":true},{"ty":"fl","c":{"a":0,"k":[0.1608,0.1804,0.2392,1],"ix":4},"o":{"a":0,"k":100,"ix":5},"r":1,"nm":"Fill 1","mn":"ADBE Vector Graphic - Fill","hd":false,"_render":true},{"ty":"tr","p":{"a":0,"k":[0,0],"ix":2},"a":{"a":0,"k":[0,0],"ix":1},"s":{"a":0,"k":[100,100],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":0,"k":15,"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"Transform","_render":true}],"nm":"Group 3","np":2,"cix":2,"ix":3,"mn":"ADBE Vector Group","hd":false,"_render":true},{"ty":"gr","it":[{"ind":0,"ty":"sh","ix":1,"ks":{"a":0,"k":{"i":[[70.961,-35.165],[68.35199999999999,-38.51],[65.22600000000001,-37.275],[65.467,-37.155],[69.426,-37.087999999999994],[69.90499999999999,-33.896]],"o":[[70.148,-37.766999999999996],[65.83399999999999,-37.723],[65.113,-36.959999999999994],[67.628,-37.830999999999996],[70.14699999999999,-34.781],[70.40799999999999,-33.938]],"v":[[70.606,-36.299],[66.594,-37.96],[64.793,-36.733],[65.872,-37.281],[69.886,-35.62],[69.332,-33.183]],"c":true},"ix":2},"nm":"Path 1","mn":"ADBE Vector Shape - Group","hd":false,"_render":true},{"ty":"fl","c":{"a":0,"k":[0.1608,0.1804,0.2392,1],"ix":4},"o":{"a":0,"k":100,"ix":5},"r":1,"nm":"Fill 1","mn":"ADBE Vector Graphic - Fill","hd":false,"_render":true},{"ty":"tr","p":{"a":0,"k":[0,0],"ix":2},"a":{"a":0,"k":[0,0],"ix":1},"s":{"a":0,"k":[100,100],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":0,"k":15,"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"Transform","_render":true}],"nm":"Group 4","np":2,"cix":2,"ix":4,"mn":"ADBE Vector Group","hd":false,"_render":true},{"ty":"gr","it":[{"ind":0,"ty":"sh","ix":1,"ks":{"a":0,"k":{"i":[[28.616,-48.685],[32.486,-49.665],[33.778999999999996,-46.946],[34.503,-48.48],[31.131999999999998,-51.003],[28.294,-48.797999999999995]],"o":[[30.557,-49.919],[33.719,-47.723],[34.326,-47.199],[33.059999999999995,-50.748999999999995],[28.796,-49.518],[28.316,-48.376]],"v":[[29.003,-48.93],[33.311,-48.369],[33.547,-46.201],[33.885,-49.452],[29.578,-50.014],[28.055,-48.047]],"c":true},"ix":2},"nm":"Path 1","mn":"ADBE Vector Shape - Group","hd":false,"_render":true},{"ty":"fl","c":{"a":0,"k":[0.1608,0.1804,0.2392,1],"ix":4},"o":{"a":0,"k":100,"ix":5},"r":1,"nm":"Fill 1","mn":"ADBE Vector Graphic - Fill","hd":false,"_render":true},{"ty":"tr","p":{"a":0,"k":[0,0],"ix":2},"a":{"a":0,"k":[0,0],"ix":1},"s":{"a":0,"k":[100,100],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":0,"k":15,"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"Transform","_render":true}],"nm":"Group 5","np":2,"cix":2,"ix":5,"mn":"ADBE Vector Group","hd":false,"_render":true},{"ty":"gr","it":[{"ind":0,"ty":"sh","ix":1,"ks":{"a":0,"k":{"i":[[71.899,-7.729000000000001],[71.17399999999999,-7.095],[67.74300000000001,-4.631],[66.204,-5.908],[66.39399999999999,-4.856],[69.86,-3.932]],"o":[[71.411,-7.932],[69.541,-5.013],[66.38000000000001,-5.7010000000000005],[66.128,-5.470000000000001],[68.06099999999999,-3.55],[71.78099999999999,-6.378]],"v":[[71.363,-8.701],[70.615,-6.382],[66.599,-5.53],[66.065,-6.139],[66.919,-4.446],[70.934,-5.298]],"c":true},"ix":2},"nm":"Path 1","mn":"ADBE Vector Shape - Group","hd":false,"_render":true},{"ty":"fl","c":{"a":0,"k":[0.1608,0.1804,0.2392,1],"ix":4},"o":{"a":0,"k":100,"ix":5},"r":1,"nm":"Fill 1","mn":"ADBE Vector Graphic - Fill","hd":false,"_render":true},{"ty":"tr","p":{"a":0,"k":[0,0],"ix":2},"a":{"a":0,"k":[0,0],"ix":1},"s":{"a":0,"k":[100,100],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":0,"k":15,"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"Transform","_render":true}],"nm":"Group 6","np":2,"cix":2,"ix":6,"mn":"ADBE Vector Group","hd":false,"_render":true},{"ty":"gr","it":[{"ind":0,"ty":"sh","ix":1,"ks":{"a":0,"k":{"i":[[22.177,-5.878],[10.531,-12.873],[12.288,-18.687],[10.234,-16.953],[11.829,-6.228],[25.552,-5.032]],"o":[[13.786000000000001,-8.183],[11.937000000000001,-17.993],[11.295000000000002,-18.336],[8.573,-10.919],[21.32,-3.6210000000000004],[25.341,-6.432]],"v":[[18.998,-6.753],[11.726,-17.226],[12.723,-19.32],[9.772,-15.272],[17.041,-4.796],[27.605,-7.976]],"c":true},"ix":2},"nm":"Path 1","mn":"ADBE Vector Shape - Group","hd":false,"_render":true},{"ty":"fl","c":{"a":0,"k":[0.1608,0.1804,0.2392,1],"ix":4},"o":{"a":0,"k":100,"ix":5},"r":1,"nm":"Fill 1","mn":"ADBE Vector Graphic - Fill","hd":false,"_render":true},{"ty":"tr","p":{"a":0,"k":[0,0],"ix":2},"a":{"a":0,"k":[0,0],"ix":1},"s":{"a":0,"k":[100,100],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":0,"k":15,"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"Transform","_render":true}],"nm":"Group 7","np":2,"cix":2,"ix":7,"mn":"ADBE Vector Group","hd":false,"_render":true},{"ty":"gr","it":[{"ind":0,"ty":"sh","ix":1,"ks":{"a":0,"k":{"i":[[22.991,-15.293],[16.315,-16.371000000000002],[16.993000000000002,-15.144],[21.955000000000002,-11.488],[20.847,-8.566],[23.257,-9.387]],"o":[[18.406,-16.886],[16.038,-15.209],[20.484,-13.933],[21.069000000000003,-8.940000000000001],[22.037,-8.506],[24.458000000000002,-12.849]],"v":[[20.441,-16.179],[15.17,-15.06],[17.934,-14.817],[21.213,-9.358],[20.57,-8.239],[23.719,-10.717]],"c":true},"ix":2},"nm":"Path 1","mn":"ADBE Vector Shape - Group","hd":false,"_render":true},{"ty":"fl","c":{"a":0,"k":[0.1608,0.1804,0.2392,1],"ix":4},"o":{"a":0,"k":100,"ix":5},"r":1,"nm":"Fill 1","mn":"ADBE Vector Graphic - Fill","hd":false,"_render":true},{"ty":"tr","p":{"a":0,"k":[0,0],"ix":2},"a":{"a":0,"k":[0,0],"ix":1},"s":{"a":0,"k":[100,100],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":0,"k":15,"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"Transform","_render":true}],"nm":"Group 8","np":2,"cix":2,"ix":8,"mn":"ADBE Vector Group","hd":false,"_render":true},{"ty":"gr","it":[{"ind":0,"ty":"sh","ix":1,"ks":{"a":0,"k":{"i":[[-32.501999999999995,-10.601],[-28.603,-14.304],[-24.25,-12.647],[-25.558,-14.18],[-32.844,-14.911999999999999],[-33.219,-9.835]],"o":[[-31.490999999999996,-13.509],[-24.943,-13.033],[-24.36,-13.249],[-29.956999999999997,-15.706],[-34.028999999999996,-11.496],[-32.264,-9.577]],"v":[[-32.178,-11.531],[-25.728,-13.304],[-23.656,-12.199],[-27.08,-14.708],[-33.531,-12.933],[-31.64,-8.647]],"c":true},"ix":2},"nm":"Path 1","mn":"ADBE Vector Shape - Group","hd":false,"_render":true},{"ty":"fl","c":{"a":0,"k":[0.1608,0.1804,0.2392,1],"ix":4},"o":{"a":0,"k":100,"ix":5},"r":1,"nm":"Fill 1","mn":"ADBE Vector Graphic - Fill","hd":false,"_render":true},{"ty":"tr","p":{"a":0,"k":[0,0],"ix":2},"a":{"a":0,"k":[0,0],"ix":1},"s":{"a":0,"k":[100,100],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":0,"k":15,"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"Transform","_render":true}],"nm":"Group 9","np":2,"cix":2,"ix":9,"mn":"ADBE Vector Group","hd":false,"_render":true},{"ty":"gr","it":[{"ind":0,"ty":"sh","ix":1,"ks":{"a":0,"k":{"i":[[-20.689,-11.156],[-21.099999999999998,-10.294],[-26.314,-5.470000000000001],[-33.485,-8.482],[-31.806,-6.51],[-21.648,-5.417]],"o":[[-21.264,-11.455],[-22.401,-6.545],[-32.009,-7.446000000000001],[-33.605000000000004,-8.019],[-25.563,-4.3420000000000005],[-20.215,-9.544]],"v":[[-21.833,-12.551],[-21.47,-9.227],[-30.213,-6.823],[-34.466,-9.677],[-29.459,-5.695],[-20.718,-8.099]],"c":true},"ix":2},"nm":"Path 1","mn":"ADBE Vector Shape - Group","hd":false,"_render":true},{"ty":"fl","c":{"a":0,"k":[0.1608,0.1804,0.2392,1],"ix":4},"o":{"a":0,"k":100,"ix":5},"r":1,"nm":"Fill 1","mn":"ADBE Vector Graphic - Fill","hd":false,"_render":true},{"ty":"tr","p":{"a":0,"k":[0,0],"ix":2},"a":{"a":0,"k":[0,0],"ix":1},"s":{"a":0,"k":[100,100],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":0,"k":15,"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"Transform","_render":true}],"nm":"Group 10","np":2,"cix":2,"ix":10,"mn":"ADBE Vector Group","hd":false,"_render":true},{"ty":"gr","it":[{"ind":0,"ty":"sh","ix":1,"ks":{"a":0,"k":{"i":[[-22.430999999999997,-11.295000000000002],[-26.694,-6.545999999999999],[-34.217999999999996,-10.956],[-29.956999999999997,-15.706]],"o":[[-23.805,-7.339],[-32.444,-8.542],[-32.844,-14.911999999999999],[-24.205,-13.71]],"v":[[-23.118,-9.319],[-29.569,-7.544],[-33.531,-12.933],[-27.08,-14.708]],"c":true},"ix":2},"nm":"Path 1","mn":"ADBE Vector Shape - Group","hd":false,"_render":true},{"ty":"fl","c":{"a":0,"k":[0.8492,0.8811,0.8908,1],"ix":4},"o":{"a":0,"k":100,"ix":5},"r":1,"nm":"Fill 1","mn":"ADBE Vector Graphic - Fill","hd":false,"_render":true},{"ty":"tr","p":{"a":0,"k":[0,0],"ix":2},"a":{"a":0,"k":[0,0],"ix":1},"s":{"a":0,"k":[100,100],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":0,"k":100,"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"Transform","_render":true}],"nm":"Group 11","np":2,"cix":2,"ix":11,"mn":"ADBE Vector Group","hd":false,"_render":true},{"ty":"gr","it":[{"ind":0,"ty":"sh","ix":1,"ks":{"a":0,"k":{"i":[[69.94800000000001,43.833],[66.551,48.152],[33.673,74.20400000000001],[6.997999999999999,53.308],[10.418999999999999,65.935],[54.145,75.937]],"o":[[68.21600000000001,41.851],[53.394999999999996,69.203],[11.681999999999999,60.46],[5.345,55.875],[34.424,80.93799999999999],[68.616,52.782]],"v":[[68.168,35.698],[63,53.832],[18.95,65.003],[5.265,45.381],[19.7,71.737],[63.752,60.564]],"c":true},"ix":2},"nm":"Path 1","mn":"ADBE Vector Shape - Group","hd":false,"_render":true},{"ty":"fl","c":{"a":0,"k":[0.1608,0.1804,0.2392,1],"ix":4},"o":{"a":0,"k":100,"ix":5},"r":1,"nm":"Fill 1","mn":"ADBE Vector Graphic - Fill","hd":false,"_render":true},{"ty":"tr","p":{"a":0,"k":[0,0],"ix":2},"a":{"a":0,"k":[0,0],"ix":1},"s":{"a":0,"k":[100,100],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":0,"k":15,"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"Transform","_render":true}],"nm":"Group 12","np":2,"cix":2,"ix":12,"mn":"ADBE Vector Group","hd":false,"_render":true},{"ty":"gr","it":[{"ind":0,"ty":"sh","ix":1,"ks":{"a":0,"k":{"i":[[19.425,44.167],[43.839,26.515],[59.768,36.876000000000005],[55.975,24.964],[22.219,15.828],[12.038,46.475]],"o":[[28.84,29.1],[57.873000000000005,35.285],[60.274,31.680000000000003],[37.22,13.245999999999999],[10.167,35.114000000000004],[18.582,49.336]],"v":[[22.212,39.707],[55.713,33.936],[61.403,38.633],[49.094,20.665],[15.59,26.436],[19.402,54.384]],"c":true},"ix":2},"nm":"Path 1","mn":"ADBE Vector Shape - Group","hd":false,"_render":true},{"ty":"fl","c":{"a":0,"k":[0.1608,0.1804,0.2392,1],"ix":4},"o":{"a":0,"k":100,"ix":5},"r":1,"nm":"Fill 1","mn":"ADBE Vector Graphic - Fill","hd":false,"_render":true},{"ty":"tr","p":{"a":0,"k":[0,0],"ix":2},"a":{"a":0,"k":[0,0],"ix":1},"s":{"a":0,"k":[100,100],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":0,"k":15,"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"Transform","_render":true}],"nm":"Group 13","np":2,"cix":2,"ix":13,"mn":"ADBE Vector Group","hd":false,"_render":true},{"ty":"gr","it":[{"ind":0,"ty":"sh","ix":1,"ks":{"a":0,"k":{"i":[[37.22,13.245999999999999],[65.222,42.701],[36.966,66.497],[8.965,37.043]],"o":[[60.968,28.085],[51.967,63.915],[13.216,51.656000000000006],[22.219,15.828]],"v":[[49.094,20.665],[58.595,53.308],[25.092,59.078],[15.59,26.436]],"c":true},"ix":2},"nm":"Path 1","mn":"ADBE Vector Shape - Group","hd":false,"_render":true},{"ty":"fl","c":{"a":0,"k":[0.8492,0.8811,0.8908,1],"ix":4},"o":{"a":0,"k":100,"ix":5},"r":1,"nm":"Fill 1","mn":"ADBE Vector Graphic - Fill","hd":false,"_render":true},{"ty":"tr","p":{"a":0,"k":[0,0],"ix":2},"a":{"a":0,"k":[0,0],"ix":1},"s":{"a":0,"k":[100,100],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":0,"k":100,"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"Transform","_render":true}],"nm":"Group 14","np":2,"cix":2,"ix":14,"mn":"ADBE Vector Group","hd":false,"_render":true},{"ty":"gr","it":[{"ind":0,"ty":"sh","ix":1,"ks":{"a":0,"k":{"i":[[-14.491000000000001,84.43700000000001],[-15.829,83.503],[-24.576999999999998,88.09],[-34.238,81.041],[-33.545,86.64800000000001],[-17.89,92.306]],"o":[[-15.623000000000001,82.83500000000001],[-18.493000000000002,87.767],[-32.366,83.221],[-35.875,82.3],[-23.973,92.629],[-14.446,86.791]],"v":[[-15.492,82.141],[-16.207,84.109],[-29.793,84.831],[-35.211,78.806],[-29.189,89.367],[-15.603,88.646]],"c":true},"ix":2},"nm":"Path 1","mn":"ADBE Vector Shape - Group","hd":false,"_render":true},{"ty":"fl","c":{"a":0,"k":[0.1608,0.1804,0.2392,1],"ix":4},"o":{"a":0,"k":100,"ix":5},"r":1,"nm":"Fill 1","mn":"ADBE Vector Graphic - Fill","hd":false,"_render":true},{"ty":"tr","p":{"a":0,"k":[0,0],"ix":2},"a":{"a":0,"k":[0,0],"ix":1},"s":{"a":0,"k":[100,100],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":0,"k":15,"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"Transform","_render":true}],"nm":"Group 15","np":2,"cix":2,"ix":15,"mn":"ADBE Vector Group","hd":false,"_render":true},{"ty":"gr","it":[{"ind":0,"ty":"sh","ix":1,"ks":{"a":0,"k":{"i":[[-38.038000000000004,76.303],[-26.004,69.825],[-10.982999999999999,81.87400000000001],[-13.356000000000002,76.029],[-34.265,68.56500000000001],[-38.688,76.878]],"o":[[-34.265,70.265],[-14.031000000000002,77.308],[-10.187999999999999,81.46400000000001],[-26.004,68.12700000000001],[-38.335,75.078],[-38.397000000000006,77.50099999999999]],"v":[[-37.371,75.236],[-18.917,74.256],[-10.575,86.046],[-18.917,72.554],[-37.371,73.534],[-38.505,78.758]],"c":true},"ix":2},"nm":"Path 1","mn":"ADBE Vector Shape - Group","hd":false,"_render":true},{"ty":"fl","c":{"a":0,"k":[0.1608,0.1804,0.2392,1],"ix":4},"o":{"a":0,"k":100,"ix":5},"r":1,"nm":"Fill 1","mn":"ADBE Vector Graphic - Fill","hd":false,"_render":true},{"ty":"tr","p":{"a":0,"k":[0,0],"ix":2},"a":{"a":0,"k":[0,0],"ix":1},"s":{"a":0,"k":[100,100],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":0,"k":15,"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"Transform","_render":true}],"nm":"Group 16","np":2,"cix":2,"ix":16,"mn":"ADBE Vector Group","hd":false,"_render":true},{"ty":"gr","it":[{"ind":0,"ty":"sh","ix":1,"ks":{"a":0,"k":{"i":[[-26.886,37.863],[-28.859,39.469],[-49.775999999999996,54.037],[-72.027,38.106],[-68.59,47.516],[-35.138,56.607]],"o":[[-28.172,36.278],[-35.968,51.786],[-67.691,43.693],[-73.429,39.736],[-48.944,58.858999999999995],[-27.208000000000002,42.876]],"v":[[-28.32,33.034],[-30.507,42.326],[-61.346,47.355],[-73.768,32.247],[-60.516,52.178],[-29.675,47.15]],"c":true},"ix":2},"nm":"Path 1","mn":"ADBE Vector Shape - Group","hd":false,"_render":true},{"ty":"fl","c":{"a":0,"k":[0.1608,0.1804,0.2392,1],"ix":4},"o":{"a":0,"k":100,"ix":5},"r":1,"nm":"Fill 1","mn":"ADBE Vector Graphic - Fill","hd":false,"_render":true},{"ty":"tr","p":{"a":0,"k":[0,0],"ix":2},"a":{"a":0,"k":[0,0],"ix":1},"s":{"a":0,"k":[100,100],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":0,"k":15,"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"Transform","_render":true}],"nm":"Group 17","np":2,"cix":2,"ix":17,"mn":"ADBE Vector Group","hd":false,"_render":true},{"ty":"gr","it":[{"ind":0,"ty":"sh","ix":1,"ks":{"a":0,"k":{"i":[[-66.824,31.959],[-52.675,23.618],[-38.062999999999995,34.553],[-37.568999999999996,23.968],[-62.388999999999996,15.764],[-68.69,29.491999999999997]],"o":[[-62.388999999999996,24.858],[-40.812,31.033],[-34.333,31.979],[-52.675,14.528],[-68.481,25.515],[-67.125,32.607]],"v":[[-66.439,31.341],[-44.745,28.575],[-36.775,38.266],[-44.745,19.483],[-66.439,22.247],[-67.384,33.269]],"c":true},"ix":2},"nm":"Path 1","mn":"ADBE Vector Shape - Group","hd":false,"_render":true},{"ty":"fl","c":{"a":0,"k":[0.1608,0.1804,0.2392,1],"ix":4},"o":{"a":0,"k":100,"ix":5},"r":1,"nm":"Fill 1","mn":"ADBE Vector Graphic - Fill","hd":false,"_render":true},{"ty":"tr","p":{"a":0,"k":[0,0],"ix":2},"a":{"a":0,"k":[0,0],"ix":1},"s":{"a":0,"k":[100,100],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":0,"k":15,"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"Transform","_render":true}],"nm":"Group 18","np":2,"cix":2,"ix":18,"mn":"ADBE Vector Group","hd":false,"_render":true},{"ty":"gr","it":[{"ind":0,"ty":"sh","ix":1,"ks":{"a":0,"k":{"i":[[-52.675,14.528],[-33.669000000000004,33.712],[-51.482,47.913],[-70.48899999999999,28.73]],"o":[[-36.815,24.44],[-41.769,46.678],[-67.346,38.003],[-62.388999999999996,15.764]],"v":[[-44.745,19.483],[-37.719,40.195],[-59.414,42.958],[-66.439,22.247]],"c":true},"ix":2},"nm":"Path 1","mn":"ADBE Vector Shape - Group","hd":false,"_render":true},{"ty":"fl","c":{"a":0,"k":[0.8492,0.8811,0.8908,1],"ix":4},"o":{"a":0,"k":100,"ix":5},"r":1,"nm":"Fill 1","mn":"ADBE Vector Graphic - Fill","hd":false,"_render":true},{"ty":"tr","p":{"a":0,"k":[0,0],"ix":2},"a":{"a":0,"k":[0,0],"ix":1},"s":{"a":0,"k":[100,100],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":0,"k":100,"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"Transform","_render":true}],"nm":"Group 19","np":2,"cix":2,"ix":19,"mn":"ADBE Vector Group","hd":false,"_render":true},{"ty":"gr","it":[{"ind":0,"ty":"sh","ix":1,"ks":{"a":0,"k":{"i":[[-69.696,-17.217000000000002],[-82.684,-19.333],[-86.952,-12.324],[-79.45899999999999,-16.615],[-72.61500000000001,-8.063],[-73.27000000000001,-4.8919999999999995]],"o":[[-77.02000000000001,-21.791],[-86.53,-13.175999999999998],[-83.965,-15.156],[-73.283,-12.753],[-73.855,-4.1370000000000005],[-69.424,-11.049]],"v":[[-73.358,-19.504],[-86.009,-14.011],[-87.297,-11.467],[-76.371,-14.684],[-74.474,-3.451],[-72.751,-5.725]],"c":true},"ix":2},"nm":"Path 1","mn":"ADBE Vector Shape - Group","hd":false,"_render":true},{"ty":"fl","c":{"a":0,"k":[0.1608,0.1804,0.2392,1],"ix":4},"o":{"a":0,"k":100,"ix":5},"r":1,"nm":"Fill 1","mn":"ADBE Vector Graphic - Fill","hd":false,"_render":true},{"ty":"tr","p":{"a":0,"k":[0,0],"ix":2},"a":{"a":0,"k":[0,0],"ix":1},"s":{"a":0,"k":[100,100],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":0,"k":15,"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"Transform","_render":true}],"nm":"Group 20","np":2,"cix":2,"ix":20,"mn":"ADBE Vector Group","hd":false,"_render":true},{"ty":"gr","it":[{"ind":0,"ty":"sh","ix":1,"ks":{"a":0,"k":{"i":[[-82.853,4.946],[-93.595,-9.371],[-90.586,-15.693],[-95.481,1.9219999999999997],[-76.24199999999999,5.058],[-70.61200000000001,-4.024]],"o":[[-92.88499999999999,-1.322],[-90.18900000000001,-16.256],[-95.88300000000001,-7.217],[-84.631,8.702],[-70.945,-3.419],[-75.307,2.0570000000000004]],"v":[[-87.868,1.81],[-89.784,-16.8],[-90.956,-15.1],[-90.056,5.313],[-71.315,-2.825],[-70.302,-4.627]],"c":true},"ix":2},"nm":"Path 1","mn":"ADBE Vector Shape - Group","hd":false,"_render":true},{"ty":"fl","c":{"a":0,"k":[0.1608,0.1804,0.2392,1],"ix":4},"o":{"a":0,"k":100,"ix":5},"r":1,"nm":"Fill 1","mn":"ADBE Vector Graphic - Fill","hd":false,"_render":true},{"ty":"tr","p":{"a":0,"k":[0,0],"ix":2},"a":{"a":0,"k":[0,0],"ix":1},"s":{"a":0,"k":[100,100],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":0,"k":15,"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"Transform","_render":true}],"nm":"Group 21","np":2,"cix":2,"ix":21,"mn":"ADBE Vector Group","hd":false,"_render":true},{"ty":"gr","it":[{"ind":0,"ty":"sh","ix":1,"ks":{"a":0,"k":{"i":[[-72.983,-23.325],[-66.511,-13.84],[-65.981,-20.44],[-74.673,-24.178]],"o":[[-67.334,-19.793999999999997],[-65.563,-13.867],[-72.03999999999999,-24.225],[-75.089,-23.554]],"v":[[-71.238,-22.237],[-68.908,-8.004],[-69.972,-22.934],[-77.259,-23.058]],"c":true},"ix":2},"nm":"Path 1","mn":"ADBE Vector Shape - Group","hd":false,"_render":true},{"ty":"fl","c":{"a":0,"k":[0.1608,0.1804,0.2392,1],"ix":4},"o":{"a":0,"k":100,"ix":5},"r":1,"nm":"Fill 1","mn":"ADBE Vector Graphic - Fill","hd":false,"_render":true},{"ty":"tr","p":{"a":0,"k":[0,0],"ix":2},"a":{"a":0,"k":[0,0],"ix":1},"s":{"a":0,"k":[100,100],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":0,"k":15,"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"Transform","_render":true}],"nm":"Group 22","np":2,"cix":2,"ix":22,"mn":"ADBE Vector Group","hd":false,"_render":true},{"ty":"gr","it":[{"ind":0,"ty":"sh","ix":1,"ks":{"a":0,"k":{"i":[[-77.02000000000001,-21.791],[-69.426,-11.049],[-81.74000000000001,2.057],[-89.336,-8.69]],"o":[[-69.696,-17.217000000000002],[-76.07600000000001,-0.4039999999999999],[-89.06200000000001,-2.5210000000000004],[-82.684,-19.333]],"v":[[-73.358,-19.504],[-72.751,-5.725],[-85.4,-0.232],[-86.009,-14.011]],"c":true},"ix":2},"nm":"Path 1","mn":"ADBE Vector Shape - Group","hd":false,"_render":true},{"ty":"fl","c":{"a":0,"k":[0.8492,0.8811,0.8908,1],"ix":4},"o":{"a":0,"k":100,"ix":5},"r":1,"nm":"Fill 1","mn":"ADBE Vector Graphic - Fill","hd":false,"_render":true},{"ty":"tr","p":{"a":0,"k":[0,0],"ix":2},"a":{"a":0,"k":[0,0],"ix":1},"s":{"a":0,"k":[100,100],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":0,"k":100,"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"Transform","_render":true}],"nm":"Group 23","np":2,"cix":2,"ix":23,"mn":"ADBE Vector Group","hd":false,"_render":true},{"ty":"gr","it":[{"ind":0,"ty":"sh","ix":1,"ks":{"a":0,"k":{"i":[[8.23,-75.121],[4.97,-80.988],[4.731,-79.345],[1.915,-73.265],[-3.469,-72.707],[-1.007,-72.148]],"o":[[7.742,-79.556],[3.403,-80.45200000000001],[5.147,-75.574],[-2.908,-72.73],[-2.657,-72.304],[4.998,-72.814]],"v":[[7.966,-77.5],[1.477,-81.034],[4.885,-77.95],[-2.335,-72.794],[-4.013,-72.714],[0.748,-72.344]],"c":true},"ix":2},"nm":"Path 1","mn":"ADBE Vector Shape - Group","hd":false,"_render":true},{"ty":"fl","c":{"a":0,"k":[0.1608,0.1804,0.2392,1],"ix":4},"o":{"a":0,"k":100,"ix":5},"r":1,"nm":"Fill 1","mn":"ADBE Vector Graphic - Fill","hd":false,"_render":true},{"ty":"tr","p":{"a":0,"k":[0,0],"ix":2},"a":{"a":0,"k":[0,0],"ix":1},"s":{"a":0,"k":[100,100],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":0,"k":15,"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"Transform","_render":true}],"nm":"Group 24","np":2,"cix":2,"ix":24,"mn":"ADBE Vector Group","hd":false,"_render":true},{"ty":"gr","it":[{"ind":0,"ty":"sh","ix":1,"ks":{"a":0,"k":{"i":[[-10.14,-72.862],[-5.581,-82.108],[6.806000000000001,-82.916],[3.194,-83.947],[-12.467,-79.09400000000001],[-9.745,-71.345]],"o":[[-10.756,-78.413],[4.102,-83.178],[6.590000000000001,-83.406],[-7.293,-82.78699999999999],[-11.803,-73.093],[-8.796,-71.4]],"v":[[-10.333,-74.607],[1.223,-82.861],[9.005,-82.22],[-0.49,-83.541],[-12.044,-75.287],[-6.749,-70.4]],"c":true},"ix":2},"nm":"Path 1","mn":"ADBE Vector Shape - Group","hd":false,"_render":true},{"ty":"fl","c":{"a":0,"k":[0.1608,0.1804,0.2392,1],"ix":4},"o":{"a":0,"k":100,"ix":5},"r":1,"nm":"Fill 1","mn":"ADBE Vector Graphic - Fill","hd":false,"_render":true},{"ty":"tr","p":{"a":0,"k":[0,0],"ix":2},"a":{"a":0,"k":[0,0],"ix":1},"s":{"a":0,"k":[100,100],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":0,"k":15,"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"Transform","_render":true}],"nm":"Group 25","np":2,"cix":2,"ix":25,"mn":"ADBE Vector Group","hd":false,"_render":true},{"ty":"gr","it":[{"ind":0,"ty":"sh","ix":1,"ks":{"a":0,"k":{"i":[[30.096,-41.275999999999996],[25.959,-46.384],[26.862000000000002,-48.690999999999995],[25.213,-41.957],[32.456,-41.052],[34.486,-44.638999999999996]],"o":[[26.326,-43.451],[27.041999999999998,-48.938],[24.953,-45.384],[29.359,-39.567],[34.365,-44.36],[32.815999999999995,-42.424]],"v":[[28.21,-42.364],[27.22,-49.185],[26.704,-48.416],[27.285,-40.762],[34.206,-44.085],[34.614,-44.918]],"c":true},"ix":2},"nm":"Path 1","mn":"ADBE Vector Shape - Group","hd":false,"_render":true},{"ty":"fl","c":{"a":0,"k":[0.8492,0.8811,0.8908,1],"ix":4},"o":{"a":0,"k":100,"ix":5},"r":1,"nm":"Fill 1","mn":"ADBE Vector Graphic - Fill","hd":false,"_render":true},{"ty":"tr","p":{"a":0,"k":[0,0],"ix":2},"a":{"a":0,"k":[0,0],"ix":1},"s":{"a":0,"k":[100,100],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":0,"k":100,"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"Transform","_render":true}],"nm":"Group 26","np":2,"cix":2,"ix":26,"mn":"ADBE Vector Group","hd":false,"_render":true},{"ty":"gr","it":[{"ind":0,"ty":"sh","ix":1,"ks":{"a":0,"k":{"i":[[7.704000000000001,-79.879],[4.998,-72.814],[-7.16,-73.42],[-4.455,-80.485]],"o":[[8.23,-75.121],[-3.502,-71.87299999999999],[-7.683999999999999,-78.17800000000001],[4.045,-81.426]],"v":[[7.966,-77.5],[0.748,-72.344],[-7.422,-75.799],[-0.205,-80.955]],"c":true},"ix":2},"nm":"Path 1","mn":"ADBE Vector Shape - Group","hd":false,"_render":true},{"ty":"fl","c":{"a":0,"k":[0.8492,0.8811,0.8908,1],"ix":4},"o":{"a":0,"k":100,"ix":5},"r":1,"nm":"Fill 1","mn":"ADBE Vector Graphic - Fill","hd":false,"_render":true},{"ty":"tr","p":{"a":0,"k":[0,0],"ix":2},"a":{"a":0,"k":[0,0],"ix":1},"s":{"a":0,"k":[100,100],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":0,"k":100,"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"Transform","_render":true}],"nm":"Group 27","np":2,"cix":2,"ix":27,"mn":"ADBE Vector Group","hd":false,"_render":true},{"ty":"gr","it":[{"ind":0,"ty":"sh","ix":1,"ks":{"a":0,"k":{"i":[[-24.119,-50.946999999999996],[-26.852999999999998,-56.709],[-29.961999999999996,-48.588],[-44.628,-44.650999999999996],[-47.206,-46.229],[-39.346000000000004,-40.816]],"o":[[-26.182000000000002,-55.903999999999996],[-26.589,-53.437],[-40.04,-44.39],[-47.263000000000005,-46.419999999999995],[-45.470000000000006,-42.056],[-27.553,-45.727000000000004]],"v":[[-25.777,-54.931],[-27.72,-57.338],[-35.58,-46.249],[-47.319,-46.611],[-47.13,-46.041],[-33.448,-43.27]],"c":true},"ix":2},"nm":"Path 1","mn":"ADBE Vector Shape - Group","hd":false,"_render":true},{"ty":"fl","c":{"a":0,"k":[0.1608,0.1804,0.2392,1],"ix":4},"o":{"a":0,"k":100,"ix":5},"r":1,"nm":"Fill 1","mn":"ADBE Vector Graphic - Fill","hd":false,"_render":true},{"ty":"tr","p":{"a":0,"k":[0,0],"ix":2},"a":{"a":0,"k":[0,0],"ix":1},"s":{"a":0,"k":[100,100],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":0,"k":15,"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"Transform","_render":true}],"nm":"Group 28","np":2,"cix":2,"ix":28,"mn":"ADBE Vector Group","hd":false,"_render":true},{"ty":"gr","it":[{"ind":0,"ty":"sh","ix":1,"ks":{"a":0,"k":{"i":[[-50.617999999999995,-41.538000000000004],[-48.339,-56.362],[-26.128,-62.482],[-33.499,-64.47800000000001],[-55.045,-49.794999999999995],[-50.777,-40.947]],"o":[[-53.515,-48.491],[-32.881,-62.799],[-25.786,-63.634],[-49.869,-57.664],[-51.891,-42.224],[-50.04,-40.706]],"v":[[-51.013,-42.487],[-39.451,-60.064],[-22.041,-59.705],[-40.981,-61.362],[-52.543,-43.788],[-49.336,-39.969]],"c":true},"ix":2},"nm":"Path 1","mn":"ADBE Vector Shape - Group","hd":false,"_render":true},{"ty":"fl","c":{"a":0,"k":[0.8492,0.8811,0.8908,1],"ix":4},"o":{"a":0,"k":100,"ix":5},"r":1,"nm":"Fill 1","mn":"ADBE Vector Graphic - Fill","hd":false,"_render":true},{"ty":"tr","p":{"a":0,"k":[0,0],"ix":2},"a":{"a":0,"k":[0,0],"ix":1},"s":{"a":0,"k":[100,100],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":0,"k":100,"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"Transform","_render":true}],"nm":"Group 29","np":2,"cix":2,"ix":29,"mn":"ADBE Vector Group","hd":false,"_render":true},{"ty":"gr","it":[{"ind":0,"ty":"sh","ix":1,"ks":{"a":0,"k":{"i":[[-27.437,-58.918],[-27.55,-45.727000000000004],[-45.470000000000006,-42.056],[-45.354,-55.25]],"o":[[-24.119,-50.946999999999996],[-39.346000000000004,-40.816],[-48.788000000000004,-50.026999999999994],[-33.56100000000001,-60.159]],"v":[[-25.777,-54.931],[-33.448,-43.27],[-47.13,-46.041],[-39.459,-57.705]],"c":true},"ix":2},"nm":"Path 1","mn":"ADBE Vector Shape - Group","hd":false,"_render":true},{"ty":"fl","c":{"a":0,"k":[0.8492,0.8811,0.8908,1],"ix":4},"o":{"a":0,"k":100,"ix":5},"r":1,"nm":"Fill 1","mn":"ADBE Vector Graphic - Fill","hd":false,"_render":true},{"ty":"tr","p":{"a":0,"k":[0,0],"ix":2},"a":{"a":0,"k":[0,0],"ix":1},"s":{"a":0,"k":[100,100],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":0,"k":100,"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"Transform","_render":true}],"nm":"Group 30","np":2,"cix":2,"ix":30,"mn":"ADBE Vector Group","hd":false,"_render":true},{"ty":"gr","it":[{"ind":0,"ty":"sh","ix":1,"ks":{"a":0,"k":{"i":[[44.968,58.831],[-88.685,25.527],[-100.163,-5.49],[-56.295,99.462],[95.802,55.590999999999994],[95.065,-12.281999999999998]],"o":[[-49.326,58.831],[-99.42399999999999,-12.281999999999998],[-100.163,55.590999999999994],[51.935,99.462],[95.802,-5.49],[84.32600000000001,25.527]],"v":[[-2.18,58.831],[-98.041,-18.836],[-100.163,1.48],[-2.18,99.462],[95.802,1.48],[93.682,-18.836]],"c":true},"ix":2},"nm":"Path 1","mn":"ADBE Vector Shape - Group","hd":false,"_render":true},{"ty":"fl","c":{"a":0,"k":[0.1608,0.1804,0.2392,1],"ix":4},"o":{"a":0,"k":100,"ix":5},"r":1,"nm":"Fill 1","mn":"ADBE Vector Graphic - Fill","hd":false,"_render":true},{"ty":"tr","p":{"a":0,"k":[0,0],"ix":2},"a":{"a":0,"k":[0,0],"ix":1},"s":{"a":0,"k":[100,100],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":0,"k":15,"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"Transform","_render":true}],"nm":"Group 31","np":2,"cix":2,"ix":31,"mn":"ADBE Vector Group","hd":false,"_render":true},{"ty":"gr","it":[{"ind":0,"ty":"sh","ix":1,"ks":{"a":0,"k":{"i":[[95.802,-52.635000000000005],[51.935,99.462],[-100.163,55.590999999999994],[-56.295,-96.505]],"o":[[95.802,55.590999999999994],[-56.295,99.462],[-100.163,-52.635000000000005],[51.935,-96.505]],"v":[[95.802,1.48],[-2.18,99.462],[-100.163,1.48],[-2.18,-96.505]],"c":true},"ix":2},"nm":"Path 1","mn":"ADBE Vector Shape - Group","hd":false,"_render":true},{"ty":"fl","c":{"a":0,"k":[0.964,0.9732,0.976,1],"ix":4},"o":{"a":0,"k":100,"ix":5},"r":1,"nm":"Fill 1","mn":"ADBE Vector Graphic - Fill","hd":false,"_render":true},{"ty":"tr","p":{"a":0,"k":[0,0],"ix":2},"a":{"a":0,"k":[0,0],"ix":1},"s":{"a":0,"k":[100,100],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":0,"k":100,"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"Transform","_render":true}],"nm":"Group 32","np":2,"cix":2,"ix":32,"mn":"ADBE Vector Group","hd":false,"_render":true},{"ty":"gr","it":[{"ind":0,"ty":"sh","ix":1,"ks":{"a":0,"k":{"i":[[-55.72,-51.164],[-64.95,-39.483],[-78.994,-32.187],[-85.226,-37.108],[-92.52,-51.153999999999996],[-83.289,-62.832],[-69.246,-70.128],[-63.018,-65.208]],"o":[[-60.644,-44.936],[-69.87400000000001,-33.254999999999995],[-85.226,-37.108],[-91.451,-42.028999999999996],[-87.598,-57.382],[-78.367,-69.06],[-63.018,-65.208],[-56.79,-60.287]],"v":[[-60.644,-44.936],[-64.95,-39.483],[-85.226,-37.108],[-85.226,-37.108],[-87.598,-57.382],[-83.289,-62.832],[-63.018,-65.208],[-63.018,-65.208]],"c":true},"ix":2},"nm":"Path 1","mn":"ADBE Vector Shape - Group","hd":false,"_render":true},{"ty":"fl","c":{"a":0,"k":[0.8245,0.8636,0.8755,1],"ix":4},"o":{"a":0,"k":100,"ix":5},"r":1,"nm":"Fill 1","mn":"ADBE Vector Graphic - Fill","hd":false,"_render":true},{"ty":"tr","p":{"a":0,"k":[0,0],"ix":2},"a":{"a":0,"k":[0,0],"ix":1},"s":{"a":0,"k":[100,100],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":0,"k":100,"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"Transform","_render":true}],"nm":"Group 33","np":2,"cix":2,"ix":33,"mn":"ADBE Vector Group","hd":false,"_render":true},{"ty":"gr","it":[{"ind":0,"ty":"sh","ix":1,"ks":{"a":0,"k":{"i":[[-18.781,-102.15299999999999],[-1.971,-101.576],[7.496,-96.913],[6.981,-82.011],[2.3219999999999996,-72.544],[-14.488,-73.125],[-23.955,-77.784],[-23.44,-92.686]],"o":[[-13.492,-101.972],[3.3179999999999996,-101.392],[7.315,-91.625],[6.798,-76.72399999999999],[-2.967,-72.727],[-19.777,-73.306],[-23.772,-83.075],[-23.257,-97.97500000000001]],"v":[[-13.492,-101.972],[-1.971,-101.576],[7.315,-91.625],[6.981,-82.011],[-2.967,-72.727],[-14.488,-73.125],[-23.772,-83.075],[-23.44,-92.686]],"c":true},"ix":2},"nm":"Path 1","mn":"ADBE Vector Shape - Group","hd":false,"_render":true},{"ty":"fl","c":{"a":0,"k":[0.8245,0.8636,0.8755,1],"ix":4},"o":{"a":0,"k":100,"ix":5},"r":1,"nm":"Fill 1","mn":"ADBE Vector Graphic - Fill","hd":false,"_render":true},{"ty":"tr","p":{"a":0,"k":[0,0],"ix":2},"a":{"a":0,"k":[0,0],"ix":1},"s":{"a":0,"k":[100,100],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":0,"k":100,"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"Transform","_render":true}],"nm":"Group 34","np":2,"cix":2,"ix":34,"mn":"ADBE Vector Group","hd":false,"_render":true},{"ty":"gr","it":[{"ind":0,"ty":"sh","ix":1,"ks":{"a":0,"k":{"i":[[75.169,-68.525],[83.252,-57.681],[86.07,-44.945],[76.755,-38.002],[64.018,-35.184],[55.935,-46.03],[53.117999999999995,-58.765],[62.434,-65.708]],"o":[[79.079,-63.279],[87.161,-52.437],[80.824,-41.036],[71.509,-34.091],[60.11,-40.43],[52.026,-51.276],[58.364,-62.676],[67.678,-69.619]],"v":[[79.079,-63.279],[83.252,-57.681],[80.824,-41.036],[76.755,-38.002],[60.11,-40.43],[55.935,-46.03],[58.364,-62.676],[62.434,-65.708]],"c":true},"ix":2},"nm":"Path 1","mn":"ADBE Vector Shape - Group","hd":false,"_render":true},{"ty":"fl","c":{"a":0,"k":[0.8245,0.8636,0.8755,1],"ix":4},"o":{"a":0,"k":100,"ix":5},"r":1,"nm":"Fill 1","mn":"ADBE Vector Graphic - Fill","hd":false,"_render":true},{"ty":"tr","p":{"a":0,"k":[0,0],"ix":2},"a":{"a":0,"k":[0,0],"ix":1},"s":{"a":0,"k":[100,100],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":0,"k":100,"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"Transform","_render":true}],"nm":"Group 35","np":2,"cix":2,"ix":35,"mn":"ADBE Vector Group","hd":false,"_render":true},{"ty":"gr","it":[{"ind":0,"ty":"sh","ix":1,"ks":{"a":0,"k":{"i":[[66.466,28.323999999999998],[72.585,12.338],[79.911,5.058],[94.114,10.497],[101.395,17.821],[95.276,33.807],[87.95,41.086],[73.747,35.648]],"o":[[68.317,23.487],[74.43799999999999,7.5009999999999994],[84.748,6.91],[98.95100000000001,12.347999999999999],[99.544,22.658],[93.423,38.644000000000005],[83.113,39.234],[68.908,33.797000000000004]],"v":[[68.317,23.487],[72.585,12.338],[84.748,6.91],[94.114,10.497],[99.544,22.658],[95.276,33.807],[83.113,39.234],[73.747,35.648]],"c":true},"ix":2},"nm":"Path 1","mn":"ADBE Vector Shape - Group","hd":false,"_render":true},{"ty":"fl","c":{"a":0,"k":[0,0.7255,0.949,1],"ix":4},"o":{"a":0,"k":100,"ix":5},"r":1,"nm":"Fill 1","mn":"ADBE Vector Graphic - Fill","hd":false,"_render":true},{"ty":"tr","p":{"a":0,"k":[0,0],"ix":2},"a":{"a":0,"k":[0,0],"ix":1},"s":{"a":0,"k":[100,100],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":0,"k":100,"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"Transform","_render":true}],"nm":"Group 36","np":2,"cix":2,"ix":36,"mn":"ADBE Vector Group","hd":false,"_render":true},{"ty":"gr","it":[{"ind":0,"ty":"sh","ix":1,"ks":{"a":0,"k":{"i":[[-27.932,71.50200000000001],[-13.736,73.243],[-3.085,79.702],[-4.594,92.003],[-11.051,102.654],[-25.246,100.913],[-35.897999999999996,94.456],[-34.388,82.153]],"o":[[-21.732,72.263],[-7.5360000000000005,74.00399999999999],[-3.847,85.902],[-5.356,98.203],[-17.253,101.893],[-31.448,100.152],[-35.138,88.254],[-33.628,75.953]],"v":[[-21.732,72.263],[-13.736,73.243],[-3.847,85.902],[-4.594,92.003],[-17.253,101.893],[-25.246,100.913],[-35.138,88.254],[-34.388,82.153]],"c":true},"ix":2},"nm":"Path 1","mn":"ADBE Vector Shape - Group","hd":false,"_render":true},{"ty":"fl","c":{"a":0,"k":[0,0.7255,0.949,1],"ix":4},"o":{"a":0,"k":100,"ix":5},"r":1,"nm":"Fill 1","mn":"ADBE Vector Graphic - Fill","hd":false,"_render":true},{"ty":"tr","p":{"a":0,"k":[0,0],"ix":2},"a":{"a":0,"k":[0,0],"ix":1},"s":{"a":0,"k":[100,100],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":0,"k":100,"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"Transform","_render":true}],"nm":"Group 37","np":2,"cix":2,"ix":37,"mn":"ADBE Vector Group","hd":false,"_render":true}],"ip":0,"op":60,"st":0,"bm":0,"completed":true},{"ddd":0,"ind":2,"ty":4,"nm":"glow","parent":1,"sr":1,"ks":{"o":{"a":1,"k":[{"i":{"x":[0.833],"y":[0.833]},"o":{"x":[0.167],"y":[0.167]},"n":["0p833_0p833_0p167_0p167"],"t":0,"s":[10],"e":[30]},{"i":{"x":[0.833],"y":[0.833]},"o":{"x":[0.167],"y":[0.167]},"n":["0p833_0p833_0p167_0p167"],"t":10.467,"s":[30],"e":[10]},{"t":20}],"ix":11,"x":"var $bm_rt;\\n$bm_rt = loopOut();"},"r":{"a":0,"k":0,"ix":10},"p":{"a":0,"k":[-0.54,-0.015,0],"ix":2},"a":{"a":0,"k":[-0.41,-0.012,0],"ix":1},"s":{"a":0,"k":[109,109,100],"ix":6}},"ao":0,"shapes":[{"ty":"gr","it":[{"ind":0,"ty":"sh","ix":1,"ks":{"a":0,"k":{"i":[[118.428,-65.645],[65.224,118.826],[-119.249,65.62],[-66.042,-118.85]],"o":[[118.428,65.62],[-66.042,118.826],[-119.249,-65.645],[65.224,-118.85]],"v":[[118.428,-0.011],[-0.41,118.826],[-119.249,-0.011],[-0.41,-118.85]],"c":true},"ix":2},"nm":"Path 1","mn":"ADBE Vector Shape - Group","hd":false,"_render":true},{"ty":"fl","c":{"a":0,"k":[1,1,1,1],"ix":4},"o":{"a":0,"k":100,"ix":5},"r":1,"nm":"Fill 1","mn":"ADBE Vector Graphic - Fill","hd":false,"_render":true},{"ty":"tr","p":{"a":0,"k":[0,0],"ix":2},"a":{"a":0,"k":[0,0],"ix":1},"s":{"a":0,"k":[100,100],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":0,"k":100,"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"Transform","_render":true}],"nm":"Group 1","np":2,"cix":2,"ix":1,"mn":"ADBE Vector Group","hd":false,"_render":true}],"ip":0,"op":60,"st":0,"bm":0,"completed":true},{"ddd":0,"ind":3,"ty":4,"nm":"L4","sr":1,"ks":{"o":{"a":0,"k":100,"ix":11},"r":{"a":1,"k":[{"i":{"x":[0.833],"y":[0.833]},"o":{"x":[0.167],"y":[0.167]},"n":["0p833_0p833_0p167_0p167"],"t":0,"s":[0],"e":[8]},{"i":{"x":[0.833],"y":[0.833]},"o":{"x":[0.167],"y":[0.167]},"n":["0p833_0p833_0p167_0p167"],"t":10,"s":[8],"e":[0]},{"t":20}],"ix":10},"p":{"a":1,"k":[{"i":{"x":0.833,"y":0.833},"o":{"x":0.167,"y":0.167},"n":"0p833_0p833_0p167_0p167","t":0,"s":[127.941,130.571,0],"e":[121.941,113.571,0],"to":null,"ti":null},{"i":{"x":0.833,"y":0.833},"o":{"x":0.167,"y":0.167},"n":"0p833_0p833_0p167_0p167","t":10,"s":[121.941,113.571,0],"e":[127.941,130.571,0],"to":[0,0,0],"ti":[-1,-2.83333325386047,0]},{"t":20}],"ix":2},"a":{"a":0,"k":[-239.059,5.571,0],"ix":1},"s":{"a":0,"k":[85,85,100],"ix":6}},"ao":0,"shapes":[{"ty":"gr","it":[{"ty":"gr","it":[{"ind":0,"ty":"sh","ix":1,"ks":{"a":0,"k":{"i":[[-193.913,-98.943],[-176.18,-104.327],[-176.807,25.039],[-194.54,30.422]],"o":[[-193.913,-98.943],[-176.18,-104.327],[-176.807,25.039],[-194.54,30.422]],"v":[[-193.913,-98.943],[-176.18,-104.327],[-176.807,25.039],[-194.54,30.422]],"c":true},"ix":2},"nm":"Path 1","mn":"ADBE Vector Shape - Group","hd":false,"_render":true},{"ty":"fl","c":{"a":0,"k":[0,0.7255,0.949,1],"ix":4},"o":{"a":0,"k":100,"ix":5},"r":1,"nm":"Fill 1","mn":"ADBE Vector Graphic - Fill","hd":false,"_render":true},{"ty":"tr","p":{"a":0,"k":[0,0],"ix":2},"a":{"a":0,"k":[0,0],"ix":1},"s":{"a":0,"k":[100,100],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":0,"k":100,"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"Transform","_render":true}],"nm":"Group 1","np":2,"cix":2,"ix":1,"mn":"ADBE Vector Group","hd":false,"_render":true},{"ty":"tr","p":{"a":0,"k":[0,0],"ix":2},"a":{"a":0,"k":[0,0],"ix":1},"s":{"a":0,"k":[100,100],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":0,"k":100,"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"Transform","_render":true}],"nm":"Group 1","np":1,"cix":2,"ix":1,"mn":"ADBE Vector Group","hd":false,"_render":true},{"ty":"gr","it":[{"ty":"gr","it":[{"ind":0,"ty":"sh","ix":1,"ks":{"a":0,"k":{"i":[[-234.696,28.094],[-234.305,-52.56],[-282.935,25.297],[-234.696,28.094]],"o":[[-234.696,28.094],[-234.305,-52.56],[-282.935,25.297],[-234.696,28.094]],"v":[[-234.696,28.094],[-234.305,-52.56],[-282.935,25.297],[-234.696,28.094]],"c":false},"ix":2},"nm":"Path 1","mn":"ADBE Vector Shape - Group","hd":false,"_render":true},{"ind":1,"ty":"sh","ix":2,"ks":{"a":0,"k":{"i":[[-194.54,30.422],[-170.851,31.796],[-171.031,69.02],[-194.72,67.647],[-194.965,118.215],[-235.121,115.886],[-234.876,65.319],[-325,60.093],[-324.849,28.761],[-241.284,-101.69],[-193.913,-98.943]],"o":[[-194.54,30.422],[-170.851,31.796],[-171.031,69.02],[-194.72,67.647],[-194.965,118.215],[-235.121,115.886],[-234.876,65.319],[-325,60.093],[-324.849,28.761],[-241.284,-101.69],[-193.913,-98.943]],"v":[[-194.54,30.422],[-170.851,31.796],[-171.031,69.02],[-194.72,67.647],[-194.965,118.215],[-235.121,115.886],[-234.876,65.319],[-325,60.093],[-324.849,28.761],[-241.284,-101.69],[-193.913,-98.943]],"c":true},"ix":2},"nm":"Path 2","mn":"ADBE Vector Shape - Group","hd":false,"_render":true},{"ty":"fl","c":{"a":0,"k":[0.9072,0.9268,0.9328,1],"ix":4},"o":{"a":0,"k":100,"ix":5},"r":1,"nm":"Fill 1","mn":"ADBE Vector Graphic - Fill","hd":false,"_render":true},{"ty":"tr","p":{"a":0,"k":[0,0],"ix":2},"a":{"a":0,"k":[0,0],"ix":1},"s":{"a":0,"k":[100,100],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":0,"k":100,"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"Transform","_render":true}],"nm":"Group 1","np":3,"cix":2,"ix":1,"mn":"ADBE Vector Group","hd":false,"_render":true},{"ty":"tr","p":{"a":0,"k":[0,0],"ix":2},"a":{"a":0,"k":[0,0],"ix":1},"s":{"a":0,"k":[100,100],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":0,"k":100,"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"Transform","_render":true}],"nm":"Group 2","np":1,"cix":2,"ix":2,"mn":"ADBE Vector Group","hd":false,"_render":true},{"ty":"gr","it":[{"ty":"gr","it":[{"ind":0,"ty":"sh","ix":1,"ks":{"a":0,"k":{"i":[[-241.284,-101.69],[-223.551,-107.073],[-176.18,-104.327],[-193.913,-98.943]],"o":[[-241.284,-101.69],[-223.551,-107.073],[-176.18,-104.327],[-193.913,-98.943]],"v":[[-241.284,-101.69],[-223.551,-107.073],[-176.18,-104.327],[-193.913,-98.943]],"c":true},"ix":2},"nm":"Path 1","mn":"ADBE Vector Shape - Group","hd":false,"_render":true},{"ty":"fl","c":{"a":0,"k":[0.5671,0.6237,0.6929,1],"ix":4},"o":{"a":0,"k":100,"ix":5},"r":1,"nm":"Fill 1","mn":"ADBE Vector Graphic - Fill","hd":false,"_render":true},{"ty":"tr","p":{"a":0,"k":[0,0],"ix":2},"a":{"a":0,"k":[0,0],"ix":1},"s":{"a":0,"k":[100,100],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":0,"k":100,"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"Transform","_render":true}],"nm":"Group 1","np":2,"cix":2,"ix":1,"mn":"ADBE Vector Group","hd":false,"_render":true},{"ty":"tr","p":{"a":0,"k":[0,0],"ix":2},"a":{"a":0,"k":[0,0],"ix":1},"s":{"a":0,"k":[100,100],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":0,"k":100,"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"Transform","_render":true}],"nm":"Group 3","np":1,"cix":2,"ix":3,"mn":"ADBE Vector Group","hd":false,"_render":true},{"ty":"gr","it":[{"ty":"gr","it":[{"ind":0,"ty":"sh","ix":1,"ks":{"a":0,"k":{"i":[[-194.54,30.422],[-176.807,25.039],[-153.118,26.412],[-170.851,31.796]],"o":[[-194.54,30.422],[-176.807,25.039],[-153.118,26.412],[-170.851,31.796]],"v":[[-194.54,30.422],[-176.807,25.039],[-153.118,26.412],[-170.851,31.796]],"c":true},"ix":2},"nm":"Path 1","mn":"ADBE Vector Shape - Group","hd":false,"_render":true},{"ty":"fl","c":{"a":0,"k":[0.5671,0.6237,0.6929,1],"ix":4},"o":{"a":0,"k":100,"ix":5},"r":1,"nm":"Fill 1","mn":"ADBE Vector Graphic - Fill","hd":false,"_render":true},{"ty":"tr","p":{"a":0,"k":[0,0],"ix":2},"a":{"a":0,"k":[0,0],"ix":1},"s":{"a":0,"k":[100,100],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":0,"k":100,"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"Transform","_render":true}],"nm":"Group 1","np":2,"cix":2,"ix":1,"mn":"ADBE Vector Group","hd":false,"_render":true},{"ty":"tr","p":{"a":0,"k":[0,0],"ix":2},"a":{"a":0,"k":[0,0],"ix":1},"s":{"a":0,"k":[100,100],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":0,"k":100,"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"Transform","_render":true}],"nm":"Group 4","np":1,"cix":2,"ix":4,"mn":"ADBE Vector Group","hd":false,"_render":true},{"ty":"gr","it":[{"ty":"gr","it":[{"ind":0,"ty":"sh","ix":1,"ks":{"a":0,"k":{"i":[[-170.851,31.796],[-153.118,26.412],[-153.298,63.637],[-171.031,69.02]],"o":[[-170.851,31.796],[-153.118,26.412],[-153.298,63.637],[-171.031,69.02]],"v":[[-170.851,31.796],[-153.118,26.412],[-153.298,63.637],[-171.031,69.02]],"c":true},"ix":2},"nm":"Path 1","mn":"ADBE Vector Shape - Group","hd":false,"_render":true},{"ty":"fl","c":{"a":0,"k":[0.4735,0.5424,0.6265,1],"ix":4},"o":{"a":0,"k":100,"ix":5},"r":1,"nm":"Fill 1","mn":"ADBE Vector Graphic - Fill","hd":false,"_render":true},{"ty":"tr","p":{"a":0,"k":[0,0],"ix":2},"a":{"a":0,"k":[0,0],"ix":1},"s":{"a":0,"k":[100,100],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":0,"k":100,"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"Transform","_render":true}],"nm":"Group 1","np":2,"cix":2,"ix":1,"mn":"ADBE Vector Group","hd":false,"_render":true},{"ty":"tr","p":{"a":0,"k":[0,0],"ix":2},"a":{"a":0,"k":[0,0],"ix":1},"s":{"a":0,"k":[100,100],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":0,"k":100,"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"Transform","_render":true}],"nm":"Group 5","np":1,"cix":2,"ix":5,"mn":"ADBE Vector Group","hd":false,"_render":true},{"ty":"gr","it":[{"ty":"gr","it":[{"ind":0,"ty":"sh","ix":1,"ks":{"a":0,"k":{"i":[[-194.72,67.647],[-176.987,62.263],[-177.232,112.831],[-194.965,118.215]],"o":[[-194.72,67.647],[-176.987,62.263],[-177.232,112.831],[-194.965,118.215]],"v":[[-194.72,67.647],[-176.987,62.263],[-177.232,112.831],[-194.965,118.215]],"c":true},"ix":2},"nm":"Path 1","mn":"ADBE Vector Shape - Group","hd":false,"_render":true},{"ty":"fl","c":{"a":0,"k":[0,0.7255,0.949,1],"ix":4},"o":{"a":0,"k":100,"ix":5},"r":1,"nm":"Fill 1","mn":"ADBE Vector Graphic - Fill","hd":false,"_render":true},{"ty":"tr","p":{"a":0,"k":[0,0],"ix":2},"a":{"a":0,"k":[0,0],"ix":1},"s":{"a":0,"k":[100,100],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":0,"k":100,"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"Transform","_render":true}],"nm":"Group 1","np":2,"cix":2,"ix":1,"mn":"ADBE Vector Group","hd":false,"_render":true},{"ty":"tr","p":{"a":0,"k":[0,0],"ix":2},"a":{"a":0,"k":[0,0],"ix":1},"s":{"a":0,"k":[100,100],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":0,"k":100,"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"Transform","_render":true}],"nm":"Group 6","np":1,"cix":2,"ix":6,"mn":"ADBE Vector Group","hd":false,"_render":true},{"ty":"gr","it":[{"ty":"gr","it":[{"ind":0,"ty":"sh","ix":1,"ks":{"a":0,"k":{"i":[[-234.305,-52.56],[-216.572,-57.944],[-265.202,19.914],[-282.935,25.297]],"o":[[-234.305,-52.56],[-216.572,-57.944],[-265.202,19.914],[-282.935,25.297]],"v":[[-234.305,-52.56],[-216.572,-57.944],[-265.202,19.914],[-282.935,25.297]],"c":true},"ix":2},"nm":"Path 1","mn":"ADBE Vector Shape - Group","hd":false,"_render":true},{"ty":"fl","c":{"a":0,"k":[0.5671,0.6237,0.6929,1],"ix":4},"o":{"a":0,"k":100,"ix":5},"r":1,"nm":"Fill 1","mn":"ADBE Vector Graphic - Fill","hd":false,"_render":true},{"ty":"tr","p":{"a":0,"k":[0,0],"ix":2},"a":{"a":0,"k":[0,0],"ix":1},"s":{"a":0,"k":[100,100],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":0,"k":100,"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"Transform","_render":true}],"nm":"Group 1","np":2,"cix":2,"ix":1,"mn":"ADBE Vector Group","hd":false,"_render":true},{"ty":"tr","p":{"a":0,"k":[0,0],"ix":2},"a":{"a":0,"k":[0,0],"ix":1},"s":{"a":0,"k":[100,100],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":0,"k":100,"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"Transform","_render":true}],"nm":"Group 7","np":1,"cix":2,"ix":7,"mn":"ADBE Vector Group","hd":false,"_render":true},{"ty":"gr","it":[{"ty":"gr","it":[{"ind":0,"ty":"sh","ix":1,"ks":{"a":0,"k":{"i":[[-282.935,25.297],[-265.202,19.914],[-216.963,22.71],[-234.696,28.094]],"o":[[-282.935,25.297],[-265.202,19.914],[-216.963,22.71],[-234.696,28.094]],"v":[[-282.935,25.297],[-265.202,19.914],[-216.963,22.71],[-234.696,28.094]],"c":true},"ix":2},"nm":"Path 1","mn":"ADBE Vector Shape - Group","hd":false,"_render":true},{"ty":"fl","c":{"a":0,"k":[0.4735,0.5424,0.6265,1],"ix":4},"o":{"a":0,"k":100,"ix":5},"r":1,"nm":"Fill 1","mn":"ADBE Vector Graphic - Fill","hd":false,"_render":true},{"ty":"tr","p":{"a":0,"k":[0,0],"ix":2},"a":{"a":0,"k":[0,0],"ix":1},"s":{"a":0,"k":[100,100],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":0,"k":100,"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"Transform","_render":true}],"nm":"Group 1","np":2,"cix":2,"ix":1,"mn":"ADBE Vector Group","hd":false,"_render":true},{"ty":"tr","p":{"a":0,"k":[0,0],"ix":2},"a":{"a":0,"k":[0,0],"ix":1},"s":{"a":0,"k":[100,100],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":0,"k":100,"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"Transform","_render":true}],"nm":"Group 8","np":1,"cix":2,"ix":8,"mn":"ADBE Vector Group","hd":false,"_render":true}],"ip":0,"op":60,"st":0,"bm":0,"completed":true},{"ddd":0,"ind":4,"ty":4,"nm":"R4","sr":1,"ks":{"o":{"a":0,"k":100,"ix":11},"r":{"a":1,"k":[{"i":{"x":[0.833],"y":[0.833]},"o":{"x":[0.167],"y":[0.167]},"n":["0p833_0p833_0p167_0p167"],"t":0,"s":[0],"e":[-7]},{"i":{"x":[0.833],"y":[0.833]},"o":{"x":[0.167],"y":[0.167]},"n":["0p833_0p833_0p167_0p167"],"t":10,"s":[-7],"e":[0]},{"t":20}],"ix":10},"p":{"a":1,"k":[{"i":{"x":0.833,"y":0.833},"o":{"x":0.167,"y":0.167},"n":"0p833_0p833_0p167_0p167","t":0,"s":[493.238,133.262,0],"e":[484.238,119.262,0],"to":[-1.5,-2.33333325386047,0],"ti":[0,2.66666674613953,0]},{"i":{"x":0.833,"y":0.833},"o":{"x":0.167,"y":0.167},"n":"0p833_0p833_0p167_0p167","t":10,"s":[484.238,119.262,0],"e":[493.238,133.262,0],"to":[0,0,0],"ti":[-1.5,-2.33333325386047,0]},{"t":20}],"ix":2},"a":{"a":0,"k":[238.238,8.262,0],"ix":1},"s":{"a":0,"k":[85,85,100],"ix":6}},"ao":0,"shapes":[{"ty":"gr","it":[{"ty":"gr","it":[{"ind":0,"ty":"sh","ix":1,"ks":{"a":0,"k":{"i":[[164.274,65.924],[152.008,68.266],[151.476,36.714],[163.743,34.371]],"o":[[164.274,65.924],[152.008,68.266],[151.476,36.714],[163.743,34.371]],"v":[[164.274,65.924],[152.008,68.266],[151.476,36.714],[163.743,34.371]],"c":true},"ix":2},"nm":"Path 1","mn":"ADBE Vector Shape - Group","hd":false,"_render":true},{"ty":"fl","c":{"a":0,"k":[0.4735,0.5424,0.6265,1],"ix":4},"o":{"a":0,"k":100,"ix":5},"r":1,"nm":"Fill 1","mn":"ADBE Vector Graphic - Fill","hd":false,"_render":true},{"ty":"tr","p":{"a":0,"k":[0,0],"ix":2},"a":{"a":0,"k":[0,0],"ix":1},"s":{"a":0,"k":[100,100],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":0,"k":100,"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"Transform","_render":true}],"nm":"Group 1","np":2,"cix":2,"ix":1,"mn":"ADBE Vector Group","hd":false,"_render":true},{"ty":"tr","p":{"a":0,"k":[0,0],"ix":2},"a":{"a":0,"k":[0,0],"ix":1},"s":{"a":0,"k":[100,100],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":0,"k":100,"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"Transform","_render":true}],"nm":"Group 1","np":1,"cix":2,"ix":1,"mn":"ADBE Vector Group","hd":false,"_render":true},{"ty":"gr","it":[{"ty":"gr","it":[{"ind":0,"ty":"sh","ix":1,"ks":{"a":0,"k":{"i":[[257.722,28.099],[256.354,-53.129],[207.366,28.281],[257.722,28.099]],"o":[[257.722,28.099],[256.354,-53.129],[207.366,28.281],[257.722,28.099]],"v":[[257.722,28.099],[256.354,-53.129],[207.366,28.281],[257.722,28.099]],"c":false},"ix":2},"nm":"Path 1","mn":"ADBE Vector Shape - Group","hd":false,"_render":true},{"ind":1,"ty":"sh","ix":2,"ks":{"a":0,"k":{"i":[[299.64,27.948],[324.369,27.859],[325,65.345],[300.271,65.434],[301.129,116.356],[259.211,116.507],[258.353,65.585],[164.274,65.924],[163.743,34.371],[247.997,-102.147],[297.446,-102.325]],"o":[[299.64,27.948],[324.369,27.859],[325,65.345],[300.271,65.434],[301.129,116.356],[259.211,116.507],[258.353,65.585],[164.274,65.924],[163.743,34.371],[247.997,-102.147],[297.446,-102.325]],"v":[[299.64,27.948],[324.369,27.859],[325,65.345],[300.271,65.434],[301.129,116.356],[259.211,116.507],[258.353,65.585],[164.274,65.924],[163.743,34.371],[247.997,-102.147],[297.446,-102.325]],"c":true},"ix":2},"nm":"Path 2","mn":"ADBE Vector Shape - Group","hd":false,"_render":true},{"ty":"fl","c":{"a":0,"k":[0.9072,0.9268,0.9328,1],"ix":4},"o":{"a":0,"k":100,"ix":5},"r":1,"nm":"Fill 1","mn":"ADBE Vector Graphic - Fill","hd":false,"_render":true},{"ty":"tr","p":{"a":0,"k":[0,0],"ix":2},"a":{"a":0,"k":[0,0],"ix":1},"s":{"a":0,"k":[100,100],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":0,"k":100,"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"Transform","_render":true}],"nm":"Group 1","np":3,"cix":2,"ix":1,"mn":"ADBE Vector Group","hd":false,"_render":true},{"ty":"tr","p":{"a":0,"k":[0,0],"ix":2},"a":{"a":0,"k":[0,0],"ix":1},"s":{"a":0,"k":[100,100],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":0,"k":100,"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"Transform","_render":true}],"nm":"Group 2","np":1,"cix":2,"ix":2,"mn":"ADBE Vector Group","hd":false,"_render":true},{"ty":"gr","it":[{"ty":"gr","it":[{"ind":0,"ty":"sh","ix":1,"ks":{"a":0,"k":{"i":[[258.353,65.585],[246.087,67.927],[152.008,68.266],[164.274,65.924]],"o":[[258.353,65.585],[246.087,67.927],[152.008,68.266],[164.274,65.924]],"v":[[258.353,65.585],[246.087,67.927],[152.008,68.266],[164.274,65.924]],"c":true},"ix":2},"nm":"Path 1","mn":"ADBE Vector Shape - Group","hd":false,"_render":true},{"ty":"fl","c":{"a":0,"k":[0.0784,0.3137,0.5961,1],"ix":4},"o":{"a":0,"k":100,"ix":5},"r":1,"nm":"Fill 1","mn":"ADBE Vector Graphic - Fill","hd":false,"_render":true},{"ty":"tr","p":{"a":0,"k":[0,0],"ix":2},"a":{"a":0,"k":[0,0],"ix":1},"s":{"a":0,"k":[100,100],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":0,"k":100,"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"Transform","_render":true}],"nm":"Group 1","np":2,"cix":2,"ix":1,"mn":"ADBE Vector Group","hd":false,"_render":true},{"ty":"tr","p":{"a":0,"k":[0,0],"ix":2},"a":{"a":0,"k":[0,0],"ix":1},"s":{"a":0,"k":[100,100],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":0,"k":100,"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"Transform","_render":true}],"nm":"Group 3","np":1,"cix":2,"ix":3,"mn":"ADBE Vector Group","hd":false,"_render":true},{"ty":"gr","it":[{"ty":"gr","it":[{"ind":0,"ty":"sh","ix":1,"ks":{"a":0,"k":{"i":[[163.743,34.371],[151.476,36.714],[235.73,-99.804],[247.997,-102.147]],"o":[[163.743,34.371],[151.476,36.714],[235.73,-99.804],[247.997,-102.147]],"v":[[163.743,34.371],[151.476,36.714],[235.73,-99.804],[247.997,-102.147]],"c":true},"ix":2},"nm":"Path 1","mn":"ADBE Vector Shape - Group","hd":false,"_render":true},{"ty":"fl","c":{"a":0,"k":[0.5671,0.6237,0.6929,1],"ix":4},"o":{"a":0,"k":100,"ix":5},"r":1,"nm":"Fill 1","mn":"ADBE Vector Graphic - Fill","hd":false,"_render":true},{"ty":"tr","p":{"a":0,"k":[0,0],"ix":2},"a":{"a":0,"k":[0,0],"ix":1},"s":{"a":0,"k":[100,100],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":0,"k":100,"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"Transform","_render":true}],"nm":"Group 1","np":2,"cix":2,"ix":1,"mn":"ADBE Vector Group","hd":false,"_render":true},{"ty":"tr","p":{"a":0,"k":[0,0],"ix":2},"a":{"a":0,"k":[0,0],"ix":1},"s":{"a":0,"k":[100,100],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":0,"k":100,"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"Transform","_render":true}],"nm":"Group 4","np":1,"cix":2,"ix":4,"mn":"ADBE Vector Group","hd":false,"_render":true},{"ty":"gr","it":[{"ty":"gr","it":[{"ind":0,"ty":"sh","ix":1,"ks":{"a":0,"k":{"i":[[259.211,116.507],[246.944,118.85],[246.087,67.927],[258.353,65.585]],"o":[[259.211,116.507],[246.944,118.85],[246.087,67.927],[258.353,65.585]],"v":[[259.211,116.507],[246.944,118.85],[246.087,67.927],[258.353,65.585]],"c":true},"ix":2},"nm":"Path 1","mn":"ADBE Vector Shape - Group","hd":false,"_render":true},{"ty":"fl","c":{"a":0,"k":[0.4735,0.5424,0.6265,1],"ix":4},"o":{"a":0,"k":100,"ix":5},"r":1,"nm":"Fill 1","mn":"ADBE Vector Graphic - Fill","hd":false,"_render":true},{"ty":"tr","p":{"a":0,"k":[0,0],"ix":2},"a":{"a":0,"k":[0,0],"ix":1},"s":{"a":0,"k":[100,100],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":0,"k":100,"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"Transform","_render":true}],"nm":"Group 1","np":2,"cix":2,"ix":1,"mn":"ADBE Vector Group","hd":false,"_render":true},{"ty":"tr","p":{"a":0,"k":[0,0],"ix":2},"a":{"a":0,"k":[0,0],"ix":1},"s":{"a":0,"k":[100,100],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":0,"k":100,"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"Transform","_render":true}],"nm":"Group 5","np":1,"cix":2,"ix":5,"mn":"ADBE Vector Group","hd":false,"_render":true},{"ty":"gr","it":[{"ty":"gr","it":[{"ind":0,"ty":"sh","ix":1,"ks":{"a":0,"k":{"i":[[301.129,116.356],[288.862,118.699],[246.944,118.85],[259.211,116.507]],"o":[[301.129,116.356],[288.862,118.699],[246.944,118.85],[259.211,116.507]],"v":[[301.129,116.356],[288.862,118.699],[246.944,118.85],[259.211,116.507]],"c":true},"ix":2},"nm":"Path 1","mn":"ADBE Vector Shape - Group","hd":false,"_render":true},{"ty":"fl","c":{"a":0,"k":[0.0784,0.3137,0.5961,1],"ix":4},"o":{"a":0,"k":100,"ix":5},"r":1,"nm":"Fill 1","mn":"ADBE Vector Graphic - Fill","hd":false,"_render":true},{"ty":"tr","p":{"a":0,"k":[0,0],"ix":2},"a":{"a":0,"k":[0,0],"ix":1},"s":{"a":0,"k":[100,100],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":0,"k":100,"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"Transform","_render":true}],"nm":"Group 1","np":2,"cix":2,"ix":1,"mn":"ADBE Vector Group","hd":false,"_render":true},{"ty":"tr","p":{"a":0,"k":[0,0],"ix":2},"a":{"a":0,"k":[0,0],"ix":1},"s":{"a":0,"k":[100,100],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":0,"k":100,"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"Transform","_render":true}],"nm":"Group 6","np":1,"cix":2,"ix":6,"mn":"ADBE Vector Group","hd":false,"_render":true},{"ty":"gr","it":[{"ty":"gr","it":[{"ind":0,"ty":"sh","ix":1,"ks":{"a":0,"k":{"i":[[257.722,28.099],[245.455,30.442],[244.088,-50.787],[256.354,-53.129]],"o":[[257.722,28.099],[245.455,30.442],[244.088,-50.787],[256.354,-53.129]],"v":[[257.722,28.099],[245.455,30.442],[244.088,-50.787],[256.354,-53.129]],"c":true},"ix":2},"nm":"Path 1","mn":"ADBE Vector Shape - Group","hd":false,"_render":true},{"ty":"fl","c":{"a":0,"k":[0.4735,0.5424,0.6265,1],"ix":4},"o":{"a":0,"k":100,"ix":5},"r":1,"nm":"Fill 1","mn":"ADBE Vector Graphic - Fill","hd":false,"_render":true},{"ty":"tr","p":{"a":0,"k":[0,0],"ix":2},"a":{"a":0,"k":[0,0],"ix":1},"s":{"a":0,"k":[100,100],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":0,"k":100,"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"Transform","_render":true}],"nm":"Group 1","np":2,"cix":2,"ix":1,"mn":"ADBE Vector Group","hd":false,"_render":true},{"ty":"tr","p":{"a":0,"k":[0,0],"ix":2},"a":{"a":0,"k":[0,0],"ix":1},"s":{"a":0,"k":[100,100],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":0,"k":100,"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"Transform","_render":true}],"nm":"Group 7","np":1,"cix":2,"ix":7,"mn":"ADBE Vector Group","hd":false,"_render":true},{"ty":"gr","it":[{"ty":"gr","it":[{"ind":0,"ty":"sh","ix":1,"ks":{"a":0,"k":{"i":[[325,65.345],[312.734,67.687],[288.005,67.776],[300.271,65.434]],"o":[[325,65.345],[312.734,67.687],[288.005,67.776],[300.271,65.434]],"v":[[325,65.345],[312.734,67.687],[288.005,67.776],[300.271,65.434]],"c":true},"ix":2},"nm":"Path 1","mn":"ADBE Vector Shape - Group","hd":false,"_render":true},{"ty":"fl","c":{"a":0,"k":[0.0784,0.3137,0.5961,1],"ix":4},"o":{"a":0,"k":100,"ix":5},"r":1,"nm":"Fill 1","mn":"ADBE Vector Graphic - Fill","hd":false,"_render":true},{"ty":"tr","p":{"a":0,"k":[0,0],"ix":2},"a":{"a":0,"k":[0,0],"ix":1},"s":{"a":0,"k":[100,100],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":0,"k":100,"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"Transform","_render":true}],"nm":"Group 1","np":2,"cix":2,"ix":1,"mn":"ADBE Vector Group","hd":false,"_render":true},{"ty":"tr","p":{"a":0,"k":[0,0],"ix":2},"a":{"a":0,"k":[0,0],"ix":1},"s":{"a":0,"k":[100,100],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":0,"k":100,"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"Transform","_render":true}],"nm":"Group 8","np":1,"cix":2,"ix":8,"mn":"ADBE Vector Group","hd":false,"_render":true}],"ip":0,"op":60,"st":0,"bm":0,"completed":true}],"markers":[],"__complete":true}');

/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId); }
/******/ __webpack_require__.O(0, ["pages/_app","main"], function() { return __webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5Cakshy%5COneDrive%5CDesktop%5CAkshay%5CLOOP_PROJECTS%5Cshade_cms%5Cwebsite%5Csrc%5Cpages%5C404.js&page=%2F404!"); });
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);