<svg width="70" height="70" viewBox="0 0 70 70" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="userGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#fdf2f8;stop-opacity:0.8" />
      <stop offset="100%" style="stop-color:#fce7f3;stop-opacity:0.6" />
    </linearGradient>
    <filter id="blur" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur in="SourceGraphic" stdDeviation="1"/>
    </filter>
  </defs>
  
  <!-- Background -->
  <circle cx="35" cy="35" r="35" fill="url(#userGradient)" stroke="#cbd5e1" stroke-width="0.5" opacity="0.7"/>
  
  <!-- User Icon -->
  <circle cx="35" cy="25" r="12" fill="rgba(148,163,184,0.2)" opacity="0.6" filter="url(#blur)"/>
  <path d="M20 55 Q20 45 35 45 Q50 45 50 55" fill="rgba(148,163,184,0.2)" opacity="0.6" filter="url(#blur)"/>
  
  <!-- Face Details -->
  <circle cx="32" cy="23" r="1.5" fill="rgba(148,163,184,0.4)" opacity="0.5"/>
  <circle cx="38" cy="23" r="1.5" fill="rgba(148,163,184,0.4)" opacity="0.5"/>
  <path d="M30 28 Q35 31 40 28" stroke="rgba(148,163,184,0.4)" stroke-width="1" fill="none" stroke-linecap="round" opacity="0.5"/>
</svg>
