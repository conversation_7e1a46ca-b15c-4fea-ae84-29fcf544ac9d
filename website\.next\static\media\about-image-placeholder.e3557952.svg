<svg width="500" height="500" viewBox="0 0 500 500" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="aboutGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#10B981" stop-opacity="0.4" />
      <stop offset="50%" stop-color="#059669" stop-opacity="0.3" />
      <stop offset="100%" stop-color="#047857" stop-opacity="0.2" />
    </linearGradient>
    <linearGradient id="aboutAccent" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" stop-color="#34D399" stop-opacity="0.15" />
      <stop offset="100%" stop-color="#10B981" stop-opacity="0.05" />
    </linearGradient>
    <linearGradient id="aboutSecondary" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" stop-color="#6EE7B7" stop-opacity="0.1" />
      <stop offset="100%" stop-color="#34D399" stop-opacity="0.05" />
    </linearGradient>
    <filter id="blur" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur in="SourceGraphic" stdDeviation="3"/>
    </filter>
    <filter id="lightBlur" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur in="SourceGraphic" stdDeviation="1.5"/>
    </filter>
  </defs>
  
  <!-- Main background -->
  <rect width="500" height="500" fill="url(#aboutGradient)"/>
  
  <!-- Accent lines -->
  <rect x="0" y="0" width="500" height="3" fill="url(#aboutAccent)"/>
  <rect x="0" y="497" width="500" height="3" fill="url(#aboutAccent)"/>
  
  <!-- Decorative elements -->
  <circle cx="80" cy="80" r="25" fill="rgba(255,255,255,0.05)" filter="url(#blur)"/>
  <circle cx="420" cy="420" r="20" fill="rgba(255,255,255,0.04)" filter="url(#blur)"/>
  <circle cx="250" cy="250" r="12" fill="rgba(255,255,255,0.06)" filter="url(#lightBlur)"/>
  <circle cx="150" cy="350" r="8" fill="rgba(255,255,255,0.05)" filter="url(#blur)"/>
  <circle cx="350" cy="150" r="6" fill="rgba(255,255,255,0.08)" filter="url(#lightBlur)"/>
  
  <!-- About us icon representation - People/Team -->
  <g transform="translate(250, 200)" filter="url(#blur)">
    <!-- Main building/company structure -->
    <rect x="-60" y="-30" width="120" height="60" rx="12" fill="rgba(255,255,255,0.08)"/>
    <rect x="-50" y="-20" width="100" height="40" rx="8" fill="rgba(255,255,255,0.1)"/>
    
    <!-- People silhouettes -->
    <circle cx="-30" cy="-10" r="8" fill="rgba(255,255,255,0.12)"/>
    <circle cx="0" cy="-10" r="8" fill="rgba(255,255,255,0.12)"/>
    <circle cx="30" cy="-10" r="8" fill="rgba(255,255,255,0.12)"/>
    
    <!-- Base/platform -->
    <rect x="-70" y="20" width="140" height="8" rx="4" fill="rgba(255,255,255,0.1)"/>
  </g>
  
  <!-- Mission/Vision/Goal representation -->
  <g transform="translate(250, 350)" filter="url(#blur)">
    <!-- Target/goal circles -->
    <circle cx="-40" cy="0" r="15" fill="rgba(255,255,255,0.05)" stroke="rgba(255,255,255,0.1)" stroke-width="2"/>
    <circle cx="0" cy="0" r="20" fill="rgba(255,255,255,0.05)" stroke="rgba(255,255,255,0.1)" stroke-width="2"/>
    <circle cx="40" cy="0" r="15" fill="rgba(255,255,255,0.05)" stroke="rgba(255,255,255,0.1)" stroke-width="2"/>
    
    <!-- Connecting lines -->
    <line x1="-25" y1="0" x2="25" y2="0" stroke="rgba(255,255,255,0.1)" stroke-width="2"/>
  </g>
  
  <!-- Subtle overlay pattern -->
  <rect x="0" y="0" width="500" height="500" fill="url(#aboutSecondary)" opacity="0.15"/>
</svg>
