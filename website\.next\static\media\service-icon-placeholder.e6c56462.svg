<svg width="44" height="44" viewBox="0 0 44 44" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="serviceGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#e0f2fe;stop-opacity:0.8" />
      <stop offset="100%" style="stop-color:#bae6fd;stop-opacity:0.6" />
    </linearGradient>
    <filter id="blur" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur in="SourceGraphic" stdDeviation="1"/>
    </filter>
  </defs>
  
  <!-- Background -->
  <rect width="44" height="44" rx="8" fill="url(#serviceGradient)" stroke="#cbd5e1" stroke-width="0.5" opacity="0.7"/>
  
  <!-- Gear Icon -->
  <circle cx="22" cy="22" r="8" fill="none" stroke="#94a3b8" stroke-width="1.5" opacity="0.6" filter="url(#blur)"/>
  <circle cx="22" cy="22" r="3" fill="#94a3b8" opacity="0.6" filter="url(#blur)"/>
  
  <!-- Gear Teeth -->
  <rect x="20" y="8" width="4" height="6" fill="#94a3b8" rx="2" opacity="0.5" filter="url(#blur)"/>
  <rect x="20" y="30" width="4" height="6" fill="#94a3b8" rx="2" opacity="0.5" filter="url(#blur)"/>
  <rect x="8" y="20" width="6" height="4" fill="#94a3b8" rx="2" opacity="0.5" filter="url(#blur)"/>
  <rect x="30" y="20" width="6" height="4" fill="#94a3b8" rx="2" opacity="0.5" filter="url(#blur)"/>
  
  <!-- Diagonal Teeth -->
  <rect x="26" y="10" width="3" height="4" fill="#94a3b8" rx="1" transform="rotate(45 27.5 12)" opacity="0.5" filter="url(#blur)"/>
  <rect x="15" y="30" width="3" height="4" fill="#94a3b8" rx="1" transform="rotate(45 16.5 32)" opacity="0.5" filter="url(#blur)"/>
  <rect x="10" y="26" width="4" height="3" fill="#94a3b8" rx="1" transform="rotate(45 12 27.5)" opacity="0.5" filter="url(#blur)"/>
  <rect x="30" y="15" width="4" height="3" fill="#94a3b8" rx="1" transform="rotate(45 32 16.5)" opacity="0.5" filter="url(#blur)"/>
</svg>
