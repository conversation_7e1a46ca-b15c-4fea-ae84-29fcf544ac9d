// Job Listing Schema
// This schema defines the structure for job postings in the careers section

// Enums for Job-related fields
enum JobType {
  FULL_TIME
  PART_TIME
  CONTRACT
  INTERNSHIP
  REMOTE
  HYBRID
}

enum JobStatus {
  ACTIVE
  INACTIVE
  CLOSED
  DRAFT
}

enum ExperienceLevel {
  ENTRY_LEVEL
  MID_LEVEL
  SENIOR_LEVEL
  EXECUTIVE
  INTERNSHIP
}

enum JobCategory {
  ENGINEERING
  MANAGEMENT
  DESIGN
  MARKETING
  SALES
  HR
  FINANCE
  OPERATIONS
  IT
  CONSTRUCTION
  PROJECT_MANAGEMENT
  QUALITY_ASSURANCE
  SAFETY
  OTHER
}

// Main Job model
model Job {
  id                String          @id @default(cuid())
  
  // Basic Information
  title             String          // Job title (e.g., "Software Engineer")
  slug              String          @unique // URL-friendly version of title
  category          JobCategory     @default(OTHER)
  department        String?         // Department name
  
  // Job Details
  description       String          @db.Text // Full job description
  shortDescription  String?         @db.Text // Brief summary for listing page
  responsibilities  Json?           // Array of job responsibilities
  requirements      Json?           // Array of job requirements
  qualifications    Json?           // Array of qualifications
  benefits          Json?           // Array of benefits offered
  
  // Job Specifications
  jobType           JobType         @default(FULL_TIME)
  experienceLevel   ExperienceLevel @default(ENTRY_LEVEL)
  minExperience     Int?            // Minimum years of experience
  maxExperience     Int?            // Maximum years of experience
  
  // Location and Remote Work
  location          String          // Job location (e.g., "Riyadh, SA")
  isRemote          Boolean         @default(false)
  isHybrid          Boolean         @default(false)
  
  // Salary Information (optional)
  minSalary         Decimal?        @db.Decimal(10, 2)
  maxSalary         Decimal?        @db.Decimal(10, 2)
  currency          String?         @default("SAR")
  salaryPeriod      String?         @default("MONTHLY") // MONTHLY, YEARLY, HOURLY
  
  // Application Details
  applicationDeadline DateTime?     // When applications close
  applicationEmail    String?       // Email for applications
  applicationUrl      String?       // External application URL
  applicationInstructions String?   @db.Text // Special instructions for applicants
  
  // SEO and Display
  metaTitle         String?
  metaDescription   String?         @db.Text
  keywords          Json?           // Array of keywords for search
  
  // Status and Visibility
  status            JobStatus       @default(DRAFT)
  isPublished       Boolean         @default(false)
  isFeatured        Boolean         @default(false) // Featured jobs appear first
  
  // Tracking
  viewCount         Int             @default(0)
  applicationCount  Int             @default(0)
  
  // Timestamps
  createdAt         DateTime        @default(now())
  updatedAt         DateTime        @updatedAt
  publishedAt       DateTime?       // When the job was published
  
  // Relations
  createdBy         String          // User ID who created the job
  updatedBy         String?         // User ID who last updated the job
  
  // Applications relation (for future expansion)
  applications      JobApplication[]
  
  @@map("jobs")
  @@index([status, isPublished])
  @@index([category, jobType])
  @@index([location])
  @@index([createdAt])
  @@index([applicationDeadline])
}

// Job Application model (for future expansion)
model JobApplication {
  id              String    @id @default(cuid())
  
  // Job relation
  jobId           String
  job             Job       @relation(fields: [jobId], references: [id], onDelete: Cascade)
  
  // Applicant Information
  firstName       String
  lastName        String
  email           String
  phone           String?
  
  // Application Details
  coverLetter     String?   @db.Text
  resumeUrl       String?   // URL to uploaded resume
  portfolioUrl    String?   // Portfolio/LinkedIn URL
  
  // Additional Information
  expectedSalary  Decimal?  @db.Decimal(10, 2)
  availableFrom   DateTime?
  additionalInfo  String?   @db.Text
  
  // Status
  status          ApplicationStatus @default(PENDING)
  
  // Timestamps
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt
  
  @@map("job_applications")
  @@index([jobId])
  @@index([email])
  @@index([status])
  @@index([createdAt])
}

enum ApplicationStatus {
  PENDING
  REVIEWING
  SHORTLISTED
  INTERVIEWED
  OFFERED
  HIRED
  REJECTED
  WITHDRAWN
}
