{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Akshay\\\\LOOP_PROJECTS\\\\shade_cms\\\\dashboard\\\\src\\\\features\\\\Resources\\\\components\\\\websiteComponent\\\\CareersPage.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useRef, useState } from \"react\";\nimport content from \"./content.json\";\n// import styles from \"./Career.module.scss\";\n// import localFont from \"next/font/local\";\nimport { motion } from \"framer-motion\";\n// import ApplyModal from \"./ApplyModal\";\n// import Pagination from \"../../common/Pagination\";\nimport Pagination from \"./subparts/Pagination\";\n// const AnimatedText = dynamic(() => import('@/common/AnimatedText'), { ssr: false });\nimport Arrow from \"../../../../assets/icons/right-wrrow.svg\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { updateMainContent } from \"../../../common/homeContentSlice\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CareerPage = ({\n  language,\n  screen\n}) => {\n  _s();\n  var _currentContent$jobLi, _currentContent$banne, _currentContent$banne2, _currentContent$filte, _currentContent$filte2, _currentContent$filte3, _currentContent$filte4, _currentContent$filte5, _currentContent$filte6, _currentContent$filte7, _currentContent$filte8;\n  const isLeftAlign = language === 'en';\n  const contentRef = useRef([]);\n  const dispatch = useDispatch();\n  const isPhone = screen < 760;\n  const isTablet = screen > 761 && screen < 1100;\n  const ImageFromRedux = useSelector(state => state.homeContent.present.images);\n  const currentContent = useSelector(state => state.homeContent.present.careers);\n  const [activeIndex, setActiveIndex] = useState(null);\n  const [isModal, setIsModal] = useState(false);\n  const [selectedJob, setSelectedJob] = useState(null);\n  const [selectedPage, setSelectedPage] = useState(1);\n  const [totalDocuments, setTotalDocuments] = useState(0);\n  const itemsPerPage = 10;\n  const [searchTerm, setSearchTerm] = useState(null);\n  const [filteredJobs, setFilteredJobs] = useState((currentContent === null || currentContent === void 0 ? void 0 : (_currentContent$jobLi = currentContent.jobListSection) === null || _currentContent$jobLi === void 0 ? void 0 : _currentContent$jobLi.jobs) || []);\n  const toggleAccordion = index => {\n    setActiveIndex(activeIndex === index ? null : index);\n  };\n  const debounce = (func, delay) => {\n    let timeoutId;\n    return (...args) => {\n      if (timeoutId) clearTimeout(timeoutId);\n      timeoutId = setTimeout(() => {\n        func(...args);\n      }, delay);\n    };\n  };\n\n  // Search function\n  const searchJobs = term => {\n    var _currentContent$jobLi2;\n    setSearchTerm(term);\n    let filtered = (currentContent === null || currentContent === void 0 ? void 0 : (_currentContent$jobLi2 = currentContent.jobListSection) === null || _currentContent$jobLi2 === void 0 ? void 0 : _currentContent$jobLi2.jobs) || [];\n    if (term) {\n      filtered = filtered.filter(job => job.title.value[language].toLowerCase().includes(term.toLowerCase()));\n    }\n\n    // Filter only displayed jobs\n    filtered = filtered.filter(job => job.display);\n    setFilteredJobs(filtered);\n    setTotalDocuments(filtered.length);\n\n    // Reset page number if fewer than 10 jobs are displayed\n    if (filtered.length <= itemsPerPage) {\n      setSelectedPage(1);\n    }\n  };\n  const debouncedSearchJobs = debounce(searchJobs, 300);\n  const handleChange = e => {\n    const value = e.target.value;\n    debouncedSearchJobs(value);\n  };\n  useEffect(() => {\n    let isMounted = true; // Flag to track if component is mounted\n\n    dispatch(updateMainContent({\n      currentPath: \"careers\",\n      payload: content.careers\n    }));\n    return () => {\n      isMounted = false; // Mark as unmounted when component unmounts\n    };\n  }, []);\n\n  // useEffect to reset filtered jobs when content changes\n  useEffect(() => {\n    var _currentContent$jobLi3, _currentContent$jobLi4;\n    const displayedJobs = (currentContent === null || currentContent === void 0 ? void 0 : (_currentContent$jobLi3 = currentContent.jobListSection) === null || _currentContent$jobLi3 === void 0 ? void 0 : (_currentContent$jobLi4 = _currentContent$jobLi3.jobs) === null || _currentContent$jobLi4 === void 0 ? void 0 : _currentContent$jobLi4.filter(job => job.display)) || [];\n    setFilteredJobs(displayedJobs);\n    setTotalDocuments(displayedJobs.length);\n\n    // Reset pagination when job count is below 10\n    if (displayedJobs.length <= itemsPerPage) {\n      setSelectedPage(1);\n    }\n  }, [currentContent]);\n  const handlePageChange = result => {\n    setSelectedPage(result);\n  };\n  const paginatedJobs = filteredJobs.slice((selectedPage - 1) * itemsPerPage, selectedPage * itemsPerPage);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"w-full\",\n    children: [/*#__PURE__*/_jsxDEV(\"section\", {\n      className: `relative h-full w-full bg-cover bg-center ${isLeftAlign ? 'scale-x-[-1]' : ''}  `,\n      style: {\n        height: 1200 * 0.436,\n        backgroundImage: ImageFromRedux !== null && ImageFromRedux !== void 0 && ImageFromRedux.careersBanner ? `url(${ImageFromRedux.careersBanner})` : \"url('https://loopwebsite.s3.ap-south-1.amazonaws.com/Hero+(2).jpg')\"\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `container h-full relative ${isPhone ? \"px-10\" : \"px-20\"} flex items-center ${isLeftAlign ? \"justify-end\" : \"justify-end\"}   `,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `flex flex-col ${isLeftAlign ? 'right-5 text-left items-start ' : 'left-5 text-right items-end'} ${isPhone ? \"max-w-[70%]\" : \"max-w-[55%]\"} w-full ${isLeftAlign ? 'scale-x-[-1]' : ''}`,\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: `text-[#292E3D] ${isPhone ? \"text-3xl\" : \"text-[50px] leading-[77px] tracking-[-3.5px]\"} font-medium  mb-4`,\n            children: currentContent === null || currentContent === void 0 ? void 0 : (_currentContent$banne = currentContent.bannerSection) === null || _currentContent$banne === void 0 ? void 0 : _currentContent$banne.title[language]\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 120,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: `text-[#0E172FB3] ${isPhone ? \"\" : \"leading-[28px]\"} text-sm font-semibold  mb-6 word-spacing-5`,\n            children: currentContent === null || currentContent === void 0 ? void 0 : (_currentContent$banne2 = currentContent.bannerSection) === null || _currentContent$banne2 === void 0 ? void 0 : _currentContent$banne2.description[language]\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 124,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 119,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 118,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 112,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: `${language === \"en\" ? \"text-left\" : \"\"} py-8 pt-16 px-20`,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `flex items-center gap-5 \n                ${isPhone ? \"flex-col\" : isTablet ? \"gap-4 flex-col-reverse\" : \"justify-between\"} \n                ${!isLeftAlign && !isPhone && !isTablet ? \"flex-row-reverse\" : \"\"}`,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: `flex items-center ${!isLeftAlign && \"flex-row-reverse\"} gap-2.5 p-2 border rounded-lg border-gray-300  \n                    ${isPhone ? \"w-full\" : isTablet ? \"px-4\" : \"w-auto\"}`,\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              src: \"https://loopwebsite.s3.ap-south-1.amazonaws.com/material-symbols_search+(1).svg\",\n              alt: \"icon\",\n              width: 24,\n              height: 24,\n              className: \"w-6 h-6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 158,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              dir: !isLeftAlign ? \"rtl\" : \"ltr\",\n              title: currentContent === null || currentContent === void 0 ? void 0 : (_currentContent$filte = currentContent.filterSection) === null || _currentContent$filte === void 0 ? void 0 : (_currentContent$filte2 = _currentContent$filte.inputBox) === null || _currentContent$filte2 === void 0 ? void 0 : _currentContent$filte2.placeholder[language],\n              placeholder: currentContent === null || currentContent === void 0 ? void 0 : (_currentContent$filte3 = currentContent.filterSection) === null || _currentContent$filte3 === void 0 ? void 0 : (_currentContent$filte4 = _currentContent$filte3.inputBox) === null || _currentContent$filte4 === void 0 ? void 0 : _currentContent$filte4.placeholder[language],\n              className: `w-full border-none focus:outline-none text-gray-700 text-base font-light`,\n              \"aria-label\": currentContent === null || currentContent === void 0 ? void 0 : (_currentContent$filte5 = currentContent.filterSection) === null || _currentContent$filte5 === void 0 ? void 0 : (_currentContent$filte6 = _currentContent$filte5.inputBox) === null || _currentContent$filte6 === void 0 ? void 0 : _currentContent$filte6.aria_label[language],\n              onChange: e => handleChange(e, \"Job Search\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 165,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 154,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `flex items-center gap-8 \n                    ${isPhone ? \"flex-col w-full gap-4\" : isTablet ? \"gap-6\" : \"\"} relative`,\n            children: currentContent === null || currentContent === void 0 ? void 0 : (_currentContent$filte7 = currentContent.filterSection) === null || _currentContent$filte7 === void 0 ? void 0 : (_currentContent$filte8 = _currentContent$filte7.filtersSelections) === null || _currentContent$filte8 === void 0 ? void 0 : _currentContent$filte8.map((filter, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `relative flex ${isPhone ? \"w-full justify-between\" : \"\"} bg-transparent`,\n              children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                src: \"https://loopwebsite.s3.ap-south-1.amazonaws.com/weui_arrow-outlined+(1).svg\",\n                alt: \"icon\",\n                width: 20,\n                height: 20,\n                className: `${isPhone ? \"absolute top-2 left-2 z-[1]\" : \"\"} `\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 180,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                className: `border-none z-[2] bg-transparent text-gray-700 text-center text-sm font-light focus:outline-none\n                                ${isPhone ? \"w-full text-left p-2 border rounded-lg\" : isTablet ? \"px-2\" : \"\"}`,\n                \"aria-label\": filter === null || filter === void 0 ? void 0 : filter.title[language],\n                onChange: e => handleChange(e, filter.title[language]),\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: filter === null || filter === void 0 ? void 0 : filter.title[language]\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 193,\n                  columnNumber: 41\n                }, this), filter.options.map((option, optIndex) => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: option === null || option === void 0 ? void 0 : option.value,\n                  children: option === null || option === void 0 ? void 0 : option.title[language]\n                }, optIndex, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 195,\n                  columnNumber: 45\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 187,\n                columnNumber: 37\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 179,\n              columnNumber: 33\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 174,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 149,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 148,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 145,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: `w-full ${isLeftAlign ? \"text-left\" : \"text-right\"} px-12`,\n      children: [(paginatedJobs === null || paginatedJobs === void 0 ? void 0 : paginatedJobs.length) < 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex h-24 w-full items-center justify-center\",\n        children: /*#__PURE__*/_jsxDEV(\"h1\", {\n          children: \"Oops... Not found !\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 212,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 211,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `container mx-auto p-4 ${isPhone && \"px-2\"}`,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-8\",\n          children: paginatedJobs === null || paginatedJobs === void 0 ? void 0 : paginatedJobs.map((job, index) => {\n            var _job$title, _job$title2, _job$location, _job$location2, _job$deadline, _job$deadline2, _currentContent$jobLi5, _currentContent$jobLi6, _currentContent$jobLi7, _currentContent$jobLi8, _contentRef$current$i;\n            if (!job.display) return null;\n            return /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `rounded-lg bg-gray-100 py-8 ${isPhone ? \"px-2\" : \"px-4\"} shadow-md`,\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: `flex items-center justify-between cursor-pointer ${isPhone ? \"flex-col gap-2\" : isTablet ? \"flex-wrap gap-1\" : \"gap-5\"} ${!isLeftAlign ? \"flex-row-reverse\" : \"\"}`,\n                onClick: () => toggleAccordion(index),\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `flex ${isPhone ? \"\" : isTablet ? \"\" : \"w-3/4\"} ${isPhone ? \"w-[80%] pl-2\" : \"\"} items-center gap-2 ${!isLeftAlign && \"flex-row-reverse\"}`,\n                  children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                    src: \"https://loopwebsite.s3.ap-south-1.amazonaws.com/weui_arrow-outlined+(1).svg\",\n                    alt: \"icon\",\n                    width: 28,\n                    height: 28\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 227,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                      className: \"text-md font-bold text-gray-800\",\n                      children: job === null || job === void 0 ? void 0 : (_job$title = job.title) === null || _job$title === void 0 ? void 0 : _job$title.key[language]\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 234,\n                      columnNumber: 49\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm font-light text-gray-600\",\n                      children: job === null || job === void 0 ? void 0 : (_job$title2 = job.title) === null || _job$title2 === void 0 ? void 0 : _job$title2.value[language]\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 235,\n                      columnNumber: 49\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 233,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 226,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: ` ${isPhone ? \"\" : isTablet ? \"\" : \"w-1/2\"} ${isPhone ? \" w-[70%] pl-5\" : `${!isLeftAlign ? \"border-r pr-5\" : \"border-l pl-5\"} `} border-gray-300`,\n                  children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                    className: \"text-sm font-light text-gray-600\",\n                    children: job === null || job === void 0 ? void 0 : (_job$location = job.location) === null || _job$location === void 0 ? void 0 : _job$location.key[language]\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 239,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-md font-bold text-gray-800\",\n                    children: job === null || job === void 0 ? void 0 : (_job$location2 = job.location) === null || _job$location2 === void 0 ? void 0 : _job$location2.value[language]\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 240,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 238,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: ` ${isPhone ? \"\" : isTablet ? \"\" : \"w-1/2\"} ${isPhone ? \" w-[70%] pl-5\" : \"border-l border-r px-5\"} border-gray-300`,\n                  children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                    className: \"text-sm font-light text-gray-600\",\n                    children: job === null || job === void 0 ? void 0 : (_job$deadline = job.deadline) === null || _job$deadline === void 0 ? void 0 : _job$deadline.key[language]\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 243,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-md font-bold text-gray-800\",\n                    children: job === null || job === void 0 ? void 0 : (_job$deadline2 = job.deadline) === null || _job$deadline2 === void 0 ? void 0 : _job$deadline2.value[language]\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 244,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 242,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `mt-4 ${isPhone ? \"\" : isTablet ? \"\" : \"\"} flex items-center gap-6 ${!isLeftAlign && \"flex-row-reverse\"} ${isTablet ? \"w-\" : \"\"}`,\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"rounded bg-[#00b9f2] px-3 w-[102px] py-2 text-white text-xs\",\n                    onClick: () => {\n                      var _job$title3;\n                      setIsModal(true);\n                      setSelectedJob(job === null || job === void 0 ? void 0 : (_job$title3 = job.title) === null || _job$title3 === void 0 ? void 0 : _job$title3.value[language]);\n                    },\n                    children: currentContent === null || currentContent === void 0 ? void 0 : (_currentContent$jobLi5 = currentContent.jobListSection) === null || _currentContent$jobLi5 === void 0 ? void 0 : (_currentContent$jobLi6 = _currentContent$jobLi5.buttons[0]) === null || _currentContent$jobLi6 === void 0 ? void 0 : _currentContent$jobLi6.text[language]\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 247,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"rounded border border-[#00b9f2] px-6 py-2 text-[#00b9f2] text-xs\",\n                    children: currentContent === null || currentContent === void 0 ? void 0 : (_currentContent$jobLi7 = currentContent.jobListSection) === null || _currentContent$jobLi7 === void 0 ? void 0 : (_currentContent$jobLi8 = _currentContent$jobLi7.buttons[1]) === null || _currentContent$jobLi8 === void 0 ? void 0 : _currentContent$jobLi8.text[language]\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 256,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 246,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 222,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                ref: el => contentRef.current[index] = el,\n                initial: false,\n                animate: activeIndex === index ? {\n                  height: ((_contentRef$current$i = contentRef.current[index]) === null || _contentRef$current$i === void 0 ? void 0 : _contentRef$current$i.scrollHeight) || \"auto\",\n                  opacity: 1\n                } : {\n                  height: 0,\n                  opacity: 0\n                },\n                transition: {\n                  duration: 0.5,\n                  ease: [0.4, 0, 0.2, 1]\n                },\n                className: \"overflow-hidden\",\n                children: /*#__PURE__*/_jsxDEV(\"ul\", {\n                  className: \"mt-5 list-disc space-y-3 pl-6 text-gray-700\",\n                  children: job.descriptionList.map((desc, i) => /*#__PURE__*/_jsxDEV(\"li\", {\n                    children: desc[language]\n                  }, i, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 277,\n                    columnNumber: 49\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 275,\n                  columnNumber: 41\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 264,\n                columnNumber: 37\n              }, this)]\n            }, job.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 220,\n              columnNumber: 33\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 216,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 215,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 209,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"flex justify-end gap-5 h-[94px] pr-12\",\n      children: /*#__PURE__*/_jsxDEV(Pagination, {\n        totalDocuments: totalDocuments,\n        handlePageChange: handlePageChange,\n        selectedPage: selectedPage,\n        language: language\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 290,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 289,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 110,\n    columnNumber: 9\n  }, this);\n};\n_s(CareerPage, \"TaqIrbGjgp/SfkS85monylruUi0=\", false, function () {\n  return [useDispatch, useSelector, useSelector];\n});\n_c = CareerPage;\nexport default CareerPage;\n\n// {/*\n//  <section\n//         className={` ${language === \"en\" && styles.leftAlign}   ${\n//           styles.career_banner_wrap\n//         }`}\n//       >\n//         <div\n//           className=\"container\"\n//           style={{ height: \"100%\", position: \"relative\" }}\n//         >\n//           <div className={styles.content}>\n//             {/* <AnimatedText text=\"مهنة\" Wrapper=\"h2\" repeatDelay={0.04} className={`${styles.title} ${BankGothic.className}`} /> */}\n\n//             <h1 className={`${styles.title} `}>\n//               {currentContent?.bannerSection?.title[language]}\n//             </h1>\n//             <p className={`${styles.description} ${BankGothic.className}`}>\n//               {currentContent?.bannerSection?.description[language]}\n//             </p>\n//             {/* <Button\n//               className={styles.view_btn}\n//               onClick={() => setIsModal(true)}\n//             >\n//               <Image\n//                 src={Arrow}\n//                 width=\"18\"\n//                 height=\"17\"\n//                 alt=\"\"\n//                 className={styles.arrow_btn}\n//               />\n//               {currentContent?.bannerSection?.button?.text[language]}\n//             </Button> */}\n//           </div>\n//         </div>\n//       </section>\n\n//       <section\n//         className={` ${language === \"en\" && styles.leftAlign}   ${\n//           styles.job_filter_container\n//         }`}\n//       >\n//         <div className=\"container\">\n//           <div className={styles.job_wrap}>\n//             <div className={styles.serach_wrap}>\n//               <Image\n//                 src=\"https://loopwebsite.s3.ap-south-1.amazonaws.com/material-symbols_search+(1).svg\"\n//                 alt=\"icon\"\n//                 width={24}\n//                 height={24}\n//                 className={styles.icon}\n//               />\n//               <input\n//                 title={\n//                   currentContent?.filterSection?.inputBox?.placeholder[language]\n//                 }\n//                 placeholder={\n//                   currentContent?.filterSection?.inputBox?.placeholder[language]\n//                 }\n//                 className={`${BankGothic.className} ${styles.input_form}`}\n//                 aria-label={\n//                   currentContent?.filterSection?.inputBox?.aria_label[language]\n//                 }\n//                 onChange={(e) => handleChange(e, \"Job Search\")} // Update to call handleChange\n//               />\n//             </div>\n//             <div className={styles.select_filter_wrap}>\n//               {currentContent?.filterSection?.filtersSelections?.map(\n//                 (filter, index) => (\n//                   <div key={index} className={styles.select_wrapper}>\n//                     <select\n//                       className={`${BankGothic.className} ${styles.select_form}`}\n//                       aria-label={filter?.title[language]}\n//                       onChange={(e) => handleChange(e, filter.title[language])}\n//                     >\n//                       {/* // Updated here */}\n//                       <option value=\"\" disabled selected>\n//                         {filter?.title[language]}\n//                       </option>\n//                       {filter.options.map((option, optIndex) => (\n//                         <option key={optIndex} value={option?.value}>\n//                           {option?.title[language]}\n//                         </option>\n//                       ))}\n//                     </select>\n//                   </div>\n//                 )\n//               )}\n//             </div>\n//           </div>\n//         </div>\n//       </section>\n\n//       <section\n//         className={` ${language === \"en\" && styles.leftAlign}   ${\n//           styles.job_List_container\n//         }`}\n//       >\n//         {paginatedJobs?.length < 1 && (\n//           <>\n//             <div\n//               style={{\n//                 height: \"100px\",\n//                 width: \"100%\",\n//                 display: \"flex\",\n//                 justifyContent: \"center\",\n//                 alignItems: \"center\",\n//               }}\n//             >\n//               <h1>\n//                 {language === \"en\"\n//                   ? \"Oops... Not found !\"\n//                   : \"Oops... Not found !\"}\n//               </h1>\n//             </div>\n//           </>\n//         )}\n//         <div className=\"container\">\n//           <div className={styles.accordion}>\n//             {paginatedJobs?.map((job, index) => (\n//               <div key={job.id} className={styles.accordion_item}>\n//                 <div className={styles.accordion_title}>\n//                   <div\n//                     className={styles.Accordion_head}\n//                     onClick={() => toggleAccordion(index)}\n//                   >\n//                     <div className={styles.jobname_wrap}>\n//                       <Image\n//                         src=\"https://loopwebsite.s3.ap-south-1.amazonaws.com/weui_arrow-outlined+(1).svg\"\n//                         alt=\"icon\"\n//                         width={28}\n//                         height={28}\n//                         className={styles.icon}\n//                       />\n//                       <div>\n//                         <h5\n//                           className={`${styles.title} ${BankGothic.className}`}\n//                         >\n//                           {job?.title?.key[language]}\n//                         </h5>\n//                         <p\n//                           className={`${styles.subtitle} ${BankGothic.className}`}\n//                         >\n//                           {job?.title?.value[language]}\n//                         </p>\n//                       </div>\n//                     </div>\n//                     <div className={styles.job_location_wrap}>\n//                       <h5 className={`${styles.title} ${BankGothic.className}`}>\n//                         {job?.location?.key[language]}\n//                       </h5>\n//                       <p\n//                         className={`${styles.subtitle} ${BankGothic.className}`}\n//                       >\n//                         {job?.location?.value[language]}\n//                       </p>\n//                     </div>\n//                     <div className={styles.job_apply_wrap}>\n//                       <h5 className={`${styles.title} ${BankGothic.className}`}>\n//                         {job?.deadline?.key[language]}\n//                       </h5>\n//                       <p\n//                         className={`${styles.subtitle} ${BankGothic.className}`}\n//                       >\n//                         {job?.deadline?.value[language]}\n//                       </p>\n//                     </div>\n//                   </div>\n//                   <div className={styles.button_group}>\n//                     <Button\n//                       className={styles.primary}\n//                       onClick={() => {\n//                         setIsModal(true);\n//                         setSelectedJob(job?.title?.value[language]);\n//                       }}\n//                     >\n//                       {\n//                         currentContent?.jobListSection?.buttons[0]?.text[\n//                           language\n//                         ]\n//                       }\n//                     </Button>\n//                     <Button\n//                       className={styles.outline}\n//                       onClick={() => router.push(`/career/${job.id}`)}\n//                     >\n//                       {\n//                         currentContent?.jobListSection?.buttons[1]?.text[\n//                           language\n//                         ]\n//                       }\n//                     </Button>\n//                   </div>\n//                 </div>\n\n//                 <motion.div\n//                   ref={(el) => (contentRef.current[index] = el)}\n//                   initial={false}\n//                   animate={\n//                     activeIndex === index\n//                       ? {\n//                           height:\n//                             contentRef.current[index]?.scrollHeight || \"auto\",\n//                           opacity: 1,\n//                         }\n//                       : { height: 0, opacity: 0 }\n//                   }\n//                   transition={{ duration: 0.5, ease: [0.4, 0, 0.2, 1] }}\n//                   className={`${styles.accordion_content}`}\n//                 >\n//                   <ul className={styles.job_list_wrap}>\n//                     {job.descriptionList.map((desc, i) => (\n//                       <li key={i} className={BankGothic.className}>\n//                         {desc[language]}\n//                       </li>\n//                     ))}\n//                   </ul>\n//                 </motion.div>\n//               </div>\n//             ))}\n//           </div>\n//         </div>\n//       </section>\n\n//       <section className={styles.paginationSection}>\n//         <Pagination\n//           totalDocuments={totalDocuments}\n//           handlePageChange={handlePageChange}\n//           selectedPage={selectedPage}\n//         />\n//       </section>\n\n//       <ApplyModal\n//         isModal={isModal}\n//         jobTitle={selectedJob}\n//         onClose={() => setIsModal(false)}\n//       /> \n// */}\nvar _c;\n$RefreshReg$(_c, \"CareerPage\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "useState", "content", "motion", "Pagination", "Arrow", "useDispatch", "useSelector", "update<PERSON>ain<PERSON><PERSON>nt", "jsxDEV", "_jsxDEV", "CareerPage", "language", "screen", "_s", "_currentContent$jobLi", "_currentContent$banne", "_currentContent$banne2", "_currentContent$filte", "_currentContent$filte2", "_currentContent$filte3", "_currentContent$filte4", "_currentContent$filte5", "_currentContent$filte6", "_currentContent$filte7", "_currentContent$filte8", "isLeftAlign", "contentRef", "dispatch", "isPhone", "isTablet", "ImageFromRedux", "state", "homeContent", "present", "images", "currentC<PERSON>nt", "careers", "activeIndex", "setActiveIndex", "isModal", "setIsModal", "<PERSON><PERSON><PERSON>", "setSelected<PERSON>ob", "selectedPage", "setSelectedPage", "totalDocuments", "setTotalDocuments", "itemsPerPage", "searchTerm", "setSearchTerm", "filteredJobs", "setFilteredJobs", "jobListSection", "jobs", "to<PERSON><PERSON><PERSON><PERSON><PERSON>", "index", "debounce", "func", "delay", "timeoutId", "args", "clearTimeout", "setTimeout", "searchJobs", "term", "_currentContent$jobLi2", "filtered", "filter", "job", "title", "value", "toLowerCase", "includes", "display", "length", "debouncedSearchJobs", "handleChange", "e", "target", "isMounted", "currentPath", "payload", "_currentContent$jobLi3", "_currentContent$jobLi4", "displayedJobs", "handlePageChange", "result", "paginatedJobs", "slice", "className", "children", "style", "height", "backgroundImage", "careersBanner", "bannerSection", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "description", "src", "alt", "width", "dir", "filterSection", "inputBox", "placeholder", "aria_label", "onChange", "filtersSelections", "map", "options", "option", "optIndex", "_job$title", "_job$title2", "_job$location", "_job$location2", "_job$deadline", "_job$deadline2", "_currentContent$jobLi5", "_currentContent$jobLi6", "_currentContent$jobLi7", "_currentContent$jobLi8", "_contentRef$current$i", "onClick", "key", "location", "deadline", "_job$title3", "buttons", "text", "div", "ref", "el", "current", "initial", "animate", "scrollHeight", "opacity", "transition", "duration", "ease", "descriptionList", "desc", "i", "id", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Akshay/LOOP_PROJECTS/shade_cms/dashboard/src/features/Resources/components/websiteComponent/CareersPage.jsx"], "sourcesContent": ["import React, { useEffect, useRef, useState } from \"react\";\nimport content from \"./content.json\"\n// import styles from \"./Career.module.scss\";\n// import localFont from \"next/font/local\";\nimport { motion } from \"framer-motion\";\n// import ApplyModal from \"./ApplyModal\";\n// import Pagination from \"../../common/Pagination\";\nimport Pagination from \"./subparts/Pagination\";\n// const AnimatedText = dynamic(() => import('@/common/AnimatedText'), { ssr: false });\nimport Arrow from \"../../../../assets/icons/right-wrrow.svg\"\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { updateMainContent } from \"../../../common/homeContentSlice\";\n\nconst CareerPage = ({ language, screen }) => {\n    const isLeftAlign = language === 'en'\n    const contentRef = useRef([]);\n    const dispatch = useDispatch()\n    const isPhone = screen < 760\n    const isTablet = screen > 761 && screen < 1100\n    const ImageFromRedux = useSelector((state) => state.homeContent.present.images)\n    const currentContent = useSelector((state) => state.homeContent.present.careers)\n    const [activeIndex, setActiveIndex] = useState(null);\n    const [isModal, setIsModal] = useState(false);\n    const [selectedJob, setSelectedJob] = useState(null);\n    const [selectedPage, setSelectedPage] = useState(1);\n    const [totalDocuments, setTotalDocuments] = useState(0);\n    const itemsPerPage = 10;\n    const [searchTerm, setSearchTerm] = useState(null);\n    const [filteredJobs, setFilteredJobs] = useState(\n        currentContent?.jobListSection?.jobs || []\n    );\n    const toggleAccordion = (index) => {\n        setActiveIndex(activeIndex === index ? null : index);\n    };\n    const debounce = (func, delay) => {\n        let timeoutId;\n        return (...args) => {\n            if (timeoutId) clearTimeout(timeoutId);\n            timeoutId = setTimeout(() => {\n                func(...args);\n            }, delay);\n        };\n    };\n\n    // Search function\n    const searchJobs = (term) => {\n        setSearchTerm(term);\n        let filtered = currentContent?.jobListSection?.jobs || [];\n\n        if (term) {\n            filtered = filtered.filter((job) =>\n                job.title.value[language].toLowerCase().includes(term.toLowerCase())\n            );\n        }\n\n        // Filter only displayed jobs\n        filtered = filtered.filter((job) => job.display);\n\n        setFilteredJobs(filtered);\n        setTotalDocuments(filtered.length);\n\n        // Reset page number if fewer than 10 jobs are displayed\n        if (filtered.length <= itemsPerPage) {\n            setSelectedPage(1);\n        }\n    };\n\n\n    const debouncedSearchJobs = debounce(searchJobs, 300);\n\n    const handleChange = (e) => {\n        const value = e.target.value;\n        debouncedSearchJobs(value);\n    };\n\n    useEffect(() => {\n        let isMounted = true; // Flag to track if component is mounted\n\n        dispatch(updateMainContent({ currentPath: \"careers\", payload: content.careers }));\n\n        return () => {\n            isMounted = false; // Mark as unmounted when component unmounts\n        };\n    }, [])\n\n    // useEffect to reset filtered jobs when content changes\n    useEffect(() => {\n        const displayedJobs = currentContent?.jobListSection?.jobs?.filter(job => job.display) || [];\n        setFilteredJobs(displayedJobs);\n        setTotalDocuments(displayedJobs.length);\n\n        // Reset pagination when job count is below 10\n        if (displayedJobs.length <= itemsPerPage) {\n            setSelectedPage(1);\n        }\n    }, [currentContent]);\n\n\n    const handlePageChange = (result) => {\n        setSelectedPage(result);\n    };\n\n    const paginatedJobs = filteredJobs.slice(\n        (selectedPage - 1) * itemsPerPage,\n        selectedPage * itemsPerPage\n    );\n\n\n    return (\n        <div className=\"w-full\">\n            {/* banner */}\n            <section className={`relative h-full w-full bg-cover bg-center ${isLeftAlign ? 'scale-x-[-1]' : ''}  `}\n                style={{\n                    height: 1200 * 0.436,\n                    backgroundImage: ImageFromRedux?.careersBanner ? `url(${ImageFromRedux.careersBanner})` :\n                        \"url('https://loopwebsite.s3.ap-south-1.amazonaws.com/Hero+(2).jpg')\"\n                }}>\n                <div className={`container h-full relative ${isPhone ? \"px-10\" : \"px-20\"} flex items-center ${isLeftAlign ? \"justify-end\" : \"justify-end\"}   `}>\n                    <div className={`flex flex-col ${isLeftAlign ? 'right-5 text-left items-start ' : 'left-5 text-right items-end'} ${isPhone ? \"max-w-[70%]\" : \"max-w-[55%]\"} w-full ${isLeftAlign ? 'scale-x-[-1]' : ''}`}>\n                        <h1 className={`text-[#292E3D] ${isPhone ? \"text-3xl\" : \"text-[50px] leading-[77px] tracking-[-3.5px]\"} font-medium  mb-4`}>\n                            {currentContent?.bannerSection?.title[language]}\n\n                        </h1>\n                        <p className={`text-[#0E172FB3] ${isPhone ? \"\" : \"leading-[28px]\"} text-sm font-semibold  mb-6 word-spacing-5`}>\n                            {currentContent?.bannerSection?.description[language]}\n                        </p>\n                        {/* <button\n                            className={`relative px-5 py-2 ${isPhone ? \"text-xs\" : \"text-sm\"} font-medium bg-[#00B9F2] text-white rounded flex items-center justify-start gap-2 ${isLeftAlign ? \"flex-row-reverse\" : \"\"}`}\n                        // onClick={() => router.push(\"/services\")}\n                        >\n                            <img\n                                src={Arrow}\n                                alt=\"Arrow\"\n                                className={` ${isLeftAlign ? 'scale-x-[-1]' : ''} ${isPhone ? \"w-[12px] h-[12px]\" : \"w-[14px] h-[14px]\"}`}\n                            />\n                            <p>\n                                {currentContent?.bannerSection?.button?.[language]}\n                            </p>\n                        </button> */}\n                    </div>\n                </div>\n            </section>\n\n            {/* filters and search */}\n            <section\n                className={`${language === \"en\" ? \"text-left\" : \"\"} py-8 pt-16 px-20`}\n            >\n                <div className=\"container\">\n                    <div\n                        className={`flex items-center gap-5 \n                ${isPhone ? \"flex-col\" : isTablet ? \"gap-4 flex-col-reverse\" : \"justify-between\"} \n                ${!isLeftAlign && !isPhone && !isTablet ? \"flex-row-reverse\" : \"\"}`}\n                    >\n                        <div\n                            className={`flex items-center ${!isLeftAlign && \"flex-row-reverse\"} gap-2.5 p-2 border rounded-lg border-gray-300  \n                    ${isPhone ? \"w-full\" : isTablet ? \"px-4\" : \"w-auto\"}`}\n                        >\n                            <img\n                                src=\"https://loopwebsite.s3.ap-south-1.amazonaws.com/material-symbols_search+(1).svg\"\n                                alt=\"icon\"\n                                width={24}\n                                height={24}\n                                className=\"w-6 h-6\"\n                            />\n                            <input\n                                dir={!isLeftAlign ? \"rtl\" : \"ltr\"}\n                                title={currentContent?.filterSection?.inputBox?.placeholder[language]}\n                                placeholder={currentContent?.filterSection?.inputBox?.placeholder[language]}\n                                className={`w-full border-none focus:outline-none text-gray-700 text-base font-light`}\n                                aria-label={currentContent?.filterSection?.inputBox?.aria_label[language]}\n                                onChange={(e) => handleChange(e, \"Job Search\")}\n                            />\n                        </div>\n                        <div\n                            className={`flex items-center gap-8 \n                    ${isPhone ? \"flex-col w-full gap-4\" : isTablet ? \"gap-6\" : \"\"} relative`}\n                        >\n                            {currentContent?.filterSection?.filtersSelections?.map((filter, index) => (\n                                <div key={index} className={`relative flex ${isPhone ? \"w-full justify-between\" : \"\"} bg-transparent`}>\n                                    <img\n                                        src=\"https://loopwebsite.s3.ap-south-1.amazonaws.com/weui_arrow-outlined+(1).svg\"\n                                        alt=\"icon\"\n                                        width={20}\n                                        height={20}\n                                        className={`${isPhone ? \"absolute top-2 left-2 z-[1]\" : \"\"} `}\n                                    />\n                                    <select\n                                        className={`border-none z-[2] bg-transparent text-gray-700 text-center text-sm font-light focus:outline-none\n                                ${isPhone ? \"w-full text-left p-2 border rounded-lg\" : isTablet ? \"px-2\" : \"\"}`}\n                                        aria-label={filter?.title[language]}\n                                        onChange={(e) => handleChange(e, filter.title[language])}\n                                    >\n                                        <option value=\"\">{filter?.title[language]}</option>\n                                        {filter.options.map((option, optIndex) => (\n                                            <option key={optIndex} value={option?.value}>\n                                                {option?.title[language]}\n                                            </option>\n                                        ))}\n                                    </select>\n                                </div>\n                            ))}\n                        </div>\n                    </div>\n                </div>\n            </section>\n\n\n            {/* jobs */}\n            <section className={`w-full ${isLeftAlign ? \"text-left\" : \"text-right\"} px-12`}>\n                {paginatedJobs?.length < 1 && (\n                    <div className=\"flex h-24 w-full items-center justify-center\">\n                        <h1>Oops... Not found !</h1>\n                    </div>\n                )}\n                <div className={`container mx-auto p-4 ${isPhone && \"px-2\"}`}>\n                    <div className=\"space-y-8\">\n                        {paginatedJobs?.map((job, index) => {\n                            if (!job.display) return null;\n                            return (\n                                <div key={job.id} className={`rounded-lg bg-gray-100 py-8 ${isPhone ? \"px-2\" : \"px-4\"} shadow-md`}>\n                                    {/* Ensure flex-row-reverse for mirroring layout */}\n                                    <div\n                                        className={`flex items-center justify-between cursor-pointer ${isPhone ? \"flex-col gap-2\" : isTablet ? \"flex-wrap gap-1\" : \"gap-5\"} ${!isLeftAlign ? \"flex-row-reverse\" : \"\"}`}\n                                        onClick={() => toggleAccordion(index)}\n                                    >\n                                        <div className={`flex ${isPhone ? \"\" : isTablet ? \"\" : \"w-3/4\"} ${isPhone ? \"w-[80%] pl-2\" : \"\"} items-center gap-2 ${!isLeftAlign && \"flex-row-reverse\"}`}>\n                                            <img\n                                                src=\"https://loopwebsite.s3.ap-south-1.amazonaws.com/weui_arrow-outlined+(1).svg\"\n                                                alt=\"icon\"\n                                                width={28}\n                                                height={28}\n                                            />\n                                            <div>\n                                                <h5 className=\"text-md font-bold text-gray-800\">{job?.title?.key[language]}</h5>\n                                                <p className=\"text-sm font-light text-gray-600\">{job?.title?.value[language]}</p>\n                                            </div>\n                                        </div>\n                                        <div className={` ${isPhone ? \"\" : isTablet ? \"\" : \"w-1/2\"} ${isPhone ? \" w-[70%] pl-5\" : `${!isLeftAlign?\"border-r pr-5\":\"border-l pl-5\"} `} border-gray-300`}>\n                                            <h5 className=\"text-sm font-light text-gray-600\">{job?.location?.key[language]}</h5>\n                                            <p className=\"text-md font-bold text-gray-800\">{job?.location?.value[language]}</p>\n                                        </div>\n                                        <div className={` ${isPhone ? \"\" : isTablet ? \"\" : \"w-1/2\"} ${isPhone ? \" w-[70%] pl-5\" : \"border-l border-r px-5\"} border-gray-300`}>\n                                            <h5 className=\"text-sm font-light text-gray-600\">{job?.deadline?.key[language]}</h5>\n                                            <p className=\"text-md font-bold text-gray-800\">{job?.deadline?.value[language]}</p>\n                                        </div>\n                                        <div className={`mt-4 ${isPhone ? \"\" : isTablet ? \"\" : \"\"} flex items-center gap-6 ${!isLeftAlign && \"flex-row-reverse\"} ${isTablet ? \"w-\" : \"\"}`}>\n                                            <button\n                                                className=\"rounded bg-[#00b9f2] px-3 w-[102px] py-2 text-white text-xs\"\n                                                onClick={() => {\n                                                    setIsModal(true);\n                                                    setSelectedJob(job?.title?.value[language]);\n                                                }}\n                                            >\n                                                {currentContent?.jobListSection?.buttons[0]?.text[language]}\n                                            </button>\n                                            <button\n                                                className=\"rounded border border-[#00b9f2] px-6 py-2 text-[#00b9f2] text-xs\"\n                                            >\n                                                {currentContent?.jobListSection?.buttons[1]?.text[language]}\n                                            </button>\n                                        </div>\n                                    </div>\n\n                                    <motion.div\n                                        ref={(el) => (contentRef.current[index] = el)}\n                                        initial={false}\n                                        animate={\n                                            activeIndex === index\n                                                ? { height: contentRef.current[index]?.scrollHeight || \"auto\", opacity: 1 }\n                                                : { height: 0, opacity: 0 }\n                                        }\n                                        transition={{ duration: 0.5, ease: [0.4, 0, 0.2, 1] }}\n                                        className=\"overflow-hidden\"\n                                    >\n                                        <ul className=\"mt-5 list-disc space-y-3 pl-6 text-gray-700\">\n                                            {job.descriptionList.map((desc, i) => (\n                                                <li key={i}>{desc[language]}</li>\n                                            ))}\n                                        </ul>\n                                    </motion.div>\n                                </div>\n                            );\n                        })}\n                    </div>\n                </div>\n            </section>\n\n            {/* pagination */}\n            <section className=\"flex justify-end gap-5 h-[94px] pr-12\">\n                <Pagination\n                    totalDocuments={totalDocuments}\n                    handlePageChange={handlePageChange}\n                    selectedPage={selectedPage}\n                    language={language}\n                />\n            </section>\n\n\n\n\n        </div>\n    );\n};\n\nexport default CareerPage;\n\n\n\n// {/*\n//  <section\n//         className={` ${language === \"en\" && styles.leftAlign}   ${\n//           styles.career_banner_wrap\n//         }`}\n//       >\n//         <div\n//           className=\"container\"\n//           style={{ height: \"100%\", position: \"relative\" }}\n//         >\n//           <div className={styles.content}>\n//             {/* <AnimatedText text=\"مهنة\" Wrapper=\"h2\" repeatDelay={0.04} className={`${styles.title} ${BankGothic.className}`} /> */}\n\n//             <h1 className={`${styles.title} `}>\n//               {currentContent?.bannerSection?.title[language]}\n//             </h1>\n//             <p className={`${styles.description} ${BankGothic.className}`}>\n//               {currentContent?.bannerSection?.description[language]}\n//             </p>\n//             {/* <Button\n//               className={styles.view_btn}\n//               onClick={() => setIsModal(true)}\n//             >\n//               <Image\n//                 src={Arrow}\n//                 width=\"18\"\n//                 height=\"17\"\n//                 alt=\"\"\n//                 className={styles.arrow_btn}\n//               />\n//               {currentContent?.bannerSection?.button?.text[language]}\n//             </Button> */}\n//           </div>\n//         </div>\n//       </section>\n\n//       <section\n//         className={` ${language === \"en\" && styles.leftAlign}   ${\n//           styles.job_filter_container\n//         }`}\n//       >\n//         <div className=\"container\">\n//           <div className={styles.job_wrap}>\n//             <div className={styles.serach_wrap}>\n//               <Image\n//                 src=\"https://loopwebsite.s3.ap-south-1.amazonaws.com/material-symbols_search+(1).svg\"\n//                 alt=\"icon\"\n//                 width={24}\n//                 height={24}\n//                 className={styles.icon}\n//               />\n//               <input\n//                 title={\n//                   currentContent?.filterSection?.inputBox?.placeholder[language]\n//                 }\n//                 placeholder={\n//                   currentContent?.filterSection?.inputBox?.placeholder[language]\n//                 }\n//                 className={`${BankGothic.className} ${styles.input_form}`}\n//                 aria-label={\n//                   currentContent?.filterSection?.inputBox?.aria_label[language]\n//                 }\n//                 onChange={(e) => handleChange(e, \"Job Search\")} // Update to call handleChange\n//               />\n//             </div>\n//             <div className={styles.select_filter_wrap}>\n//               {currentContent?.filterSection?.filtersSelections?.map(\n//                 (filter, index) => (\n//                   <div key={index} className={styles.select_wrapper}>\n//                     <select\n//                       className={`${BankGothic.className} ${styles.select_form}`}\n//                       aria-label={filter?.title[language]}\n//                       onChange={(e) => handleChange(e, filter.title[language])}\n//                     >\n//                       {/* // Updated here */}\n//                       <option value=\"\" disabled selected>\n//                         {filter?.title[language]}\n//                       </option>\n//                       {filter.options.map((option, optIndex) => (\n//                         <option key={optIndex} value={option?.value}>\n//                           {option?.title[language]}\n//                         </option>\n//                       ))}\n//                     </select>\n//                   </div>\n//                 )\n//               )}\n//             </div>\n//           </div>\n//         </div>\n//       </section>\n\n//       <section\n//         className={` ${language === \"en\" && styles.leftAlign}   ${\n//           styles.job_List_container\n//         }`}\n//       >\n//         {paginatedJobs?.length < 1 && (\n//           <>\n//             <div\n//               style={{\n//                 height: \"100px\",\n//                 width: \"100%\",\n//                 display: \"flex\",\n//                 justifyContent: \"center\",\n//                 alignItems: \"center\",\n//               }}\n//             >\n//               <h1>\n//                 {language === \"en\"\n//                   ? \"Oops... Not found !\"\n//                   : \"Oops... Not found !\"}\n//               </h1>\n//             </div>\n//           </>\n//         )}\n//         <div className=\"container\">\n//           <div className={styles.accordion}>\n//             {paginatedJobs?.map((job, index) => (\n//               <div key={job.id} className={styles.accordion_item}>\n//                 <div className={styles.accordion_title}>\n//                   <div\n//                     className={styles.Accordion_head}\n//                     onClick={() => toggleAccordion(index)}\n//                   >\n//                     <div className={styles.jobname_wrap}>\n//                       <Image\n//                         src=\"https://loopwebsite.s3.ap-south-1.amazonaws.com/weui_arrow-outlined+(1).svg\"\n//                         alt=\"icon\"\n//                         width={28}\n//                         height={28}\n//                         className={styles.icon}\n//                       />\n//                       <div>\n//                         <h5\n//                           className={`${styles.title} ${BankGothic.className}`}\n//                         >\n//                           {job?.title?.key[language]}\n//                         </h5>\n//                         <p\n//                           className={`${styles.subtitle} ${BankGothic.className}`}\n//                         >\n//                           {job?.title?.value[language]}\n//                         </p>\n//                       </div>\n//                     </div>\n//                     <div className={styles.job_location_wrap}>\n//                       <h5 className={`${styles.title} ${BankGothic.className}`}>\n//                         {job?.location?.key[language]}\n//                       </h5>\n//                       <p\n//                         className={`${styles.subtitle} ${BankGothic.className}`}\n//                       >\n//                         {job?.location?.value[language]}\n//                       </p>\n//                     </div>\n//                     <div className={styles.job_apply_wrap}>\n//                       <h5 className={`${styles.title} ${BankGothic.className}`}>\n//                         {job?.deadline?.key[language]}\n//                       </h5>\n//                       <p\n//                         className={`${styles.subtitle} ${BankGothic.className}`}\n//                       >\n//                         {job?.deadline?.value[language]}\n//                       </p>\n//                     </div>\n//                   </div>\n//                   <div className={styles.button_group}>\n//                     <Button\n//                       className={styles.primary}\n//                       onClick={() => {\n//                         setIsModal(true);\n//                         setSelectedJob(job?.title?.value[language]);\n//                       }}\n//                     >\n//                       {\n//                         currentContent?.jobListSection?.buttons[0]?.text[\n//                           language\n//                         ]\n//                       }\n//                     </Button>\n//                     <Button\n//                       className={styles.outline}\n//                       onClick={() => router.push(`/career/${job.id}`)}\n//                     >\n//                       {\n//                         currentContent?.jobListSection?.buttons[1]?.text[\n//                           language\n//                         ]\n//                       }\n//                     </Button>\n//                   </div>\n//                 </div>\n\n//                 <motion.div\n//                   ref={(el) => (contentRef.current[index] = el)}\n//                   initial={false}\n//                   animate={\n//                     activeIndex === index\n//                       ? {\n//                           height:\n//                             contentRef.current[index]?.scrollHeight || \"auto\",\n//                           opacity: 1,\n//                         }\n//                       : { height: 0, opacity: 0 }\n//                   }\n//                   transition={{ duration: 0.5, ease: [0.4, 0, 0.2, 1] }}\n//                   className={`${styles.accordion_content}`}\n//                 >\n//                   <ul className={styles.job_list_wrap}>\n//                     {job.descriptionList.map((desc, i) => (\n//                       <li key={i} className={BankGothic.className}>\n//                         {desc[language]}\n//                       </li>\n//                     ))}\n//                   </ul>\n//                 </motion.div>\n//               </div>\n//             ))}\n//           </div>\n//         </div>\n//       </section>\n\n//       <section className={styles.paginationSection}>\n//         <Pagination\n//           totalDocuments={totalDocuments}\n//           handlePageChange={handlePageChange}\n//           selectedPage={selectedPage}\n//         />\n//       </section>\n\n//       <ApplyModal\n//         isModal={isModal}\n//         jobTitle={selectedJob}\n//         onClose={() => setIsModal(false)}\n//       /> \n// */}\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AAC1D,OAAOC,OAAO,MAAM,gBAAgB;AACpC;AACA;AACA,SAASC,MAAM,QAAQ,eAAe;AACtC;AACA;AACA,OAAOC,UAAU,MAAM,uBAAuB;AAC9C;AACA,OAAOC,KAAK,MAAM,0CAA0C;AAC5D,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,iBAAiB,QAAQ,kCAAkC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErE,MAAMC,UAAU,GAAGA,CAAC;EAAEC,QAAQ;EAAEC;AAAO,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;EACzC,MAAMC,WAAW,GAAGd,QAAQ,KAAK,IAAI;EACrC,MAAMe,UAAU,GAAG3B,MAAM,CAAC,EAAE,CAAC;EAC7B,MAAM4B,QAAQ,GAAGtB,WAAW,CAAC,CAAC;EAC9B,MAAMuB,OAAO,GAAGhB,MAAM,GAAG,GAAG;EAC5B,MAAMiB,QAAQ,GAAGjB,MAAM,GAAG,GAAG,IAAIA,MAAM,GAAG,IAAI;EAC9C,MAAMkB,cAAc,GAAGxB,WAAW,CAAEyB,KAAK,IAAKA,KAAK,CAACC,WAAW,CAACC,OAAO,CAACC,MAAM,CAAC;EAC/E,MAAMC,cAAc,GAAG7B,WAAW,CAAEyB,KAAK,IAAKA,KAAK,CAACC,WAAW,CAACC,OAAO,CAACG,OAAO,CAAC;EAChF,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGtC,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACuC,OAAO,EAAEC,UAAU,CAAC,GAAGxC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACyC,WAAW,EAAEC,cAAc,CAAC,GAAG1C,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAAC2C,YAAY,EAAEC,eAAe,CAAC,GAAG5C,QAAQ,CAAC,CAAC,CAAC;EACnD,MAAM,CAAC6C,cAAc,EAAEC,iBAAiB,CAAC,GAAG9C,QAAQ,CAAC,CAAC,CAAC;EACvD,MAAM+C,YAAY,GAAG,EAAE;EACvB,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGjD,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAACkD,YAAY,EAAEC,eAAe,CAAC,GAAGnD,QAAQ,CAC5C,CAAAmC,cAAc,aAAdA,cAAc,wBAAArB,qBAAA,GAAdqB,cAAc,CAAEiB,cAAc,cAAAtC,qBAAA,uBAA9BA,qBAAA,CAAgCuC,IAAI,KAAI,EAC5C,CAAC;EACD,MAAMC,eAAe,GAAIC,KAAK,IAAK;IAC/BjB,cAAc,CAACD,WAAW,KAAKkB,KAAK,GAAG,IAAI,GAAGA,KAAK,CAAC;EACxD,CAAC;EACD,MAAMC,QAAQ,GAAGA,CAACC,IAAI,EAAEC,KAAK,KAAK;IAC9B,IAAIC,SAAS;IACb,OAAO,CAAC,GAAGC,IAAI,KAAK;MAChB,IAAID,SAAS,EAAEE,YAAY,CAACF,SAAS,CAAC;MACtCA,SAAS,GAAGG,UAAU,CAAC,MAAM;QACzBL,IAAI,CAAC,GAAGG,IAAI,CAAC;MACjB,CAAC,EAAEF,KAAK,CAAC;IACb,CAAC;EACL,CAAC;;EAED;EACA,MAAMK,UAAU,GAAIC,IAAI,IAAK;IAAA,IAAAC,sBAAA;IACzBhB,aAAa,CAACe,IAAI,CAAC;IACnB,IAAIE,QAAQ,GAAG,CAAA/B,cAAc,aAAdA,cAAc,wBAAA8B,sBAAA,GAAd9B,cAAc,CAAEiB,cAAc,cAAAa,sBAAA,uBAA9BA,sBAAA,CAAgCZ,IAAI,KAAI,EAAE;IAEzD,IAAIW,IAAI,EAAE;MACNE,QAAQ,GAAGA,QAAQ,CAACC,MAAM,CAAEC,GAAG,IAC3BA,GAAG,CAACC,KAAK,CAACC,KAAK,CAAC3D,QAAQ,CAAC,CAAC4D,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACR,IAAI,CAACO,WAAW,CAAC,CAAC,CACvE,CAAC;IACL;;IAEA;IACAL,QAAQ,GAAGA,QAAQ,CAACC,MAAM,CAAEC,GAAG,IAAKA,GAAG,CAACK,OAAO,CAAC;IAEhDtB,eAAe,CAACe,QAAQ,CAAC;IACzBpB,iBAAiB,CAACoB,QAAQ,CAACQ,MAAM,CAAC;;IAElC;IACA,IAAIR,QAAQ,CAACQ,MAAM,IAAI3B,YAAY,EAAE;MACjCH,eAAe,CAAC,CAAC,CAAC;IACtB;EACJ,CAAC;EAGD,MAAM+B,mBAAmB,GAAGnB,QAAQ,CAACO,UAAU,EAAE,GAAG,CAAC;EAErD,MAAMa,YAAY,GAAIC,CAAC,IAAK;IACxB,MAAMP,KAAK,GAAGO,CAAC,CAACC,MAAM,CAACR,KAAK;IAC5BK,mBAAmB,CAACL,KAAK,CAAC;EAC9B,CAAC;EAEDxE,SAAS,CAAC,MAAM;IACZ,IAAIiF,SAAS,GAAG,IAAI,CAAC,CAAC;;IAEtBpD,QAAQ,CAACpB,iBAAiB,CAAC;MAAEyE,WAAW,EAAE,SAAS;MAAEC,OAAO,EAAEhF,OAAO,CAACmC;IAAQ,CAAC,CAAC,CAAC;IAEjF,OAAO,MAAM;MACT2C,SAAS,GAAG,KAAK,CAAC,CAAC;IACvB,CAAC;EACL,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAjF,SAAS,CAAC,MAAM;IAAA,IAAAoF,sBAAA,EAAAC,sBAAA;IACZ,MAAMC,aAAa,GAAG,CAAAjD,cAAc,aAAdA,cAAc,wBAAA+C,sBAAA,GAAd/C,cAAc,CAAEiB,cAAc,cAAA8B,sBAAA,wBAAAC,sBAAA,GAA9BD,sBAAA,CAAgC7B,IAAI,cAAA8B,sBAAA,uBAApCA,sBAAA,CAAsChB,MAAM,CAACC,GAAG,IAAIA,GAAG,CAACK,OAAO,CAAC,KAAI,EAAE;IAC5FtB,eAAe,CAACiC,aAAa,CAAC;IAC9BtC,iBAAiB,CAACsC,aAAa,CAACV,MAAM,CAAC;;IAEvC;IACA,IAAIU,aAAa,CAACV,MAAM,IAAI3B,YAAY,EAAE;MACtCH,eAAe,CAAC,CAAC,CAAC;IACtB;EACJ,CAAC,EAAE,CAACT,cAAc,CAAC,CAAC;EAGpB,MAAMkD,gBAAgB,GAAIC,MAAM,IAAK;IACjC1C,eAAe,CAAC0C,MAAM,CAAC;EAC3B,CAAC;EAED,MAAMC,aAAa,GAAGrC,YAAY,CAACsC,KAAK,CACpC,CAAC7C,YAAY,GAAG,CAAC,IAAII,YAAY,EACjCJ,YAAY,GAAGI,YACnB,CAAC;EAGD,oBACItC,OAAA;IAAKgF,SAAS,EAAC,QAAQ;IAAAC,QAAA,gBAEnBjF,OAAA;MAASgF,SAAS,EAAE,6CAA6ChE,WAAW,GAAG,cAAc,GAAG,EAAE,IAAK;MACnGkE,KAAK,EAAE;QACHC,MAAM,EAAE,IAAI,GAAG,KAAK;QACpBC,eAAe,EAAE/D,cAAc,aAAdA,cAAc,eAAdA,cAAc,CAAEgE,aAAa,GAAG,OAAOhE,cAAc,CAACgE,aAAa,GAAG,GACnF;MACR,CAAE;MAAAJ,QAAA,eACFjF,OAAA;QAAKgF,SAAS,EAAE,6BAA6B7D,OAAO,GAAG,OAAO,GAAG,OAAO,sBAAsBH,WAAW,GAAG,aAAa,GAAG,aAAa,KAAM;QAAAiE,QAAA,eAC3IjF,OAAA;UAAKgF,SAAS,EAAE,iBAAiBhE,WAAW,GAAG,gCAAgC,GAAG,6BAA6B,IAAIG,OAAO,GAAG,aAAa,GAAG,aAAa,WAAWH,WAAW,GAAG,cAAc,GAAG,EAAE,EAAG;UAAAiE,QAAA,gBACrMjF,OAAA;YAAIgF,SAAS,EAAE,kBAAkB7D,OAAO,GAAG,UAAU,GAAG,8CAA8C,oBAAqB;YAAA8D,QAAA,EACtHvD,cAAc,aAAdA,cAAc,wBAAApB,qBAAA,GAAdoB,cAAc,CAAE4D,aAAa,cAAAhF,qBAAA,uBAA7BA,qBAAA,CAA+BsD,KAAK,CAAC1D,QAAQ;UAAC;YAAAqF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAE/C,CAAC,eACL1F,OAAA;YAAGgF,SAAS,EAAE,oBAAoB7D,OAAO,GAAG,EAAE,GAAG,gBAAgB,6CAA8C;YAAA8D,QAAA,EAC1GvD,cAAc,aAAdA,cAAc,wBAAAnB,sBAAA,GAAdmB,cAAc,CAAE4D,aAAa,cAAA/E,sBAAA,uBAA7BA,sBAAA,CAA+BoF,WAAW,CAACzF,QAAQ;UAAC;YAAAqF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAcH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGV1F,OAAA;MACIgF,SAAS,EAAE,GAAG9E,QAAQ,KAAK,IAAI,GAAG,WAAW,GAAG,EAAE,mBAAoB;MAAA+E,QAAA,eAEtEjF,OAAA;QAAKgF,SAAS,EAAC,WAAW;QAAAC,QAAA,eACtBjF,OAAA;UACIgF,SAAS,EAAE;AACnC,kBAAkB7D,OAAO,GAAG,UAAU,GAAGC,QAAQ,GAAG,wBAAwB,GAAG,iBAAiB;AAChG,kBAAkB,CAACJ,WAAW,IAAI,CAACG,OAAO,IAAI,CAACC,QAAQ,GAAG,kBAAkB,GAAG,EAAE,EAAG;UAAA6D,QAAA,gBAE5DjF,OAAA;YACIgF,SAAS,EAAE,qBAAqB,CAAChE,WAAW,IAAI,kBAAkB;AAC9F,sBAAsBG,OAAO,GAAG,QAAQ,GAAGC,QAAQ,GAAG,MAAM,GAAG,QAAQ,EAAG;YAAA6D,QAAA,gBAE9CjF,OAAA;cACI4F,GAAG,EAAC,iFAAiF;cACrFC,GAAG,EAAC,MAAM;cACVC,KAAK,EAAE,EAAG;cACVX,MAAM,EAAE,EAAG;cACXH,SAAS,EAAC;YAAS;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB,CAAC,eACF1F,OAAA;cACI+F,GAAG,EAAE,CAAC/E,WAAW,GAAG,KAAK,GAAG,KAAM;cAClC4C,KAAK,EAAElC,cAAc,aAAdA,cAAc,wBAAAlB,qBAAA,GAAdkB,cAAc,CAAEsE,aAAa,cAAAxF,qBAAA,wBAAAC,sBAAA,GAA7BD,qBAAA,CAA+ByF,QAAQ,cAAAxF,sBAAA,uBAAvCA,sBAAA,CAAyCyF,WAAW,CAAChG,QAAQ,CAAE;cACtEgG,WAAW,EAAExE,cAAc,aAAdA,cAAc,wBAAAhB,sBAAA,GAAdgB,cAAc,CAAEsE,aAAa,cAAAtF,sBAAA,wBAAAC,sBAAA,GAA7BD,sBAAA,CAA+BuF,QAAQ,cAAAtF,sBAAA,uBAAvCA,sBAAA,CAAyCuF,WAAW,CAAChG,QAAQ,CAAE;cAC5E8E,SAAS,EAAE,0EAA2E;cACtF,cAAYtD,cAAc,aAAdA,cAAc,wBAAAd,sBAAA,GAAdc,cAAc,CAAEsE,aAAa,cAAApF,sBAAA,wBAAAC,sBAAA,GAA7BD,sBAAA,CAA+BqF,QAAQ,cAAApF,sBAAA,uBAAvCA,sBAAA,CAAyCsF,UAAU,CAACjG,QAAQ,CAAE;cAC1EkG,QAAQ,EAAGhC,CAAC,IAAKD,YAAY,CAACC,CAAC,EAAE,YAAY;YAAE;cAAAmB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eACN1F,OAAA;YACIgF,SAAS,EAAE;AACvC,sBAAsB7D,OAAO,GAAG,uBAAuB,GAAGC,QAAQ,GAAG,OAAO,GAAG,EAAE,WAAY;YAAA6D,QAAA,EAEhEvD,cAAc,aAAdA,cAAc,wBAAAZ,sBAAA,GAAdY,cAAc,CAAEsE,aAAa,cAAAlF,sBAAA,wBAAAC,sBAAA,GAA7BD,sBAAA,CAA+BuF,iBAAiB,cAAAtF,sBAAA,uBAAhDA,sBAAA,CAAkDuF,GAAG,CAAC,CAAC5C,MAAM,EAAEZ,KAAK,kBACjE9C,OAAA;cAAiBgF,SAAS,EAAE,iBAAiB7D,OAAO,GAAG,wBAAwB,GAAG,EAAE,iBAAkB;cAAA8D,QAAA,gBAClGjF,OAAA;gBACI4F,GAAG,EAAC,6EAA6E;gBACjFC,GAAG,EAAC,MAAM;gBACVC,KAAK,EAAE,EAAG;gBACVX,MAAM,EAAE,EAAG;gBACXH,SAAS,EAAE,GAAG7D,OAAO,GAAG,6BAA6B,GAAG,EAAE;cAAI;gBAAAoE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjE,CAAC,eACF1F,OAAA;gBACIgF,SAAS,EAAE;AACnD,kCAAkC7D,OAAO,GAAG,wCAAwC,GAAGC,QAAQ,GAAG,MAAM,GAAG,EAAE,EAAG;gBACxE,cAAYsC,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEE,KAAK,CAAC1D,QAAQ,CAAE;gBACpCkG,QAAQ,EAAGhC,CAAC,IAAKD,YAAY,CAACC,CAAC,EAAEV,MAAM,CAACE,KAAK,CAAC1D,QAAQ,CAAC,CAAE;gBAAA+E,QAAA,gBAEzDjF,OAAA;kBAAQ6D,KAAK,EAAC,EAAE;kBAAAoB,QAAA,EAAEvB,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEE,KAAK,CAAC1D,QAAQ;gBAAC;kBAAAqF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAS,CAAC,EAClDhC,MAAM,CAAC6C,OAAO,CAACD,GAAG,CAAC,CAACE,MAAM,EAAEC,QAAQ,kBACjCzG,OAAA;kBAAuB6D,KAAK,EAAE2C,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAE3C,KAAM;kBAAAoB,QAAA,EACvCuB,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAE5C,KAAK,CAAC1D,QAAQ;gBAAC,GADfuG,QAAQ;kBAAAlB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEb,CACX,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA,GApBH5C,KAAK;cAAAyC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAqBV,CACR;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAIV1F,OAAA;MAASgF,SAAS,EAAE,UAAUhE,WAAW,GAAG,WAAW,GAAG,YAAY,QAAS;MAAAiE,QAAA,GAC1E,CAAAH,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEb,MAAM,IAAG,CAAC,iBACtBjE,OAAA;QAAKgF,SAAS,EAAC,8CAA8C;QAAAC,QAAA,eACzDjF,OAAA;UAAAiF,QAAA,EAAI;QAAmB;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3B,CACR,eACD1F,OAAA;QAAKgF,SAAS,EAAE,yBAAyB7D,OAAO,IAAI,MAAM,EAAG;QAAA8D,QAAA,eACzDjF,OAAA;UAAKgF,SAAS,EAAC,WAAW;UAAAC,QAAA,EACrBH,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEwB,GAAG,CAAC,CAAC3C,GAAG,EAAEb,KAAK,KAAK;YAAA,IAAA4D,UAAA,EAAAC,WAAA,EAAAC,aAAA,EAAAC,cAAA,EAAAC,aAAA,EAAAC,cAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,qBAAA;YAChC,IAAI,CAACzD,GAAG,CAACK,OAAO,EAAE,OAAO,IAAI;YAC7B,oBACIhE,OAAA;cAAkBgF,SAAS,EAAE,+BAA+B7D,OAAO,GAAG,MAAM,GAAG,MAAM,YAAa;cAAA8D,QAAA,gBAE9FjF,OAAA;gBACIgF,SAAS,EAAE,oDAAoD7D,OAAO,GAAG,gBAAgB,GAAGC,QAAQ,GAAG,iBAAiB,GAAG,OAAO,IAAI,CAACJ,WAAW,GAAG,kBAAkB,GAAG,EAAE,EAAG;gBAC/KqG,OAAO,EAAEA,CAAA,KAAMxE,eAAe,CAACC,KAAK,CAAE;gBAAAmC,QAAA,gBAEtCjF,OAAA;kBAAKgF,SAAS,EAAE,QAAQ7D,OAAO,GAAG,EAAE,GAAGC,QAAQ,GAAG,EAAE,GAAG,OAAO,IAAID,OAAO,GAAG,cAAc,GAAG,EAAE,uBAAuB,CAACH,WAAW,IAAI,kBAAkB,EAAG;kBAAAiE,QAAA,gBACvJjF,OAAA;oBACI4F,GAAG,EAAC,6EAA6E;oBACjFC,GAAG,EAAC,MAAM;oBACVC,KAAK,EAAE,EAAG;oBACVX,MAAM,EAAE;kBAAG;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACd,CAAC,eACF1F,OAAA;oBAAAiF,QAAA,gBACIjF,OAAA;sBAAIgF,SAAS,EAAC,iCAAiC;sBAAAC,QAAA,EAAEtB,GAAG,aAAHA,GAAG,wBAAA+C,UAAA,GAAH/C,GAAG,CAAEC,KAAK,cAAA8C,UAAA,uBAAVA,UAAA,CAAYY,GAAG,CAACpH,QAAQ;oBAAC;sBAAAqF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eAChF1F,OAAA;sBAAGgF,SAAS,EAAC,kCAAkC;sBAAAC,QAAA,EAAEtB,GAAG,aAAHA,GAAG,wBAAAgD,WAAA,GAAHhD,GAAG,CAAEC,KAAK,cAAA+C,WAAA,uBAAVA,WAAA,CAAY9C,KAAK,CAAC3D,QAAQ;oBAAC;sBAAAqF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChF,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eACN1F,OAAA;kBAAKgF,SAAS,EAAE,IAAI7D,OAAO,GAAG,EAAE,GAAGC,QAAQ,GAAG,EAAE,GAAG,OAAO,IAAID,OAAO,GAAG,eAAe,GAAG,GAAG,CAACH,WAAW,GAAC,eAAe,GAAC,eAAe,GAAG,kBAAmB;kBAAAiE,QAAA,gBAC3JjF,OAAA;oBAAIgF,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,EAAEtB,GAAG,aAAHA,GAAG,wBAAAiD,aAAA,GAAHjD,GAAG,CAAE4D,QAAQ,cAAAX,aAAA,uBAAbA,aAAA,CAAeU,GAAG,CAACpH,QAAQ;kBAAC;oBAAAqF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACpF1F,OAAA;oBAAGgF,SAAS,EAAC,iCAAiC;oBAAAC,QAAA,EAAEtB,GAAG,aAAHA,GAAG,wBAAAkD,cAAA,GAAHlD,GAAG,CAAE4D,QAAQ,cAAAV,cAAA,uBAAbA,cAAA,CAAehD,KAAK,CAAC3D,QAAQ;kBAAC;oBAAAqF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClF,CAAC,eACN1F,OAAA;kBAAKgF,SAAS,EAAE,IAAI7D,OAAO,GAAG,EAAE,GAAGC,QAAQ,GAAG,EAAE,GAAG,OAAO,IAAID,OAAO,GAAG,eAAe,GAAG,wBAAwB,kBAAmB;kBAAA8D,QAAA,gBACjIjF,OAAA;oBAAIgF,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,EAAEtB,GAAG,aAAHA,GAAG,wBAAAmD,aAAA,GAAHnD,GAAG,CAAE6D,QAAQ,cAAAV,aAAA,uBAAbA,aAAA,CAAeQ,GAAG,CAACpH,QAAQ;kBAAC;oBAAAqF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACpF1F,OAAA;oBAAGgF,SAAS,EAAC,iCAAiC;oBAAAC,QAAA,EAAEtB,GAAG,aAAHA,GAAG,wBAAAoD,cAAA,GAAHpD,GAAG,CAAE6D,QAAQ,cAAAT,cAAA,uBAAbA,cAAA,CAAelD,KAAK,CAAC3D,QAAQ;kBAAC;oBAAAqF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClF,CAAC,eACN1F,OAAA;kBAAKgF,SAAS,EAAE,QAAQ7D,OAAO,GAAG,EAAE,GAAGC,QAAQ,GAAG,EAAE,GAAG,EAAE,4BAA4B,CAACJ,WAAW,IAAI,kBAAkB,IAAII,QAAQ,GAAG,IAAI,GAAG,EAAE,EAAG;kBAAA6D,QAAA,gBAC9IjF,OAAA;oBACIgF,SAAS,EAAC,6DAA6D;oBACvEqC,OAAO,EAAEA,CAAA,KAAM;sBAAA,IAAAI,WAAA;sBACX1F,UAAU,CAAC,IAAI,CAAC;sBAChBE,cAAc,CAAC0B,GAAG,aAAHA,GAAG,wBAAA8D,WAAA,GAAH9D,GAAG,CAAEC,KAAK,cAAA6D,WAAA,uBAAVA,WAAA,CAAY5D,KAAK,CAAC3D,QAAQ,CAAC,CAAC;oBAC/C,CAAE;oBAAA+E,QAAA,EAEDvD,cAAc,aAAdA,cAAc,wBAAAsF,sBAAA,GAAdtF,cAAc,CAAEiB,cAAc,cAAAqE,sBAAA,wBAAAC,sBAAA,GAA9BD,sBAAA,CAAgCU,OAAO,CAAC,CAAC,CAAC,cAAAT,sBAAA,uBAA1CA,sBAAA,CAA4CU,IAAI,CAACzH,QAAQ;kBAAC;oBAAAqF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvD,CAAC,eACT1F,OAAA;oBACIgF,SAAS,EAAC,kEAAkE;oBAAAC,QAAA,EAE3EvD,cAAc,aAAdA,cAAc,wBAAAwF,sBAAA,GAAdxF,cAAc,CAAEiB,cAAc,cAAAuE,sBAAA,wBAAAC,sBAAA,GAA9BD,sBAAA,CAAgCQ,OAAO,CAAC,CAAC,CAAC,cAAAP,sBAAA,uBAA1CA,sBAAA,CAA4CQ,IAAI,CAACzH,QAAQ;kBAAC;oBAAAqF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eAEN1F,OAAA,CAACP,MAAM,CAACmI,GAAG;gBACPC,GAAG,EAAGC,EAAE,IAAM7G,UAAU,CAAC8G,OAAO,CAACjF,KAAK,CAAC,GAAGgF,EAAI;gBAC9CE,OAAO,EAAE,KAAM;gBACfC,OAAO,EACHrG,WAAW,KAAKkB,KAAK,GACf;kBAAEqC,MAAM,EAAE,EAAAiC,qBAAA,GAAAnG,UAAU,CAAC8G,OAAO,CAACjF,KAAK,CAAC,cAAAsE,qBAAA,uBAAzBA,qBAAA,CAA2Bc,YAAY,KAAI,MAAM;kBAAEC,OAAO,EAAE;gBAAE,CAAC,GACzE;kBAAEhD,MAAM,EAAE,CAAC;kBAAEgD,OAAO,EAAE;gBAAE,CACjC;gBACDC,UAAU,EAAE;kBAAEC,QAAQ,EAAE,GAAG;kBAAEC,IAAI,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC;gBAAE,CAAE;gBACtDtD,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,eAE3BjF,OAAA;kBAAIgF,SAAS,EAAC,6CAA6C;kBAAAC,QAAA,EACtDtB,GAAG,CAAC4E,eAAe,CAACjC,GAAG,CAAC,CAACkC,IAAI,EAAEC,CAAC,kBAC7BzI,OAAA;oBAAAiF,QAAA,EAAauD,IAAI,CAACtI,QAAQ;kBAAC,GAAlBuI,CAAC;oBAAAlD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAsB,CACnC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC;YAAA,GA5DP/B,GAAG,CAAC+E,EAAE;cAAAnD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA6DX,CAAC;UAEd,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGV1F,OAAA;MAASgF,SAAS,EAAC,uCAAuC;MAAAC,QAAA,eACtDjF,OAAA,CAACN,UAAU;QACP0C,cAAc,EAAEA,cAAe;QAC/BwC,gBAAgB,EAAEA,gBAAiB;QACnC1C,YAAY,EAAEA,YAAa;QAC3BhC,QAAQ,EAAEA;MAAS;QAAAqF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAKT,CAAC;AAEd,CAAC;AAACtF,EAAA,CAjSIH,UAAU;EAAA,QAGKL,WAAW,EAGLC,WAAW,EACXA,WAAW;AAAA;AAAA8I,EAAA,GAPhC1I,UAAU;AAmShB,eAAeA,UAAU;;AAIzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA,IAAA0I,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}