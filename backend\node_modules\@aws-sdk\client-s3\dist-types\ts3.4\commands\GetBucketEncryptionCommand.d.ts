import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  GetBucketEncryptionOutput,
  GetBucketEncryptionRequest,
} from "../models/models_0";
import {
  S3ClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../S3Client";
export { __MetadataBearer };
export { $Command };
export interface GetBucketEncryptionCommandInput
  extends GetBucketEncryptionRequest {}
export interface GetBucketEncryptionCommandOutput
  extends GetBucketEncryptionOutput,
    __MetadataBearer {}
declare const GetBucketEncryptionCommand_base: {
  new (
    input: GetBucketEncryptionCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    GetBucketEncryptionCommandInput,
    GetBucketEncryptionCommandOutput,
    S3ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: GetBucketEncryptionCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    GetBucketEncryptionCommandInput,
    GetBucketEncryptionCommandOutput,
    S3ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class GetBucketEncryptionCommand extends GetBucketEncryptionCommand_base {
  protected static __types: {
    api: {
      input: GetBucketEncryptionRequest;
      output: GetBucketEncryptionOutput;
    };
    sdk: {
      input: GetBucketEncryptionCommandInput;
      output: GetBucketEncryptionCommandOutput;
    };
  };
}
