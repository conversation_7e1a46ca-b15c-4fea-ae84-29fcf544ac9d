{"c": ["webpack"], "r": ["pages/career/[careerId]"], "m": ["./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[6].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/next-font-loader/index.js??ruleSet[1].rules[6].oneOf[5].use[2]!./node_modules/next/font/local/target.css?{\"path\":\"src\\\\components\\\\career\\\\CareerDetail.jsx\",\"import\":\"\",\"arguments\":[{\"src\":\"../../../public/font/BankGothicLtBTLight.ttf\",\"display\":\"swap\"}],\"variableName\":\"BankGothic\"}", "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5Cakshy%5COneDrive%5CDesktop%5CAkshay%5CLOOP_PROJECTS%5Cshade_cms%5Cwebsite%5Csrc%5Cpages%5Ccareer%5C%5BcareerId%5D.js&page=%2Fcareer%2F%5BcareerId%5D!", "./node_modules/next/font/local/target.css?{\"path\":\"src\\\\components\\\\career\\\\CareerDetail.jsx\",\"import\":\"\",\"arguments\":[{\"src\":\"../../../public/font/BankGothicLtBTLight.ttf\",\"display\":\"swap\"}],\"variableName\":\"BankGothic\"}", "./src/common/useTruncate.js", "./src/components/career/CareerDetail.jsx", "./src/pages/career/[careerId].js"]}