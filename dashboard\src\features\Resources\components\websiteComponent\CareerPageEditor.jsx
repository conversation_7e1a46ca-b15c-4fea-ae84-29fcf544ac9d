import React from 'react';

const CareerPageEditor = ({ 
  content, 
  language, 
  screen, 
  width, 
  highlight = false,
  liveContent,
  fullScreen,
  purpose 
}) => {
  // Extract hero/banner section from content
  const bannerSection = content?.[1]?.content || {};

  // Responsive values matching SCSS exactly
  const isTablet = screen <= 768 && screen > 480;
  const isMobile = screen <= 480;

  // Hero section styles - EXACT from SCSS
  const heroSectionStyle = {
    position: 'relative',
    minHeight: isMobile || isTablet ? 'auto' : '100vh',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    padding: isMobile ? '100px 16px 60px' : isTablet ? '120px 20px 80px' : '140px 20px 100px',
    overflow: 'hidden',
    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
  };

  const heroBackgroundStyle = {
    position: 'absolute',
    top: 0,
    left: 0,
    width: '100%',
    height: '100%',
    background: 'url("https://loopwebsite.s3.ap-south-1.amazonaws.com/Hero+(2).jpg") no-repeat center center',
    backgroundSize: 'cover',
    zIndex: 0
  };

  const heroBackgroundBeforeStyle = {
    position: 'absolute',
    top: 0,
    left: 0,
    width: '100%',
    height: '100%',
    background: 'linear-gradient(135deg, rgba(37, 99, 235, 0.85) 0%, rgba(59, 130, 246, 0.75) 50%, rgba(147, 51, 234, 0.8) 100%)',
    zIndex: 1
  };

  const heroBackgroundAfterStyle = {
    position: 'absolute',
    top: 0,
    left: 0,
    width: '100%',
    height: '100%',
    background: 'radial-gradient(circle at 20% 50%, rgba(255, 255, 255, 0.1) 0%, transparent 50%)',
    zIndex: 2,
    pointerEvents: 'none'
  };

  const heroOverlayStyle = {
    position: 'absolute',
    bottom: 0,
    left: 0,
    width: '100%',
    height: '30%',
    background: 'linear-gradient(to top, rgba(0, 0, 0, 0.3) 0%, transparent 100%)',
    zIndex: 1
  };

  const heroContentStyle = {
    position: 'relative',
    zIndex: 2,
    maxWidth: '1200px',
    margin: '0 auto',
    textAlign: 'center',
    width: '100%'
  };

  // Title responsive font size: clamp(48px, 8vw, 96px)
  const titleFontSize = screen > 1024 ? '96px' : screen > 768 ? `${Math.min(96, screen * 0.08)}px` : '48px';
  const heroTitleStyle = {
    fontSize: titleFontSize,
    fontWeight: 600,
    color: '#ffffff',
    margin: `0 0 ${isMobile ? '30px' : isTablet ? '40px' : '60px'}`,
    letterSpacing: '-0.02em',
    lineHeight: '1',
    textTransform: 'uppercase',
    textShadow: '0 4px 20px rgba(0, 0, 0, 0.3)',
    border: highlight && liveContent?.[1]?.content?.title?.[language] !== bannerSection?.title?.[language] ? '3px solid #FFD700' : 'none',
    padding: highlight ? '10px' : '0'
  };

  const heroCardStyle = {
    background: 'rgba(255, 255, 255, 0.98)',
    backdropFilter: 'blur(20px)',
    borderRadius: isMobile ? '16px' : isTablet ? '20px' : '24px',
    padding: 0,
    boxShadow: '0 25px 50px rgba(0, 0, 0, 0.25)',
    border: '1px solid rgba(255, 255, 255, 0.4)',
    overflow: 'hidden',
    maxWidth: '1000px',
    margin: '0 auto',
    textAlign: 'left'
  };

  const heroCardContentStyle = {
    padding: isMobile ? '32px 24px' : isTablet ? '40px 40px' : screen > 1024 ? '60px 80px' : '50px 60px'
  };

  // Paragraph font size: clamp(16px, 1.8vw, 20px)
  const paragraphFontSize = screen > 768 ? `${Math.min(20, Math.max(16, screen * 0.018))}px` : '15px';
  const paragraphLineHeight = screen > 768 ? '1.8' : screen > 480 ? '1.7' : '1.65';
  const paragraphMarginBottom = isMobile ? '20px' : isTablet ? '24px' : '28px';

  const heroParagraphStyle = {
    fontSize: paragraphFontSize,
    lineHeight: paragraphLineHeight,
    color: '#1e293b',
    marginBottom: paragraphMarginBottom,
    textAlign: 'left',
    fontWeight: 400
  };

  return (
    <div className="career-page-editor" style={{ 
      width: '100%', 
      backgroundColor: '#ffffff',
      fontFamily: 'BankGothic-Medium-lt, BankGothic-Regular, Arial, sans-serif',
      overflow: 'hidden'
    }}>
      {/* Hero Section - EXACT structure from website */}
      <section style={heroSectionStyle}>
        <div style={heroOverlayStyle}></div>
        <div style={heroBackgroundStyle}>
          <div style={heroBackgroundBeforeStyle}></div>
          <div style={heroBackgroundAfterStyle}></div>
        </div>
        
        <div className="container" style={{ position: 'relative', zIndex: 2 }}>
          <div style={heroContentStyle}>
            <h1 style={heroTitleStyle}>
              {bannerSection?.title?.[language] || 'CAREERS'}
            </h1>
            
            <div style={heroCardStyle}>
              <div style={heroCardContentStyle}>
                <p 
                  style={{
                    ...heroParagraphStyle,
                    border: highlight && liveContent?.[1]?.content?.description?.[language] !== bannerSection?.description?.[language] ? '3px solid #FFD700' : 'none',
                    padding: highlight ? '10px' : '0'
                  }}
                >
                  {bannerSection?.description?.[language] || 'Every day we help our clients build capabilities, solve their toughest problems and create better outcomes. Our purpose is fueled by our people.'}
                </p>
                
                <p 
                  style={{
                    ...heroParagraphStyle,
                    border: highlight && liveContent?.[1]?.content?.description2?.[language] !== bannerSection?.description2?.[language] ? '3px solid #FFD700' : 'none',
                    padding: highlight ? '10px' : '0'
                  }}
                >
                  {bannerSection?.description2?.[language] || 'We believe in the power of human potential and our growth is driven by hiring exceptional talent. From entry-level to leadership positions, our people are encouraged to harness opportunity and add value in every direction for themselves, our clients and our communities. We build teams that trust one another, focus on common goals, and work towards achieving them.'}
                </p>
                
                <p 
                  style={{
                    ...heroParagraphStyle,
                    marginBottom: 0,
                    border: highlight && liveContent?.[1]?.content?.description3?.[language] !== bannerSection?.description3?.[language] ? '3px solid #FFD700' : 'none',
                    padding: highlight ? '10px' : '0'
                  }}
                >
                  {bannerSection?.description3?.[language] || 'Shade has a fair mix of experience, new ideas, and enthusiasm. We are an equal opportunity employer and have people from diverse cultures and backgrounds in our workforce. We look for passionate, curious, creative and solution-driven team players. We hope you find our team doing interesting work, come work with us.'}
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default CareerPageEditor;
