import { useState, useEffect } from 'react'
import { useDispatch } from 'react-redux'
import TitleCard from '../../components/Cards/TitleCard'
import { showNotification } from '../common/headerSlice'
import JobsTable from './components/JobsTable'
import JobModal from './components/JobModal'
import SearchBar from '../../components/Input/SearchBar'
import { PlusIcon } from '@heroicons/react/24/outline'

const INITIAL_JOB_OBJ = {
    title: "",
    category: "OTHER",
    department: "",
    description: "",
    shortDescription: "",
    responsibilities: [],
    requirements: [],
    qualifications: [],
    benefits: [],
    jobType: "FULL_TIME",
    experienceLevel: "ENTRY_LEVEL",
    minExperience: "",
    maxExperience: "",
    location: "",
    isRemote: false,
    isHybrid: false,
    minSalary: "",
    maxSalary: "",
    currency: "SAR",
    salaryPeriod: "MONTHLY",
    applicationDeadline: "",
    applicationEmail: "",
    applicationUrl: "",
    applicationInstructions: "",
    metaTitle: "",
    metaDescription: "",
    keywords: [],
    status: "DRAFT",
    isPublished: false,
    isFeatured: false
}

function Jobs() {
    const dispatch = useDispatch()

    const [jobs, setJobs] = useState([])
    const [loading, setLoading] = useState(false)
    const [isModalOpen, setIsModalOpen] = useState(false)
    const [modalType, setModalType] = useState('CREATE') // CREATE, EDIT, VIEW
    const [selectedJob, setSelectedJob] = useState(INITIAL_JOB_OBJ)
    const [searchText, setSearchText] = useState("")
    const [currentPage, setCurrentPage] = useState(1)
    const [totalPages, setTotalPages] = useState(1)
    const [filters, setFilters] = useState({
        status: '',
        category: '',
        jobType: '',
        isPublished: ''
    })

    // Debug: Log when component mounts
    useEffect(() => {
        console.log('Jobs component mounted')
    }, [])

    // Fetch jobs from API
    const fetchJobs = async (page = 1, search = '', filterParams = {}) => {
        setLoading(true)
        try {
            const queryParams = new URLSearchParams({
                page: page.toString(),
                limit: '10',
                ...(search && { search }),
                ...filterParams
            })

            const response = await fetch(`/api/jobs?${queryParams}`, {
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('token')}`,
                    'Content-Type': 'application/json'
                }
            })

            if (response.ok) {
                const data = await response.json()
                setJobs(data.data.jobs)
                setTotalPages(data.data.pagination.pages)
                setCurrentPage(data.data.pagination.page)
            } else {
                throw new Error('Failed to fetch jobs')
            }
        } catch (error) {
            dispatch(showNotification({ message: 'Error fetching jobs', status: 0 }))
        } finally {
            setLoading(false)
        }
    }

    useEffect(() => {
        fetchJobs(currentPage, searchText, filters)
    }, [currentPage, searchText, filters])

    const handleCreateJob = () => {
        console.log('Create job button clicked') // Debug log
        setSelectedJob(INITIAL_JOB_OBJ)
        setModalType('CREATE')
        setIsModalOpen(true)
        console.log('Modal should be open:', true) // Debug log
    }

    const handleEditJob = (job) => {
        setSelectedJob(job)
        setModalType('EDIT')
        setIsModalOpen(true)
    }

    const handleViewJob = (job) => {
        setSelectedJob(job)
        setModalType('VIEW')
        setIsModalOpen(true)
    }

    const handleDeleteJob = async (jobId) => {
        if (window.confirm('Are you sure you want to delete this job?')) {
            try {
                const response = await fetch(`/api/jobs/${jobId}`, {
                    method: 'DELETE',
                    headers: {
                        'Authorization': `Bearer ${localStorage.getItem('token')}`,
                        'Content-Type': 'application/json'
                    }
                })

                if (response.ok) {
                    dispatch(showNotification({ message: 'Job deleted successfully', status: 1 }))
                    fetchJobs(currentPage, searchText, filters)
                } else {
                    throw new Error('Failed to delete job')
                }
            } catch (error) {
                dispatch(showNotification({ message: 'Error deleting job', status: 0 }))
            }
        }
    }

    const handleModalSubmit = async (jobData) => {
        try {
            const url = modalType === 'CREATE' ? '/api/jobs' : `/api/jobs/${selectedJob.id}`
            const method = modalType === 'CREATE' ? 'POST' : 'PUT'

            const response = await fetch(url, {
                method,
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('token')}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(jobData)
            })

            if (response.ok) {
                const message = modalType === 'CREATE' ? 'Job created successfully' : 'Job updated successfully'
                dispatch(showNotification({ message, status: 1 }))
                setIsModalOpen(false)
                fetchJobs(currentPage, searchText, filters)
            } else {
                throw new Error(`Failed to ${modalType.toLowerCase()} job`)
            }
        } catch (error) {
            dispatch(showNotification({ message: `Error ${modalType.toLowerCase()}ing job`, status: 0 }))
        }
    }

    const handleSearch = (value) => {
        setSearchText(value)
        setCurrentPage(1)
    }

    const handleFilterChange = (newFilters) => {
        setFilters(newFilters)
        setCurrentPage(1)
    }

    const handlePageChange = (page) => {
        setCurrentPage(page)
    }

    const TopSideButtons = () => {
        return (
            <div className="inline-block float-right">
                <button 
                    className="btn px-6 btn-sm normal-case btn-primary"
                    onClick={handleCreateJob}
                >
                    <PlusIcon className="w-4 mr-2" />
                    Add New Job
                </button>
            </div>
        )
    }

    return (
        <>
            <TitleCard title="Job Management" topSideButtons={<TopSideButtons />}>
                <div className="mb-4">
                    <SearchBar 
                        searchText={searchText}
                        styleClass="mr-4"
                        setSearchText={handleSearch}
                        placeholderText="Search jobs..."
                    />
                </div>

                <JobsTable 
                    jobs={jobs}
                    loading={loading}
                    onEdit={handleEditJob}
                    onView={handleViewJob}
                    onDelete={handleDeleteJob}
                    filters={filters}
                    onFilterChange={handleFilterChange}
                    currentPage={currentPage}
                    totalPages={totalPages}
                    onPageChange={handlePageChange}
                />
            </TitleCard>

            {isModalOpen && (
                <JobModal
                    isOpen={isModalOpen}
                    onClose={() => setIsModalOpen(false)}
                    onSubmit={handleModalSubmit}
                    job={selectedJob}
                    mode={modalType}
                />
            )}
        </>
    )
}

export default Jobs
