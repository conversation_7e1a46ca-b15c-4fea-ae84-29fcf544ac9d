"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __name = (target, value) => __defProp(target, "name", { value, configurable: true });
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// src/index.ts
var index_exports = {};
__export(index_exports, {
  AbortMultipartUploadCommand: () => AbortMultipartUploadCommand,
  AnalyticsFilter: () => AnalyticsFilter,
  AnalyticsS3ExportFileFormat: () => AnalyticsS3ExportFileFormat,
  ArchiveStatus: () => ArchiveStatus,
  BucketAccelerateStatus: () => BucketAccelerateStatus,
  BucketAlreadyExists: () => BucketAlreadyExists,
  BucketAlreadyOwnedByYou: () => BucketAlreadyOwnedByYou,
  BucketCannedACL: () => BucketCannedACL,
  BucketLocationConstraint: () => BucketLocationConstraint,
  BucketLogsPermission: () => BucketLogsPermission,
  BucketType: () => BucketType,
  BucketVersioningStatus: () => BucketVersioningStatus,
  ChecksumAlgorithm: () => ChecksumAlgorithm,
  ChecksumMode: () => ChecksumMode,
  ChecksumType: () => ChecksumType,
  CompleteMultipartUploadCommand: () => CompleteMultipartUploadCommand,
  CompleteMultipartUploadOutputFilterSensitiveLog: () => CompleteMultipartUploadOutputFilterSensitiveLog,
  CompleteMultipartUploadRequestFilterSensitiveLog: () => CompleteMultipartUploadRequestFilterSensitiveLog,
  CompressionType: () => CompressionType,
  CopyObjectCommand: () => CopyObjectCommand,
  CopyObjectOutputFilterSensitiveLog: () => CopyObjectOutputFilterSensitiveLog,
  CopyObjectRequestFilterSensitiveLog: () => CopyObjectRequestFilterSensitiveLog,
  CreateBucketCommand: () => CreateBucketCommand,
  CreateBucketMetadataConfigurationCommand: () => CreateBucketMetadataConfigurationCommand,
  CreateBucketMetadataTableConfigurationCommand: () => CreateBucketMetadataTableConfigurationCommand,
  CreateMultipartUploadCommand: () => CreateMultipartUploadCommand,
  CreateMultipartUploadOutputFilterSensitiveLog: () => CreateMultipartUploadOutputFilterSensitiveLog,
  CreateMultipartUploadRequestFilterSensitiveLog: () => CreateMultipartUploadRequestFilterSensitiveLog,
  CreateSessionCommand: () => CreateSessionCommand,
  CreateSessionOutputFilterSensitiveLog: () => CreateSessionOutputFilterSensitiveLog,
  CreateSessionRequestFilterSensitiveLog: () => CreateSessionRequestFilterSensitiveLog,
  DataRedundancy: () => DataRedundancy,
  DeleteBucketAnalyticsConfigurationCommand: () => DeleteBucketAnalyticsConfigurationCommand,
  DeleteBucketCommand: () => DeleteBucketCommand,
  DeleteBucketCorsCommand: () => DeleteBucketCorsCommand,
  DeleteBucketEncryptionCommand: () => DeleteBucketEncryptionCommand,
  DeleteBucketIntelligentTieringConfigurationCommand: () => DeleteBucketIntelligentTieringConfigurationCommand,
  DeleteBucketInventoryConfigurationCommand: () => DeleteBucketInventoryConfigurationCommand,
  DeleteBucketLifecycleCommand: () => DeleteBucketLifecycleCommand,
  DeleteBucketMetadataConfigurationCommand: () => DeleteBucketMetadataConfigurationCommand,
  DeleteBucketMetadataTableConfigurationCommand: () => DeleteBucketMetadataTableConfigurationCommand,
  DeleteBucketMetricsConfigurationCommand: () => DeleteBucketMetricsConfigurationCommand,
  DeleteBucketOwnershipControlsCommand: () => DeleteBucketOwnershipControlsCommand,
  DeleteBucketPolicyCommand: () => DeleteBucketPolicyCommand,
  DeleteBucketReplicationCommand: () => DeleteBucketReplicationCommand,
  DeleteBucketTaggingCommand: () => DeleteBucketTaggingCommand,
  DeleteBucketWebsiteCommand: () => DeleteBucketWebsiteCommand,
  DeleteMarkerReplicationStatus: () => DeleteMarkerReplicationStatus,
  DeleteObjectCommand: () => DeleteObjectCommand,
  DeleteObjectTaggingCommand: () => DeleteObjectTaggingCommand,
  DeleteObjectsCommand: () => DeleteObjectsCommand,
  DeletePublicAccessBlockCommand: () => DeletePublicAccessBlockCommand,
  EncodingType: () => EncodingType,
  EncryptionFilterSensitiveLog: () => EncryptionFilterSensitiveLog,
  EncryptionTypeMismatch: () => EncryptionTypeMismatch,
  Event: () => Event,
  ExistingObjectReplicationStatus: () => ExistingObjectReplicationStatus,
  ExpirationState: () => ExpirationState,
  ExpirationStatus: () => ExpirationStatus,
  ExpressionType: () => ExpressionType,
  FileHeaderInfo: () => FileHeaderInfo,
  FilterRuleName: () => FilterRuleName,
  GetBucketAccelerateConfigurationCommand: () => GetBucketAccelerateConfigurationCommand,
  GetBucketAclCommand: () => GetBucketAclCommand,
  GetBucketAnalyticsConfigurationCommand: () => GetBucketAnalyticsConfigurationCommand,
  GetBucketCorsCommand: () => GetBucketCorsCommand,
  GetBucketEncryptionCommand: () => GetBucketEncryptionCommand,
  GetBucketEncryptionOutputFilterSensitiveLog: () => GetBucketEncryptionOutputFilterSensitiveLog,
  GetBucketIntelligentTieringConfigurationCommand: () => GetBucketIntelligentTieringConfigurationCommand,
  GetBucketInventoryConfigurationCommand: () => GetBucketInventoryConfigurationCommand,
  GetBucketInventoryConfigurationOutputFilterSensitiveLog: () => GetBucketInventoryConfigurationOutputFilterSensitiveLog,
  GetBucketLifecycleConfigurationCommand: () => GetBucketLifecycleConfigurationCommand,
  GetBucketLocationCommand: () => GetBucketLocationCommand,
  GetBucketLoggingCommand: () => GetBucketLoggingCommand,
  GetBucketMetadataConfigurationCommand: () => GetBucketMetadataConfigurationCommand,
  GetBucketMetadataTableConfigurationCommand: () => GetBucketMetadataTableConfigurationCommand,
  GetBucketMetricsConfigurationCommand: () => GetBucketMetricsConfigurationCommand,
  GetBucketNotificationConfigurationCommand: () => GetBucketNotificationConfigurationCommand,
  GetBucketOwnershipControlsCommand: () => GetBucketOwnershipControlsCommand,
  GetBucketPolicyCommand: () => GetBucketPolicyCommand,
  GetBucketPolicyStatusCommand: () => GetBucketPolicyStatusCommand,
  GetBucketReplicationCommand: () => GetBucketReplicationCommand,
  GetBucketRequestPaymentCommand: () => GetBucketRequestPaymentCommand,
  GetBucketTaggingCommand: () => GetBucketTaggingCommand,
  GetBucketVersioningCommand: () => GetBucketVersioningCommand,
  GetBucketWebsiteCommand: () => GetBucketWebsiteCommand,
  GetObjectAclCommand: () => GetObjectAclCommand,
  GetObjectAttributesCommand: () => GetObjectAttributesCommand,
  GetObjectAttributesRequestFilterSensitiveLog: () => GetObjectAttributesRequestFilterSensitiveLog,
  GetObjectCommand: () => GetObjectCommand,
  GetObjectLegalHoldCommand: () => GetObjectLegalHoldCommand,
  GetObjectLockConfigurationCommand: () => GetObjectLockConfigurationCommand,
  GetObjectOutputFilterSensitiveLog: () => GetObjectOutputFilterSensitiveLog,
  GetObjectRequestFilterSensitiveLog: () => GetObjectRequestFilterSensitiveLog,
  GetObjectRetentionCommand: () => GetObjectRetentionCommand,
  GetObjectTaggingCommand: () => GetObjectTaggingCommand,
  GetObjectTorrentCommand: () => GetObjectTorrentCommand,
  GetObjectTorrentOutputFilterSensitiveLog: () => GetObjectTorrentOutputFilterSensitiveLog,
  GetPublicAccessBlockCommand: () => GetPublicAccessBlockCommand,
  HeadBucketCommand: () => HeadBucketCommand,
  HeadObjectCommand: () => HeadObjectCommand,
  HeadObjectOutputFilterSensitiveLog: () => HeadObjectOutputFilterSensitiveLog,
  HeadObjectRequestFilterSensitiveLog: () => HeadObjectRequestFilterSensitiveLog,
  IdempotencyParameterMismatch: () => IdempotencyParameterMismatch,
  IntelligentTieringAccessTier: () => IntelligentTieringAccessTier,
  IntelligentTieringStatus: () => IntelligentTieringStatus,
  InvalidObjectState: () => InvalidObjectState,
  InvalidRequest: () => InvalidRequest,
  InvalidWriteOffset: () => InvalidWriteOffset,
  InventoryConfigurationFilterSensitiveLog: () => InventoryConfigurationFilterSensitiveLog,
  InventoryConfigurationState: () => InventoryConfigurationState,
  InventoryDestinationFilterSensitiveLog: () => InventoryDestinationFilterSensitiveLog,
  InventoryEncryptionFilterSensitiveLog: () => InventoryEncryptionFilterSensitiveLog,
  InventoryFormat: () => InventoryFormat,
  InventoryFrequency: () => InventoryFrequency,
  InventoryIncludedObjectVersions: () => InventoryIncludedObjectVersions,
  InventoryOptionalField: () => InventoryOptionalField,
  InventoryS3BucketDestinationFilterSensitiveLog: () => InventoryS3BucketDestinationFilterSensitiveLog,
  JSONType: () => JSONType,
  ListBucketAnalyticsConfigurationsCommand: () => ListBucketAnalyticsConfigurationsCommand,
  ListBucketIntelligentTieringConfigurationsCommand: () => ListBucketIntelligentTieringConfigurationsCommand,
  ListBucketInventoryConfigurationsCommand: () => ListBucketInventoryConfigurationsCommand,
  ListBucketInventoryConfigurationsOutputFilterSensitiveLog: () => ListBucketInventoryConfigurationsOutputFilterSensitiveLog,
  ListBucketMetricsConfigurationsCommand: () => ListBucketMetricsConfigurationsCommand,
  ListBucketsCommand: () => ListBucketsCommand,
  ListDirectoryBucketsCommand: () => ListDirectoryBucketsCommand,
  ListMultipartUploadsCommand: () => ListMultipartUploadsCommand,
  ListObjectVersionsCommand: () => ListObjectVersionsCommand,
  ListObjectsCommand: () => ListObjectsCommand,
  ListObjectsV2Command: () => ListObjectsV2Command,
  ListPartsCommand: () => ListPartsCommand,
  ListPartsRequestFilterSensitiveLog: () => ListPartsRequestFilterSensitiveLog,
  LocationType: () => LocationType,
  MFADelete: () => MFADelete,
  MFADeleteStatus: () => MFADeleteStatus,
  MetadataDirective: () => MetadataDirective,
  MetricsFilter: () => MetricsFilter,
  MetricsStatus: () => MetricsStatus,
  NoSuchBucket: () => NoSuchBucket,
  NoSuchKey: () => NoSuchKey,
  NoSuchUpload: () => NoSuchUpload,
  NotFound: () => NotFound,
  ObjectAlreadyInActiveTierError: () => ObjectAlreadyInActiveTierError,
  ObjectAttributes: () => ObjectAttributes,
  ObjectCannedACL: () => ObjectCannedACL,
  ObjectLockEnabled: () => ObjectLockEnabled,
  ObjectLockLegalHoldStatus: () => ObjectLockLegalHoldStatus,
  ObjectLockMode: () => ObjectLockMode,
  ObjectLockRetentionMode: () => ObjectLockRetentionMode,
  ObjectNotInActiveTierError: () => ObjectNotInActiveTierError,
  ObjectOwnership: () => ObjectOwnership,
  ObjectStorageClass: () => ObjectStorageClass,
  ObjectVersionStorageClass: () => ObjectVersionStorageClass,
  OptionalObjectAttributes: () => OptionalObjectAttributes,
  OutputLocationFilterSensitiveLog: () => OutputLocationFilterSensitiveLog,
  OwnerOverride: () => OwnerOverride,
  PartitionDateSource: () => PartitionDateSource,
  Payer: () => Payer,
  Permission: () => Permission,
  Protocol: () => Protocol,
  PutBucketAccelerateConfigurationCommand: () => PutBucketAccelerateConfigurationCommand,
  PutBucketAclCommand: () => PutBucketAclCommand,
  PutBucketAnalyticsConfigurationCommand: () => PutBucketAnalyticsConfigurationCommand,
  PutBucketCorsCommand: () => PutBucketCorsCommand,
  PutBucketEncryptionCommand: () => PutBucketEncryptionCommand,
  PutBucketEncryptionRequestFilterSensitiveLog: () => PutBucketEncryptionRequestFilterSensitiveLog,
  PutBucketIntelligentTieringConfigurationCommand: () => PutBucketIntelligentTieringConfigurationCommand,
  PutBucketInventoryConfigurationCommand: () => PutBucketInventoryConfigurationCommand,
  PutBucketInventoryConfigurationRequestFilterSensitiveLog: () => PutBucketInventoryConfigurationRequestFilterSensitiveLog,
  PutBucketLifecycleConfigurationCommand: () => PutBucketLifecycleConfigurationCommand,
  PutBucketLoggingCommand: () => PutBucketLoggingCommand,
  PutBucketMetricsConfigurationCommand: () => PutBucketMetricsConfigurationCommand,
  PutBucketNotificationConfigurationCommand: () => PutBucketNotificationConfigurationCommand,
  PutBucketOwnershipControlsCommand: () => PutBucketOwnershipControlsCommand,
  PutBucketPolicyCommand: () => PutBucketPolicyCommand,
  PutBucketReplicationCommand: () => PutBucketReplicationCommand,
  PutBucketRequestPaymentCommand: () => PutBucketRequestPaymentCommand,
  PutBucketTaggingCommand: () => PutBucketTaggingCommand,
  PutBucketVersioningCommand: () => PutBucketVersioningCommand,
  PutBucketWebsiteCommand: () => PutBucketWebsiteCommand,
  PutObjectAclCommand: () => PutObjectAclCommand,
  PutObjectCommand: () => PutObjectCommand,
  PutObjectLegalHoldCommand: () => PutObjectLegalHoldCommand,
  PutObjectLockConfigurationCommand: () => PutObjectLockConfigurationCommand,
  PutObjectOutputFilterSensitiveLog: () => PutObjectOutputFilterSensitiveLog,
  PutObjectRequestFilterSensitiveLog: () => PutObjectRequestFilterSensitiveLog,
  PutObjectRetentionCommand: () => PutObjectRetentionCommand,
  PutObjectTaggingCommand: () => PutObjectTaggingCommand,
  PutPublicAccessBlockCommand: () => PutPublicAccessBlockCommand,
  QuoteFields: () => QuoteFields,
  RenameObjectCommand: () => RenameObjectCommand,
  ReplicaModificationsStatus: () => ReplicaModificationsStatus,
  ReplicationRuleStatus: () => ReplicationRuleStatus,
  ReplicationStatus: () => ReplicationStatus,
  ReplicationTimeStatus: () => ReplicationTimeStatus,
  RequestCharged: () => RequestCharged,
  RequestPayer: () => RequestPayer,
  RestoreObjectCommand: () => RestoreObjectCommand,
  RestoreObjectRequestFilterSensitiveLog: () => RestoreObjectRequestFilterSensitiveLog,
  RestoreRequestFilterSensitiveLog: () => RestoreRequestFilterSensitiveLog,
  RestoreRequestType: () => RestoreRequestType,
  S3: () => S3,
  S3Client: () => S3Client,
  S3LocationFilterSensitiveLog: () => S3LocationFilterSensitiveLog,
  S3ServiceException: () => S3ServiceException,
  S3TablesBucketType: () => S3TablesBucketType,
  SSEKMSFilterSensitiveLog: () => SSEKMSFilterSensitiveLog,
  SelectObjectContentCommand: () => SelectObjectContentCommand,
  SelectObjectContentEventStream: () => SelectObjectContentEventStream,
  SelectObjectContentEventStreamFilterSensitiveLog: () => SelectObjectContentEventStreamFilterSensitiveLog,
  SelectObjectContentOutputFilterSensitiveLog: () => SelectObjectContentOutputFilterSensitiveLog,
  SelectObjectContentRequestFilterSensitiveLog: () => SelectObjectContentRequestFilterSensitiveLog,
  ServerSideEncryption: () => ServerSideEncryption,
  ServerSideEncryptionByDefaultFilterSensitiveLog: () => ServerSideEncryptionByDefaultFilterSensitiveLog,
  ServerSideEncryptionConfigurationFilterSensitiveLog: () => ServerSideEncryptionConfigurationFilterSensitiveLog,
  ServerSideEncryptionRuleFilterSensitiveLog: () => ServerSideEncryptionRuleFilterSensitiveLog,
  SessionCredentialsFilterSensitiveLog: () => SessionCredentialsFilterSensitiveLog,
  SessionMode: () => SessionMode,
  SseKmsEncryptedObjectsStatus: () => SseKmsEncryptedObjectsStatus,
  StorageClass: () => StorageClass,
  StorageClassAnalysisSchemaVersion: () => StorageClassAnalysisSchemaVersion,
  TableSseAlgorithm: () => TableSseAlgorithm,
  TaggingDirective: () => TaggingDirective,
  Tier: () => Tier,
  TooManyParts: () => TooManyParts,
  TransitionDefaultMinimumObjectSize: () => TransitionDefaultMinimumObjectSize,
  TransitionStorageClass: () => TransitionStorageClass,
  Type: () => Type,
  UpdateBucketMetadataInventoryTableConfigurationCommand: () => UpdateBucketMetadataInventoryTableConfigurationCommand,
  UpdateBucketMetadataJournalTableConfigurationCommand: () => UpdateBucketMetadataJournalTableConfigurationCommand,
  UploadPartCommand: () => UploadPartCommand,
  UploadPartCopyCommand: () => UploadPartCopyCommand,
  UploadPartCopyOutputFilterSensitiveLog: () => UploadPartCopyOutputFilterSensitiveLog,
  UploadPartCopyRequestFilterSensitiveLog: () => UploadPartCopyRequestFilterSensitiveLog,
  UploadPartOutputFilterSensitiveLog: () => UploadPartOutputFilterSensitiveLog,
  UploadPartRequestFilterSensitiveLog: () => UploadPartRequestFilterSensitiveLog,
  WriteGetObjectResponseCommand: () => WriteGetObjectResponseCommand,
  WriteGetObjectResponseRequestFilterSensitiveLog: () => WriteGetObjectResponseRequestFilterSensitiveLog,
  __Client: () => import_smithy_client.Client,
  paginateListBuckets: () => paginateListBuckets,
  paginateListDirectoryBuckets: () => paginateListDirectoryBuckets,
  paginateListObjectsV2: () => paginateListObjectsV2,
  paginateListParts: () => paginateListParts,
  waitForBucketExists: () => waitForBucketExists,
  waitForBucketNotExists: () => waitForBucketNotExists,
  waitForObjectExists: () => waitForObjectExists,
  waitForObjectNotExists: () => waitForObjectNotExists,
  waitUntilBucketExists: () => waitUntilBucketExists,
  waitUntilBucketNotExists: () => waitUntilBucketNotExists,
  waitUntilObjectExists: () => waitUntilObjectExists,
  waitUntilObjectNotExists: () => waitUntilObjectNotExists
});
module.exports = __toCommonJS(index_exports);

// src/S3Client.ts
var import_middleware_expect_continue = require("@aws-sdk/middleware-expect-continue");
var import_middleware_flexible_checksums = require("@aws-sdk/middleware-flexible-checksums");
var import_middleware_host_header = require("@aws-sdk/middleware-host-header");
var import_middleware_logger = require("@aws-sdk/middleware-logger");
var import_middleware_recursion_detection = require("@aws-sdk/middleware-recursion-detection");
var import_middleware_sdk_s32 = require("@aws-sdk/middleware-sdk-s3");
var import_middleware_user_agent = require("@aws-sdk/middleware-user-agent");
var import_config_resolver = require("@smithy/config-resolver");
var import_core3 = require("@smithy/core");
var import_eventstream_serde_config_resolver = require("@smithy/eventstream-serde-config-resolver");
var import_middleware_content_length = require("@smithy/middleware-content-length");

var import_middleware_retry = require("@smithy/middleware-retry");

var import_httpAuthSchemeProvider = require("./auth/httpAuthSchemeProvider");

// src/commands/CreateSessionCommand.ts
var import_middleware_sdk_s3 = require("@aws-sdk/middleware-sdk-s3");
var import_middleware_endpoint = require("@smithy/middleware-endpoint");
var import_middleware_serde = require("@smithy/middleware-serde");


// src/endpoint/EndpointParameters.ts
var resolveClientEndpointParameters = /* @__PURE__ */ __name((options) => {
  return Object.assign(options, {
    useFipsEndpoint: options.useFipsEndpoint ?? false,
    useDualstackEndpoint: options.useDualstackEndpoint ?? false,
    forcePathStyle: options.forcePathStyle ?? false,
    useAccelerateEndpoint: options.useAccelerateEndpoint ?? false,
    useGlobalEndpoint: options.useGlobalEndpoint ?? false,
    disableMultiregionAccessPoints: options.disableMultiregionAccessPoints ?? false,
    defaultSigningName: "s3"
  });
}, "resolveClientEndpointParameters");
var commonParams = {
  ForcePathStyle: { type: "clientContextParams", name: "forcePathStyle" },
  UseArnRegion: { type: "clientContextParams", name: "useArnRegion" },
  DisableMultiRegionAccessPoints: { type: "clientContextParams", name: "disableMultiregionAccessPoints" },
  Accelerate: { type: "clientContextParams", name: "useAccelerateEndpoint" },
  DisableS3ExpressSessionAuth: { type: "clientContextParams", name: "disableS3ExpressSessionAuth" },
  UseGlobalEndpoint: { type: "builtInParams", name: "useGlobalEndpoint" },
  UseFIPS: { type: "builtInParams", name: "useFipsEndpoint" },
  Endpoint: { type: "builtInParams", name: "endpoint" },
  Region: { type: "builtInParams", name: "region" },
  UseDualStack: { type: "builtInParams", name: "useDualstackEndpoint" }
};

// src/models/models_0.ts


// src/models/S3ServiceException.ts
var import_smithy_client = require("@smithy/smithy-client");
var S3ServiceException = class _S3ServiceException extends import_smithy_client.ServiceException {
  static {
    __name(this, "S3ServiceException");
  }
  /**
   * @internal
   */
  constructor(options) {
    super(options);
    Object.setPrototypeOf(this, _S3ServiceException.prototype);
  }
};

// src/models/models_0.ts
var RequestCharged = {
  requester: "requester"
};
var RequestPayer = {
  requester: "requester"
};
var NoSuchUpload = class _NoSuchUpload extends S3ServiceException {
  static {
    __name(this, "NoSuchUpload");
  }
  name = "NoSuchUpload";
  $fault = "client";
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "NoSuchUpload",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _NoSuchUpload.prototype);
  }
};
var BucketAccelerateStatus = {
  Enabled: "Enabled",
  Suspended: "Suspended"
};
var Type = {
  AmazonCustomerByEmail: "AmazonCustomerByEmail",
  CanonicalUser: "CanonicalUser",
  Group: "Group"
};
var Permission = {
  FULL_CONTROL: "FULL_CONTROL",
  READ: "READ",
  READ_ACP: "READ_ACP",
  WRITE: "WRITE",
  WRITE_ACP: "WRITE_ACP"
};
var OwnerOverride = {
  Destination: "Destination"
};
var ChecksumType = {
  COMPOSITE: "COMPOSITE",
  FULL_OBJECT: "FULL_OBJECT"
};
var ServerSideEncryption = {
  AES256: "AES256",
  aws_fsx: "aws:fsx",
  aws_kms: "aws:kms",
  aws_kms_dsse: "aws:kms:dsse"
};
var ObjectCannedACL = {
  authenticated_read: "authenticated-read",
  aws_exec_read: "aws-exec-read",
  bucket_owner_full_control: "bucket-owner-full-control",
  bucket_owner_read: "bucket-owner-read",
  private: "private",
  public_read: "public-read",
  public_read_write: "public-read-write"
};
var ChecksumAlgorithm = {
  CRC32: "CRC32",
  CRC32C: "CRC32C",
  CRC64NVME: "CRC64NVME",
  SHA1: "SHA1",
  SHA256: "SHA256"
};
var MetadataDirective = {
  COPY: "COPY",
  REPLACE: "REPLACE"
};
var ObjectLockLegalHoldStatus = {
  OFF: "OFF",
  ON: "ON"
};
var ObjectLockMode = {
  COMPLIANCE: "COMPLIANCE",
  GOVERNANCE: "GOVERNANCE"
};
var StorageClass = {
  DEEP_ARCHIVE: "DEEP_ARCHIVE",
  EXPRESS_ONEZONE: "EXPRESS_ONEZONE",
  FSX_OPENZFS: "FSX_OPENZFS",
  GLACIER: "GLACIER",
  GLACIER_IR: "GLACIER_IR",
  INTELLIGENT_TIERING: "INTELLIGENT_TIERING",
  ONEZONE_IA: "ONEZONE_IA",
  OUTPOSTS: "OUTPOSTS",
  REDUCED_REDUNDANCY: "REDUCED_REDUNDANCY",
  SNOW: "SNOW",
  STANDARD: "STANDARD",
  STANDARD_IA: "STANDARD_IA"
};
var TaggingDirective = {
  COPY: "COPY",
  REPLACE: "REPLACE"
};
var ObjectNotInActiveTierError = class _ObjectNotInActiveTierError extends S3ServiceException {
  static {
    __name(this, "ObjectNotInActiveTierError");
  }
  name = "ObjectNotInActiveTierError";
  $fault = "client";
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "ObjectNotInActiveTierError",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _ObjectNotInActiveTierError.prototype);
  }
};
var BucketAlreadyExists = class _BucketAlreadyExists extends S3ServiceException {
  static {
    __name(this, "BucketAlreadyExists");
  }
  name = "BucketAlreadyExists";
  $fault = "client";
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "BucketAlreadyExists",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _BucketAlreadyExists.prototype);
  }
};
var BucketAlreadyOwnedByYou = class _BucketAlreadyOwnedByYou extends S3ServiceException {
  static {
    __name(this, "BucketAlreadyOwnedByYou");
  }
  name = "BucketAlreadyOwnedByYou";
  $fault = "client";
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "BucketAlreadyOwnedByYou",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _BucketAlreadyOwnedByYou.prototype);
  }
};
var BucketCannedACL = {
  authenticated_read: "authenticated-read",
  private: "private",
  public_read: "public-read",
  public_read_write: "public-read-write"
};
var DataRedundancy = {
  SingleAvailabilityZone: "SingleAvailabilityZone",
  SingleLocalZone: "SingleLocalZone"
};
var BucketType = {
  Directory: "Directory"
};
var LocationType = {
  AvailabilityZone: "AvailabilityZone",
  LocalZone: "LocalZone"
};
var BucketLocationConstraint = {
  EU: "EU",
  af_south_1: "af-south-1",
  ap_east_1: "ap-east-1",
  ap_northeast_1: "ap-northeast-1",
  ap_northeast_2: "ap-northeast-2",
  ap_northeast_3: "ap-northeast-3",
  ap_south_1: "ap-south-1",
  ap_south_2: "ap-south-2",
  ap_southeast_1: "ap-southeast-1",
  ap_southeast_2: "ap-southeast-2",
  ap_southeast_3: "ap-southeast-3",
  ap_southeast_4: "ap-southeast-4",
  ap_southeast_5: "ap-southeast-5",
  ca_central_1: "ca-central-1",
  cn_north_1: "cn-north-1",
  cn_northwest_1: "cn-northwest-1",
  eu_central_1: "eu-central-1",
  eu_central_2: "eu-central-2",
  eu_north_1: "eu-north-1",
  eu_south_1: "eu-south-1",
  eu_south_2: "eu-south-2",
  eu_west_1: "eu-west-1",
  eu_west_2: "eu-west-2",
  eu_west_3: "eu-west-3",
  il_central_1: "il-central-1",
  me_central_1: "me-central-1",
  me_south_1: "me-south-1",
  sa_east_1: "sa-east-1",
  us_east_2: "us-east-2",
  us_gov_east_1: "us-gov-east-1",
  us_gov_west_1: "us-gov-west-1",
  us_west_1: "us-west-1",
  us_west_2: "us-west-2"
};
var ObjectOwnership = {
  BucketOwnerEnforced: "BucketOwnerEnforced",
  BucketOwnerPreferred: "BucketOwnerPreferred",
  ObjectWriter: "ObjectWriter"
};
var InventoryConfigurationState = {
  DISABLED: "DISABLED",
  ENABLED: "ENABLED"
};
var TableSseAlgorithm = {
  AES256: "AES256",
  aws_kms: "aws:kms"
};
var ExpirationState = {
  DISABLED: "DISABLED",
  ENABLED: "ENABLED"
};
var SessionMode = {
  ReadOnly: "ReadOnly",
  ReadWrite: "ReadWrite"
};
var NoSuchBucket = class _NoSuchBucket extends S3ServiceException {
  static {
    __name(this, "NoSuchBucket");
  }
  name = "NoSuchBucket";
  $fault = "client";
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "NoSuchBucket",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _NoSuchBucket.prototype);
  }
};
var AnalyticsFilter;
((AnalyticsFilter2) => {
  AnalyticsFilter2.visit = /* @__PURE__ */ __name((value, visitor) => {
    if (value.Prefix !== void 0) return visitor.Prefix(value.Prefix);
    if (value.Tag !== void 0) return visitor.Tag(value.Tag);
    if (value.And !== void 0) return visitor.And(value.And);
    return visitor._(value.$unknown[0], value.$unknown[1]);
  }, "visit");
})(AnalyticsFilter || (AnalyticsFilter = {}));
var AnalyticsS3ExportFileFormat = {
  CSV: "CSV"
};
var StorageClassAnalysisSchemaVersion = {
  V_1: "V_1"
};
var IntelligentTieringStatus = {
  Disabled: "Disabled",
  Enabled: "Enabled"
};
var IntelligentTieringAccessTier = {
  ARCHIVE_ACCESS: "ARCHIVE_ACCESS",
  DEEP_ARCHIVE_ACCESS: "DEEP_ARCHIVE_ACCESS"
};
var InventoryFormat = {
  CSV: "CSV",
  ORC: "ORC",
  Parquet: "Parquet"
};
var InventoryIncludedObjectVersions = {
  All: "All",
  Current: "Current"
};
var InventoryOptionalField = {
  BucketKeyStatus: "BucketKeyStatus",
  ChecksumAlgorithm: "ChecksumAlgorithm",
  ETag: "ETag",
  EncryptionStatus: "EncryptionStatus",
  IntelligentTieringAccessTier: "IntelligentTieringAccessTier",
  IsMultipartUploaded: "IsMultipartUploaded",
  LastModifiedDate: "LastModifiedDate",
  ObjectAccessControlList: "ObjectAccessControlList",
  ObjectLockLegalHoldStatus: "ObjectLockLegalHoldStatus",
  ObjectLockMode: "ObjectLockMode",
  ObjectLockRetainUntilDate: "ObjectLockRetainUntilDate",
  ObjectOwner: "ObjectOwner",
  ReplicationStatus: "ReplicationStatus",
  Size: "Size",
  StorageClass: "StorageClass"
};
var InventoryFrequency = {
  Daily: "Daily",
  Weekly: "Weekly"
};
var TransitionStorageClass = {
  DEEP_ARCHIVE: "DEEP_ARCHIVE",
  GLACIER: "GLACIER",
  GLACIER_IR: "GLACIER_IR",
  INTELLIGENT_TIERING: "INTELLIGENT_TIERING",
  ONEZONE_IA: "ONEZONE_IA",
  STANDARD_IA: "STANDARD_IA"
};
var ExpirationStatus = {
  Disabled: "Disabled",
  Enabled: "Enabled"
};
var TransitionDefaultMinimumObjectSize = {
  all_storage_classes_128K: "all_storage_classes_128K",
  varies_by_storage_class: "varies_by_storage_class"
};
var BucketLogsPermission = {
  FULL_CONTROL: "FULL_CONTROL",
  READ: "READ",
  WRITE: "WRITE"
};
var PartitionDateSource = {
  DeliveryTime: "DeliveryTime",
  EventTime: "EventTime"
};
var S3TablesBucketType = {
  aws: "aws",
  customer: "customer"
};
var MetricsFilter;
((MetricsFilter2) => {
  MetricsFilter2.visit = /* @__PURE__ */ __name((value, visitor) => {
    if (value.Prefix !== void 0) return visitor.Prefix(value.Prefix);
    if (value.Tag !== void 0) return visitor.Tag(value.Tag);
    if (value.AccessPointArn !== void 0) return visitor.AccessPointArn(value.AccessPointArn);
    if (value.And !== void 0) return visitor.And(value.And);
    return visitor._(value.$unknown[0], value.$unknown[1]);
  }, "visit");
})(MetricsFilter || (MetricsFilter = {}));
var Event = {
  s3_IntelligentTiering: "s3:IntelligentTiering",
  s3_LifecycleExpiration_: "s3:LifecycleExpiration:*",
  s3_LifecycleExpiration_Delete: "s3:LifecycleExpiration:Delete",
  s3_LifecycleExpiration_DeleteMarkerCreated: "s3:LifecycleExpiration:DeleteMarkerCreated",
  s3_LifecycleTransition: "s3:LifecycleTransition",
  s3_ObjectAcl_Put: "s3:ObjectAcl:Put",
  s3_ObjectCreated_: "s3:ObjectCreated:*",
  s3_ObjectCreated_CompleteMultipartUpload: "s3:ObjectCreated:CompleteMultipartUpload",
  s3_ObjectCreated_Copy: "s3:ObjectCreated:Copy",
  s3_ObjectCreated_Post: "s3:ObjectCreated:Post",
  s3_ObjectCreated_Put: "s3:ObjectCreated:Put",
  s3_ObjectRemoved_: "s3:ObjectRemoved:*",
  s3_ObjectRemoved_Delete: "s3:ObjectRemoved:Delete",
  s3_ObjectRemoved_DeleteMarkerCreated: "s3:ObjectRemoved:DeleteMarkerCreated",
  s3_ObjectRestore_: "s3:ObjectRestore:*",
  s3_ObjectRestore_Completed: "s3:ObjectRestore:Completed",
  s3_ObjectRestore_Delete: "s3:ObjectRestore:Delete",
  s3_ObjectRestore_Post: "s3:ObjectRestore:Post",
  s3_ObjectTagging_: "s3:ObjectTagging:*",
  s3_ObjectTagging_Delete: "s3:ObjectTagging:Delete",
  s3_ObjectTagging_Put: "s3:ObjectTagging:Put",
  s3_ReducedRedundancyLostObject: "s3:ReducedRedundancyLostObject",
  s3_Replication_: "s3:Replication:*",
  s3_Replication_OperationFailedReplication: "s3:Replication:OperationFailedReplication",
  s3_Replication_OperationMissedThreshold: "s3:Replication:OperationMissedThreshold",
  s3_Replication_OperationNotTracked: "s3:Replication:OperationNotTracked",
  s3_Replication_OperationReplicatedAfterThreshold: "s3:Replication:OperationReplicatedAfterThreshold"
};
var FilterRuleName = {
  prefix: "prefix",
  suffix: "suffix"
};
var DeleteMarkerReplicationStatus = {
  Disabled: "Disabled",
  Enabled: "Enabled"
};
var MetricsStatus = {
  Disabled: "Disabled",
  Enabled: "Enabled"
};
var ReplicationTimeStatus = {
  Disabled: "Disabled",
  Enabled: "Enabled"
};
var ExistingObjectReplicationStatus = {
  Disabled: "Disabled",
  Enabled: "Enabled"
};
var ReplicaModificationsStatus = {
  Disabled: "Disabled",
  Enabled: "Enabled"
};
var SseKmsEncryptedObjectsStatus = {
  Disabled: "Disabled",
  Enabled: "Enabled"
};
var ReplicationRuleStatus = {
  Disabled: "Disabled",
  Enabled: "Enabled"
};
var Payer = {
  BucketOwner: "BucketOwner",
  Requester: "Requester"
};
var MFADeleteStatus = {
  Disabled: "Disabled",
  Enabled: "Enabled"
};
var BucketVersioningStatus = {
  Enabled: "Enabled",
  Suspended: "Suspended"
};
var Protocol = {
  http: "http",
  https: "https"
};
var ReplicationStatus = {
  COMPLETE: "COMPLETE",
  COMPLETED: "COMPLETED",
  FAILED: "FAILED",
  PENDING: "PENDING",
  REPLICA: "REPLICA"
};
var ChecksumMode = {
  ENABLED: "ENABLED"
};
var InvalidObjectState = class _InvalidObjectState extends S3ServiceException {
  static {
    __name(this, "InvalidObjectState");
  }
  name = "InvalidObjectState";
  $fault = "client";
  StorageClass;
  AccessTier;
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "InvalidObjectState",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _InvalidObjectState.prototype);
    this.StorageClass = opts.StorageClass;
    this.AccessTier = opts.AccessTier;
  }
};
var NoSuchKey = class _NoSuchKey extends S3ServiceException {
  static {
    __name(this, "NoSuchKey");
  }
  name = "NoSuchKey";
  $fault = "client";
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "NoSuchKey",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _NoSuchKey.prototype);
  }
};
var ObjectAttributes = {
  CHECKSUM: "Checksum",
  ETAG: "ETag",
  OBJECT_PARTS: "ObjectParts",
  OBJECT_SIZE: "ObjectSize",
  STORAGE_CLASS: "StorageClass"
};
var ObjectLockEnabled = {
  Enabled: "Enabled"
};
var ObjectLockRetentionMode = {
  COMPLIANCE: "COMPLIANCE",
  GOVERNANCE: "GOVERNANCE"
};
var NotFound = class _NotFound extends S3ServiceException {
  static {
    __name(this, "NotFound");
  }
  name = "NotFound";
  $fault = "client";
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "NotFound",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _NotFound.prototype);
  }
};
var ArchiveStatus = {
  ARCHIVE_ACCESS: "ARCHIVE_ACCESS",
  DEEP_ARCHIVE_ACCESS: "DEEP_ARCHIVE_ACCESS"
};
var EncodingType = {
  url: "url"
};
var CompleteMultipartUploadOutputFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.SSEKMSKeyId && { SSEKMSKeyId: import_smithy_client.SENSITIVE_STRING }
}), "CompleteMultipartUploadOutputFilterSensitiveLog");
var CompleteMultipartUploadRequestFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.SSECustomerKey && { SSECustomerKey: import_smithy_client.SENSITIVE_STRING }
}), "CompleteMultipartUploadRequestFilterSensitiveLog");
var CopyObjectOutputFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.SSEKMSKeyId && { SSEKMSKeyId: import_smithy_client.SENSITIVE_STRING },
  ...obj.SSEKMSEncryptionContext && { SSEKMSEncryptionContext: import_smithy_client.SENSITIVE_STRING }
}), "CopyObjectOutputFilterSensitiveLog");
var CopyObjectRequestFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.SSECustomerKey && { SSECustomerKey: import_smithy_client.SENSITIVE_STRING },
  ...obj.SSEKMSKeyId && { SSEKMSKeyId: import_smithy_client.SENSITIVE_STRING },
  ...obj.SSEKMSEncryptionContext && { SSEKMSEncryptionContext: import_smithy_client.SENSITIVE_STRING },
  ...obj.CopySourceSSECustomerKey && { CopySourceSSECustomerKey: import_smithy_client.SENSITIVE_STRING }
}), "CopyObjectRequestFilterSensitiveLog");
var CreateMultipartUploadOutputFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.SSEKMSKeyId && { SSEKMSKeyId: import_smithy_client.SENSITIVE_STRING },
  ...obj.SSEKMSEncryptionContext && { SSEKMSEncryptionContext: import_smithy_client.SENSITIVE_STRING }
}), "CreateMultipartUploadOutputFilterSensitiveLog");
var CreateMultipartUploadRequestFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.SSECustomerKey && { SSECustomerKey: import_smithy_client.SENSITIVE_STRING },
  ...obj.SSEKMSKeyId && { SSEKMSKeyId: import_smithy_client.SENSITIVE_STRING },
  ...obj.SSEKMSEncryptionContext && { SSEKMSEncryptionContext: import_smithy_client.SENSITIVE_STRING }
}), "CreateMultipartUploadRequestFilterSensitiveLog");
var SessionCredentialsFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.SecretAccessKey && { SecretAccessKey: import_smithy_client.SENSITIVE_STRING },
  ...obj.SessionToken && { SessionToken: import_smithy_client.SENSITIVE_STRING }
}), "SessionCredentialsFilterSensitiveLog");
var CreateSessionOutputFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.SSEKMSKeyId && { SSEKMSKeyId: import_smithy_client.SENSITIVE_STRING },
  ...obj.SSEKMSEncryptionContext && { SSEKMSEncryptionContext: import_smithy_client.SENSITIVE_STRING },
  ...obj.Credentials && { Credentials: SessionCredentialsFilterSensitiveLog(obj.Credentials) }
}), "CreateSessionOutputFilterSensitiveLog");
var CreateSessionRequestFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.SSEKMSKeyId && { SSEKMSKeyId: import_smithy_client.SENSITIVE_STRING },
  ...obj.SSEKMSEncryptionContext && { SSEKMSEncryptionContext: import_smithy_client.SENSITIVE_STRING }
}), "CreateSessionRequestFilterSensitiveLog");
var ServerSideEncryptionByDefaultFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.KMSMasterKeyID && { KMSMasterKeyID: import_smithy_client.SENSITIVE_STRING }
}), "ServerSideEncryptionByDefaultFilterSensitiveLog");
var ServerSideEncryptionRuleFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.ApplyServerSideEncryptionByDefault && {
    ApplyServerSideEncryptionByDefault: ServerSideEncryptionByDefaultFilterSensitiveLog(
      obj.ApplyServerSideEncryptionByDefault
    )
  }
}), "ServerSideEncryptionRuleFilterSensitiveLog");
var ServerSideEncryptionConfigurationFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.Rules && { Rules: obj.Rules.map((item) => ServerSideEncryptionRuleFilterSensitiveLog(item)) }
}), "ServerSideEncryptionConfigurationFilterSensitiveLog");
var GetBucketEncryptionOutputFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.ServerSideEncryptionConfiguration && {
    ServerSideEncryptionConfiguration: ServerSideEncryptionConfigurationFilterSensitiveLog(
      obj.ServerSideEncryptionConfiguration
    )
  }
}), "GetBucketEncryptionOutputFilterSensitiveLog");
var SSEKMSFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.KeyId && { KeyId: import_smithy_client.SENSITIVE_STRING }
}), "SSEKMSFilterSensitiveLog");
var InventoryEncryptionFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.SSEKMS && { SSEKMS: SSEKMSFilterSensitiveLog(obj.SSEKMS) }
}), "InventoryEncryptionFilterSensitiveLog");
var InventoryS3BucketDestinationFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.Encryption && { Encryption: InventoryEncryptionFilterSensitiveLog(obj.Encryption) }
}), "InventoryS3BucketDestinationFilterSensitiveLog");
var InventoryDestinationFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.S3BucketDestination && {
    S3BucketDestination: InventoryS3BucketDestinationFilterSensitiveLog(obj.S3BucketDestination)
  }
}), "InventoryDestinationFilterSensitiveLog");
var InventoryConfigurationFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.Destination && { Destination: InventoryDestinationFilterSensitiveLog(obj.Destination) }
}), "InventoryConfigurationFilterSensitiveLog");
var GetBucketInventoryConfigurationOutputFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.InventoryConfiguration && {
    InventoryConfiguration: InventoryConfigurationFilterSensitiveLog(obj.InventoryConfiguration)
  }
}), "GetBucketInventoryConfigurationOutputFilterSensitiveLog");
var GetObjectOutputFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.SSEKMSKeyId && { SSEKMSKeyId: import_smithy_client.SENSITIVE_STRING }
}), "GetObjectOutputFilterSensitiveLog");
var GetObjectRequestFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.SSECustomerKey && { SSECustomerKey: import_smithy_client.SENSITIVE_STRING }
}), "GetObjectRequestFilterSensitiveLog");
var GetObjectAttributesRequestFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.SSECustomerKey && { SSECustomerKey: import_smithy_client.SENSITIVE_STRING }
}), "GetObjectAttributesRequestFilterSensitiveLog");
var GetObjectTorrentOutputFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj
}), "GetObjectTorrentOutputFilterSensitiveLog");
var HeadObjectOutputFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.SSEKMSKeyId && { SSEKMSKeyId: import_smithy_client.SENSITIVE_STRING }
}), "HeadObjectOutputFilterSensitiveLog");
var HeadObjectRequestFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.SSECustomerKey && { SSECustomerKey: import_smithy_client.SENSITIVE_STRING }
}), "HeadObjectRequestFilterSensitiveLog");
var ListBucketInventoryConfigurationsOutputFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.InventoryConfigurationList && {
    InventoryConfigurationList: obj.InventoryConfigurationList.map(
      (item) => InventoryConfigurationFilterSensitiveLog(item)
    )
  }
}), "ListBucketInventoryConfigurationsOutputFilterSensitiveLog");

// src/protocols/Aws_restXml.ts
var import_core = require("@aws-sdk/core");
var import_xml_builder = require("@aws-sdk/xml-builder");
var import_core2 = require("@smithy/core");
var import_protocol_http = require("@smithy/protocol-http");

var import_uuid = require("uuid");

// src/models/models_1.ts

var ObjectStorageClass = {
  DEEP_ARCHIVE: "DEEP_ARCHIVE",
  EXPRESS_ONEZONE: "EXPRESS_ONEZONE",
  FSX_OPENZFS: "FSX_OPENZFS",
  GLACIER: "GLACIER",
  GLACIER_IR: "GLACIER_IR",
  INTELLIGENT_TIERING: "INTELLIGENT_TIERING",
  ONEZONE_IA: "ONEZONE_IA",
  OUTPOSTS: "OUTPOSTS",
  REDUCED_REDUNDANCY: "REDUCED_REDUNDANCY",
  SNOW: "SNOW",
  STANDARD: "STANDARD",
  STANDARD_IA: "STANDARD_IA"
};
var OptionalObjectAttributes = {
  RESTORE_STATUS: "RestoreStatus"
};
var ObjectVersionStorageClass = {
  STANDARD: "STANDARD"
};
var MFADelete = {
  Disabled: "Disabled",
  Enabled: "Enabled"
};
var EncryptionTypeMismatch = class _EncryptionTypeMismatch extends S3ServiceException {
  static {
    __name(this, "EncryptionTypeMismatch");
  }
  name = "EncryptionTypeMismatch";
  $fault = "client";
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "EncryptionTypeMismatch",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _EncryptionTypeMismatch.prototype);
  }
};
var InvalidRequest = class _InvalidRequest extends S3ServiceException {
  static {
    __name(this, "InvalidRequest");
  }
  name = "InvalidRequest";
  $fault = "client";
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "InvalidRequest",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _InvalidRequest.prototype);
  }
};
var InvalidWriteOffset = class _InvalidWriteOffset extends S3ServiceException {
  static {
    __name(this, "InvalidWriteOffset");
  }
  name = "InvalidWriteOffset";
  $fault = "client";
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "InvalidWriteOffset",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _InvalidWriteOffset.prototype);
  }
};
var TooManyParts = class _TooManyParts extends S3ServiceException {
  static {
    __name(this, "TooManyParts");
  }
  name = "TooManyParts";
  $fault = "client";
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "TooManyParts",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _TooManyParts.prototype);
  }
};
var IdempotencyParameterMismatch = class _IdempotencyParameterMismatch extends S3ServiceException {
  static {
    __name(this, "IdempotencyParameterMismatch");
  }
  name = "IdempotencyParameterMismatch";
  $fault = "client";
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "IdempotencyParameterMismatch",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _IdempotencyParameterMismatch.prototype);
  }
};
var ObjectAlreadyInActiveTierError = class _ObjectAlreadyInActiveTierError extends S3ServiceException {
  static {
    __name(this, "ObjectAlreadyInActiveTierError");
  }
  name = "ObjectAlreadyInActiveTierError";
  $fault = "client";
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "ObjectAlreadyInActiveTierError",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _ObjectAlreadyInActiveTierError.prototype);
  }
};
var Tier = {
  Bulk: "Bulk",
  Expedited: "Expedited",
  Standard: "Standard"
};
var ExpressionType = {
  SQL: "SQL"
};
var CompressionType = {
  BZIP2: "BZIP2",
  GZIP: "GZIP",
  NONE: "NONE"
};
var FileHeaderInfo = {
  IGNORE: "IGNORE",
  NONE: "NONE",
  USE: "USE"
};
var JSONType = {
  DOCUMENT: "DOCUMENT",
  LINES: "LINES"
};
var QuoteFields = {
  ALWAYS: "ALWAYS",
  ASNEEDED: "ASNEEDED"
};
var RestoreRequestType = {
  SELECT: "SELECT"
};
var SelectObjectContentEventStream;
((SelectObjectContentEventStream3) => {
  SelectObjectContentEventStream3.visit = /* @__PURE__ */ __name((value, visitor) => {
    if (value.Records !== void 0) return visitor.Records(value.Records);
    if (value.Stats !== void 0) return visitor.Stats(value.Stats);
    if (value.Progress !== void 0) return visitor.Progress(value.Progress);
    if (value.Cont !== void 0) return visitor.Cont(value.Cont);
    if (value.End !== void 0) return visitor.End(value.End);
    return visitor._(value.$unknown[0], value.$unknown[1]);
  }, "visit");
})(SelectObjectContentEventStream || (SelectObjectContentEventStream = {}));
var ListPartsRequestFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.SSECustomerKey && { SSECustomerKey: import_smithy_client.SENSITIVE_STRING }
}), "ListPartsRequestFilterSensitiveLog");
var PutBucketEncryptionRequestFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.ServerSideEncryptionConfiguration && {
    ServerSideEncryptionConfiguration: ServerSideEncryptionConfigurationFilterSensitiveLog(
      obj.ServerSideEncryptionConfiguration
    )
  }
}), "PutBucketEncryptionRequestFilterSensitiveLog");
var PutBucketInventoryConfigurationRequestFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.InventoryConfiguration && {
    InventoryConfiguration: InventoryConfigurationFilterSensitiveLog(obj.InventoryConfiguration)
  }
}), "PutBucketInventoryConfigurationRequestFilterSensitiveLog");
var PutObjectOutputFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.SSEKMSKeyId && { SSEKMSKeyId: import_smithy_client.SENSITIVE_STRING },
  ...obj.SSEKMSEncryptionContext && { SSEKMSEncryptionContext: import_smithy_client.SENSITIVE_STRING }
}), "PutObjectOutputFilterSensitiveLog");
var PutObjectRequestFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.SSECustomerKey && { SSECustomerKey: import_smithy_client.SENSITIVE_STRING },
  ...obj.SSEKMSKeyId && { SSEKMSKeyId: import_smithy_client.SENSITIVE_STRING },
  ...obj.SSEKMSEncryptionContext && { SSEKMSEncryptionContext: import_smithy_client.SENSITIVE_STRING }
}), "PutObjectRequestFilterSensitiveLog");
var EncryptionFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.KMSKeyId && { KMSKeyId: import_smithy_client.SENSITIVE_STRING }
}), "EncryptionFilterSensitiveLog");
var S3LocationFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.Encryption && { Encryption: EncryptionFilterSensitiveLog(obj.Encryption) }
}), "S3LocationFilterSensitiveLog");
var OutputLocationFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.S3 && { S3: S3LocationFilterSensitiveLog(obj.S3) }
}), "OutputLocationFilterSensitiveLog");
var RestoreRequestFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.OutputLocation && { OutputLocation: OutputLocationFilterSensitiveLog(obj.OutputLocation) }
}), "RestoreRequestFilterSensitiveLog");
var RestoreObjectRequestFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.RestoreRequest && { RestoreRequest: RestoreRequestFilterSensitiveLog(obj.RestoreRequest) }
}), "RestoreObjectRequestFilterSensitiveLog");
var SelectObjectContentEventStreamFilterSensitiveLog = /* @__PURE__ */ __name((obj) => {
  if (obj.Records !== void 0) return { Records: obj.Records };
  if (obj.Stats !== void 0) return { Stats: obj.Stats };
  if (obj.Progress !== void 0) return { Progress: obj.Progress };
  if (obj.Cont !== void 0) return { Cont: obj.Cont };
  if (obj.End !== void 0) return { End: obj.End };
  if (obj.$unknown !== void 0) return { [obj.$unknown[0]]: "UNKNOWN" };
}, "SelectObjectContentEventStreamFilterSensitiveLog");
var SelectObjectContentOutputFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.Payload && { Payload: "STREAMING_CONTENT" }
}), "SelectObjectContentOutputFilterSensitiveLog");
var SelectObjectContentRequestFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.SSECustomerKey && { SSECustomerKey: import_smithy_client.SENSITIVE_STRING }
}), "SelectObjectContentRequestFilterSensitiveLog");
var UploadPartOutputFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.SSEKMSKeyId && { SSEKMSKeyId: import_smithy_client.SENSITIVE_STRING }
}), "UploadPartOutputFilterSensitiveLog");
var UploadPartRequestFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.SSECustomerKey && { SSECustomerKey: import_smithy_client.SENSITIVE_STRING }
}), "UploadPartRequestFilterSensitiveLog");
var UploadPartCopyOutputFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.SSEKMSKeyId && { SSEKMSKeyId: import_smithy_client.SENSITIVE_STRING }
}), "UploadPartCopyOutputFilterSensitiveLog");
var UploadPartCopyRequestFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.SSECustomerKey && { SSECustomerKey: import_smithy_client.SENSITIVE_STRING },
  ...obj.CopySourceSSECustomerKey && { CopySourceSSECustomerKey: import_smithy_client.SENSITIVE_STRING }
}), "UploadPartCopyRequestFilterSensitiveLog");
var WriteGetObjectResponseRequestFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.SSEKMSKeyId && { SSEKMSKeyId: import_smithy_client.SENSITIVE_STRING }
}), "WriteGetObjectResponseRequestFilterSensitiveLog");

// src/protocols/Aws_restXml.ts
var se_AbortMultipartUploadCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core2.requestBuilder)(input, context);
  const headers = (0, import_smithy_client.map)({}, import_smithy_client.isSerializableHeaderValue, {
    [_xarp]: input[_RP],
    [_xaebo]: input[_EBO],
    [_xaimit]: [() => (0, import_smithy_client.isSerializableHeaderValue)(input[_IMIT]), () => (0, import_smithy_client.dateToUtcString)(input[_IMIT]).toString()]
  });
  b.bp("/{Key+}");
  b.p("Bucket", () => input.Bucket, "{Bucket}", false);
  b.p("Key", () => input.Key, "{Key+}", true);
  const query = (0, import_smithy_client.map)({
    [_xi]: [, "AbortMultipartUpload"],
    [_uI]: [, (0, import_smithy_client.expectNonNull)(input[_UI], `UploadId`)]
  });
  let body;
  b.m("DELETE").h(headers).q(query).b(body);
  return b.build();
}, "se_AbortMultipartUploadCommand");
var se_CompleteMultipartUploadCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core2.requestBuilder)(input, context);
  const headers = (0, import_smithy_client.map)({}, import_smithy_client.isSerializableHeaderValue, {
    "content-type": "application/xml",
    [_xacc]: input[_CCRC],
    [_xacc_]: input[_CCRCC],
    [_xacc__]: input[_CCRCNVME],
    [_xacs]: input[_CSHA],
    [_xacs_]: input[_CSHAh],
    [_xact]: input[_CT],
    [_xamos]: [() => (0, import_smithy_client.isSerializableHeaderValue)(input[_MOS]), () => input[_MOS].toString()],
    [_xarp]: input[_RP],
    [_xaebo]: input[_EBO],
    [_im]: input[_IM],
    [_inm]: input[_INM],
    [_xasseca]: input[_SSECA],
    [_xasseck]: input[_SSECK],
    [_xasseckm]: input[_SSECKMD]
  });
  b.bp("/{Key+}");
  b.p("Bucket", () => input.Bucket, "{Bucket}", false);
  b.p("Key", () => input.Key, "{Key+}", true);
  const query = (0, import_smithy_client.map)({
    [_uI]: [, (0, import_smithy_client.expectNonNull)(input[_UI], `UploadId`)]
  });
  let body;
  let contents;
  if (input.MultipartUpload !== void 0) {
    contents = se_CompletedMultipartUpload(input.MultipartUpload, context);
    contents = contents.n("CompleteMultipartUpload");
    body = _ve;
    contents.a("xmlns", "http://s3.amazonaws.com/doc/2006-03-01/");
    body += contents.toString();
  }
  b.m("POST").h(headers).q(query).b(body);
  return b.build();
}, "se_CompleteMultipartUploadCommand");
var se_CopyObjectCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core2.requestBuilder)(input, context);
  const headers = (0, import_smithy_client.map)({}, import_smithy_client.isSerializableHeaderValue, {
    ...input.Metadata !== void 0 && Object.keys(input.Metadata).reduce((acc, suffix) => {
      acc[`x-amz-meta-${suffix.toLowerCase()}`] = input.Metadata[suffix];
      return acc;
    }, {}),
    [_xaa]: input[_ACL],
    [_cc]: input[_CC],
    [_xaca]: input[_CA],
    [_cd]: input[_CD],
    [_ce]: input[_CE],
    [_cl]: input[_CL],
    [_ct]: input[_CTo],
    [_xacs__]: input[_CS],
    [_xacsim]: input[_CSIM],
    [_xacsims]: [() => (0, import_smithy_client.isSerializableHeaderValue)(input[_CSIMS]), () => (0, import_smithy_client.dateToUtcString)(input[_CSIMS]).toString()],
    [_xacsinm]: input[_CSINM],
    [_xacsius]: [() => (0, import_smithy_client.isSerializableHeaderValue)(input[_CSIUS]), () => (0, import_smithy_client.dateToUtcString)(input[_CSIUS]).toString()],
    [_e]: [() => (0, import_smithy_client.isSerializableHeaderValue)(input[_E]), () => (0, import_smithy_client.dateToUtcString)(input[_E]).toString()],
    [_xagfc]: input[_GFC],
    [_xagr]: input[_GR],
    [_xagra]: input[_GRACP],
    [_xagwa]: input[_GWACP],
    [_xamd]: input[_MD],
    [_xatd]: input[_TD],
    [_xasse]: input[_SSE],
    [_xasc]: input[_SC],
    [_xawrl]: input[_WRL],
    [_xasseca]: input[_SSECA],
    [_xasseck]: input[_SSECK],
    [_xasseckm]: input[_SSECKMD],
    [_xasseakki]: input[_SSEKMSKI],
    [_xassec]: input[_SSEKMSEC],
    [_xassebke]: [() => (0, import_smithy_client.isSerializableHeaderValue)(input[_BKE]), () => input[_BKE].toString()],
    [_xacssseca]: input[_CSSSECA],
    [_xacssseck]: input[_CSSSECK],
    [_xacssseckm]: input[_CSSSECKMD],
    [_xarp]: input[_RP],
    [_xat]: input[_T],
    [_xaolm]: input[_OLM],
    [_xaolrud]: [() => (0, import_smithy_client.isSerializableHeaderValue)(input[_OLRUD]), () => (0, import_smithy_client.serializeDateTime)(input[_OLRUD]).toString()],
    [_xaollh]: input[_OLLHS],
    [_xaebo]: input[_EBO],
    [_xasebo]: input[_ESBO]
  });
  b.bp("/{Key+}");
  b.p("Bucket", () => input.Bucket, "{Bucket}", false);
  b.p("Key", () => input.Key, "{Key+}", true);
  const query = (0, import_smithy_client.map)({
    [_xi]: [, "CopyObject"]
  });
  let body;
  b.m("PUT").h(headers).q(query).b(body);
  return b.build();
}, "se_CopyObjectCommand");
var se_CreateBucketCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core2.requestBuilder)(input, context);
  const headers = (0, import_smithy_client.map)({}, import_smithy_client.isSerializableHeaderValue, {
    "content-type": "application/xml",
    [_xaa]: input[_ACL],
    [_xagfc]: input[_GFC],
    [_xagr]: input[_GR],
    [_xagra]: input[_GRACP],
    [_xagw]: input[_GW],
    [_xagwa]: input[_GWACP],
    [_xabole]: [() => (0, import_smithy_client.isSerializableHeaderValue)(input[_OLEFB]), () => input[_OLEFB].toString()],
    [_xaoo]: input[_OO]
  });
  b.bp("/");
  b.p("Bucket", () => input.Bucket, "{Bucket}", false);
  let body;
  let contents;
  if (input.CreateBucketConfiguration !== void 0) {
    contents = se_CreateBucketConfiguration(input.CreateBucketConfiguration, context);
    body = _ve;
    contents.a("xmlns", "http://s3.amazonaws.com/doc/2006-03-01/");
    body += contents.toString();
  }
  b.m("PUT").h(headers).b(body);
  return b.build();
}, "se_CreateBucketCommand");
var se_CreateBucketMetadataConfigurationCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core2.requestBuilder)(input, context);
  const headers = (0, import_smithy_client.map)({}, import_smithy_client.isSerializableHeaderValue, {
    "content-type": "application/xml",
    [_cm]: input[_CMD],
    [_xasca]: input[_CA],
    [_xaebo]: input[_EBO]
  });
  b.bp("/");
  b.p("Bucket", () => input.Bucket, "{Bucket}", false);
  const query = (0, import_smithy_client.map)({
    [_mC]: [, ""]
  });
  let body;
  let contents;
  if (input.MetadataConfiguration !== void 0) {
    contents = se_MetadataConfiguration(input.MetadataConfiguration, context);
    body = _ve;
    contents.a("xmlns", "http://s3.amazonaws.com/doc/2006-03-01/");
    body += contents.toString();
  }
  b.m("POST").h(headers).q(query).b(body);
  return b.build();
}, "se_CreateBucketMetadataConfigurationCommand");
var se_CreateBucketMetadataTableConfigurationCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core2.requestBuilder)(input, context);
  const headers = (0, import_smithy_client.map)({}, import_smithy_client.isSerializableHeaderValue, {
    "content-type": "application/xml",
    [_cm]: input[_CMD],
    [_xasca]: input[_CA],
    [_xaebo]: input[_EBO]
  });
  b.bp("/");
  b.p("Bucket", () => input.Bucket, "{Bucket}", false);
  const query = (0, import_smithy_client.map)({
    [_mT]: [, ""]
  });
  let body;
  let contents;
  if (input.MetadataTableConfiguration !== void 0) {
    contents = se_MetadataTableConfiguration(input.MetadataTableConfiguration, context);
    body = _ve;
    contents.a("xmlns", "http://s3.amazonaws.com/doc/2006-03-01/");
    body += contents.toString();
  }
  b.m("POST").h(headers).q(query).b(body);
  return b.build();
}, "se_CreateBucketMetadataTableConfigurationCommand");
var se_CreateMultipartUploadCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core2.requestBuilder)(input, context);
  const headers = (0, import_smithy_client.map)({}, import_smithy_client.isSerializableHeaderValue, {
    ...input.Metadata !== void 0 && Object.keys(input.Metadata).reduce((acc, suffix) => {
      acc[`x-amz-meta-${suffix.toLowerCase()}`] = input.Metadata[suffix];
      return acc;
    }, {}),
    [_xaa]: input[_ACL],
    [_cc]: input[_CC],
    [_cd]: input[_CD],
    [_ce]: input[_CE],
    [_cl]: input[_CL],
    [_ct]: input[_CTo],
    [_e]: [() => (0, import_smithy_client.isSerializableHeaderValue)(input[_E]), () => (0, import_smithy_client.dateToUtcString)(input[_E]).toString()],
    [_xagfc]: input[_GFC],
    [_xagr]: input[_GR],
    [_xagra]: input[_GRACP],
    [_xagwa]: input[_GWACP],
    [_xasse]: input[_SSE],
    [_xasc]: input[_SC],
    [_xawrl]: input[_WRL],
    [_xasseca]: input[_SSECA],
    [_xasseck]: input[_SSECK],
    [_xasseckm]: input[_SSECKMD],
    [_xasseakki]: input[_SSEKMSKI],
    [_xassec]: input[_SSEKMSEC],
    [_xassebke]: [() => (0, import_smithy_client.isSerializableHeaderValue)(input[_BKE]), () => input[_BKE].toString()],
    [_xarp]: input[_RP],
    [_xat]: input[_T],
    [_xaolm]: input[_OLM],
    [_xaolrud]: [() => (0, import_smithy_client.isSerializableHeaderValue)(input[_OLRUD]), () => (0, import_smithy_client.serializeDateTime)(input[_OLRUD]).toString()],
    [_xaollh]: input[_OLLHS],
    [_xaebo]: input[_EBO],
    [_xaca]: input[_CA],
    [_xact]: input[_CT]
  });
  b.bp("/{Key+}");
  b.p("Bucket", () => input.Bucket, "{Bucket}", false);
  b.p("Key", () => input.Key, "{Key+}", true);
  const query = (0, import_smithy_client.map)({
    [_u]: [, ""]
  });
  let body;
  b.m("POST").h(headers).q(query).b(body);
  return b.build();
}, "se_CreateMultipartUploadCommand");
var se_CreateSessionCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core2.requestBuilder)(input, context);
  const headers = (0, import_smithy_client.map)({}, import_smithy_client.isSerializableHeaderValue, {
    [_xacsm]: input[_SM],
    [_xasse]: input[_SSE],
    [_xasseakki]: input[_SSEKMSKI],
    [_xassec]: input[_SSEKMSEC],
    [_xassebke]: [() => (0, import_smithy_client.isSerializableHeaderValue)(input[_BKE]), () => input[_BKE].toString()]
  });
  b.bp("/");
  b.p("Bucket", () => input.Bucket, "{Bucket}", false);
  const query = (0, import_smithy_client.map)({
    [_s]: [, ""]
  });
  let body;
  b.m("GET").h(headers).q(query).b(body);
  return b.build();
}, "se_CreateSessionCommand");
var se_DeleteBucketCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core2.requestBuilder)(input, context);
  const headers = (0, import_smithy_client.map)({}, import_smithy_client.isSerializableHeaderValue, {
    [_xaebo]: input[_EBO]
  });
  b.bp("/");
  b.p("Bucket", () => input.Bucket, "{Bucket}", false);
  let body;
  b.m("DELETE").h(headers).b(body);
  return b.build();
}, "se_DeleteBucketCommand");
var se_DeleteBucketAnalyticsConfigurationCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core2.requestBuilder)(input, context);
  const headers = (0, import_smithy_client.map)({}, import_smithy_client.isSerializableHeaderValue, {
    [_xaebo]: input[_EBO]
  });
  b.bp("/");
  b.p("Bucket", () => input.Bucket, "{Bucket}", false);
  const query = (0, import_smithy_client.map)({
    [_a]: [, ""],
    [_i]: [, (0, import_smithy_client.expectNonNull)(input[_I], `Id`)]
  });
  let body;
  b.m("DELETE").h(headers).q(query).b(body);
  return b.build();
}, "se_DeleteBucketAnalyticsConfigurationCommand");
var se_DeleteBucketCorsCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core2.requestBuilder)(input, context);
  const headers = (0, import_smithy_client.map)({}, import_smithy_client.isSerializableHeaderValue, {
    [_xaebo]: input[_EBO]
  });
  b.bp("/");
  b.p("Bucket", () => input.Bucket, "{Bucket}", false);
  const query = (0, import_smithy_client.map)({
    [_c]: [, ""]
  });
  let body;
  b.m("DELETE").h(headers).q(query).b(body);
  return b.build();
}, "se_DeleteBucketCorsCommand");
var se_DeleteBucketEncryptionCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core2.requestBuilder)(input, context);
  const headers = (0, import_smithy_client.map)({}, import_smithy_client.isSerializableHeaderValue, {
    [_xaebo]: input[_EBO]
  });
  b.bp("/");
  b.p("Bucket", () => input.Bucket, "{Bucket}", false);
  const query = (0, import_smithy_client.map)({
    [_en]: [, ""]
  });
  let body;
  b.m("DELETE").h(headers).q(query).b(body);
  return b.build();
}, "se_DeleteBucketEncryptionCommand");
var se_DeleteBucketIntelligentTieringConfigurationCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core2.requestBuilder)(input, context);
  const headers = (0, import_smithy_client.map)({}, import_smithy_client.isSerializableHeaderValue, {
    [_xaebo]: input[_EBO]
  });
  b.bp("/");
  b.p("Bucket", () => input.Bucket, "{Bucket}", false);
  const query = (0, import_smithy_client.map)({
    [_it]: [, ""],
    [_i]: [, (0, import_smithy_client.expectNonNull)(input[_I], `Id`)]
  });
  let body;
  b.m("DELETE").h(headers).q(query).b(body);
  return b.build();
}, "se_DeleteBucketIntelligentTieringConfigurationCommand");
var se_DeleteBucketInventoryConfigurationCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core2.requestBuilder)(input, context);
  const headers = (0, import_smithy_client.map)({}, import_smithy_client.isSerializableHeaderValue, {
    [_xaebo]: input[_EBO]
  });
  b.bp("/");
  b.p("Bucket", () => input.Bucket, "{Bucket}", false);
  const query = (0, import_smithy_client.map)({
    [_in]: [, ""],
    [_i]: [, (0, import_smithy_client.expectNonNull)(input[_I], `Id`)]
  });
  let body;
  b.m("DELETE").h(headers).q(query).b(body);
  return b.build();
}, "se_DeleteBucketInventoryConfigurationCommand");
var se_DeleteBucketLifecycleCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core2.requestBuilder)(input, context);
  const headers = (0, import_smithy_client.map)({}, import_smithy_client.isSerializableHeaderValue, {
    [_xaebo]: input[_EBO]
  });
  b.bp("/");
  b.p("Bucket", () => input.Bucket, "{Bucket}", false);
  const query = (0, import_smithy_client.map)({
    [_l]: [, ""]
  });
  let body;
  b.m("DELETE").h(headers).q(query).b(body);
  return b.build();
}, "se_DeleteBucketLifecycleCommand");
var se_DeleteBucketMetadataConfigurationCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core2.requestBuilder)(input, context);
  const headers = (0, import_smithy_client.map)({}, import_smithy_client.isSerializableHeaderValue, {
    [_xaebo]: input[_EBO]
  });
  b.bp("/");
  b.p("Bucket", () => input.Bucket, "{Bucket}", false);
  const query = (0, import_smithy_client.map)({
    [_mC]: [, ""]
  });
  let body;
  b.m("DELETE").h(headers).q(query).b(body);
  return b.build();
}, "se_DeleteBucketMetadataConfigurationCommand");
var se_DeleteBucketMetadataTableConfigurationCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core2.requestBuilder)(input, context);
  const headers = (0, import_smithy_client.map)({}, import_smithy_client.isSerializableHeaderValue, {
    [_xaebo]: input[_EBO]
  });
  b.bp("/");
  b.p("Bucket", () => input.Bucket, "{Bucket}", false);
  const query = (0, import_smithy_client.map)({
    [_mT]: [, ""]
  });
  let body;
  b.m("DELETE").h(headers).q(query).b(body);
  return b.build();
}, "se_DeleteBucketMetadataTableConfigurationCommand");
var se_DeleteBucketMetricsConfigurationCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core2.requestBuilder)(input, context);
  const headers = (0, import_smithy_client.map)({}, import_smithy_client.isSerializableHeaderValue, {
    [_xaebo]: input[_EBO]
  });
  b.bp("/");
  b.p("Bucket", () => input.Bucket, "{Bucket}", false);
  const query = (0, import_smithy_client.map)({
    [_m]: [, ""],
    [_i]: [, (0, import_smithy_client.expectNonNull)(input[_I], `Id`)]
  });
  let body;
  b.m("DELETE").h(headers).q(query).b(body);
  return b.build();
}, "se_DeleteBucketMetricsConfigurationCommand");
var se_DeleteBucketOwnershipControlsCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core2.requestBuilder)(input, context);
  const headers = (0, import_smithy_client.map)({}, import_smithy_client.isSerializableHeaderValue, {
    [_xaebo]: input[_EBO]
  });
  b.bp("/");
  b.p("Bucket", () => input.Bucket, "{Bucket}", false);
  const query = (0, import_smithy_client.map)({
    [_oC]: [, ""]
  });
  let body;
  b.m("DELETE").h(headers).q(query).b(body);
  return b.build();
}, "se_DeleteBucketOwnershipControlsCommand");
var se_DeleteBucketPolicyCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core2.requestBuilder)(input, context);
  const headers = (0, import_smithy_client.map)({}, import_smithy_client.isSerializableHeaderValue, {
    [_xaebo]: input[_EBO]
  });
  b.bp("/");
  b.p("Bucket", () => input.Bucket, "{Bucket}", false);
  const query = (0, import_smithy_client.map)({
    [_p]: [, ""]
  });
  let body;
  b.m("DELETE").h(headers).q(query).b(body);
  return b.build();
}, "se_DeleteBucketPolicyCommand");
var se_DeleteBucketReplicationCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core2.requestBuilder)(input, context);
  const headers = (0, import_smithy_client.map)({}, import_smithy_client.isSerializableHeaderValue, {
    [_xaebo]: input[_EBO]
  });
  b.bp("/");
  b.p("Bucket", () => input.Bucket, "{Bucket}", false);
  const query = (0, import_smithy_client.map)({
    [_r]: [, ""]
  });
  let body;
  b.m("DELETE").h(headers).q(query).b(body);
  return b.build();
}, "se_DeleteBucketReplicationCommand");
var se_DeleteBucketTaggingCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core2.requestBuilder)(input, context);
  const headers = (0, import_smithy_client.map)({}, import_smithy_client.isSerializableHeaderValue, {
    [_xaebo]: input[_EBO]
  });
  b.bp("/");
  b.p("Bucket", () => input.Bucket, "{Bucket}", false);
  const query = (0, import_smithy_client.map)({
    [_t]: [, ""]
  });
  let body;
  b.m("DELETE").h(headers).q(query).b(body);
  return b.build();
}, "se_DeleteBucketTaggingCommand");
var se_DeleteBucketWebsiteCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core2.requestBuilder)(input, context);
  const headers = (0, import_smithy_client.map)({}, import_smithy_client.isSerializableHeaderValue, {
    [_xaebo]: input[_EBO]
  });
  b.bp("/");
  b.p("Bucket", () => input.Bucket, "{Bucket}", false);
  const query = (0, import_smithy_client.map)({
    [_w]: [, ""]
  });
  let body;
  b.m("DELETE").h(headers).q(query).b(body);
  return b.build();
}, "se_DeleteBucketWebsiteCommand");
var se_DeleteObjectCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core2.requestBuilder)(input, context);
  const headers = (0, import_smithy_client.map)({}, import_smithy_client.isSerializableHeaderValue, {
    [_xam]: input[_MFA],
    [_xarp]: input[_RP],
    [_xabgr]: [() => (0, import_smithy_client.isSerializableHeaderValue)(input[_BGR]), () => input[_BGR].toString()],
    [_xaebo]: input[_EBO],
    [_im]: input[_IM],
    [_xaimlmt]: [() => (0, import_smithy_client.isSerializableHeaderValue)(input[_IMLMT]), () => (0, import_smithy_client.dateToUtcString)(input[_IMLMT]).toString()],
    [_xaims]: [() => (0, import_smithy_client.isSerializableHeaderValue)(input[_IMS]), () => input[_IMS].toString()]
  });
  b.bp("/{Key+}");
  b.p("Bucket", () => input.Bucket, "{Bucket}", false);
  b.p("Key", () => input.Key, "{Key+}", true);
  const query = (0, import_smithy_client.map)({
    [_xi]: [, "DeleteObject"],
    [_vI]: [, input[_VI]]
  });
  let body;
  b.m("DELETE").h(headers).q(query).b(body);
  return b.build();
}, "se_DeleteObjectCommand");
var se_DeleteObjectsCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core2.requestBuilder)(input, context);
  const headers = (0, import_smithy_client.map)({}, import_smithy_client.isSerializableHeaderValue, {
    "content-type": "application/xml",
    [_xam]: input[_MFA],
    [_xarp]: input[_RP],
    [_xabgr]: [() => (0, import_smithy_client.isSerializableHeaderValue)(input[_BGR]), () => input[_BGR].toString()],
    [_xaebo]: input[_EBO],
    [_xasca]: input[_CA]
  });
  b.bp("/");
  b.p("Bucket", () => input.Bucket, "{Bucket}", false);
  const query = (0, import_smithy_client.map)({
    [_d]: [, ""]
  });
  let body;
  let contents;
  if (input.Delete !== void 0) {
    contents = se_Delete(input.Delete, context);
    body = _ve;
    contents.a("xmlns", "http://s3.amazonaws.com/doc/2006-03-01/");
    body += contents.toString();
  }
  b.m("POST").h(headers).q(query).b(body);
  return b.build();
}, "se_DeleteObjectsCommand");
var se_DeleteObjectTaggingCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core2.requestBuilder)(input, context);
  const headers = (0, import_smithy_client.map)({}, import_smithy_client.isSerializableHeaderValue, {
    [_xaebo]: input[_EBO]
  });
  b.bp("/{Key+}");
  b.p("Bucket", () => input.Bucket, "{Bucket}", false);
  b.p("Key", () => input.Key, "{Key+}", true);
  const query = (0, import_smithy_client.map)({
    [_t]: [, ""],
    [_vI]: [, input[_VI]]
  });
  let body;
  b.m("DELETE").h(headers).q(query).b(body);
  return b.build();
}, "se_DeleteObjectTaggingCommand");
var se_DeletePublicAccessBlockCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core2.requestBuilder)(input, context);
  const headers = (0, import_smithy_client.map)({}, import_smithy_client.isSerializableHeaderValue, {
    [_xaebo]: input[_EBO]
  });
  b.bp("/");
  b.p("Bucket", () => input.Bucket, "{Bucket}", false);
  const query = (0, import_smithy_client.map)({
    [_pAB]: [, ""]
  });
  let body;
  b.m("DELETE").h(headers).q(query).b(body);
  return b.build();
}, "se_DeletePublicAccessBlockCommand");
var se_GetBucketAccelerateConfigurationCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core2.requestBuilder)(input, context);
  const headers = (0, import_smithy_client.map)({}, import_smithy_client.isSerializableHeaderValue, {
    [_xaebo]: input[_EBO],
    [_xarp]: input[_RP]
  });
  b.bp("/");
  b.p("Bucket", () => input.Bucket, "{Bucket}", false);
  const query = (0, import_smithy_client.map)({
    [_ac]: [, ""]
  });
  let body;
  b.m("GET").h(headers).q(query).b(body);
  return b.build();
}, "se_GetBucketAccelerateConfigurationCommand");
var se_GetBucketAclCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core2.requestBuilder)(input, context);
  const headers = (0, import_smithy_client.map)({}, import_smithy_client.isSerializableHeaderValue, {
    [_xaebo]: input[_EBO]
  });
  b.bp("/");
  b.p("Bucket", () => input.Bucket, "{Bucket}", false);
  const query = (0, import_smithy_client.map)({
    [_acl]: [, ""]
  });
  let body;
  b.m("GET").h(headers).q(query).b(body);
  return b.build();
}, "se_GetBucketAclCommand");
var se_GetBucketAnalyticsConfigurationCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core2.requestBuilder)(input, context);
  const headers = (0, import_smithy_client.map)({}, import_smithy_client.isSerializableHeaderValue, {
    [_xaebo]: input[_EBO]
  });
  b.bp("/");
  b.p("Bucket", () => input.Bucket, "{Bucket}", false);
  const query = (0, import_smithy_client.map)({
    [_a]: [, ""],
    [_xi]: [, "GetBucketAnalyticsConfiguration"],
    [_i]: [, (0, import_smithy_client.expectNonNull)(input[_I], `Id`)]
  });
  let body;
  b.m("GET").h(headers).q(query).b(body);
  return b.build();
}, "se_GetBucketAnalyticsConfigurationCommand");
var se_GetBucketCorsCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core2.requestBuilder)(input, context);
  const headers = (0, import_smithy_client.map)({}, import_smithy_client.isSerializableHeaderValue, {
    [_xaebo]: input[_EBO]
  });
  b.bp("/");
  b.p("Bucket", () => input.Bucket, "{Bucket}", false);
  const query = (0, import_smithy_client.map)({
    [_c]: [, ""]
  });
  let body;
  b.m("GET").h(headers).q(query).b(body);
  return b.build();
}, "se_GetBucketCorsCommand");
var se_GetBucketEncryptionCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core2.requestBuilder)(input, context);
  const headers = (0, import_smithy_client.map)({}, import_smithy_client.isSerializableHeaderValue, {
    [_xaebo]: input[_EBO]
  });
  b.bp("/");
  b.p("Bucket", () => input.Bucket, "{Bucket}", false);
  const query = (0, import_smithy_client.map)({
    [_en]: [, ""]
  });
  let body;
  b.m("GET").h(headers).q(query).b(body);
  return b.build();
}, "se_GetBucketEncryptionCommand");
var se_GetBucketIntelligentTieringConfigurationCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core2.requestBuilder)(input, context);
  const headers = (0, import_smithy_client.map)({}, import_smithy_client.isSerializableHeaderValue, {
    [_xaebo]: input[_EBO]
  });
  b.bp("/");
  b.p("Bucket", () => input.Bucket, "{Bucket}", false);
  const query = (0, import_smithy_client.map)({
    [_it]: [, ""],
    [_xi]: [, "GetBucketIntelligentTieringConfiguration"],
    [_i]: [, (0, import_smithy_client.expectNonNull)(input[_I], `Id`)]
  });
  let body;
  b.m("GET").h(headers).q(query).b(body);
  return b.build();
}, "se_GetBucketIntelligentTieringConfigurationCommand");
var se_GetBucketInventoryConfigurationCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core2.requestBuilder)(input, context);
  const headers = (0, import_smithy_client.map)({}, import_smithy_client.isSerializableHeaderValue, {
    [_xaebo]: input[_EBO]
  });
  b.bp("/");
  b.p("Bucket", () => input.Bucket, "{Bucket}", false);
  const query = (0, import_smithy_client.map)({
    [_in]: [, ""],
    [_xi]: [, "GetBucketInventoryConfiguration"],
    [_i]: [, (0, import_smithy_client.expectNonNull)(input[_I], `Id`)]
  });
  let body;
  b.m("GET").h(headers).q(query).b(body);
  return b.build();
}, "se_GetBucketInventoryConfigurationCommand");
var se_GetBucketLifecycleConfigurationCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core2.requestBuilder)(input, context);
  const headers = (0, import_smithy_client.map)({}, import_smithy_client.isSerializableHeaderValue, {
    [_xaebo]: input[_EBO]
  });
  b.bp("/");
  b.p("Bucket", () => input.Bucket, "{Bucket}", false);
  const query = (0, import_smithy_client.map)({
    [_l]: [, ""]
  });
  let body;
  b.m("GET").h(headers).q(query).b(body);
  return b.build();
}, "se_GetBucketLifecycleConfigurationCommand");
var se_GetBucketLocationCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core2.requestBuilder)(input, context);
  const headers = (0, import_smithy_client.map)({}, import_smithy_client.isSerializableHeaderValue, {
    [_xaebo]: input[_EBO]
  });
  b.bp("/");
  b.p("Bucket", () => input.Bucket, "{Bucket}", false);
  const query = (0, import_smithy_client.map)({
    [_lo]: [, ""]
  });
  let body;
  b.m("GET").h(headers).q(query).b(body);
  return b.build();
}, "se_GetBucketLocationCommand");
var se_GetBucketLoggingCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core2.requestBuilder)(input, context);
  const headers = (0, import_smithy_client.map)({}, import_smithy_client.isSerializableHeaderValue, {
    [_xaebo]: input[_EBO]
  });
  b.bp("/");
  b.p("Bucket", () => input.Bucket, "{Bucket}", false);
  const query = (0, import_smithy_client.map)({
    [_log]: [, ""]
  });
  let body;
  b.m("GET").h(headers).q(query).b(body);
  return b.build();
}, "se_GetBucketLoggingCommand");
var se_GetBucketMetadataConfigurationCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core2.requestBuilder)(input, context);
  const headers = (0, import_smithy_client.map)({}, import_smithy_client.isSerializableHeaderValue, {
    [_xaebo]: input[_EBO]
  });
  b.bp("/");
  b.p("Bucket", () => input.Bucket, "{Bucket}", false);
  const query = (0, import_smithy_client.map)({
    [_mC]: [, ""]
  });
  let body;
  b.m("GET").h(headers).q(query).b(body);
  return b.build();
}, "se_GetBucketMetadataConfigurationCommand");
var se_GetBucketMetadataTableConfigurationCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core2.requestBuilder)(input, context);
  const headers = (0, import_smithy_client.map)({}, import_smithy_client.isSerializableHeaderValue, {
    [_xaebo]: input[_EBO]
  });
  b.bp("/");
  b.p("Bucket", () => input.Bucket, "{Bucket}", false);
  const query = (0, import_smithy_client.map)({
    [_mT]: [, ""]
  });
  let body;
  b.m("GET").h(headers).q(query).b(body);
  return b.build();
}, "se_GetBucketMetadataTableConfigurationCommand");
var se_GetBucketMetricsConfigurationCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core2.requestBuilder)(input, context);
  const headers = (0, import_smithy_client.map)({}, import_smithy_client.isSerializableHeaderValue, {
    [_xaebo]: input[_EBO]
  });
  b.bp("/");
  b.p("Bucket", () => input.Bucket, "{Bucket}", false);
  const query = (0, import_smithy_client.map)({
    [_m]: [, ""],
    [_xi]: [, "GetBucketMetricsConfiguration"],
    [_i]: [, (0, import_smithy_client.expectNonNull)(input[_I], `Id`)]
  });
  let body;
  b.m("GET").h(headers).q(query).b(body);
  return b.build();
}, "se_GetBucketMetricsConfigurationCommand");
var se_GetBucketNotificationConfigurationCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core2.requestBuilder)(input, context);
  const headers = (0, import_smithy_client.map)({}, import_smithy_client.isSerializableHeaderValue, {
    [_xaebo]: input[_EBO]
  });
  b.bp("/");
  b.p("Bucket", () => input.Bucket, "{Bucket}", false);
  const query = (0, import_smithy_client.map)({
    [_n]: [, ""]
  });
  let body;
  b.m("GET").h(headers).q(query).b(body);
  return b.build();
}, "se_GetBucketNotificationConfigurationCommand");
var se_GetBucketOwnershipControlsCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core2.requestBuilder)(input, context);
  const headers = (0, import_smithy_client.map)({}, import_smithy_client.isSerializableHeaderValue, {
    [_xaebo]: input[_EBO]
  });
  b.bp("/");
  b.p("Bucket", () => input.Bucket, "{Bucket}", false);
  const query = (0, import_smithy_client.map)({
    [_oC]: [, ""]
  });
  let body;
  b.m("GET").h(headers).q(query).b(body);
  return b.build();
}, "se_GetBucketOwnershipControlsCommand");
var se_GetBucketPolicyCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core2.requestBuilder)(input, context);
  const headers = (0, import_smithy_client.map)({}, import_smithy_client.isSerializableHeaderValue, {
    [_xaebo]: input[_EBO]
  });
  b.bp("/");
  b.p("Bucket", () => input.Bucket, "{Bucket}", false);
  const query = (0, import_smithy_client.map)({
    [_p]: [, ""]
  });
  let body;
  b.m("GET").h(headers).q(query).b(body);
  return b.build();
}, "se_GetBucketPolicyCommand");
var se_GetBucketPolicyStatusCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core2.requestBuilder)(input, context);
  const headers = (0, import_smithy_client.map)({}, import_smithy_client.isSerializableHeaderValue, {
    [_xaebo]: input[_EBO]
  });
  b.bp("/");
  b.p("Bucket", () => input.Bucket, "{Bucket}", false);
  const query = (0, import_smithy_client.map)({
    [_pS]: [, ""]
  });
  let body;
  b.m("GET").h(headers).q(query).b(body);
  return b.build();
}, "se_GetBucketPolicyStatusCommand");
var se_GetBucketReplicationCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core2.requestBuilder)(input, context);
  const headers = (0, import_smithy_client.map)({}, import_smithy_client.isSerializableHeaderValue, {
    [_xaebo]: input[_EBO]
  });
  b.bp("/");
  b.p("Bucket", () => input.Bucket, "{Bucket}", false);
  const query = (0, import_smithy_client.map)({
    [_r]: [, ""]
  });
  let body;
  b.m("GET").h(headers).q(query).b(body);
  return b.build();
}, "se_GetBucketReplicationCommand");
var se_GetBucketRequestPaymentCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core2.requestBuilder)(input, context);
  const headers = (0, import_smithy_client.map)({}, import_smithy_client.isSerializableHeaderValue, {
    [_xaebo]: input[_EBO]
  });
  b.bp("/");
  b.p("Bucket", () => input.Bucket, "{Bucket}", false);
  const query = (0, import_smithy_client.map)({
    [_rP]: [, ""]
  });
  let body;
  b.m("GET").h(headers).q(query).b(body);
  return b.build();
}, "se_GetBucketRequestPaymentCommand");
var se_GetBucketTaggingCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core2.requestBuilder)(input, context);
  const headers = (0, import_smithy_client.map)({}, import_smithy_client.isSerializableHeaderValue, {
    [_xaebo]: input[_EBO]
  });
  b.bp("/");
  b.p("Bucket", () => input.Bucket, "{Bucket}", false);
  const query = (0, import_smithy_client.map)({
    [_t]: [, ""]
  });
  let body;
  b.m("GET").h(headers).q(query).b(body);
  return b.build();
}, "se_GetBucketTaggingCommand");
var se_GetBucketVersioningCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core2.requestBuilder)(input, context);
  const headers = (0, import_smithy_client.map)({}, import_smithy_client.isSerializableHeaderValue, {
    [_xaebo]: input[_EBO]
  });
  b.bp("/");
  b.p("Bucket", () => input.Bucket, "{Bucket}", false);
  const query = (0, import_smithy_client.map)({
    [_v]: [, ""]
  });
  let body;
  b.m("GET").h(headers).q(query).b(body);
  return b.build();
}, "se_GetBucketVersioningCommand");
var se_GetBucketWebsiteCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core2.requestBuilder)(input, context);
  const headers = (0, import_smithy_client.map)({}, import_smithy_client.isSerializableHeaderValue, {
    [_xaebo]: input[_EBO]
  });
  b.bp("/");
  b.p("Bucket", () => input.Bucket, "{Bucket}", false);
  const query = (0, import_smithy_client.map)({
    [_w]: [, ""]
  });
  let body;
  b.m("GET").h(headers).q(query).b(body);
  return b.build();
}, "se_GetBucketWebsiteCommand");
var se_GetObjectCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core2.requestBuilder)(input, context);
  const headers = (0, import_smithy_client.map)({}, import_smithy_client.isSerializableHeaderValue, {
    [_im]: input[_IM],
    [_ims]: [() => (0, import_smithy_client.isSerializableHeaderValue)(input[_IMSf]), () => (0, import_smithy_client.dateToUtcString)(input[_IMSf]).toString()],
    [_inm]: input[_INM],
    [_ius]: [() => (0, import_smithy_client.isSerializableHeaderValue)(input[_IUS]), () => (0, import_smithy_client.dateToUtcString)(input[_IUS]).toString()],
    [_ra]: input[_R],
    [_xasseca]: input[_SSECA],
    [_xasseck]: input[_SSECK],
    [_xasseckm]: input[_SSECKMD],
    [_xarp]: input[_RP],
    [_xaebo]: input[_EBO],
    [_xacm]: input[_CM]
  });
  b.bp("/{Key+}");
  b.p("Bucket", () => input.Bucket, "{Bucket}", false);
  b.p("Key", () => input.Key, "{Key+}", true);
  const query = (0, import_smithy_client.map)({
    [_xi]: [, "GetObject"],
    [_rcc]: [, input[_RCC]],
    [_rcd]: [, input[_RCD]],
    [_rce]: [, input[_RCE]],
    [_rcl]: [, input[_RCL]],
    [_rct]: [, input[_RCT]],
    [_re]: [() => input.ResponseExpires !== void 0, () => (0, import_smithy_client.dateToUtcString)(input[_RE]).toString()],
    [_vI]: [, input[_VI]],
    [_pN]: [() => input.PartNumber !== void 0, () => input[_PN].toString()]
  });
  let body;
  b.m("GET").h(headers).q(query).b(body);
  return b.build();
}, "se_GetObjectCommand");
var se_GetObjectAclCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core2.requestBuilder)(input, context);
  const headers = (0, import_smithy_client.map)({}, import_smithy_client.isSerializableHeaderValue, {
    [_xarp]: input[_RP],
    [_xaebo]: input[_EBO]
  });
  b.bp("/{Key+}");
  b.p("Bucket", () => input.Bucket, "{Bucket}", false);
  b.p("Key", () => input.Key, "{Key+}", true);
  const query = (0, import_smithy_client.map)({
    [_acl]: [, ""],
    [_vI]: [, input[_VI]]
  });
  let body;
  b.m("GET").h(headers).q(query).b(body);
  return b.build();
}, "se_GetObjectAclCommand");
var se_GetObjectAttributesCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core2.requestBuilder)(input, context);
  const headers = (0, import_smithy_client.map)({}, import_smithy_client.isSerializableHeaderValue, {
    [_xamp]: [() => (0, import_smithy_client.isSerializableHeaderValue)(input[_MP]), () => input[_MP].toString()],
    [_xapnm]: input[_PNM],
    [_xasseca]: input[_SSECA],
    [_xasseck]: input[_SSECK],
    [_xasseckm]: input[_SSECKMD],
    [_xarp]: input[_RP],
    [_xaebo]: input[_EBO],
    [_xaoa]: [() => (0, import_smithy_client.isSerializableHeaderValue)(input[_OA]), () => (input[_OA] || []).map(import_smithy_client.quoteHeader).join(", ")]
  });
  b.bp("/{Key+}");
  b.p("Bucket", () => input.Bucket, "{Bucket}", false);
  b.p("Key", () => input.Key, "{Key+}", true);
  const query = (0, import_smithy_client.map)({
    [_at]: [, ""],
    [_vI]: [, input[_VI]]
  });
  let body;
  b.m("GET").h(headers).q(query).b(body);
  return b.build();
}, "se_GetObjectAttributesCommand");
var se_GetObjectLegalHoldCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core2.requestBuilder)(input, context);
  const headers = (0, import_smithy_client.map)({}, import_smithy_client.isSerializableHeaderValue, {
    [_xarp]: input[_RP],
    [_xaebo]: input[_EBO]
  });
  b.bp("/{Key+}");
  b.p("Bucket", () => input.Bucket, "{Bucket}", false);
  b.p("Key", () => input.Key, "{Key+}", true);
  const query = (0, import_smithy_client.map)({
    [_lh]: [, ""],
    [_vI]: [, input[_VI]]
  });
  let body;
  b.m("GET").h(headers).q(query).b(body);
  return b.build();
}, "se_GetObjectLegalHoldCommand");
var se_GetObjectLockConfigurationCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core2.requestBuilder)(input, context);
  const headers = (0, import_smithy_client.map)({}, import_smithy_client.isSerializableHeaderValue, {
    [_xaebo]: input[_EBO]
  });
  b.bp("/");
  b.p("Bucket", () => input.Bucket, "{Bucket}", false);
  const query = (0, import_smithy_client.map)({
    [_ol]: [, ""]
  });
  let body;
  b.m("GET").h(headers).q(query).b(body);
  return b.build();
}, "se_GetObjectLockConfigurationCommand");
var se_GetObjectRetentionCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core2.requestBuilder)(input, context);
  const headers = (0, import_smithy_client.map)({}, import_smithy_client.isSerializableHeaderValue, {
    [_xarp]: input[_RP],
    [_xaebo]: input[_EBO]
  });
  b.bp("/{Key+}");
  b.p("Bucket", () => input.Bucket, "{Bucket}", false);
  b.p("Key", () => input.Key, "{Key+}", true);
  const query = (0, import_smithy_client.map)({
    [_ret]: [, ""],
    [_vI]: [, input[_VI]]
  });
  let body;
  b.m("GET").h(headers).q(query).b(body);
  return b.build();
}, "se_GetObjectRetentionCommand");
var se_GetObjectTaggingCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core2.requestBuilder)(input, context);
  const headers = (0, import_smithy_client.map)({}, import_smithy_client.isSerializableHeaderValue, {
    [_xaebo]: input[_EBO],
    [_xarp]: input[_RP]
  });
  b.bp("/{Key+}");
  b.p("Bucket", () => input.Bucket, "{Bucket}", false);
  b.p("Key", () => input.Key, "{Key+}", true);
  const query = (0, import_smithy_client.map)({
    [_t]: [, ""],
    [_vI]: [, input[_VI]]
  });
  let body;
  b.m("GET").h(headers).q(query).b(body);
  return b.build();
}, "se_GetObjectTaggingCommand");
var se_GetObjectTorrentCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core2.requestBuilder)(input, context);
  const headers = (0, import_smithy_client.map)({}, import_smithy_client.isSerializableHeaderValue, {
    [_xarp]: input[_RP],
    [_xaebo]: input[_EBO]
  });
  b.bp("/{Key+}");
  b.p("Bucket", () => input.Bucket, "{Bucket}", false);
  b.p("Key", () => input.Key, "{Key+}", true);
  const query = (0, import_smithy_client.map)({
    [_to]: [, ""]
  });
  let body;
  b.m("GET").h(headers).q(query).b(body);
  return b.build();
}, "se_GetObjectTorrentCommand");
var se_GetPublicAccessBlockCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core2.requestBuilder)(input, context);
  const headers = (0, import_smithy_client.map)({}, import_smithy_client.isSerializableHeaderValue, {
    [_xaebo]: input[_EBO]
  });
  b.bp("/");
  b.p("Bucket", () => input.Bucket, "{Bucket}", false);
  const query = (0, import_smithy_client.map)({
    [_pAB]: [, ""]
  });
  let body;
  b.m("GET").h(headers).q(query).b(body);
  return b.build();
}, "se_GetPublicAccessBlockCommand");
var se_HeadBucketCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core2.requestBuilder)(input, context);
  const headers = (0, import_smithy_client.map)({}, import_smithy_client.isSerializableHeaderValue, {
    [_xaebo]: input[_EBO]
  });
  b.bp("/");
  b.p("Bucket", () => input.Bucket, "{Bucket}", false);
  let body;
  b.m("HEAD").h(headers).b(body);
  return b.build();
}, "se_HeadBucketCommand");
var se_HeadObjectCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core2.requestBuilder)(input, context);
  const headers = (0, import_smithy_client.map)({}, import_smithy_client.isSerializableHeaderValue, {
    [_im]: input[_IM],
    [_ims]: [() => (0, import_smithy_client.isSerializableHeaderValue)(input[_IMSf]), () => (0, import_smithy_client.dateToUtcString)(input[_IMSf]).toString()],
    [_inm]: input[_INM],
    [_ius]: [() => (0, import_smithy_client.isSerializableHeaderValue)(input[_IUS]), () => (0, import_smithy_client.dateToUtcString)(input[_IUS]).toString()],
    [_ra]: input[_R],
    [_xasseca]: input[_SSECA],
    [_xasseck]: input[_SSECK],
    [_xasseckm]: input[_SSECKMD],
    [_xarp]: input[_RP],
    [_xaebo]: input[_EBO],
    [_xacm]: input[_CM]
  });
  b.bp("/{Key+}");
  b.p("Bucket", () => input.Bucket, "{Bucket}", false);
  b.p("Key", () => input.Key, "{Key+}", true);
  const query = (0, import_smithy_client.map)({
    [_rcc]: [, input[_RCC]],
    [_rcd]: [, input[_RCD]],
    [_rce]: [, input[_RCE]],
    [_rcl]: [, input[_RCL]],
    [_rct]: [, input[_RCT]],
    [_re]: [() => input.ResponseExpires !== void 0, () => (0, import_smithy_client.dateToUtcString)(input[_RE]).toString()],
    [_vI]: [, input[_VI]],
    [_pN]: [() => input.PartNumber !== void 0, () => input[_PN].toString()]
  });
  let body;
  b.m("HEAD").h(headers).q(query).b(body);
  return b.build();
}, "se_HeadObjectCommand");
var se_ListBucketAnalyticsConfigurationsCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core2.requestBuilder)(input, context);
  const headers = (0, import_smithy_client.map)({}, import_smithy_client.isSerializableHeaderValue, {
    [_xaebo]: input[_EBO]
  });
  b.bp("/");
  b.p("Bucket", () => input.Bucket, "{Bucket}", false);
  const query = (0, import_smithy_client.map)({
    [_a]: [, ""],
    [_xi]: [, "ListBucketAnalyticsConfigurations"],
    [_ct_]: [, input[_CTon]]
  });
  let body;
  b.m("GET").h(headers).q(query).b(body);
  return b.build();
}, "se_ListBucketAnalyticsConfigurationsCommand");
var se_ListBucketIntelligentTieringConfigurationsCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core2.requestBuilder)(input, context);
  const headers = (0, import_smithy_client.map)({}, import_smithy_client.isSerializableHeaderValue, {
    [_xaebo]: input[_EBO]
  });
  b.bp("/");
  b.p("Bucket", () => input.Bucket, "{Bucket}", false);
  const query = (0, import_smithy_client.map)({
    [_it]: [, ""],
    [_xi]: [, "ListBucketIntelligentTieringConfigurations"],
    [_ct_]: [, input[_CTon]]
  });
  let body;
  b.m("GET").h(headers).q(query).b(body);
  return b.build();
}, "se_ListBucketIntelligentTieringConfigurationsCommand");
var se_ListBucketInventoryConfigurationsCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core2.requestBuilder)(input, context);
  const headers = (0, import_smithy_client.map)({}, import_smithy_client.isSerializableHeaderValue, {
    [_xaebo]: input[_EBO]
  });
  b.bp("/");
  b.p("Bucket", () => input.Bucket, "{Bucket}", false);
  const query = (0, import_smithy_client.map)({
    [_in]: [, ""],
    [_xi]: [, "ListBucketInventoryConfigurations"],
    [_ct_]: [, input[_CTon]]
  });
  let body;
  b.m("GET").h(headers).q(query).b(body);
  return b.build();
}, "se_ListBucketInventoryConfigurationsCommand");
var se_ListBucketMetricsConfigurationsCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core2.requestBuilder)(input, context);
  const headers = (0, import_smithy_client.map)({}, import_smithy_client.isSerializableHeaderValue, {
    [_xaebo]: input[_EBO]
  });
  b.bp("/");
  b.p("Bucket", () => input.Bucket, "{Bucket}", false);
  const query = (0, import_smithy_client.map)({
    [_m]: [, ""],
    [_xi]: [, "ListBucketMetricsConfigurations"],
    [_ct_]: [, input[_CTon]]
  });
  let body;
  b.m("GET").h(headers).q(query).b(body);
  return b.build();
}, "se_ListBucketMetricsConfigurationsCommand");
var se_ListBucketsCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core2.requestBuilder)(input, context);
  const headers = {};
  b.bp("/");
  const query = (0, import_smithy_client.map)({
    [_xi]: [, "ListBuckets"],
    [_mb]: [() => input.MaxBuckets !== void 0, () => input[_MB].toString()],
    [_ct_]: [, input[_CTon]],
    [_pr]: [, input[_P]],
    [_br]: [, input[_BR]]
  });
  let body;
  b.m("GET").h(headers).q(query).b(body);
  return b.build();
}, "se_ListBucketsCommand");
var se_ListDirectoryBucketsCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core2.requestBuilder)(input, context);
  const headers = {};
  b.bp("/");
  const query = (0, import_smithy_client.map)({
    [_xi]: [, "ListDirectoryBuckets"],
    [_ct_]: [, input[_CTon]],
    [_mdb]: [() => input.MaxDirectoryBuckets !== void 0, () => input[_MDB].toString()]
  });
  let body;
  b.m("GET").h(headers).q(query).b(body);
  return b.build();
}, "se_ListDirectoryBucketsCommand");
var se_ListMultipartUploadsCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core2.requestBuilder)(input, context);
  const headers = (0, import_smithy_client.map)({}, import_smithy_client.isSerializableHeaderValue, {
    [_xaebo]: input[_EBO],
    [_xarp]: input[_RP]
  });
  b.bp("/");
  b.p("Bucket", () => input.Bucket, "{Bucket}", false);
  const query = (0, import_smithy_client.map)({
    [_u]: [, ""],
    [_de]: [, input[_D]],
    [_et]: [, input[_ET]],
    [_km]: [, input[_KM]],
    [_mu]: [() => input.MaxUploads !== void 0, () => input[_MU].toString()],
    [_pr]: [, input[_P]],
    [_uim]: [, input[_UIM]]
  });
  let body;
  b.m("GET").h(headers).q(query).b(body);
  return b.build();
}, "se_ListMultipartUploadsCommand");
var se_ListObjectsCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core2.requestBuilder)(input, context);
  const headers = (0, import_smithy_client.map)({}, import_smithy_client.isSerializableHeaderValue, {
    [_xarp]: input[_RP],
    [_xaebo]: input[_EBO],
    [_xaooa]: [() => (0, import_smithy_client.isSerializableHeaderValue)(input[_OOA]), () => (input[_OOA] || []).map(import_smithy_client.quoteHeader).join(", ")]
  });
  b.bp("/");
  b.p("Bucket", () => input.Bucket, "{Bucket}", false);
  const query = (0, import_smithy_client.map)({
    [_de]: [, input[_D]],
    [_et]: [, input[_ET]],
    [_ma]: [, input[_M]],
    [_mk]: [() => input.MaxKeys !== void 0, () => input[_MK].toString()],
    [_pr]: [, input[_P]]
  });
  let body;
  b.m("GET").h(headers).q(query).b(body);
  return b.build();
}, "se_ListObjectsCommand");
var se_ListObjectsV2Command = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core2.requestBuilder)(input, context);
  const headers = (0, import_smithy_client.map)({}, import_smithy_client.isSerializableHeaderValue, {
    [_xarp]: input[_RP],
    [_xaebo]: input[_EBO],
    [_xaooa]: [() => (0, import_smithy_client.isSerializableHeaderValue)(input[_OOA]), () => (input[_OOA] || []).map(import_smithy_client.quoteHeader).join(", ")]
  });
  b.bp("/");
  b.p("Bucket", () => input.Bucket, "{Bucket}", false);
  const query = (0, import_smithy_client.map)({
    [_lt]: [, "2"],
    [_de]: [, input[_D]],
    [_et]: [, input[_ET]],
    [_mk]: [() => input.MaxKeys !== void 0, () => input[_MK].toString()],
    [_pr]: [, input[_P]],
    [_ct_]: [, input[_CTon]],
    [_fo]: [() => input.FetchOwner !== void 0, () => input[_FO].toString()],
    [_sa]: [, input[_SA]]
  });
  let body;
  b.m("GET").h(headers).q(query).b(body);
  return b.build();
}, "se_ListObjectsV2Command");
var se_ListObjectVersionsCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core2.requestBuilder)(input, context);
  const headers = (0, import_smithy_client.map)({}, import_smithy_client.isSerializableHeaderValue, {
    [_xaebo]: input[_EBO],
    [_xarp]: input[_RP],
    [_xaooa]: [() => (0, import_smithy_client.isSerializableHeaderValue)(input[_OOA]), () => (input[_OOA] || []).map(import_smithy_client.quoteHeader).join(", ")]
  });
  b.bp("/");
  b.p("Bucket", () => input.Bucket, "{Bucket}", false);
  const query = (0, import_smithy_client.map)({
    [_ver]: [, ""],
    [_de]: [, input[_D]],
    [_et]: [, input[_ET]],
    [_km]: [, input[_KM]],
    [_mk]: [() => input.MaxKeys !== void 0, () => input[_MK].toString()],
    [_pr]: [, input[_P]],
    [_vim]: [, input[_VIM]]
  });
  let body;
  b.m("GET").h(headers).q(query).b(body);
  return b.build();
}, "se_ListObjectVersionsCommand");
var se_ListPartsCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core2.requestBuilder)(input, context);
  const headers = (0, import_smithy_client.map)({}, import_smithy_client.isSerializableHeaderValue, {
    [_xarp]: input[_RP],
    [_xaebo]: input[_EBO],
    [_xasseca]: input[_SSECA],
    [_xasseck]: input[_SSECK],
    [_xasseckm]: input[_SSECKMD]
  });
  b.bp("/{Key+}");
  b.p("Bucket", () => input.Bucket, "{Bucket}", false);
  b.p("Key", () => input.Key, "{Key+}", true);
  const query = (0, import_smithy_client.map)({
    [_xi]: [, "ListParts"],
    [_mp]: [() => input.MaxParts !== void 0, () => input[_MP].toString()],
    [_pnm]: [, input[_PNM]],
    [_uI]: [, (0, import_smithy_client.expectNonNull)(input[_UI], `UploadId`)]
  });
  let body;
  b.m("GET").h(headers).q(query).b(body);
  return b.build();
}, "se_ListPartsCommand");
var se_PutBucketAccelerateConfigurationCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core2.requestBuilder)(input, context);
  const headers = (0, import_smithy_client.map)({}, import_smithy_client.isSerializableHeaderValue, {
    "content-type": "application/xml",
    [_xaebo]: input[_EBO],
    [_xasca]: input[_CA]
  });
  b.bp("/");
  b.p("Bucket", () => input.Bucket, "{Bucket}", false);
  const query = (0, import_smithy_client.map)({
    [_ac]: [, ""]
  });
  let body;
  let contents;
  if (input.AccelerateConfiguration !== void 0) {
    contents = se_AccelerateConfiguration(input.AccelerateConfiguration, context);
    body = _ve;
    contents.a("xmlns", "http://s3.amazonaws.com/doc/2006-03-01/");
    body += contents.toString();
  }
  b.m("PUT").h(headers).q(query).b(body);
  return b.build();
}, "se_PutBucketAccelerateConfigurationCommand");
var se_PutBucketAclCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core2.requestBuilder)(input, context);
  const headers = (0, import_smithy_client.map)({}, import_smithy_client.isSerializableHeaderValue, {
    "content-type": "application/xml",
    [_xaa]: input[_ACL],
    [_cm]: input[_CMD],
    [_xasca]: input[_CA],
    [_xagfc]: input[_GFC],
    [_xagr]: input[_GR],
    [_xagra]: input[_GRACP],
    [_xagw]: input[_GW],
    [_xagwa]: input[_GWACP],
    [_xaebo]: input[_EBO]
  });
  b.bp("/");
  b.p("Bucket", () => input.Bucket, "{Bucket}", false);
  const query = (0, import_smithy_client.map)({
    [_acl]: [, ""]
  });
  let body;
  let contents;
  if (input.AccessControlPolicy !== void 0) {
    contents = se_AccessControlPolicy(input.AccessControlPolicy, context);
    body = _ve;
    contents.a("xmlns", "http://s3.amazonaws.com/doc/2006-03-01/");
    body += contents.toString();
  }
  b.m("PUT").h(headers).q(query).b(body);
  return b.build();
}, "se_PutBucketAclCommand");
var se_PutBucketAnalyticsConfigurationCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core2.requestBuilder)(input, context);
  const headers = (0, import_smithy_client.map)({}, import_smithy_client.isSerializableHeaderValue, {
    "content-type": "application/xml",
    [_xaebo]: input[_EBO]
  });
  b.bp("/");
  b.p("Bucket", () => input.Bucket, "{Bucket}", false);
  const query = (0, import_smithy_client.map)({
    [_a]: [, ""],
    [_i]: [, (0, import_smithy_client.expectNonNull)(input[_I], `Id`)]
  });
  let body;
  let contents;
  if (input.AnalyticsConfiguration !== void 0) {
    contents = se_AnalyticsConfiguration(input.AnalyticsConfiguration, context);
    body = _ve;
    contents.a("xmlns", "http://s3.amazonaws.com/doc/2006-03-01/");
    body += contents.toString();
  }
  b.m("PUT").h(headers).q(query).b(body);
  return b.build();
}, "se_PutBucketAnalyticsConfigurationCommand");
var se_PutBucketCorsCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core2.requestBuilder)(input, context);
  const headers = (0, import_smithy_client.map)({}, import_smithy_client.isSerializableHeaderValue, {
    "content-type": "application/xml",
    [_cm]: input[_CMD],
    [_xasca]: input[_CA],
    [_xaebo]: input[_EBO]
  });
  b.bp("/");
  b.p("Bucket", () => input.Bucket, "{Bucket}", false);
  const query = (0, import_smithy_client.map)({
    [_c]: [, ""]
  });
  let body;
  let contents;
  if (input.CORSConfiguration !== void 0) {
    contents = se_CORSConfiguration(input.CORSConfiguration, context);
    body = _ve;
    contents.a("xmlns", "http://s3.amazonaws.com/doc/2006-03-01/");
    body += contents.toString();
  }
  b.m("PUT").h(headers).q(query).b(body);
  return b.build();
}, "se_PutBucketCorsCommand");
var se_PutBucketEncryptionCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core2.requestBuilder)(input, context);
  const headers = (0, import_smithy_client.map)({}, import_smithy_client.isSerializableHeaderValue, {
    "content-type": "application/xml",
    [_cm]: input[_CMD],
    [_xasca]: input[_CA],
    [_xaebo]: input[_EBO]
  });
  b.bp("/");
  b.p("Bucket", () => input.Bucket, "{Bucket}", false);
  const query = (0, import_smithy_client.map)({
    [_en]: [, ""]
  });
  let body;
  let contents;
  if (input.ServerSideEncryptionConfiguration !== void 0) {
    contents = se_ServerSideEncryptionConfiguration(input.ServerSideEncryptionConfiguration, context);
    body = _ve;
    contents.a("xmlns", "http://s3.amazonaws.com/doc/2006-03-01/");
    body += contents.toString();
  }
  b.m("PUT").h(headers).q(query).b(body);
  return b.build();
}, "se_PutBucketEncryptionCommand");
var se_PutBucketIntelligentTieringConfigurationCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core2.requestBuilder)(input, context);
  const headers = (0, import_smithy_client.map)({}, import_smithy_client.isSerializableHeaderValue, {
    "content-type": "application/xml",
    [_xaebo]: input[_EBO]
  });
  b.bp("/");
  b.p("Bucket", () => input.Bucket, "{Bucket}", false);
  const query = (0, import_smithy_client.map)({
    [_it]: [, ""],
    [_i]: [, (0, import_smithy_client.expectNonNull)(input[_I], `Id`)]
  });
  let body;
  let contents;
  if (input.IntelligentTieringConfiguration !== void 0) {
    contents = se_IntelligentTieringConfiguration(input.IntelligentTieringConfiguration, context);
    body = _ve;
    contents.a("xmlns", "http://s3.amazonaws.com/doc/2006-03-01/");
    body += contents.toString();
  }
  b.m("PUT").h(headers).q(query).b(body);
  return b.build();
}, "se_PutBucketIntelligentTieringConfigurationCommand");
var se_PutBucketInventoryConfigurationCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core2.requestBuilder)(input, context);
  const headers = (0, import_smithy_client.map)({}, import_smithy_client.isSerializableHeaderValue, {
    "content-type": "application/xml",
    [_xaebo]: input[_EBO]
  });
  b.bp("/");
  b.p("Bucket", () => input.Bucket, "{Bucket}", false);
  const query = (0, import_smithy_client.map)({
    [_in]: [, ""],
    [_i]: [, (0, import_smithy_client.expectNonNull)(input[_I], `Id`)]
  });
  let body;
  let contents;
  if (input.InventoryConfiguration !== void 0) {
    contents = se_InventoryConfiguration(input.InventoryConfiguration, context);
    body = _ve;
    contents.a("xmlns", "http://s3.amazonaws.com/doc/2006-03-01/");
    body += contents.toString();
  }
  b.m("PUT").h(headers).q(query).b(body);
  return b.build();
}, "se_PutBucketInventoryConfigurationCommand");
var se_PutBucketLifecycleConfigurationCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core2.requestBuilder)(input, context);
  const headers = (0, import_smithy_client.map)({}, import_smithy_client.isSerializableHeaderValue, {
    "content-type": "application/xml",
    [_xasca]: input[_CA],
    [_xaebo]: input[_EBO],
    [_xatdmos]: input[_TDMOS]
  });
  b.bp("/");
  b.p("Bucket", () => input.Bucket, "{Bucket}", false);
  const query = (0, import_smithy_client.map)({
    [_l]: [, ""]
  });
  let body;
  let contents;
  if (input.LifecycleConfiguration !== void 0) {
    contents = se_BucketLifecycleConfiguration(input.LifecycleConfiguration, context);
    contents = contents.n("LifecycleConfiguration");
    body = _ve;
    contents.a("xmlns", "http://s3.amazonaws.com/doc/2006-03-01/");
    body += contents.toString();
  }
  b.m("PUT").h(headers).q(query).b(body);
  return b.build();
}, "se_PutBucketLifecycleConfigurationCommand");
var se_PutBucketLoggingCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core2.requestBuilder)(input, context);
  const headers = (0, import_smithy_client.map)({}, import_smithy_client.isSerializableHeaderValue, {
    "content-type": "application/xml",
    [_cm]: input[_CMD],
    [_xasca]: input[_CA],
    [_xaebo]: input[_EBO]
  });
  b.bp("/");
  b.p("Bucket", () => input.Bucket, "{Bucket}", false);
  const query = (0, import_smithy_client.map)({
    [_log]: [, ""]
  });
  let body;
  let contents;
  if (input.BucketLoggingStatus !== void 0) {
    contents = se_BucketLoggingStatus(input.BucketLoggingStatus, context);
    body = _ve;
    contents.a("xmlns", "http://s3.amazonaws.com/doc/2006-03-01/");
    body += contents.toString();
  }
  b.m("PUT").h(headers).q(query).b(body);
  return b.build();
}, "se_PutBucketLoggingCommand");
var se_PutBucketMetricsConfigurationCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core2.requestBuilder)(input, context);
  const headers = (0, import_smithy_client.map)({}, import_smithy_client.isSerializableHeaderValue, {
    "content-type": "application/xml",
    [_xaebo]: input[_EBO]
  });
  b.bp("/");
  b.p("Bucket", () => input.Bucket, "{Bucket}", false);
  const query = (0, import_smithy_client.map)({
    [_m]: [, ""],
    [_i]: [, (0, import_smithy_client.expectNonNull)(input[_I], `Id`)]
  });
  let body;
  let contents;
  if (input.MetricsConfiguration !== void 0) {
    contents = se_MetricsConfiguration(input.MetricsConfiguration, context);
    body = _ve;
    contents.a("xmlns", "http://s3.amazonaws.com/doc/2006-03-01/");
    body += contents.toString();
  }
  b.m("PUT").h(headers).q(query).b(body);
  return b.build();
}, "se_PutBucketMetricsConfigurationCommand");
var se_PutBucketNotificationConfigurationCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core2.requestBuilder)(input, context);
  const headers = (0, import_smithy_client.map)({}, import_smithy_client.isSerializableHeaderValue, {
    "content-type": "application/xml",
    [_xaebo]: input[_EBO],
    [_xasdv]: [() => (0, import_smithy_client.isSerializableHeaderValue)(input[_SDV]), () => input[_SDV].toString()]
  });
  b.bp("/");
  b.p("Bucket", () => input.Bucket, "{Bucket}", false);
  const query = (0, import_smithy_client.map)({
    [_n]: [, ""]
  });
  let body;
  let contents;
  if (input.NotificationConfiguration !== void 0) {
    contents = se_NotificationConfiguration(input.NotificationConfiguration, context);
    body = _ve;
    contents.a("xmlns", "http://s3.amazonaws.com/doc/2006-03-01/");
    body += contents.toString();
  }
  b.m("PUT").h(headers).q(query).b(body);
  return b.build();
}, "se_PutBucketNotificationConfigurationCommand");
var se_PutBucketOwnershipControlsCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core2.requestBuilder)(input, context);
  const headers = (0, import_smithy_client.map)({}, import_smithy_client.isSerializableHeaderValue, {
    "content-type": "application/xml",
    [_cm]: input[_CMD],
    [_xaebo]: input[_EBO],
    [_xasca]: input[_CA]
  });
  b.bp("/");
  b.p("Bucket", () => input.Bucket, "{Bucket}", false);
  const query = (0, import_smithy_client.map)({
    [_oC]: [, ""]
  });
  let body;
  let contents;
  if (input.OwnershipControls !== void 0) {
    contents = se_OwnershipControls(input.OwnershipControls, context);
    body = _ve;
    contents.a("xmlns", "http://s3.amazonaws.com/doc/2006-03-01/");
    body += contents.toString();
  }
  b.m("PUT").h(headers).q(query).b(body);
  return b.build();
}, "se_PutBucketOwnershipControlsCommand");
var se_PutBucketPolicyCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core2.requestBuilder)(input, context);
  const headers = (0, import_smithy_client.map)({}, import_smithy_client.isSerializableHeaderValue, {
    "content-type": "text/plain",
    [_cm]: input[_CMD],
    [_xasca]: input[_CA],
    [_xacrsba]: [() => (0, import_smithy_client.isSerializableHeaderValue)(input[_CRSBA]), () => input[_CRSBA].toString()],
    [_xaebo]: input[_EBO]
  });
  b.bp("/");
  b.p("Bucket", () => input.Bucket, "{Bucket}", false);
  const query = (0, import_smithy_client.map)({
    [_p]: [, ""]
  });
  let body;
  let contents;
  if (input.Policy !== void 0) {
    contents = input.Policy;
    body = contents;
  }
  b.m("PUT").h(headers).q(query).b(body);
  return b.build();
}, "se_PutBucketPolicyCommand");
var se_PutBucketReplicationCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core2.requestBuilder)(input, context);
  const headers = (0, import_smithy_client.map)({}, import_smithy_client.isSerializableHeaderValue, {
    "content-type": "application/xml",
    [_cm]: input[_CMD],
    [_xasca]: input[_CA],
    [_xabolt]: input[_To],
    [_xaebo]: input[_EBO]
  });
  b.bp("/");
  b.p("Bucket", () => input.Bucket, "{Bucket}", false);
  const query = (0, import_smithy_client.map)({
    [_r]: [, ""]
  });
  let body;
  let contents;
  if (input.ReplicationConfiguration !== void 0) {
    contents = se_ReplicationConfiguration(input.ReplicationConfiguration, context);
    body = _ve;
    contents.a("xmlns", "http://s3.amazonaws.com/doc/2006-03-01/");
    body += contents.toString();
  }
  b.m("PUT").h(headers).q(query).b(body);
  return b.build();
}, "se_PutBucketReplicationCommand");
var se_PutBucketRequestPaymentCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core2.requestBuilder)(input, context);
  const headers = (0, import_smithy_client.map)({}, import_smithy_client.isSerializableHeaderValue, {
    "content-type": "application/xml",
    [_cm]: input[_CMD],
    [_xasca]: input[_CA],
    [_xaebo]: input[_EBO]
  });
  b.bp("/");
  b.p("Bucket", () => input.Bucket, "{Bucket}", false);
  const query = (0, import_smithy_client.map)({
    [_rP]: [, ""]
  });
  let body;
  let contents;
  if (input.RequestPaymentConfiguration !== void 0) {
    contents = se_RequestPaymentConfiguration(input.RequestPaymentConfiguration, context);
    body = _ve;
    contents.a("xmlns", "http://s3.amazonaws.com/doc/2006-03-01/");
    body += contents.toString();
  }
  b.m("PUT").h(headers).q(query).b(body);
  return b.build();
}, "se_PutBucketRequestPaymentCommand");
var se_PutBucketTaggingCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core2.requestBuilder)(input, context);
  const headers = (0, import_smithy_client.map)({}, import_smithy_client.isSerializableHeaderValue, {
    "content-type": "application/xml",
    [_cm]: input[_CMD],
    [_xasca]: input[_CA],
    [_xaebo]: input[_EBO]
  });
  b.bp("/");
  b.p("Bucket", () => input.Bucket, "{Bucket}", false);
  const query = (0, import_smithy_client.map)({
    [_t]: [, ""]
  });
  let body;
  let contents;
  if (input.Tagging !== void 0) {
    contents = se_Tagging(input.Tagging, context);
    body = _ve;
    contents.a("xmlns", "http://s3.amazonaws.com/doc/2006-03-01/");
    body += contents.toString();
  }
  b.m("PUT").h(headers).q(query).b(body);
  return b.build();
}, "se_PutBucketTaggingCommand");
var se_PutBucketVersioningCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core2.requestBuilder)(input, context);
  const headers = (0, import_smithy_client.map)({}, import_smithy_client.isSerializableHeaderValue, {
    "content-type": "application/xml",
    [_cm]: input[_CMD],
    [_xasca]: input[_CA],
    [_xam]: input[_MFA],
    [_xaebo]: input[_EBO]
  });
  b.bp("/");
  b.p("Bucket", () => input.Bucket, "{Bucket}", false);
  const query = (0, import_smithy_client.map)({
    [_v]: [, ""]
  });
  let body;
  let contents;
  if (input.VersioningConfiguration !== void 0) {
    contents = se_VersioningConfiguration(input.VersioningConfiguration, context);
    body = _ve;
    contents.a("xmlns", "http://s3.amazonaws.com/doc/2006-03-01/");
    body += contents.toString();
  }
  b.m("PUT").h(headers).q(query).b(body);
  return b.build();
}, "se_PutBucketVersioningCommand");
var se_PutBucketWebsiteCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core2.requestBuilder)(input, context);
  const headers = (0, import_smithy_client.map)({}, import_smithy_client.isSerializableHeaderValue, {
    "content-type": "application/xml",
    [_cm]: input[_CMD],
    [_xasca]: input[_CA],
    [_xaebo]: input[_EBO]
  });
  b.bp("/");
  b.p("Bucket", () => input.Bucket, "{Bucket}", false);
  const query = (0, import_smithy_client.map)({
    [_w]: [, ""]
  });
  let body;
  let contents;
  if (input.WebsiteConfiguration !== void 0) {
    contents = se_WebsiteConfiguration(input.WebsiteConfiguration, context);
    body = _ve;
    contents.a("xmlns", "http://s3.amazonaws.com/doc/2006-03-01/");
    body += contents.toString();
  }
  b.m("PUT").h(headers).q(query).b(body);
  return b.build();
}, "se_PutBucketWebsiteCommand");
var se_PutObjectCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core2.requestBuilder)(input, context);
  const headers = (0, import_smithy_client.map)({}, import_smithy_client.isSerializableHeaderValue, {
    ...input.Metadata !== void 0 && Object.keys(input.Metadata).reduce((acc, suffix) => {
      acc[`x-amz-meta-${suffix.toLowerCase()}`] = input.Metadata[suffix];
      return acc;
    }, {}),
    [_ct]: input[_CTo] || "application/octet-stream",
    [_xaa]: input[_ACL],
    [_cc]: input[_CC],
    [_cd]: input[_CD],
    [_ce]: input[_CE],
    [_cl]: input[_CL],
    [_cl_]: [() => (0, import_smithy_client.isSerializableHeaderValue)(input[_CLo]), () => input[_CLo].toString()],
    [_cm]: input[_CMD],
    [_xasca]: input[_CA],
    [_xacc]: input[_CCRC],
    [_xacc_]: input[_CCRCC],
    [_xacc__]: input[_CCRCNVME],
    [_xacs]: input[_CSHA],
    [_xacs_]: input[_CSHAh],
    [_e]: [() => (0, import_smithy_client.isSerializableHeaderValue)(input[_E]), () => (0, import_smithy_client.dateToUtcString)(input[_E]).toString()],
    [_im]: input[_IM],
    [_inm]: input[_INM],
    [_xagfc]: input[_GFC],
    [_xagr]: input[_GR],
    [_xagra]: input[_GRACP],
    [_xagwa]: input[_GWACP],
    [_xawob]: [() => (0, import_smithy_client.isSerializableHeaderValue)(input[_WOB]), () => input[_WOB].toString()],
    [_xasse]: input[_SSE],
    [_xasc]: input[_SC],
    [_xawrl]: input[_WRL],
    [_xasseca]: input[_SSECA],
    [_xasseck]: input[_SSECK],
    [_xasseckm]: input[_SSECKMD],
    [_xasseakki]: input[_SSEKMSKI],
    [_xassec]: input[_SSEKMSEC],
    [_xassebke]: [() => (0, import_smithy_client.isSerializableHeaderValue)(input[_BKE]), () => input[_BKE].toString()],
    [_xarp]: input[_RP],
    [_xat]: input[_T],
    [_xaolm]: input[_OLM],
    [_xaolrud]: [() => (0, import_smithy_client.isSerializableHeaderValue)(input[_OLRUD]), () => (0, import_smithy_client.serializeDateTime)(input[_OLRUD]).toString()],
    [_xaollh]: input[_OLLHS],
    [_xaebo]: input[_EBO]
  });
  b.bp("/{Key+}");
  b.p("Bucket", () => input.Bucket, "{Bucket}", false);
  b.p("Key", () => input.Key, "{Key+}", true);
  const query = (0, import_smithy_client.map)({
    [_xi]: [, "PutObject"]
  });
  let body;
  let contents;
  if (input.Body !== void 0) {
    contents = input.Body;
    body = contents;
  }
  b.m("PUT").h(headers).q(query).b(body);
  return b.build();
}, "se_PutObjectCommand");
var se_PutObjectAclCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core2.requestBuilder)(input, context);
  const headers = (0, import_smithy_client.map)({}, import_smithy_client.isSerializableHeaderValue, {
    "content-type": "application/xml",
    [_xaa]: input[_ACL],
    [_cm]: input[_CMD],
    [_xasca]: input[_CA],
    [_xagfc]: input[_GFC],
    [_xagr]: input[_GR],
    [_xagra]: input[_GRACP],
    [_xagw]: input[_GW],
    [_xagwa]: input[_GWACP],
    [_xarp]: input[_RP],
    [_xaebo]: input[_EBO]
  });
  b.bp("/{Key+}");
  b.p("Bucket", () => input.Bucket, "{Bucket}", false);
  b.p("Key", () => input.Key, "{Key+}", true);
  const query = (0, import_smithy_client.map)({
    [_acl]: [, ""],
    [_vI]: [, input[_VI]]
  });
  let body;
  let contents;
  if (input.AccessControlPolicy !== void 0) {
    contents = se_AccessControlPolicy(input.AccessControlPolicy, context);
    body = _ve;
    contents.a("xmlns", "http://s3.amazonaws.com/doc/2006-03-01/");
    body += contents.toString();
  }
  b.m("PUT").h(headers).q(query).b(body);
  return b.build();
}, "se_PutObjectAclCommand");
var se_PutObjectLegalHoldCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core2.requestBuilder)(input, context);
  const headers = (0, import_smithy_client.map)({}, import_smithy_client.isSerializableHeaderValue, {
    "content-type": "application/xml",
    [_xarp]: input[_RP],
    [_cm]: input[_CMD],
    [_xasca]: input[_CA],
    [_xaebo]: input[_EBO]
  });
  b.bp("/{Key+}");
  b.p("Bucket", () => input.Bucket, "{Bucket}", false);
  b.p("Key", () => input.Key, "{Key+}", true);
  const query = (0, import_smithy_client.map)({
    [_lh]: [, ""],
    [_vI]: [, input[_VI]]
  });
  let body;
  let contents;
  if (input.LegalHold !== void 0) {
    contents = se_ObjectLockLegalHold(input.LegalHold, context);
    contents = contents.n("LegalHold");
    body = _ve;
    contents.a("xmlns", "http://s3.amazonaws.com/doc/2006-03-01/");
    body += contents.toString();
  }
  b.m("PUT").h(headers).q(query).b(body);
  return b.build();
}, "se_PutObjectLegalHoldCommand");
var se_PutObjectLockConfigurationCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core2.requestBuilder)(input, context);
  const headers = (0, import_smithy_client.map)({}, import_smithy_client.isSerializableHeaderValue, {
    "content-type": "application/xml",
    [_xarp]: input[_RP],
    [_xabolt]: input[_To],
    [_cm]: input[_CMD],
    [_xasca]: input[_CA],
    [_xaebo]: input[_EBO]
  });
  b.bp("/");
  b.p("Bucket", () => input.Bucket, "{Bucket}", false);
  const query = (0, import_smithy_client.map)({
    [_ol]: [, ""]
  });
  let body;
  let contents;
  if (input.ObjectLockConfiguration !== void 0) {
    contents = se_ObjectLockConfiguration(input.ObjectLockConfiguration, context);
    body = _ve;
    contents.a("xmlns", "http://s3.amazonaws.com/doc/2006-03-01/");
    body += contents.toString();
  }
  b.m("PUT").h(headers).q(query).b(body);
  return b.build();
}, "se_PutObjectLockConfigurationCommand");
var se_PutObjectRetentionCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core2.requestBuilder)(input, context);
  const headers = (0, import_smithy_client.map)({}, import_smithy_client.isSerializableHeaderValue, {
    "content-type": "application/xml",
    [_xarp]: input[_RP],
    [_xabgr]: [() => (0, import_smithy_client.isSerializableHeaderValue)(input[_BGR]), () => input[_BGR].toString()],
    [_cm]: input[_CMD],
    [_xasca]: input[_CA],
    [_xaebo]: input[_EBO]
  });
  b.bp("/{Key+}");
  b.p("Bucket", () => input.Bucket, "{Bucket}", false);
  b.p("Key", () => input.Key, "{Key+}", true);
  const query = (0, import_smithy_client.map)({
    [_ret]: [, ""],
    [_vI]: [, input[_VI]]
  });
  let body;
  let contents;
  if (input.Retention !== void 0) {
    contents = se_ObjectLockRetention(input.Retention, context);
    contents = contents.n("Retention");
    body = _ve;
    contents.a("xmlns", "http://s3.amazonaws.com/doc/2006-03-01/");
    body += contents.toString();
  }
  b.m("PUT").h(headers).q(query).b(body);
  return b.build();
}, "se_PutObjectRetentionCommand");
var se_PutObjectTaggingCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core2.requestBuilder)(input, context);
  const headers = (0, import_smithy_client.map)({}, import_smithy_client.isSerializableHeaderValue, {
    "content-type": "application/xml",
    [_cm]: input[_CMD],
    [_xasca]: input[_CA],
    [_xaebo]: input[_EBO],
    [_xarp]: input[_RP]
  });
  b.bp("/{Key+}");
  b.p("Bucket", () => input.Bucket, "{Bucket}", false);
  b.p("Key", () => input.Key, "{Key+}", true);
  const query = (0, import_smithy_client.map)({
    [_t]: [, ""],
    [_vI]: [, input[_VI]]
  });
  let body;
  let contents;
  if (input.Tagging !== void 0) {
    contents = se_Tagging(input.Tagging, context);
    body = _ve;
    contents.a("xmlns", "http://s3.amazonaws.com/doc/2006-03-01/");
    body += contents.toString();
  }
  b.m("PUT").h(headers).q(query).b(body);
  return b.build();
}, "se_PutObjectTaggingCommand");
var se_PutPublicAccessBlockCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core2.requestBuilder)(input, context);
  const headers = (0, import_smithy_client.map)({}, import_smithy_client.isSerializableHeaderValue, {
    "content-type": "application/xml",
    [_cm]: input[_CMD],
    [_xasca]: input[_CA],
    [_xaebo]: input[_EBO]
  });
  b.bp("/");
  b.p("Bucket", () => input.Bucket, "{Bucket}", false);
  const query = (0, import_smithy_client.map)({
    [_pAB]: [, ""]
  });
  let body;
  let contents;
  if (input.PublicAccessBlockConfiguration !== void 0) {
    contents = se_PublicAccessBlockConfiguration(input.PublicAccessBlockConfiguration, context);
    body = _ve;
    contents.a("xmlns", "http://s3.amazonaws.com/doc/2006-03-01/");
    body += contents.toString();
  }
  b.m("PUT").h(headers).q(query).b(body);
  return b.build();
}, "se_PutPublicAccessBlockCommand");
var se_RenameObjectCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core2.requestBuilder)(input, context);
  const headers = (0, import_smithy_client.map)({}, import_smithy_client.isSerializableHeaderValue, {
    [_xars]: input[_RS],
    [_im]: input[_DIM],
    [_inm]: input[_DINM],
    [_ims]: [() => (0, import_smithy_client.isSerializableHeaderValue)(input[_DIMS]), () => (0, import_smithy_client.dateToUtcString)(input[_DIMS]).toString()],
    [_ius]: [() => (0, import_smithy_client.isSerializableHeaderValue)(input[_DIUS]), () => (0, import_smithy_client.dateToUtcString)(input[_DIUS]).toString()],
    [_xarsim]: input[_SIM],
    [_xarsinm]: input[_SINM],
    [_xarsims]: [() => (0, import_smithy_client.isSerializableHeaderValue)(input[_SIMS]), () => (0, import_smithy_client.dateToUtcString)(input[_SIMS]).toString()],
    [_xarsius]: [() => (0, import_smithy_client.isSerializableHeaderValue)(input[_SIUS]), () => (0, import_smithy_client.dateToUtcString)(input[_SIUS]).toString()],
    [_xact_]: input[_CTl] ?? (0, import_uuid.v4)()
  });
  b.bp("/{Key+}");
  b.p("Bucket", () => input.Bucket, "{Bucket}", false);
  b.p("Key", () => input.Key, "{Key+}", true);
  const query = (0, import_smithy_client.map)({
    [_rO]: [, ""]
  });
  let body;
  b.m("PUT").h(headers).q(query).b(body);
  return b.build();
}, "se_RenameObjectCommand");
var se_RestoreObjectCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core2.requestBuilder)(input, context);
  const headers = (0, import_smithy_client.map)({}, import_smithy_client.isSerializableHeaderValue, {
    "content-type": "application/xml",
    [_xarp]: input[_RP],
    [_xasca]: input[_CA],
    [_xaebo]: input[_EBO]
  });
  b.bp("/{Key+}");
  b.p("Bucket", () => input.Bucket, "{Bucket}", false);
  b.p("Key", () => input.Key, "{Key+}", true);
  const query = (0, import_smithy_client.map)({
    [_res]: [, ""],
    [_vI]: [, input[_VI]]
  });
  let body;
  let contents;
  if (input.RestoreRequest !== void 0) {
    contents = se_RestoreRequest(input.RestoreRequest, context);
    body = _ve;
    contents.a("xmlns", "http://s3.amazonaws.com/doc/2006-03-01/");
    body += contents.toString();
  }
  b.m("POST").h(headers).q(query).b(body);
  return b.build();
}, "se_RestoreObjectCommand");
var se_SelectObjectContentCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core2.requestBuilder)(input, context);
  const headers = (0, import_smithy_client.map)({}, import_smithy_client.isSerializableHeaderValue, {
    "content-type": "application/xml",
    [_xasseca]: input[_SSECA],
    [_xasseck]: input[_SSECK],
    [_xasseckm]: input[_SSECKMD],
    [_xaebo]: input[_EBO]
  });
  b.bp("/{Key+}");
  b.p("Bucket", () => input.Bucket, "{Bucket}", false);
  b.p("Key", () => input.Key, "{Key+}", true);
  const query = (0, import_smithy_client.map)({
    [_se]: [, ""],
    [_st]: [, "2"]
  });
  let body;
  body = _ve;
  const bn = new import_xml_builder.XmlNode(_SOCR);
  bn.a("xmlns", "http://s3.amazonaws.com/doc/2006-03-01/");
  bn.cc(input, _Ex);
  bn.cc(input, _ETx);
  if (input[_IS] != null) {
    bn.c(se_InputSerialization(input[_IS], context).n(_IS));
  }
  if (input[_OS] != null) {
    bn.c(se_OutputSerialization(input[_OS], context).n(_OS));
  }
  if (input[_RPe] != null) {
    bn.c(se_RequestProgress(input[_RPe], context).n(_RPe));
  }
  if (input[_SR] != null) {
    bn.c(se_ScanRange(input[_SR], context).n(_SR));
  }
  body += bn.toString();
  b.m("POST").h(headers).q(query).b(body);
  return b.build();
}, "se_SelectObjectContentCommand");
var se_UpdateBucketMetadataInventoryTableConfigurationCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core2.requestBuilder)(input, context);
  const headers = (0, import_smithy_client.map)({}, import_smithy_client.isSerializableHeaderValue, {
    "content-type": "application/xml",
    [_cm]: input[_CMD],
    [_xasca]: input[_CA],
    [_xaebo]: input[_EBO]
  });
  b.bp("/");
  b.p("Bucket", () => input.Bucket, "{Bucket}", false);
  const query = (0, import_smithy_client.map)({
    [_mIT]: [, ""]
  });
  let body;
  let contents;
  if (input.InventoryTableConfiguration !== void 0) {
    contents = se_InventoryTableConfigurationUpdates(input.InventoryTableConfiguration, context);
    contents = contents.n("InventoryTableConfiguration");
    body = _ve;
    contents.a("xmlns", "http://s3.amazonaws.com/doc/2006-03-01/");
    body += contents.toString();
  }
  b.m("PUT").h(headers).q(query).b(body);
  return b.build();
}, "se_UpdateBucketMetadataInventoryTableConfigurationCommand");
var se_UpdateBucketMetadataJournalTableConfigurationCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core2.requestBuilder)(input, context);
  const headers = (0, import_smithy_client.map)({}, import_smithy_client.isSerializableHeaderValue, {
    "content-type": "application/xml",
    [_cm]: input[_CMD],
    [_xasca]: input[_CA],
    [_xaebo]: input[_EBO]
  });
  b.bp("/");
  b.p("Bucket", () => input.Bucket, "{Bucket}", false);
  const query = (0, import_smithy_client.map)({
    [_mJT]: [, ""]
  });
  let body;
  let contents;
  if (input.JournalTableConfiguration !== void 0) {
    contents = se_JournalTableConfigurationUpdates(input.JournalTableConfiguration, context);
    contents = contents.n("JournalTableConfiguration");
    body = _ve;
    contents.a("xmlns", "http://s3.amazonaws.com/doc/2006-03-01/");
    body += contents.toString();
  }
  b.m("PUT").h(headers).q(query).b(body);
  return b.build();
}, "se_UpdateBucketMetadataJournalTableConfigurationCommand");
var se_UploadPartCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core2.requestBuilder)(input, context);
  const headers = (0, import_smithy_client.map)({}, import_smithy_client.isSerializableHeaderValue, {
    "content-type": "application/octet-stream",
    [_cl_]: [() => (0, import_smithy_client.isSerializableHeaderValue)(input[_CLo]), () => input[_CLo].toString()],
    [_cm]: input[_CMD],
    [_xasca]: input[_CA],
    [_xacc]: input[_CCRC],
    [_xacc_]: input[_CCRCC],
    [_xacc__]: input[_CCRCNVME],
    [_xacs]: input[_CSHA],
    [_xacs_]: input[_CSHAh],
    [_xasseca]: input[_SSECA],
    [_xasseck]: input[_SSECK],
    [_xasseckm]: input[_SSECKMD],
    [_xarp]: input[_RP],
    [_xaebo]: input[_EBO]
  });
  b.bp("/{Key+}");
  b.p("Bucket", () => input.Bucket, "{Bucket}", false);
  b.p("Key", () => input.Key, "{Key+}", true);
  const query = (0, import_smithy_client.map)({
    [_xi]: [, "UploadPart"],
    [_pN]: [(0, import_smithy_client.expectNonNull)(input.PartNumber, `PartNumber`) != null, () => input[_PN].toString()],
    [_uI]: [, (0, import_smithy_client.expectNonNull)(input[_UI], `UploadId`)]
  });
  let body;
  let contents;
  if (input.Body !== void 0) {
    contents = input.Body;
    body = contents;
  }
  b.m("PUT").h(headers).q(query).b(body);
  return b.build();
}, "se_UploadPartCommand");
var se_UploadPartCopyCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core2.requestBuilder)(input, context);
  const headers = (0, import_smithy_client.map)({}, import_smithy_client.isSerializableHeaderValue, {
    [_xacs__]: input[_CS],
    [_xacsim]: input[_CSIM],
    [_xacsims]: [() => (0, import_smithy_client.isSerializableHeaderValue)(input[_CSIMS]), () => (0, import_smithy_client.dateToUtcString)(input[_CSIMS]).toString()],
    [_xacsinm]: input[_CSINM],
    [_xacsius]: [() => (0, import_smithy_client.isSerializableHeaderValue)(input[_CSIUS]), () => (0, import_smithy_client.dateToUtcString)(input[_CSIUS]).toString()],
    [_xacsr]: input[_CSR],
    [_xasseca]: input[_SSECA],
    [_xasseck]: input[_SSECK],
    [_xasseckm]: input[_SSECKMD],
    [_xacssseca]: input[_CSSSECA],
    [_xacssseck]: input[_CSSSECK],
    [_xacssseckm]: input[_CSSSECKMD],
    [_xarp]: input[_RP],
    [_xaebo]: input[_EBO],
    [_xasebo]: input[_ESBO]
  });
  b.bp("/{Key+}");
  b.p("Bucket", () => input.Bucket, "{Bucket}", false);
  b.p("Key", () => input.Key, "{Key+}", true);
  const query = (0, import_smithy_client.map)({
    [_xi]: [, "UploadPartCopy"],
    [_pN]: [(0, import_smithy_client.expectNonNull)(input.PartNumber, `PartNumber`) != null, () => input[_PN].toString()],
    [_uI]: [, (0, import_smithy_client.expectNonNull)(input[_UI], `UploadId`)]
  });
  let body;
  b.m("PUT").h(headers).q(query).b(body);
  return b.build();
}, "se_UploadPartCopyCommand");
var se_WriteGetObjectResponseCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core2.requestBuilder)(input, context);
  const headers = (0, import_smithy_client.map)({}, import_smithy_client.isSerializableHeaderValue, {
    "x-amz-content-sha256": "UNSIGNED-PAYLOAD",
    ...input.Metadata !== void 0 && Object.keys(input.Metadata).reduce((acc, suffix) => {
      acc[`x-amz-meta-${suffix.toLowerCase()}`] = input.Metadata[suffix];
      return acc;
    }, {}),
    "content-type": "application/octet-stream",
    [_xarr]: input[_RR],
    [_xart]: input[_RT],
    [_xafs]: [() => (0, import_smithy_client.isSerializableHeaderValue)(input[_SCt]), () => input[_SCt].toString()],
    [_xafec]: input[_EC],
    [_xafem]: input[_EM],
    [_xafhar]: input[_AR],
    [_xafhcc]: input[_CC],
    [_xafhcd]: input[_CD],
    [_xafhce]: input[_CE],
    [_xafhcl]: input[_CL],
    [_cl_]: [() => (0, import_smithy_client.isSerializableHeaderValue)(input[_CLo]), () => input[_CLo].toString()],
    [_xafhcr]: input[_CR],
    [_xafhct]: input[_CTo],
    [_xafhxacc]: input[_CCRC],
    [_xafhxacc_]: input[_CCRCC],
    [_xafhxacc__]: input[_CCRCNVME],
    [_xafhxacs]: input[_CSHA],
    [_xafhxacs_]: input[_CSHAh],
    [_xafhxadm]: [() => (0, import_smithy_client.isSerializableHeaderValue)(input[_DM]), () => input[_DM].toString()],
    [_xafhe]: input[_ETa],
    [_xafhe_]: [() => (0, import_smithy_client.isSerializableHeaderValue)(input[_E]), () => (0, import_smithy_client.dateToUtcString)(input[_E]).toString()],
    [_xafhxae]: input[_Exp],
    [_xafhlm]: [() => (0, import_smithy_client.isSerializableHeaderValue)(input[_LM]), () => (0, import_smithy_client.dateToUtcString)(input[_LM]).toString()],
    [_xafhxamm]: [() => (0, import_smithy_client.isSerializableHeaderValue)(input[_MM]), () => input[_MM].toString()],
    [_xafhxaolm]: input[_OLM],
    [_xafhxaollh]: input[_OLLHS],
    [_xafhxaolrud]: [
      () => (0, import_smithy_client.isSerializableHeaderValue)(input[_OLRUD]),
      () => (0, import_smithy_client.serializeDateTime)(input[_OLRUD]).toString()
    ],
    [_xafhxampc]: [() => (0, import_smithy_client.isSerializableHeaderValue)(input[_PC]), () => input[_PC].toString()],
    [_xafhxars]: input[_RSe],
    [_xafhxarc]: input[_RC],
    [_xafhxar]: input[_Re],
    [_xafhxasse]: input[_SSE],
    [_xafhxasseca]: input[_SSECA],
    [_xafhxasseakki]: input[_SSEKMSKI],
    [_xafhxasseckm]: input[_SSECKMD],
    [_xafhxasc]: input[_SC],
    [_xafhxatc]: [() => (0, import_smithy_client.isSerializableHeaderValue)(input[_TC]), () => input[_TC].toString()],
    [_xafhxavi]: input[_VI],
    [_xafhxassebke]: [() => (0, import_smithy_client.isSerializableHeaderValue)(input[_BKE]), () => input[_BKE].toString()]
  });
  b.bp("/WriteGetObjectResponse");
  let body;
  let contents;
  if (input.Body !== void 0) {
    contents = input.Body;
    body = contents;
  }
  let { hostname: resolvedHostname } = await context.endpoint();
  if (context.disableHostPrefix !== true) {
    resolvedHostname = "{RequestRoute}." + resolvedHostname;
    if (input.RequestRoute === void 0) {
      throw new Error("Empty value provided for input host prefix: RequestRoute.");
    }
    resolvedHostname = resolvedHostname.replace("{RequestRoute}", input.RequestRoute);
    if (!(0, import_protocol_http.isValidHostname)(resolvedHostname)) {
      throw new Error("ValidationError: prefixed hostname must be hostname compatible.");
    }
  }
  b.hn(resolvedHostname);
  b.m("POST").h(headers).b(body);
  return b.build();
}, "se_WriteGetObjectResponseCommand");
var de_AbortMultipartUploadCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 204 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output),
    [_RC]: [, output.headers[_xarc]]
  });
  await (0, import_smithy_client.collectBody)(output.body, context);
  return contents;
}, "de_AbortMultipartUploadCommand");
var de_CompleteMultipartUploadCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output),
    [_Exp]: [, output.headers[_xae]],
    [_SSE]: [, output.headers[_xasse]],
    [_VI]: [, output.headers[_xavi]],
    [_SSEKMSKI]: [, output.headers[_xasseakki]],
    [_BKE]: [() => void 0 !== output.headers[_xassebke], () => (0, import_smithy_client.parseBoolean)(output.headers[_xassebke])],
    [_RC]: [, output.headers[_xarc]]
  });
  const data = (0, import_smithy_client.expectNonNull)((0, import_smithy_client.expectObject)(await (0, import_core.parseXmlBody)(output.body, context)), "body");
  if (data[_B] != null) {
    contents[_B] = (0, import_smithy_client.expectString)(data[_B]);
  }
  if (data[_CCRC] != null) {
    contents[_CCRC] = (0, import_smithy_client.expectString)(data[_CCRC]);
  }
  if (data[_CCRCC] != null) {
    contents[_CCRCC] = (0, import_smithy_client.expectString)(data[_CCRCC]);
  }
  if (data[_CCRCNVME] != null) {
    contents[_CCRCNVME] = (0, import_smithy_client.expectString)(data[_CCRCNVME]);
  }
  if (data[_CSHA] != null) {
    contents[_CSHA] = (0, import_smithy_client.expectString)(data[_CSHA]);
  }
  if (data[_CSHAh] != null) {
    contents[_CSHAh] = (0, import_smithy_client.expectString)(data[_CSHAh]);
  }
  if (data[_CT] != null) {
    contents[_CT] = (0, import_smithy_client.expectString)(data[_CT]);
  }
  if (data[_ETa] != null) {
    contents[_ETa] = (0, import_smithy_client.expectString)(data[_ETa]);
  }
  if (data[_K] != null) {
    contents[_K] = (0, import_smithy_client.expectString)(data[_K]);
  }
  if (data[_L] != null) {
    contents[_L] = (0, import_smithy_client.expectString)(data[_L]);
  }
  return contents;
}, "de_CompleteMultipartUploadCommand");
var de_CopyObjectCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output),
    [_Exp]: [, output.headers[_xae]],
    [_CSVI]: [, output.headers[_xacsvi]],
    [_VI]: [, output.headers[_xavi]],
    [_SSE]: [, output.headers[_xasse]],
    [_SSECA]: [, output.headers[_xasseca]],
    [_SSECKMD]: [, output.headers[_xasseckm]],
    [_SSEKMSKI]: [, output.headers[_xasseakki]],
    [_SSEKMSEC]: [, output.headers[_xassec]],
    [_BKE]: [() => void 0 !== output.headers[_xassebke], () => (0, import_smithy_client.parseBoolean)(output.headers[_xassebke])],
    [_RC]: [, output.headers[_xarc]]
  });
  const data = (0, import_smithy_client.expectObject)(await (0, import_core.parseXmlBody)(output.body, context));
  contents.CopyObjectResult = de_CopyObjectResult(data, context);
  return contents;
}, "de_CopyObjectCommand");
var de_CreateBucketCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output),
    [_L]: [, output.headers[_lo]],
    [_BA]: [, output.headers[_xaba]]
  });
  await (0, import_smithy_client.collectBody)(output.body, context);
  return contents;
}, "de_CreateBucketCommand");
var de_CreateBucketMetadataConfigurationCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  await (0, import_smithy_client.collectBody)(output.body, context);
  return contents;
}, "de_CreateBucketMetadataConfigurationCommand");
var de_CreateBucketMetadataTableConfigurationCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  await (0, import_smithy_client.collectBody)(output.body, context);
  return contents;
}, "de_CreateBucketMetadataTableConfigurationCommand");
var de_CreateMultipartUploadCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output),
    [_AD]: [
      () => void 0 !== output.headers[_xaad],
      () => (0, import_smithy_client.expectNonNull)((0, import_smithy_client.parseRfc7231DateTime)(output.headers[_xaad]))
    ],
    [_ARI]: [, output.headers[_xaari]],
    [_SSE]: [, output.headers[_xasse]],
    [_SSECA]: [, output.headers[_xasseca]],
    [_SSECKMD]: [, output.headers[_xasseckm]],
    [_SSEKMSKI]: [, output.headers[_xasseakki]],
    [_SSEKMSEC]: [, output.headers[_xassec]],
    [_BKE]: [() => void 0 !== output.headers[_xassebke], () => (0, import_smithy_client.parseBoolean)(output.headers[_xassebke])],
    [_RC]: [, output.headers[_xarc]],
    [_CA]: [, output.headers[_xaca]],
    [_CT]: [, output.headers[_xact]]
  });
  const data = (0, import_smithy_client.expectNonNull)((0, import_smithy_client.expectObject)(await (0, import_core.parseXmlBody)(output.body, context)), "body");
  if (data[_B] != null) {
    contents[_B] = (0, import_smithy_client.expectString)(data[_B]);
  }
  if (data[_K] != null) {
    contents[_K] = (0, import_smithy_client.expectString)(data[_K]);
  }
  if (data[_UI] != null) {
    contents[_UI] = (0, import_smithy_client.expectString)(data[_UI]);
  }
  return contents;
}, "de_CreateMultipartUploadCommand");
var de_CreateSessionCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output),
    [_SSE]: [, output.headers[_xasse]],
    [_SSEKMSKI]: [, output.headers[_xasseakki]],
    [_SSEKMSEC]: [, output.headers[_xassec]],
    [_BKE]: [() => void 0 !== output.headers[_xassebke], () => (0, import_smithy_client.parseBoolean)(output.headers[_xassebke])]
  });
  const data = (0, import_smithy_client.expectNonNull)((0, import_smithy_client.expectObject)(await (0, import_core.parseXmlBody)(output.body, context)), "body");
  if (data[_C] != null) {
    contents[_C] = de_SessionCredentials(data[_C], context);
  }
  return contents;
}, "de_CreateSessionCommand");
var de_DeleteBucketCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 204 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  await (0, import_smithy_client.collectBody)(output.body, context);
  return contents;
}, "de_DeleteBucketCommand");
var de_DeleteBucketAnalyticsConfigurationCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 204 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  await (0, import_smithy_client.collectBody)(output.body, context);
  return contents;
}, "de_DeleteBucketAnalyticsConfigurationCommand");
var de_DeleteBucketCorsCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 204 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  await (0, import_smithy_client.collectBody)(output.body, context);
  return contents;
}, "de_DeleteBucketCorsCommand");
var de_DeleteBucketEncryptionCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 204 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  await (0, import_smithy_client.collectBody)(output.body, context);
  return contents;
}, "de_DeleteBucketEncryptionCommand");
var de_DeleteBucketIntelligentTieringConfigurationCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 204 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  await (0, import_smithy_client.collectBody)(output.body, context);
  return contents;
}, "de_DeleteBucketIntelligentTieringConfigurationCommand");
var de_DeleteBucketInventoryConfigurationCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 204 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  await (0, import_smithy_client.collectBody)(output.body, context);
  return contents;
}, "de_DeleteBucketInventoryConfigurationCommand");
var de_DeleteBucketLifecycleCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 204 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  await (0, import_smithy_client.collectBody)(output.body, context);
  return contents;
}, "de_DeleteBucketLifecycleCommand");
var de_DeleteBucketMetadataConfigurationCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 204 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  await (0, import_smithy_client.collectBody)(output.body, context);
  return contents;
}, "de_DeleteBucketMetadataConfigurationCommand");
var de_DeleteBucketMetadataTableConfigurationCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 204 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  await (0, import_smithy_client.collectBody)(output.body, context);
  return contents;
}, "de_DeleteBucketMetadataTableConfigurationCommand");
var de_DeleteBucketMetricsConfigurationCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 204 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  await (0, import_smithy_client.collectBody)(output.body, context);
  return contents;
}, "de_DeleteBucketMetricsConfigurationCommand");
var de_DeleteBucketOwnershipControlsCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 204 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  await (0, import_smithy_client.collectBody)(output.body, context);
  return contents;
}, "de_DeleteBucketOwnershipControlsCommand");
var de_DeleteBucketPolicyCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 204 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  await (0, import_smithy_client.collectBody)(output.body, context);
  return contents;
}, "de_DeleteBucketPolicyCommand");
var de_DeleteBucketReplicationCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 204 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  await (0, import_smithy_client.collectBody)(output.body, context);
  return contents;
}, "de_DeleteBucketReplicationCommand");
var de_DeleteBucketTaggingCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 204 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  await (0, import_smithy_client.collectBody)(output.body, context);
  return contents;
}, "de_DeleteBucketTaggingCommand");
var de_DeleteBucketWebsiteCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 204 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  await (0, import_smithy_client.collectBody)(output.body, context);
  return contents;
}, "de_DeleteBucketWebsiteCommand");
var de_DeleteObjectCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 204 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output),
    [_DM]: [() => void 0 !== output.headers[_xadm], () => (0, import_smithy_client.parseBoolean)(output.headers[_xadm])],
    [_VI]: [, output.headers[_xavi]],
    [_RC]: [, output.headers[_xarc]]
  });
  await (0, import_smithy_client.collectBody)(output.body, context);
  return contents;
}, "de_DeleteObjectCommand");
var de_DeleteObjectsCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output),
    [_RC]: [, output.headers[_xarc]]
  });
  const data = (0, import_smithy_client.expectNonNull)((0, import_smithy_client.expectObject)(await (0, import_core.parseXmlBody)(output.body, context)), "body");
  if (data.Deleted === "") {
    contents[_De] = [];
  } else if (data[_De] != null) {
    contents[_De] = de_DeletedObjects((0, import_smithy_client.getArrayIfSingleItem)(data[_De]), context);
  }
  if (data.Error === "") {
    contents[_Err] = [];
  } else if (data[_Er] != null) {
    contents[_Err] = de_Errors((0, import_smithy_client.getArrayIfSingleItem)(data[_Er]), context);
  }
  return contents;
}, "de_DeleteObjectsCommand");
var de_DeleteObjectTaggingCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 204 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output),
    [_VI]: [, output.headers[_xavi]]
  });
  await (0, import_smithy_client.collectBody)(output.body, context);
  return contents;
}, "de_DeleteObjectTaggingCommand");
var de_DeletePublicAccessBlockCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 204 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  await (0, import_smithy_client.collectBody)(output.body, context);
  return contents;
}, "de_DeletePublicAccessBlockCommand");
var de_GetBucketAccelerateConfigurationCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output),
    [_RC]: [, output.headers[_xarc]]
  });
  const data = (0, import_smithy_client.expectNonNull)((0, import_smithy_client.expectObject)(await (0, import_core.parseXmlBody)(output.body, context)), "body");
  if (data[_S] != null) {
    contents[_S] = (0, import_smithy_client.expectString)(data[_S]);
  }
  return contents;
}, "de_GetBucketAccelerateConfigurationCommand");
var de_GetBucketAclCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  const data = (0, import_smithy_client.expectNonNull)((0, import_smithy_client.expectObject)(await (0, import_core.parseXmlBody)(output.body, context)), "body");
  if (data.AccessControlList === "") {
    contents[_Gr] = [];
  } else if (data[_ACLc] != null && data[_ACLc][_G] != null) {
    contents[_Gr] = de_Grants((0, import_smithy_client.getArrayIfSingleItem)(data[_ACLc][_G]), context);
  }
  if (data[_O] != null) {
    contents[_O] = de_Owner(data[_O], context);
  }
  return contents;
}, "de_GetBucketAclCommand");
var de_GetBucketAnalyticsConfigurationCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  const data = (0, import_smithy_client.expectObject)(await (0, import_core.parseXmlBody)(output.body, context));
  contents.AnalyticsConfiguration = de_AnalyticsConfiguration(data, context);
  return contents;
}, "de_GetBucketAnalyticsConfigurationCommand");
var de_GetBucketCorsCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  const data = (0, import_smithy_client.expectNonNull)((0, import_smithy_client.expectObject)(await (0, import_core.parseXmlBody)(output.body, context)), "body");
  if (data.CORSRule === "") {
    contents[_CORSRu] = [];
  } else if (data[_CORSR] != null) {
    contents[_CORSRu] = de_CORSRules((0, import_smithy_client.getArrayIfSingleItem)(data[_CORSR]), context);
  }
  return contents;
}, "de_GetBucketCorsCommand");
var de_GetBucketEncryptionCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  const data = (0, import_smithy_client.expectObject)(await (0, import_core.parseXmlBody)(output.body, context));
  contents.ServerSideEncryptionConfiguration = de_ServerSideEncryptionConfiguration(data, context);
  return contents;
}, "de_GetBucketEncryptionCommand");
var de_GetBucketIntelligentTieringConfigurationCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  const data = (0, import_smithy_client.expectObject)(await (0, import_core.parseXmlBody)(output.body, context));
  contents.IntelligentTieringConfiguration = de_IntelligentTieringConfiguration(data, context);
  return contents;
}, "de_GetBucketIntelligentTieringConfigurationCommand");
var de_GetBucketInventoryConfigurationCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  const data = (0, import_smithy_client.expectObject)(await (0, import_core.parseXmlBody)(output.body, context));
  contents.InventoryConfiguration = de_InventoryConfiguration(data, context);
  return contents;
}, "de_GetBucketInventoryConfigurationCommand");
var de_GetBucketLifecycleConfigurationCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output),
    [_TDMOS]: [, output.headers[_xatdmos]]
  });
  const data = (0, import_smithy_client.expectNonNull)((0, import_smithy_client.expectObject)(await (0, import_core.parseXmlBody)(output.body, context)), "body");
  if (data.Rule === "") {
    contents[_Rul] = [];
  } else if (data[_Ru] != null) {
    contents[_Rul] = de_LifecycleRules((0, import_smithy_client.getArrayIfSingleItem)(data[_Ru]), context);
  }
  return contents;
}, "de_GetBucketLifecycleConfigurationCommand");
var de_GetBucketLocationCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  const data = (0, import_smithy_client.expectNonNull)((0, import_smithy_client.expectObject)(await (0, import_core.parseXmlBody)(output.body, context)), "body");
  if (data[_LC] != null) {
    contents[_LC] = (0, import_smithy_client.expectString)(data[_LC]);
  }
  return contents;
}, "de_GetBucketLocationCommand");
var de_GetBucketLoggingCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  const data = (0, import_smithy_client.expectNonNull)((0, import_smithy_client.expectObject)(await (0, import_core.parseXmlBody)(output.body, context)), "body");
  if (data[_LE] != null) {
    contents[_LE] = de_LoggingEnabled(data[_LE], context);
  }
  return contents;
}, "de_GetBucketLoggingCommand");
var de_GetBucketMetadataConfigurationCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  const data = (0, import_smithy_client.expectObject)(await (0, import_core.parseXmlBody)(output.body, context));
  contents.GetBucketMetadataConfigurationResult = de_GetBucketMetadataConfigurationResult(data, context);
  return contents;
}, "de_GetBucketMetadataConfigurationCommand");
var de_GetBucketMetadataTableConfigurationCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  const data = (0, import_smithy_client.expectObject)(await (0, import_core.parseXmlBody)(output.body, context));
  contents.GetBucketMetadataTableConfigurationResult = de_GetBucketMetadataTableConfigurationResult(data, context);
  return contents;
}, "de_GetBucketMetadataTableConfigurationCommand");
var de_GetBucketMetricsConfigurationCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  const data = (0, import_smithy_client.expectObject)(await (0, import_core.parseXmlBody)(output.body, context));
  contents.MetricsConfiguration = de_MetricsConfiguration(data, context);
  return contents;
}, "de_GetBucketMetricsConfigurationCommand");
var de_GetBucketNotificationConfigurationCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  const data = (0, import_smithy_client.expectNonNull)((0, import_smithy_client.expectObject)(await (0, import_core.parseXmlBody)(output.body, context)), "body");
  if (data[_EBC] != null) {
    contents[_EBC] = de_EventBridgeConfiguration(data[_EBC], context);
  }
  if (data.CloudFunctionConfiguration === "") {
    contents[_LFC] = [];
  } else if (data[_CFC] != null) {
    contents[_LFC] = de_LambdaFunctionConfigurationList((0, import_smithy_client.getArrayIfSingleItem)(data[_CFC]), context);
  }
  if (data.QueueConfiguration === "") {
    contents[_QCu] = [];
  } else if (data[_QC] != null) {
    contents[_QCu] = de_QueueConfigurationList((0, import_smithy_client.getArrayIfSingleItem)(data[_QC]), context);
  }
  if (data.TopicConfiguration === "") {
    contents[_TCop] = [];
  } else if (data[_TCo] != null) {
    contents[_TCop] = de_TopicConfigurationList((0, import_smithy_client.getArrayIfSingleItem)(data[_TCo]), context);
  }
  return contents;
}, "de_GetBucketNotificationConfigurationCommand");
var de_GetBucketOwnershipControlsCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  const data = (0, import_smithy_client.expectObject)(await (0, import_core.parseXmlBody)(output.body, context));
  contents.OwnershipControls = de_OwnershipControls(data, context);
  return contents;
}, "de_GetBucketOwnershipControlsCommand");
var de_GetBucketPolicyCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  const data = await collectBodyString(output.body, context);
  contents.Policy = (0, import_smithy_client.expectString)(data);
  return contents;
}, "de_GetBucketPolicyCommand");
var de_GetBucketPolicyStatusCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  const data = (0, import_smithy_client.expectObject)(await (0, import_core.parseXmlBody)(output.body, context));
  contents.PolicyStatus = de_PolicyStatus(data, context);
  return contents;
}, "de_GetBucketPolicyStatusCommand");
var de_GetBucketReplicationCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  const data = (0, import_smithy_client.expectObject)(await (0, import_core.parseXmlBody)(output.body, context));
  contents.ReplicationConfiguration = de_ReplicationConfiguration(data, context);
  return contents;
}, "de_GetBucketReplicationCommand");
var de_GetBucketRequestPaymentCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  const data = (0, import_smithy_client.expectNonNull)((0, import_smithy_client.expectObject)(await (0, import_core.parseXmlBody)(output.body, context)), "body");
  if (data[_Pa] != null) {
    contents[_Pa] = (0, import_smithy_client.expectString)(data[_Pa]);
  }
  return contents;
}, "de_GetBucketRequestPaymentCommand");
var de_GetBucketTaggingCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  const data = (0, import_smithy_client.expectNonNull)((0, import_smithy_client.expectObject)(await (0, import_core.parseXmlBody)(output.body, context)), "body");
  if (data.TagSet === "") {
    contents[_TS] = [];
  } else if (data[_TS] != null && data[_TS][_Ta] != null) {
    contents[_TS] = de_TagSet((0, import_smithy_client.getArrayIfSingleItem)(data[_TS][_Ta]), context);
  }
  return contents;
}, "de_GetBucketTaggingCommand");
var de_GetBucketVersioningCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  const data = (0, import_smithy_client.expectNonNull)((0, import_smithy_client.expectObject)(await (0, import_core.parseXmlBody)(output.body, context)), "body");
  if (data[_MDf] != null) {
    contents[_MFAD] = (0, import_smithy_client.expectString)(data[_MDf]);
  }
  if (data[_S] != null) {
    contents[_S] = (0, import_smithy_client.expectString)(data[_S]);
  }
  return contents;
}, "de_GetBucketVersioningCommand");
var de_GetBucketWebsiteCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  const data = (0, import_smithy_client.expectNonNull)((0, import_smithy_client.expectObject)(await (0, import_core.parseXmlBody)(output.body, context)), "body");
  if (data[_ED] != null) {
    contents[_ED] = de_ErrorDocument(data[_ED], context);
  }
  if (data[_ID] != null) {
    contents[_ID] = de_IndexDocument(data[_ID], context);
  }
  if (data[_RART] != null) {
    contents[_RART] = de_RedirectAllRequestsTo(data[_RART], context);
  }
  if (data.RoutingRules === "") {
    contents[_RRo] = [];
  } else if (data[_RRo] != null && data[_RRo][_RRou] != null) {
    contents[_RRo] = de_RoutingRules((0, import_smithy_client.getArrayIfSingleItem)(data[_RRo][_RRou]), context);
  }
  return contents;
}, "de_GetBucketWebsiteCommand");
var de_GetObjectCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output),
    [_DM]: [() => void 0 !== output.headers[_xadm], () => (0, import_smithy_client.parseBoolean)(output.headers[_xadm])],
    [_AR]: [, output.headers[_ar]],
    [_Exp]: [, output.headers[_xae]],
    [_Re]: [, output.headers[_xar]],
    [_LM]: [() => void 0 !== output.headers[_lm], () => (0, import_smithy_client.expectNonNull)((0, import_smithy_client.parseRfc7231DateTime)(output.headers[_lm]))],
    [_CLo]: [() => void 0 !== output.headers[_cl_], () => (0, import_smithy_client.strictParseLong)(output.headers[_cl_])],
    [_ETa]: [, output.headers[_eta]],
    [_CCRC]: [, output.headers[_xacc]],
    [_CCRCC]: [, output.headers[_xacc_]],
    [_CCRCNVME]: [, output.headers[_xacc__]],
    [_CSHA]: [, output.headers[_xacs]],
    [_CSHAh]: [, output.headers[_xacs_]],
    [_CT]: [, output.headers[_xact]],
    [_MM]: [() => void 0 !== output.headers[_xamm], () => (0, import_smithy_client.strictParseInt32)(output.headers[_xamm])],
    [_VI]: [, output.headers[_xavi]],
    [_CC]: [, output.headers[_cc]],
    [_CD]: [, output.headers[_cd]],
    [_CE]: [, output.headers[_ce]],
    [_CL]: [, output.headers[_cl]],
    [_CR]: [, output.headers[_cr]],
    [_CTo]: [, output.headers[_ct]],
    [_E]: [() => void 0 !== output.headers[_e], () => (0, import_smithy_client.expectNonNull)((0, import_smithy_client.parseRfc7231DateTime)(output.headers[_e]))],
    [_ES]: [, output.headers[_ex]],
    [_WRL]: [, output.headers[_xawrl]],
    [_SSE]: [, output.headers[_xasse]],
    [_SSECA]: [, output.headers[_xasseca]],
    [_SSECKMD]: [, output.headers[_xasseckm]],
    [_SSEKMSKI]: [, output.headers[_xasseakki]],
    [_BKE]: [() => void 0 !== output.headers[_xassebke], () => (0, import_smithy_client.parseBoolean)(output.headers[_xassebke])],
    [_SC]: [, output.headers[_xasc]],
    [_RC]: [, output.headers[_xarc]],
    [_RSe]: [, output.headers[_xars_]],
    [_PC]: [() => void 0 !== output.headers[_xampc], () => (0, import_smithy_client.strictParseInt32)(output.headers[_xampc])],
    [_TC]: [() => void 0 !== output.headers[_xatc], () => (0, import_smithy_client.strictParseInt32)(output.headers[_xatc])],
    [_OLM]: [, output.headers[_xaolm]],
    [_OLRUD]: [
      () => void 0 !== output.headers[_xaolrud],
      () => (0, import_smithy_client.expectNonNull)((0, import_smithy_client.parseRfc3339DateTimeWithOffset)(output.headers[_xaolrud]))
    ],
    [_OLLHS]: [, output.headers[_xaollh]],
    Metadata: [
      ,
      Object.keys(output.headers).filter((header) => header.startsWith("x-amz-meta-")).reduce((acc, header) => {
        acc[header.substring(11)] = output.headers[header];
        return acc;
      }, {})
    ]
  });
  const data = output.body;
  context.sdkStreamMixin(data);
  contents.Body = data;
  return contents;
}, "de_GetObjectCommand");
var de_GetObjectAclCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output),
    [_RC]: [, output.headers[_xarc]]
  });
  const data = (0, import_smithy_client.expectNonNull)((0, import_smithy_client.expectObject)(await (0, import_core.parseXmlBody)(output.body, context)), "body");
  if (data.AccessControlList === "") {
    contents[_Gr] = [];
  } else if (data[_ACLc] != null && data[_ACLc][_G] != null) {
    contents[_Gr] = de_Grants((0, import_smithy_client.getArrayIfSingleItem)(data[_ACLc][_G]), context);
  }
  if (data[_O] != null) {
    contents[_O] = de_Owner(data[_O], context);
  }
  return contents;
}, "de_GetObjectAclCommand");
var de_GetObjectAttributesCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output),
    [_DM]: [() => void 0 !== output.headers[_xadm], () => (0, import_smithy_client.parseBoolean)(output.headers[_xadm])],
    [_LM]: [() => void 0 !== output.headers[_lm], () => (0, import_smithy_client.expectNonNull)((0, import_smithy_client.parseRfc7231DateTime)(output.headers[_lm]))],
    [_VI]: [, output.headers[_xavi]],
    [_RC]: [, output.headers[_xarc]]
  });
  const data = (0, import_smithy_client.expectNonNull)((0, import_smithy_client.expectObject)(await (0, import_core.parseXmlBody)(output.body, context)), "body");
  if (data[_Ch] != null) {
    contents[_Ch] = de_Checksum(data[_Ch], context);
  }
  if (data[_ETa] != null) {
    contents[_ETa] = (0, import_smithy_client.expectString)(data[_ETa]);
  }
  if (data[_OP] != null) {
    contents[_OP] = de_GetObjectAttributesParts(data[_OP], context);
  }
  if (data[_OSb] != null) {
    contents[_OSb] = (0, import_smithy_client.strictParseLong)(data[_OSb]);
  }
  if (data[_SC] != null) {
    contents[_SC] = (0, import_smithy_client.expectString)(data[_SC]);
  }
  return contents;
}, "de_GetObjectAttributesCommand");
var de_GetObjectLegalHoldCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  const data = (0, import_smithy_client.expectObject)(await (0, import_core.parseXmlBody)(output.body, context));
  contents.LegalHold = de_ObjectLockLegalHold(data, context);
  return contents;
}, "de_GetObjectLegalHoldCommand");
var de_GetObjectLockConfigurationCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  const data = (0, import_smithy_client.expectObject)(await (0, import_core.parseXmlBody)(output.body, context));
  contents.ObjectLockConfiguration = de_ObjectLockConfiguration(data, context);
  return contents;
}, "de_GetObjectLockConfigurationCommand");
var de_GetObjectRetentionCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  const data = (0, import_smithy_client.expectObject)(await (0, import_core.parseXmlBody)(output.body, context));
  contents.Retention = de_ObjectLockRetention(data, context);
  return contents;
}, "de_GetObjectRetentionCommand");
var de_GetObjectTaggingCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output),
    [_VI]: [, output.headers[_xavi]]
  });
  const data = (0, import_smithy_client.expectNonNull)((0, import_smithy_client.expectObject)(await (0, import_core.parseXmlBody)(output.body, context)), "body");
  if (data.TagSet === "") {
    contents[_TS] = [];
  } else if (data[_TS] != null && data[_TS][_Ta] != null) {
    contents[_TS] = de_TagSet((0, import_smithy_client.getArrayIfSingleItem)(data[_TS][_Ta]), context);
  }
  return contents;
}, "de_GetObjectTaggingCommand");
var de_GetObjectTorrentCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output),
    [_RC]: [, output.headers[_xarc]]
  });
  const data = output.body;
  context.sdkStreamMixin(data);
  contents.Body = data;
  return contents;
}, "de_GetObjectTorrentCommand");
var de_GetPublicAccessBlockCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  const data = (0, import_smithy_client.expectObject)(await (0, import_core.parseXmlBody)(output.body, context));
  contents.PublicAccessBlockConfiguration = de_PublicAccessBlockConfiguration(data, context);
  return contents;
}, "de_GetPublicAccessBlockCommand");
var de_HeadBucketCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output),
    [_BA]: [, output.headers[_xaba]],
    [_BLT]: [, output.headers[_xablt]],
    [_BLN]: [, output.headers[_xabln]],
    [_BR]: [, output.headers[_xabr]],
    [_APA]: [() => void 0 !== output.headers[_xaapa], () => (0, import_smithy_client.parseBoolean)(output.headers[_xaapa])]
  });
  await (0, import_smithy_client.collectBody)(output.body, context);
  return contents;
}, "de_HeadBucketCommand");
var de_HeadObjectCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output),
    [_DM]: [() => void 0 !== output.headers[_xadm], () => (0, import_smithy_client.parseBoolean)(output.headers[_xadm])],
    [_AR]: [, output.headers[_ar]],
    [_Exp]: [, output.headers[_xae]],
    [_Re]: [, output.headers[_xar]],
    [_AS]: [, output.headers[_xaas]],
    [_LM]: [() => void 0 !== output.headers[_lm], () => (0, import_smithy_client.expectNonNull)((0, import_smithy_client.parseRfc7231DateTime)(output.headers[_lm]))],
    [_CLo]: [() => void 0 !== output.headers[_cl_], () => (0, import_smithy_client.strictParseLong)(output.headers[_cl_])],
    [_CCRC]: [, output.headers[_xacc]],
    [_CCRCC]: [, output.headers[_xacc_]],
    [_CCRCNVME]: [, output.headers[_xacc__]],
    [_CSHA]: [, output.headers[_xacs]],
    [_CSHAh]: [, output.headers[_xacs_]],
    [_CT]: [, output.headers[_xact]],
    [_ETa]: [, output.headers[_eta]],
    [_MM]: [() => void 0 !== output.headers[_xamm], () => (0, import_smithy_client.strictParseInt32)(output.headers[_xamm])],
    [_VI]: [, output.headers[_xavi]],
    [_CC]: [, output.headers[_cc]],
    [_CD]: [, output.headers[_cd]],
    [_CE]: [, output.headers[_ce]],
    [_CL]: [, output.headers[_cl]],
    [_CTo]: [, output.headers[_ct]],
    [_CR]: [, output.headers[_cr]],
    [_E]: [() => void 0 !== output.headers[_e], () => (0, import_smithy_client.expectNonNull)((0, import_smithy_client.parseRfc7231DateTime)(output.headers[_e]))],
    [_ES]: [, output.headers[_ex]],
    [_WRL]: [, output.headers[_xawrl]],
    [_SSE]: [, output.headers[_xasse]],
    [_SSECA]: [, output.headers[_xasseca]],
    [_SSECKMD]: [, output.headers[_xasseckm]],
    [_SSEKMSKI]: [, output.headers[_xasseakki]],
    [_BKE]: [() => void 0 !== output.headers[_xassebke], () => (0, import_smithy_client.parseBoolean)(output.headers[_xassebke])],
    [_SC]: [, output.headers[_xasc]],
    [_RC]: [, output.headers[_xarc]],
    [_RSe]: [, output.headers[_xars_]],
    [_PC]: [() => void 0 !== output.headers[_xampc], () => (0, import_smithy_client.strictParseInt32)(output.headers[_xampc])],
    [_TC]: [() => void 0 !== output.headers[_xatc], () => (0, import_smithy_client.strictParseInt32)(output.headers[_xatc])],
    [_OLM]: [, output.headers[_xaolm]],
    [_OLRUD]: [
      () => void 0 !== output.headers[_xaolrud],
      () => (0, import_smithy_client.expectNonNull)((0, import_smithy_client.parseRfc3339DateTimeWithOffset)(output.headers[_xaolrud]))
    ],
    [_OLLHS]: [, output.headers[_xaollh]],
    Metadata: [
      ,
      Object.keys(output.headers).filter((header) => header.startsWith("x-amz-meta-")).reduce((acc, header) => {
        acc[header.substring(11)] = output.headers[header];
        return acc;
      }, {})
    ]
  });
  await (0, import_smithy_client.collectBody)(output.body, context);
  return contents;
}, "de_HeadObjectCommand");
var de_ListBucketAnalyticsConfigurationsCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  const data = (0, import_smithy_client.expectNonNull)((0, import_smithy_client.expectObject)(await (0, import_core.parseXmlBody)(output.body, context)), "body");
  if (data.AnalyticsConfiguration === "") {
    contents[_ACLn] = [];
  } else if (data[_AC] != null) {
    contents[_ACLn] = de_AnalyticsConfigurationList((0, import_smithy_client.getArrayIfSingleItem)(data[_AC]), context);
  }
  if (data[_CTon] != null) {
    contents[_CTon] = (0, import_smithy_client.expectString)(data[_CTon]);
  }
  if (data[_IT] != null) {
    contents[_IT] = (0, import_smithy_client.parseBoolean)(data[_IT]);
  }
  if (data[_NCT] != null) {
    contents[_NCT] = (0, import_smithy_client.expectString)(data[_NCT]);
  }
  return contents;
}, "de_ListBucketAnalyticsConfigurationsCommand");
var de_ListBucketIntelligentTieringConfigurationsCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  const data = (0, import_smithy_client.expectNonNull)((0, import_smithy_client.expectObject)(await (0, import_core.parseXmlBody)(output.body, context)), "body");
  if (data[_CTon] != null) {
    contents[_CTon] = (0, import_smithy_client.expectString)(data[_CTon]);
  }
  if (data.IntelligentTieringConfiguration === "") {
    contents[_ITCL] = [];
  } else if (data[_ITC] != null) {
    contents[_ITCL] = de_IntelligentTieringConfigurationList((0, import_smithy_client.getArrayIfSingleItem)(data[_ITC]), context);
  }
  if (data[_IT] != null) {
    contents[_IT] = (0, import_smithy_client.parseBoolean)(data[_IT]);
  }
  if (data[_NCT] != null) {
    contents[_NCT] = (0, import_smithy_client.expectString)(data[_NCT]);
  }
  return contents;
}, "de_ListBucketIntelligentTieringConfigurationsCommand");
var de_ListBucketInventoryConfigurationsCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  const data = (0, import_smithy_client.expectNonNull)((0, import_smithy_client.expectObject)(await (0, import_core.parseXmlBody)(output.body, context)), "body");
  if (data[_CTon] != null) {
    contents[_CTon] = (0, import_smithy_client.expectString)(data[_CTon]);
  }
  if (data.InventoryConfiguration === "") {
    contents[_ICL] = [];
  } else if (data[_IC] != null) {
    contents[_ICL] = de_InventoryConfigurationList((0, import_smithy_client.getArrayIfSingleItem)(data[_IC]), context);
  }
  if (data[_IT] != null) {
    contents[_IT] = (0, import_smithy_client.parseBoolean)(data[_IT]);
  }
  if (data[_NCT] != null) {
    contents[_NCT] = (0, import_smithy_client.expectString)(data[_NCT]);
  }
  return contents;
}, "de_ListBucketInventoryConfigurationsCommand");
var de_ListBucketMetricsConfigurationsCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  const data = (0, import_smithy_client.expectNonNull)((0, import_smithy_client.expectObject)(await (0, import_core.parseXmlBody)(output.body, context)), "body");
  if (data[_CTon] != null) {
    contents[_CTon] = (0, import_smithy_client.expectString)(data[_CTon]);
  }
  if (data[_IT] != null) {
    contents[_IT] = (0, import_smithy_client.parseBoolean)(data[_IT]);
  }
  if (data.MetricsConfiguration === "") {
    contents[_MCL] = [];
  } else if (data[_MC] != null) {
    contents[_MCL] = de_MetricsConfigurationList((0, import_smithy_client.getArrayIfSingleItem)(data[_MC]), context);
  }
  if (data[_NCT] != null) {
    contents[_NCT] = (0, import_smithy_client.expectString)(data[_NCT]);
  }
  return contents;
}, "de_ListBucketMetricsConfigurationsCommand");
var de_ListBucketsCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  const data = (0, import_smithy_client.expectNonNull)((0, import_smithy_client.expectObject)(await (0, import_core.parseXmlBody)(output.body, context)), "body");
  if (data.Buckets === "") {
    contents[_Bu] = [];
  } else if (data[_Bu] != null && data[_Bu][_B] != null) {
    contents[_Bu] = de_Buckets((0, import_smithy_client.getArrayIfSingleItem)(data[_Bu][_B]), context);
  }
  if (data[_CTon] != null) {
    contents[_CTon] = (0, import_smithy_client.expectString)(data[_CTon]);
  }
  if (data[_O] != null) {
    contents[_O] = de_Owner(data[_O], context);
  }
  if (data[_P] != null) {
    contents[_P] = (0, import_smithy_client.expectString)(data[_P]);
  }
  return contents;
}, "de_ListBucketsCommand");
var de_ListDirectoryBucketsCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  const data = (0, import_smithy_client.expectNonNull)((0, import_smithy_client.expectObject)(await (0, import_core.parseXmlBody)(output.body, context)), "body");
  if (data.Buckets === "") {
    contents[_Bu] = [];
  } else if (data[_Bu] != null && data[_Bu][_B] != null) {
    contents[_Bu] = de_Buckets((0, import_smithy_client.getArrayIfSingleItem)(data[_Bu][_B]), context);
  }
  if (data[_CTon] != null) {
    contents[_CTon] = (0, import_smithy_client.expectString)(data[_CTon]);
  }
  return contents;
}, "de_ListDirectoryBucketsCommand");
var de_ListMultipartUploadsCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output),
    [_RC]: [, output.headers[_xarc]]
  });
  const data = (0, import_smithy_client.expectNonNull)((0, import_smithy_client.expectObject)(await (0, import_core.parseXmlBody)(output.body, context)), "body");
  if (data[_B] != null) {
    contents[_B] = (0, import_smithy_client.expectString)(data[_B]);
  }
  if (data.CommonPrefixes === "") {
    contents[_CP] = [];
  } else if (data[_CP] != null) {
    contents[_CP] = de_CommonPrefixList((0, import_smithy_client.getArrayIfSingleItem)(data[_CP]), context);
  }
  if (data[_D] != null) {
    contents[_D] = (0, import_smithy_client.expectString)(data[_D]);
  }
  if (data[_ET] != null) {
    contents[_ET] = (0, import_smithy_client.expectString)(data[_ET]);
  }
  if (data[_IT] != null) {
    contents[_IT] = (0, import_smithy_client.parseBoolean)(data[_IT]);
  }
  if (data[_KM] != null) {
    contents[_KM] = (0, import_smithy_client.expectString)(data[_KM]);
  }
  if (data[_MU] != null) {
    contents[_MU] = (0, import_smithy_client.strictParseInt32)(data[_MU]);
  }
  if (data[_NKM] != null) {
    contents[_NKM] = (0, import_smithy_client.expectString)(data[_NKM]);
  }
  if (data[_NUIM] != null) {
    contents[_NUIM] = (0, import_smithy_client.expectString)(data[_NUIM]);
  }
  if (data[_P] != null) {
    contents[_P] = (0, import_smithy_client.expectString)(data[_P]);
  }
  if (data[_UIM] != null) {
    contents[_UIM] = (0, import_smithy_client.expectString)(data[_UIM]);
  }
  if (data.Upload === "") {
    contents[_Up] = [];
  } else if (data[_U] != null) {
    contents[_Up] = de_MultipartUploadList((0, import_smithy_client.getArrayIfSingleItem)(data[_U]), context);
  }
  return contents;
}, "de_ListMultipartUploadsCommand");
var de_ListObjectsCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output),
    [_RC]: [, output.headers[_xarc]]
  });
  const data = (0, import_smithy_client.expectNonNull)((0, import_smithy_client.expectObject)(await (0, import_core.parseXmlBody)(output.body, context)), "body");
  if (data.CommonPrefixes === "") {
    contents[_CP] = [];
  } else if (data[_CP] != null) {
    contents[_CP] = de_CommonPrefixList((0, import_smithy_client.getArrayIfSingleItem)(data[_CP]), context);
  }
  if (data.Contents === "") {
    contents[_Co] = [];
  } else if (data[_Co] != null) {
    contents[_Co] = de_ObjectList((0, import_smithy_client.getArrayIfSingleItem)(data[_Co]), context);
  }
  if (data[_D] != null) {
    contents[_D] = (0, import_smithy_client.expectString)(data[_D]);
  }
  if (data[_ET] != null) {
    contents[_ET] = (0, import_smithy_client.expectString)(data[_ET]);
  }
  if (data[_IT] != null) {
    contents[_IT] = (0, import_smithy_client.parseBoolean)(data[_IT]);
  }
  if (data[_M] != null) {
    contents[_M] = (0, import_smithy_client.expectString)(data[_M]);
  }
  if (data[_MK] != null) {
    contents[_MK] = (0, import_smithy_client.strictParseInt32)(data[_MK]);
  }
  if (data[_N] != null) {
    contents[_N] = (0, import_smithy_client.expectString)(data[_N]);
  }
  if (data[_NM] != null) {
    contents[_NM] = (0, import_smithy_client.expectString)(data[_NM]);
  }
  if (data[_P] != null) {
    contents[_P] = (0, import_smithy_client.expectString)(data[_P]);
  }
  return contents;
}, "de_ListObjectsCommand");
var de_ListObjectsV2Command = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output),
    [_RC]: [, output.headers[_xarc]]
  });
  const data = (0, import_smithy_client.expectNonNull)((0, import_smithy_client.expectObject)(await (0, import_core.parseXmlBody)(output.body, context)), "body");
  if (data.CommonPrefixes === "") {
    contents[_CP] = [];
  } else if (data[_CP] != null) {
    contents[_CP] = de_CommonPrefixList((0, import_smithy_client.getArrayIfSingleItem)(data[_CP]), context);
  }
  if (data.Contents === "") {
    contents[_Co] = [];
  } else if (data[_Co] != null) {
    contents[_Co] = de_ObjectList((0, import_smithy_client.getArrayIfSingleItem)(data[_Co]), context);
  }
  if (data[_CTon] != null) {
    contents[_CTon] = (0, import_smithy_client.expectString)(data[_CTon]);
  }
  if (data[_D] != null) {
    contents[_D] = (0, import_smithy_client.expectString)(data[_D]);
  }
  if (data[_ET] != null) {
    contents[_ET] = (0, import_smithy_client.expectString)(data[_ET]);
  }
  if (data[_IT] != null) {
    contents[_IT] = (0, import_smithy_client.parseBoolean)(data[_IT]);
  }
  if (data[_KC] != null) {
    contents[_KC] = (0, import_smithy_client.strictParseInt32)(data[_KC]);
  }
  if (data[_MK] != null) {
    contents[_MK] = (0, import_smithy_client.strictParseInt32)(data[_MK]);
  }
  if (data[_N] != null) {
    contents[_N] = (0, import_smithy_client.expectString)(data[_N]);
  }
  if (data[_NCT] != null) {
    contents[_NCT] = (0, import_smithy_client.expectString)(data[_NCT]);
  }
  if (data[_P] != null) {
    contents[_P] = (0, import_smithy_client.expectString)(data[_P]);
  }
  if (data[_SA] != null) {
    contents[_SA] = (0, import_smithy_client.expectString)(data[_SA]);
  }
  return contents;
}, "de_ListObjectsV2Command");
var de_ListObjectVersionsCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output),
    [_RC]: [, output.headers[_xarc]]
  });
  const data = (0, import_smithy_client.expectNonNull)((0, import_smithy_client.expectObject)(await (0, import_core.parseXmlBody)(output.body, context)), "body");
  if (data.CommonPrefixes === "") {
    contents[_CP] = [];
  } else if (data[_CP] != null) {
    contents[_CP] = de_CommonPrefixList((0, import_smithy_client.getArrayIfSingleItem)(data[_CP]), context);
  }
  if (data.DeleteMarker === "") {
    contents[_DMe] = [];
  } else if (data[_DM] != null) {
    contents[_DMe] = de_DeleteMarkers((0, import_smithy_client.getArrayIfSingleItem)(data[_DM]), context);
  }
  if (data[_D] != null) {
    contents[_D] = (0, import_smithy_client.expectString)(data[_D]);
  }
  if (data[_ET] != null) {
    contents[_ET] = (0, import_smithy_client.expectString)(data[_ET]);
  }
  if (data[_IT] != null) {
    contents[_IT] = (0, import_smithy_client.parseBoolean)(data[_IT]);
  }
  if (data[_KM] != null) {
    contents[_KM] = (0, import_smithy_client.expectString)(data[_KM]);
  }
  if (data[_MK] != null) {
    contents[_MK] = (0, import_smithy_client.strictParseInt32)(data[_MK]);
  }
  if (data[_N] != null) {
    contents[_N] = (0, import_smithy_client.expectString)(data[_N]);
  }
  if (data[_NKM] != null) {
    contents[_NKM] = (0, import_smithy_client.expectString)(data[_NKM]);
  }
  if (data[_NVIM] != null) {
    contents[_NVIM] = (0, import_smithy_client.expectString)(data[_NVIM]);
  }
  if (data[_P] != null) {
    contents[_P] = (0, import_smithy_client.expectString)(data[_P]);
  }
  if (data[_VIM] != null) {
    contents[_VIM] = (0, import_smithy_client.expectString)(data[_VIM]);
  }
  if (data.Version === "") {
    contents[_Ve] = [];
  } else if (data[_V] != null) {
    contents[_Ve] = de_ObjectVersionList((0, import_smithy_client.getArrayIfSingleItem)(data[_V]), context);
  }
  return contents;
}, "de_ListObjectVersionsCommand");
var de_ListPartsCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output),
    [_AD]: [
      () => void 0 !== output.headers[_xaad],
      () => (0, import_smithy_client.expectNonNull)((0, import_smithy_client.parseRfc7231DateTime)(output.headers[_xaad]))
    ],
    [_ARI]: [, output.headers[_xaari]],
    [_RC]: [, output.headers[_xarc]]
  });
  const data = (0, import_smithy_client.expectNonNull)((0, import_smithy_client.expectObject)(await (0, import_core.parseXmlBody)(output.body, context)), "body");
  if (data[_B] != null) {
    contents[_B] = (0, import_smithy_client.expectString)(data[_B]);
  }
  if (data[_CA] != null) {
    contents[_CA] = (0, import_smithy_client.expectString)(data[_CA]);
  }
  if (data[_CT] != null) {
    contents[_CT] = (0, import_smithy_client.expectString)(data[_CT]);
  }
  if (data[_In] != null) {
    contents[_In] = de_Initiator(data[_In], context);
  }
  if (data[_IT] != null) {
    contents[_IT] = (0, import_smithy_client.parseBoolean)(data[_IT]);
  }
  if (data[_K] != null) {
    contents[_K] = (0, import_smithy_client.expectString)(data[_K]);
  }
  if (data[_MP] != null) {
    contents[_MP] = (0, import_smithy_client.strictParseInt32)(data[_MP]);
  }
  if (data[_NPNM] != null) {
    contents[_NPNM] = (0, import_smithy_client.expectString)(data[_NPNM]);
  }
  if (data[_O] != null) {
    contents[_O] = de_Owner(data[_O], context);
  }
  if (data[_PNM] != null) {
    contents[_PNM] = (0, import_smithy_client.expectString)(data[_PNM]);
  }
  if (data.Part === "") {
    contents[_Part] = [];
  } else if (data[_Par] != null) {
    contents[_Part] = de_Parts((0, import_smithy_client.getArrayIfSingleItem)(data[_Par]), context);
  }
  if (data[_SC] != null) {
    contents[_SC] = (0, import_smithy_client.expectString)(data[_SC]);
  }
  if (data[_UI] != null) {
    contents[_UI] = (0, import_smithy_client.expectString)(data[_UI]);
  }
  return contents;
}, "de_ListPartsCommand");
var de_PutBucketAccelerateConfigurationCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  await (0, import_smithy_client.collectBody)(output.body, context);
  return contents;
}, "de_PutBucketAccelerateConfigurationCommand");
var de_PutBucketAclCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  await (0, import_smithy_client.collectBody)(output.body, context);
  return contents;
}, "de_PutBucketAclCommand");
var de_PutBucketAnalyticsConfigurationCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  await (0, import_smithy_client.collectBody)(output.body, context);
  return contents;
}, "de_PutBucketAnalyticsConfigurationCommand");
var de_PutBucketCorsCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  await (0, import_smithy_client.collectBody)(output.body, context);
  return contents;
}, "de_PutBucketCorsCommand");
var de_PutBucketEncryptionCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  await (0, import_smithy_client.collectBody)(output.body, context);
  return contents;
}, "de_PutBucketEncryptionCommand");
var de_PutBucketIntelligentTieringConfigurationCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  await (0, import_smithy_client.collectBody)(output.body, context);
  return contents;
}, "de_PutBucketIntelligentTieringConfigurationCommand");
var de_PutBucketInventoryConfigurationCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  await (0, import_smithy_client.collectBody)(output.body, context);
  return contents;
}, "de_PutBucketInventoryConfigurationCommand");
var de_PutBucketLifecycleConfigurationCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output),
    [_TDMOS]: [, output.headers[_xatdmos]]
  });
  await (0, import_smithy_client.collectBody)(output.body, context);
  return contents;
}, "de_PutBucketLifecycleConfigurationCommand");
var de_PutBucketLoggingCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  await (0, import_smithy_client.collectBody)(output.body, context);
  return contents;
}, "de_PutBucketLoggingCommand");
var de_PutBucketMetricsConfigurationCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  await (0, import_smithy_client.collectBody)(output.body, context);
  return contents;
}, "de_PutBucketMetricsConfigurationCommand");
var de_PutBucketNotificationConfigurationCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  await (0, import_smithy_client.collectBody)(output.body, context);
  return contents;
}, "de_PutBucketNotificationConfigurationCommand");
var de_PutBucketOwnershipControlsCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  await (0, import_smithy_client.collectBody)(output.body, context);
  return contents;
}, "de_PutBucketOwnershipControlsCommand");
var de_PutBucketPolicyCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  await (0, import_smithy_client.collectBody)(output.body, context);
  return contents;
}, "de_PutBucketPolicyCommand");
var de_PutBucketReplicationCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  await (0, import_smithy_client.collectBody)(output.body, context);
  return contents;
}, "de_PutBucketReplicationCommand");
var de_PutBucketRequestPaymentCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  await (0, import_smithy_client.collectBody)(output.body, context);
  return contents;
}, "de_PutBucketRequestPaymentCommand");
var de_PutBucketTaggingCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  await (0, import_smithy_client.collectBody)(output.body, context);
  return contents;
}, "de_PutBucketTaggingCommand");
var de_PutBucketVersioningCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  await (0, import_smithy_client.collectBody)(output.body, context);
  return contents;
}, "de_PutBucketVersioningCommand");
var de_PutBucketWebsiteCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  await (0, import_smithy_client.collectBody)(output.body, context);
  return contents;
}, "de_PutBucketWebsiteCommand");
var de_PutObjectCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output),
    [_Exp]: [, output.headers[_xae]],
    [_ETa]: [, output.headers[_eta]],
    [_CCRC]: [, output.headers[_xacc]],
    [_CCRCC]: [, output.headers[_xacc_]],
    [_CCRCNVME]: [, output.headers[_xacc__]],
    [_CSHA]: [, output.headers[_xacs]],
    [_CSHAh]: [, output.headers[_xacs_]],
    [_CT]: [, output.headers[_xact]],
    [_SSE]: [, output.headers[_xasse]],
    [_VI]: [, output.headers[_xavi]],
    [_SSECA]: [, output.headers[_xasseca]],
    [_SSECKMD]: [, output.headers[_xasseckm]],
    [_SSEKMSKI]: [, output.headers[_xasseakki]],
    [_SSEKMSEC]: [, output.headers[_xassec]],
    [_BKE]: [() => void 0 !== output.headers[_xassebke], () => (0, import_smithy_client.parseBoolean)(output.headers[_xassebke])],
    [_Si]: [() => void 0 !== output.headers[_xaos], () => (0, import_smithy_client.strictParseLong)(output.headers[_xaos])],
    [_RC]: [, output.headers[_xarc]]
  });
  await (0, import_smithy_client.collectBody)(output.body, context);
  return contents;
}, "de_PutObjectCommand");
var de_PutObjectAclCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output),
    [_RC]: [, output.headers[_xarc]]
  });
  await (0, import_smithy_client.collectBody)(output.body, context);
  return contents;
}, "de_PutObjectAclCommand");
var de_PutObjectLegalHoldCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output),
    [_RC]: [, output.headers[_xarc]]
  });
  await (0, import_smithy_client.collectBody)(output.body, context);
  return contents;
}, "de_PutObjectLegalHoldCommand");
var de_PutObjectLockConfigurationCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output),
    [_RC]: [, output.headers[_xarc]]
  });
  await (0, import_smithy_client.collectBody)(output.body, context);
  return contents;
}, "de_PutObjectLockConfigurationCommand");
var de_PutObjectRetentionCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output),
    [_RC]: [, output.headers[_xarc]]
  });
  await (0, import_smithy_client.collectBody)(output.body, context);
  return contents;
}, "de_PutObjectRetentionCommand");
var de_PutObjectTaggingCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output),
    [_VI]: [, output.headers[_xavi]]
  });
  await (0, import_smithy_client.collectBody)(output.body, context);
  return contents;
}, "de_PutObjectTaggingCommand");
var de_PutPublicAccessBlockCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  await (0, import_smithy_client.collectBody)(output.body, context);
  return contents;
}, "de_PutPublicAccessBlockCommand");
var de_RenameObjectCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  await (0, import_smithy_client.collectBody)(output.body, context);
  return contents;
}, "de_RenameObjectCommand");
var de_RestoreObjectCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output),
    [_RC]: [, output.headers[_xarc]],
    [_ROP]: [, output.headers[_xarop]]
  });
  await (0, import_smithy_client.collectBody)(output.body, context);
  return contents;
}, "de_RestoreObjectCommand");
var de_SelectObjectContentCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  const data = output.body;
  contents.Payload = de_SelectObjectContentEventStream(data, context);
  return contents;
}, "de_SelectObjectContentCommand");
var de_UpdateBucketMetadataInventoryTableConfigurationCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  await (0, import_smithy_client.collectBody)(output.body, context);
  return contents;
}, "de_UpdateBucketMetadataInventoryTableConfigurationCommand");
var de_UpdateBucketMetadataJournalTableConfigurationCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  await (0, import_smithy_client.collectBody)(output.body, context);
  return contents;
}, "de_UpdateBucketMetadataJournalTableConfigurationCommand");
var de_UploadPartCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output),
    [_SSE]: [, output.headers[_xasse]],
    [_ETa]: [, output.headers[_eta]],
    [_CCRC]: [, output.headers[_xacc]],
    [_CCRCC]: [, output.headers[_xacc_]],
    [_CCRCNVME]: [, output.headers[_xacc__]],
    [_CSHA]: [, output.headers[_xacs]],
    [_CSHAh]: [, output.headers[_xacs_]],
    [_SSECA]: [, output.headers[_xasseca]],
    [_SSECKMD]: [, output.headers[_xasseckm]],
    [_SSEKMSKI]: [, output.headers[_xasseakki]],
    [_BKE]: [() => void 0 !== output.headers[_xassebke], () => (0, import_smithy_client.parseBoolean)(output.headers[_xassebke])],
    [_RC]: [, output.headers[_xarc]]
  });
  await (0, import_smithy_client.collectBody)(output.body, context);
  return contents;
}, "de_UploadPartCommand");
var de_UploadPartCopyCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output),
    [_CSVI]: [, output.headers[_xacsvi]],
    [_SSE]: [, output.headers[_xasse]],
    [_SSECA]: [, output.headers[_xasseca]],
    [_SSECKMD]: [, output.headers[_xasseckm]],
    [_SSEKMSKI]: [, output.headers[_xasseakki]],
    [_BKE]: [() => void 0 !== output.headers[_xassebke], () => (0, import_smithy_client.parseBoolean)(output.headers[_xassebke])],
    [_RC]: [, output.headers[_xarc]]
  });
  const data = (0, import_smithy_client.expectObject)(await (0, import_core.parseXmlBody)(output.body, context));
  contents.CopyPartResult = de_CopyPartResult(data, context);
  return contents;
}, "de_UploadPartCopyCommand");
var de_WriteGetObjectResponseCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  await (0, import_smithy_client.collectBody)(output.body, context);
  return contents;
}, "de_WriteGetObjectResponseCommand");
var de_CommandError = /* @__PURE__ */ __name(async (output, context) => {
  const parsedOutput = {
    ...output,
    body: await (0, import_core.parseXmlErrorBody)(output.body, context)
  };
  const errorCode = (0, import_core.loadRestXmlErrorCode)(output, parsedOutput.body);
  switch (errorCode) {
    case "NoSuchUpload":
    case "com.amazonaws.s3#NoSuchUpload":
      throw await de_NoSuchUploadRes(parsedOutput, context);
    case "ObjectNotInActiveTierError":
    case "com.amazonaws.s3#ObjectNotInActiveTierError":
      throw await de_ObjectNotInActiveTierErrorRes(parsedOutput, context);
    case "BucketAlreadyExists":
    case "com.amazonaws.s3#BucketAlreadyExists":
      throw await de_BucketAlreadyExistsRes(parsedOutput, context);
    case "BucketAlreadyOwnedByYou":
    case "com.amazonaws.s3#BucketAlreadyOwnedByYou":
      throw await de_BucketAlreadyOwnedByYouRes(parsedOutput, context);
    case "NoSuchBucket":
    case "com.amazonaws.s3#NoSuchBucket":
      throw await de_NoSuchBucketRes(parsedOutput, context);
    case "InvalidObjectState":
    case "com.amazonaws.s3#InvalidObjectState":
      throw await de_InvalidObjectStateRes(parsedOutput, context);
    case "NoSuchKey":
    case "com.amazonaws.s3#NoSuchKey":
      throw await de_NoSuchKeyRes(parsedOutput, context);
    case "NotFound":
    case "com.amazonaws.s3#NotFound":
      throw await de_NotFoundRes(parsedOutput, context);
    case "EncryptionTypeMismatch":
    case "com.amazonaws.s3#EncryptionTypeMismatch":
      throw await de_EncryptionTypeMismatchRes(parsedOutput, context);
    case "InvalidRequest":
    case "com.amazonaws.s3#InvalidRequest":
      throw await de_InvalidRequestRes(parsedOutput, context);
    case "InvalidWriteOffset":
    case "com.amazonaws.s3#InvalidWriteOffset":
      throw await de_InvalidWriteOffsetRes(parsedOutput, context);
    case "TooManyParts":
    case "com.amazonaws.s3#TooManyParts":
      throw await de_TooManyPartsRes(parsedOutput, context);
    case "IdempotencyParameterMismatch":
    case "com.amazonaws.s3#IdempotencyParameterMismatch":
      throw await de_IdempotencyParameterMismatchRes(parsedOutput, context);
    case "ObjectAlreadyInActiveTierError":
    case "com.amazonaws.s3#ObjectAlreadyInActiveTierError":
      throw await de_ObjectAlreadyInActiveTierErrorRes(parsedOutput, context);
    default:
      const parsedBody = parsedOutput.body;
      return throwDefaultError({
        output,
        parsedBody,
        errorCode
      });
  }
}, "de_CommandError");
var throwDefaultError = (0, import_smithy_client.withBaseException)(S3ServiceException);
var de_BucketAlreadyExistsRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const contents = (0, import_smithy_client.map)({});
  const data = parsedOutput.body;
  const exception = new BucketAlreadyExists({
    $metadata: deserializeMetadata(parsedOutput),
    ...contents
  });
  return (0, import_smithy_client.decorateServiceException)(exception, parsedOutput.body);
}, "de_BucketAlreadyExistsRes");
var de_BucketAlreadyOwnedByYouRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const contents = (0, import_smithy_client.map)({});
  const data = parsedOutput.body;
  const exception = new BucketAlreadyOwnedByYou({
    $metadata: deserializeMetadata(parsedOutput),
    ...contents
  });
  return (0, import_smithy_client.decorateServiceException)(exception, parsedOutput.body);
}, "de_BucketAlreadyOwnedByYouRes");
var de_EncryptionTypeMismatchRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const contents = (0, import_smithy_client.map)({});
  const data = parsedOutput.body;
  const exception = new EncryptionTypeMismatch({
    $metadata: deserializeMetadata(parsedOutput),
    ...contents
  });
  return (0, import_smithy_client.decorateServiceException)(exception, parsedOutput.body);
}, "de_EncryptionTypeMismatchRes");
var de_IdempotencyParameterMismatchRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const contents = (0, import_smithy_client.map)({});
  const data = parsedOutput.body;
  const exception = new IdempotencyParameterMismatch({
    $metadata: deserializeMetadata(parsedOutput),
    ...contents
  });
  return (0, import_smithy_client.decorateServiceException)(exception, parsedOutput.body);
}, "de_IdempotencyParameterMismatchRes");
var de_InvalidObjectStateRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const contents = (0, import_smithy_client.map)({});
  const data = parsedOutput.body;
  if (data[_AT] != null) {
    contents[_AT] = (0, import_smithy_client.expectString)(data[_AT]);
  }
  if (data[_SC] != null) {
    contents[_SC] = (0, import_smithy_client.expectString)(data[_SC]);
  }
  const exception = new InvalidObjectState({
    $metadata: deserializeMetadata(parsedOutput),
    ...contents
  });
  return (0, import_smithy_client.decorateServiceException)(exception, parsedOutput.body);
}, "de_InvalidObjectStateRes");
var de_InvalidRequestRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const contents = (0, import_smithy_client.map)({});
  const data = parsedOutput.body;
  const exception = new InvalidRequest({
    $metadata: deserializeMetadata(parsedOutput),
    ...contents
  });
  return (0, import_smithy_client.decorateServiceException)(exception, parsedOutput.body);
}, "de_InvalidRequestRes");
var de_InvalidWriteOffsetRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const contents = (0, import_smithy_client.map)({});
  const data = parsedOutput.body;
  const exception = new InvalidWriteOffset({
    $metadata: deserializeMetadata(parsedOutput),
    ...contents
  });
  return (0, import_smithy_client.decorateServiceException)(exception, parsedOutput.body);
}, "de_InvalidWriteOffsetRes");
var de_NoSuchBucketRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const contents = (0, import_smithy_client.map)({});
  const data = parsedOutput.body;
  const exception = new NoSuchBucket({
    $metadata: deserializeMetadata(parsedOutput),
    ...contents
  });
  return (0, import_smithy_client.decorateServiceException)(exception, parsedOutput.body);
}, "de_NoSuchBucketRes");
var de_NoSuchKeyRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const contents = (0, import_smithy_client.map)({});
  const data = parsedOutput.body;
  const exception = new NoSuchKey({
    $metadata: deserializeMetadata(parsedOutput),
    ...contents
  });
  return (0, import_smithy_client.decorateServiceException)(exception, parsedOutput.body);
}, "de_NoSuchKeyRes");
var de_NoSuchUploadRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const contents = (0, import_smithy_client.map)({});
  const data = parsedOutput.body;
  const exception = new NoSuchUpload({
    $metadata: deserializeMetadata(parsedOutput),
    ...contents
  });
  return (0, import_smithy_client.decorateServiceException)(exception, parsedOutput.body);
}, "de_NoSuchUploadRes");
var de_NotFoundRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const contents = (0, import_smithy_client.map)({});
  const data = parsedOutput.body;
  const exception = new NotFound({
    $metadata: deserializeMetadata(parsedOutput),
    ...contents
  });
  return (0, import_smithy_client.decorateServiceException)(exception, parsedOutput.body);
}, "de_NotFoundRes");
var de_ObjectAlreadyInActiveTierErrorRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const contents = (0, import_smithy_client.map)({});
  const data = parsedOutput.body;
  const exception = new ObjectAlreadyInActiveTierError({
    $metadata: deserializeMetadata(parsedOutput),
    ...contents
  });
  return (0, import_smithy_client.decorateServiceException)(exception, parsedOutput.body);
}, "de_ObjectAlreadyInActiveTierErrorRes");
var de_ObjectNotInActiveTierErrorRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const contents = (0, import_smithy_client.map)({});
  const data = parsedOutput.body;
  const exception = new ObjectNotInActiveTierError({
    $metadata: deserializeMetadata(parsedOutput),
    ...contents
  });
  return (0, import_smithy_client.decorateServiceException)(exception, parsedOutput.body);
}, "de_ObjectNotInActiveTierErrorRes");
var de_TooManyPartsRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const contents = (0, import_smithy_client.map)({});
  const data = parsedOutput.body;
  const exception = new TooManyParts({
    $metadata: deserializeMetadata(parsedOutput),
    ...contents
  });
  return (0, import_smithy_client.decorateServiceException)(exception, parsedOutput.body);
}, "de_TooManyPartsRes");
var de_SelectObjectContentEventStream = /* @__PURE__ */ __name((output, context) => {
  return context.eventStreamMarshaller.deserialize(output, async (event) => {
    if (event["Records"] != null) {
      return {
        Records: await de_RecordsEvent_event(event["Records"], context)
      };
    }
    if (event["Stats"] != null) {
      return {
        Stats: await de_StatsEvent_event(event["Stats"], context)
      };
    }
    if (event["Progress"] != null) {
      return {
        Progress: await de_ProgressEvent_event(event["Progress"], context)
      };
    }
    if (event["Cont"] != null) {
      return {
        Cont: await de_ContinuationEvent_event(event["Cont"], context)
      };
    }
    if (event["End"] != null) {
      return {
        End: await de_EndEvent_event(event["End"], context)
      };
    }
    return { $unknown: event };
  });
}, "de_SelectObjectContentEventStream");
var de_ContinuationEvent_event = /* @__PURE__ */ __name(async (output, context) => {
  const contents = {};
  const data = await (0, import_core.parseXmlBody)(output.body, context);
  Object.assign(contents, de_ContinuationEvent(data, context));
  return contents;
}, "de_ContinuationEvent_event");
var de_EndEvent_event = /* @__PURE__ */ __name(async (output, context) => {
  const contents = {};
  const data = await (0, import_core.parseXmlBody)(output.body, context);
  Object.assign(contents, de_EndEvent(data, context));
  return contents;
}, "de_EndEvent_event");
var de_ProgressEvent_event = /* @__PURE__ */ __name(async (output, context) => {
  const contents = {};
  const data = await (0, import_core.parseXmlBody)(output.body, context);
  contents.Details = de_Progress(data, context);
  return contents;
}, "de_ProgressEvent_event");
var de_RecordsEvent_event = /* @__PURE__ */ __name(async (output, context) => {
  const contents = {};
  contents.Payload = output.body;
  return contents;
}, "de_RecordsEvent_event");
var de_StatsEvent_event = /* @__PURE__ */ __name(async (output, context) => {
  const contents = {};
  const data = await (0, import_core.parseXmlBody)(output.body, context);
  contents.Details = de_Stats(data, context);
  return contents;
}, "de_StatsEvent_event");
var se_AbortIncompleteMultipartUpload = /* @__PURE__ */ __name((input, context) => {
  const bn = new import_xml_builder.XmlNode(_AIMU);
  if (input[_DAI] != null) {
    bn.c(import_xml_builder.XmlNode.of(_DAI, String(input[_DAI])).n(_DAI));
  }
  return bn;
}, "se_AbortIncompleteMultipartUpload");
var se_AccelerateConfiguration = /* @__PURE__ */ __name((input, context) => {
  const bn = new import_xml_builder.XmlNode(_ACc);
  if (input[_S] != null) {
    bn.c(import_xml_builder.XmlNode.of(_BAS, input[_S]).n(_S));
  }
  return bn;
}, "se_AccelerateConfiguration");
var se_AccessControlPolicy = /* @__PURE__ */ __name((input, context) => {
  const bn = new import_xml_builder.XmlNode(_ACP);
  bn.lc(input, "Grants", "AccessControlList", () => se_Grants(input[_Gr], context));
  if (input[_O] != null) {
    bn.c(se_Owner(input[_O], context).n(_O));
  }
  return bn;
}, "se_AccessControlPolicy");
var se_AccessControlTranslation = /* @__PURE__ */ __name((input, context) => {
  const bn = new import_xml_builder.XmlNode(_ACT);
  if (input[_O] != null) {
    bn.c(import_xml_builder.XmlNode.of(_OOw, input[_O]).n(_O));
  }
  return bn;
}, "se_AccessControlTranslation");
var se_AllowedHeaders = /* @__PURE__ */ __name((input, context) => {
  return input.filter((e) => e != null).map((entry) => {
    const n = import_xml_builder.XmlNode.of(_AH, entry);
    return n.n(_me);
  });
}, "se_AllowedHeaders");
var se_AllowedMethods = /* @__PURE__ */ __name((input, context) => {
  return input.filter((e) => e != null).map((entry) => {
    const n = import_xml_builder.XmlNode.of(_AM, entry);
    return n.n(_me);
  });
}, "se_AllowedMethods");
var se_AllowedOrigins = /* @__PURE__ */ __name((input, context) => {
  return input.filter((e) => e != null).map((entry) => {
    const n = import_xml_builder.XmlNode.of(_AO, entry);
    return n.n(_me);
  });
}, "se_AllowedOrigins");
var se_AnalyticsAndOperator = /* @__PURE__ */ __name((input, context) => {
  const bn = new import_xml_builder.XmlNode(_AAO);
  bn.cc(input, _P);
  bn.l(input, "Tags", "Tag", () => se_TagSet(input[_Tag], context));
  return bn;
}, "se_AnalyticsAndOperator");
var se_AnalyticsConfiguration = /* @__PURE__ */ __name((input, context) => {
  const bn = new import_xml_builder.XmlNode(_AC);
  if (input[_I] != null) {
    bn.c(import_xml_builder.XmlNode.of(_AI, input[_I]).n(_I));
  }
  if (input[_F] != null) {
    bn.c(se_AnalyticsFilter(input[_F], context).n(_F));
  }
  if (input[_SCA] != null) {
    bn.c(se_StorageClassAnalysis(input[_SCA], context).n(_SCA));
  }
  return bn;
}, "se_AnalyticsConfiguration");
var se_AnalyticsExportDestination = /* @__PURE__ */ __name((input, context) => {
  const bn = new import_xml_builder.XmlNode(_AED);
  if (input[_SBD] != null) {
    bn.c(se_AnalyticsS3BucketDestination(input[_SBD], context).n(_SBD));
  }
  return bn;
}, "se_AnalyticsExportDestination");
var se_AnalyticsFilter = /* @__PURE__ */ __name((input, context) => {
  const bn = new import_xml_builder.XmlNode(_AF);
  AnalyticsFilter.visit(input, {
    Prefix: /* @__PURE__ */ __name((value) => {
      if (input[_P] != null) {
        bn.c(import_xml_builder.XmlNode.of(_P, value).n(_P));
      }
    }, "Prefix"),
    Tag: /* @__PURE__ */ __name((value) => {
      if (input[_Ta] != null) {
        bn.c(se_Tag(value, context).n(_Ta));
      }
    }, "Tag"),
    And: /* @__PURE__ */ __name((value) => {
      if (input[_A] != null) {
        bn.c(se_AnalyticsAndOperator(value, context).n(_A));
      }
    }, "And"),
    _: /* @__PURE__ */ __name((name, value) => {
      if (!(value instanceof import_xml_builder.XmlNode || value instanceof import_xml_builder.XmlText)) {
        throw new Error("Unable to serialize unknown union members in XML.");
      }
      bn.c(new import_xml_builder.XmlNode(name).c(value));
    }, "_")
  });
  return bn;
}, "se_AnalyticsFilter");
var se_AnalyticsS3BucketDestination = /* @__PURE__ */ __name((input, context) => {
  const bn = new import_xml_builder.XmlNode(_ASBD);
  if (input[_Fo] != null) {
    bn.c(import_xml_builder.XmlNode.of(_ASEFF, input[_Fo]).n(_Fo));
  }
  if (input[_BAI] != null) {
    bn.c(import_xml_builder.XmlNode.of(_AIc, input[_BAI]).n(_BAI));
  }
  if (input[_B] != null) {
    bn.c(import_xml_builder.XmlNode.of(_BN, input[_B]).n(_B));
  }
  bn.cc(input, _P);
  return bn;
}, "se_AnalyticsS3BucketDestination");
var se_BucketInfo = /* @__PURE__ */ __name((input, context) => {
  const bn = new import_xml_builder.XmlNode(_BI);
  bn.cc(input, _DR);
  if (input[_Ty] != null) {
    bn.c(import_xml_builder.XmlNode.of(_BT, input[_Ty]).n(_Ty));
  }
  return bn;
}, "se_BucketInfo");
var se_BucketLifecycleConfiguration = /* @__PURE__ */ __name((input, context) => {
  const bn = new import_xml_builder.XmlNode(_BLC);
  bn.l(input, "Rules", "Rule", () => se_LifecycleRules(input[_Rul], context));
  return bn;
}, "se_BucketLifecycleConfiguration");
var se_BucketLoggingStatus = /* @__PURE__ */ __name((input, context) => {
  const bn = new import_xml_builder.XmlNode(_BLS);
  if (input[_LE] != null) {
    bn.c(se_LoggingEnabled(input[_LE], context).n(_LE));
  }
  return bn;
}, "se_BucketLoggingStatus");
var se_CompletedMultipartUpload = /* @__PURE__ */ __name((input, context) => {
  const bn = new import_xml_builder.XmlNode(_CMU);
  bn.l(input, "Parts", "Part", () => se_CompletedPartList(input[_Part], context));
  return bn;
}, "se_CompletedMultipartUpload");
var se_CompletedPart = /* @__PURE__ */ __name((input, context) => {
  const bn = new import_xml_builder.XmlNode(_CPo);
  bn.cc(input, _ETa);
  bn.cc(input, _CCRC);
  bn.cc(input, _CCRCC);
  bn.cc(input, _CCRCNVME);
  bn.cc(input, _CSHA);
  bn.cc(input, _CSHAh);
  if (input[_PN] != null) {
    bn.c(import_xml_builder.XmlNode.of(_PN, String(input[_PN])).n(_PN));
  }
  return bn;
}, "se_CompletedPart");
var se_CompletedPartList = /* @__PURE__ */ __name((input, context) => {
  return input.filter((e) => e != null).map((entry) => {
    const n = se_CompletedPart(entry, context);
    return n.n(_me);
  });
}, "se_CompletedPartList");
var se_Condition = /* @__PURE__ */ __name((input, context) => {
  const bn = new import_xml_builder.XmlNode(_Con);
  bn.cc(input, _HECRE);
  bn.cc(input, _KPE);
  return bn;
}, "se_Condition");
var se_CORSConfiguration = /* @__PURE__ */ __name((input, context) => {
  const bn = new import_xml_builder.XmlNode(_CORSC);
  bn.l(input, "CORSRules", "CORSRule", () => se_CORSRules(input[_CORSRu], context));
  return bn;
}, "se_CORSConfiguration");
var se_CORSRule = /* @__PURE__ */ __name((input, context) => {
  const bn = new import_xml_builder.XmlNode(_CORSR);
  bn.cc(input, _ID_);
  bn.l(input, "AllowedHeaders", "AllowedHeader", () => se_AllowedHeaders(input[_AHl], context));
  bn.l(input, "AllowedMethods", "AllowedMethod", () => se_AllowedMethods(input[_AMl], context));
  bn.l(input, "AllowedOrigins", "AllowedOrigin", () => se_AllowedOrigins(input[_AOl], context));
  bn.l(input, "ExposeHeaders", "ExposeHeader", () => se_ExposeHeaders(input[_EH], context));
  if (input[_MAS] != null) {
    bn.c(import_xml_builder.XmlNode.of(_MAS, String(input[_MAS])).n(_MAS));
  }
  return bn;
}, "se_CORSRule");
var se_CORSRules = /* @__PURE__ */ __name((input, context) => {
  return input.filter((e) => e != null).map((entry) => {
    const n = se_CORSRule(entry, context);
    return n.n(_me);
  });
}, "se_CORSRules");
var se_CreateBucketConfiguration = /* @__PURE__ */ __name((input, context) => {
  const bn = new import_xml_builder.XmlNode(_CBC);
  if (input[_LC] != null) {
    bn.c(import_xml_builder.XmlNode.of(_BLCu, input[_LC]).n(_LC));
  }
  if (input[_L] != null) {
    bn.c(se_LocationInfo(input[_L], context).n(_L));
  }
  if (input[_B] != null) {
    bn.c(se_BucketInfo(input[_B], context).n(_B));
  }
  bn.lc(input, "Tags", "Tags", () => se_TagSet(input[_Tag], context));
  return bn;
}, "se_CreateBucketConfiguration");
var se_CSVInput = /* @__PURE__ */ __name((input, context) => {
  const bn = new import_xml_builder.XmlNode(_CSVIn);
  bn.cc(input, _FHI);
  bn.cc(input, _Com);
  bn.cc(input, _QEC);
  bn.cc(input, _RD);
  bn.cc(input, _FD);
  bn.cc(input, _QCuo);
  if (input[_AQRD] != null) {
    bn.c(import_xml_builder.XmlNode.of(_AQRD, String(input[_AQRD])).n(_AQRD));
  }
  return bn;
}, "se_CSVInput");
var se_CSVOutput = /* @__PURE__ */ __name((input, context) => {
  const bn = new import_xml_builder.XmlNode(_CSVO);
  bn.cc(input, _QF);
  bn.cc(input, _QEC);
  bn.cc(input, _RD);
  bn.cc(input, _FD);
  bn.cc(input, _QCuo);
  return bn;
}, "se_CSVOutput");
var se_DefaultRetention = /* @__PURE__ */ __name((input, context) => {
  const bn = new import_xml_builder.XmlNode(_DRe);
  if (input[_Mo] != null) {
    bn.c(import_xml_builder.XmlNode.of(_OLRM, input[_Mo]).n(_Mo));
  }
  if (input[_Da] != null) {
    bn.c(import_xml_builder.XmlNode.of(_Da, String(input[_Da])).n(_Da));
  }
  if (input[_Y] != null) {
    bn.c(import_xml_builder.XmlNode.of(_Y, String(input[_Y])).n(_Y));
  }
  return bn;
}, "se_DefaultRetention");
var se_Delete = /* @__PURE__ */ __name((input, context) => {
  const bn = new import_xml_builder.XmlNode(_Del);
  bn.l(input, "Objects", "Object", () => se_ObjectIdentifierList(input[_Ob], context));
  if (input[_Q] != null) {
    bn.c(import_xml_builder.XmlNode.of(_Q, String(input[_Q])).n(_Q));
  }
  return bn;
}, "se_Delete");
var se_DeleteMarkerReplication = /* @__PURE__ */ __name((input, context) => {
  const bn = new import_xml_builder.XmlNode(_DMR);
  if (input[_S] != null) {
    bn.c(import_xml_builder.XmlNode.of(_DMRS, input[_S]).n(_S));
  }
  return bn;
}, "se_DeleteMarkerReplication");
var se_Destination = /* @__PURE__ */ __name((input, context) => {
  const bn = new import_xml_builder.XmlNode(_Des);
  if (input[_B] != null) {
    bn.c(import_xml_builder.XmlNode.of(_BN, input[_B]).n(_B));
  }
  if (input[_Ac] != null) {
    bn.c(import_xml_builder.XmlNode.of(_AIc, input[_Ac]).n(_Ac));
  }
  bn.cc(input, _SC);
  if (input[_ACT] != null) {
    bn.c(se_AccessControlTranslation(input[_ACT], context).n(_ACT));
  }
  if (input[_ECn] != null) {
    bn.c(se_EncryptionConfiguration(input[_ECn], context).n(_ECn));
  }
  if (input[_RTe] != null) {
    bn.c(se_ReplicationTime(input[_RTe], context).n(_RTe));
  }
  if (input[_Me] != null) {
    bn.c(se_Metrics(input[_Me], context).n(_Me));
  }
  return bn;
}, "se_Destination");
var se_Encryption = /* @__PURE__ */ __name((input, context) => {
  const bn = new import_xml_builder.XmlNode(_En);
  if (input[_ETn] != null) {
    bn.c(import_xml_builder.XmlNode.of(_SSE, input[_ETn]).n(_ETn));
  }
  if (input[_KMSKI] != null) {
    bn.c(import_xml_builder.XmlNode.of(_SSEKMSKI, input[_KMSKI]).n(_KMSKI));
  }
  bn.cc(input, _KMSC);
  return bn;
}, "se_Encryption");
var se_EncryptionConfiguration = /* @__PURE__ */ __name((input, context) => {
  const bn = new import_xml_builder.XmlNode(_ECn);
  bn.cc(input, _RKKID);
  return bn;
}, "se_EncryptionConfiguration");
var se_ErrorDocument = /* @__PURE__ */ __name((input, context) => {
  const bn = new import_xml_builder.XmlNode(_ED);
  if (input[_K] != null) {
    bn.c(import_xml_builder.XmlNode.of(_OK, input[_K]).n(_K));
  }
  return bn;
}, "se_ErrorDocument");
var se_EventBridgeConfiguration = /* @__PURE__ */ __name((input, context) => {
  const bn = new import_xml_builder.XmlNode(_EBC);
  return bn;
}, "se_EventBridgeConfiguration");
var se_EventList = /* @__PURE__ */ __name((input, context) => {
  return input.filter((e) => e != null).map((entry) => {
    const n = import_xml_builder.XmlNode.of(_Ev, entry);
    return n.n(_me);
  });
}, "se_EventList");
var se_ExistingObjectReplication = /* @__PURE__ */ __name((input, context) => {
  const bn = new import_xml_builder.XmlNode(_EOR);
  if (input[_S] != null) {
    bn.c(import_xml_builder.XmlNode.of(_EORS, input[_S]).n(_S));
  }
  return bn;
}, "se_ExistingObjectReplication");
var se_ExposeHeaders = /* @__PURE__ */ __name((input, context) => {
  return input.filter((e) => e != null).map((entry) => {
    const n = import_xml_builder.XmlNode.of(_EHx, entry);
    return n.n(_me);
  });
}, "se_ExposeHeaders");
var se_FilterRule = /* @__PURE__ */ __name((input, context) => {
  const bn = new import_xml_builder.XmlNode(_FR);
  if (input[_N] != null) {
    bn.c(import_xml_builder.XmlNode.of(_FRN, input[_N]).n(_N));
  }
  if (input[_Va] != null) {
    bn.c(import_xml_builder.XmlNode.of(_FRV, input[_Va]).n(_Va));
  }
  return bn;
}, "se_FilterRule");
var se_FilterRuleList = /* @__PURE__ */ __name((input, context) => {
  return input.filter((e) => e != null).map((entry) => {
    const n = se_FilterRule(entry, context);
    return n.n(_me);
  });
}, "se_FilterRuleList");
var se_GlacierJobParameters = /* @__PURE__ */ __name((input, context) => {
  const bn = new import_xml_builder.XmlNode(_GJP);
  bn.cc(input, _Ti);
  return bn;
}, "se_GlacierJobParameters");
var se_Grant = /* @__PURE__ */ __name((input, context) => {
  const bn = new import_xml_builder.XmlNode(_G);
  if (input[_Gra] != null) {
    const n = se_Grantee(input[_Gra], context).n(_Gra);
    n.a("xmlns:xsi", "http://www.w3.org/2001/XMLSchema-instance");
    bn.c(n);
  }
  bn.cc(input, _Pe);
  return bn;
}, "se_Grant");
var se_Grantee = /* @__PURE__ */ __name((input, context) => {
  const bn = new import_xml_builder.XmlNode(_Gra);
  bn.cc(input, _DN);
  bn.cc(input, _EA);
  bn.cc(input, _ID_);
  bn.cc(input, _URI);
  bn.a("xsi:type", input[_Ty]);
  return bn;
}, "se_Grantee");
var se_Grants = /* @__PURE__ */ __name((input, context) => {
  return input.filter((e) => e != null).map((entry) => {
    const n = se_Grant(entry, context);
    return n.n(_G);
  });
}, "se_Grants");
var se_IndexDocument = /* @__PURE__ */ __name((input, context) => {
  const bn = new import_xml_builder.XmlNode(_ID);
  bn.cc(input, _Su);
  return bn;
}, "se_IndexDocument");
var se_InputSerialization = /* @__PURE__ */ __name((input, context) => {
  const bn = new import_xml_builder.XmlNode(_IS);
  if (input[_CSV] != null) {
    bn.c(se_CSVInput(input[_CSV], context).n(_CSV));
  }
  bn.cc(input, _CTom);
  if (input[_JSON] != null) {
    bn.c(se_JSONInput(input[_JSON], context).n(_JSON));
  }
  if (input[_Parq] != null) {
    bn.c(se_ParquetInput(input[_Parq], context).n(_Parq));
  }
  return bn;
}, "se_InputSerialization");
var se_IntelligentTieringAndOperator = /* @__PURE__ */ __name((input, context) => {
  const bn = new import_xml_builder.XmlNode(_ITAO);
  bn.cc(input, _P);
  bn.l(input, "Tags", "Tag", () => se_TagSet(input[_Tag], context));
  return bn;
}, "se_IntelligentTieringAndOperator");
var se_IntelligentTieringConfiguration = /* @__PURE__ */ __name((input, context) => {
  const bn = new import_xml_builder.XmlNode(_ITC);
  if (input[_I] != null) {
    bn.c(import_xml_builder.XmlNode.of(_ITI, input[_I]).n(_I));
  }
  if (input[_F] != null) {
    bn.c(se_IntelligentTieringFilter(input[_F], context).n(_F));
  }
  if (input[_S] != null) {
    bn.c(import_xml_builder.XmlNode.of(_ITS, input[_S]).n(_S));
  }
  bn.l(input, "Tierings", "Tiering", () => se_TieringList(input[_Tie], context));
  return bn;
}, "se_IntelligentTieringConfiguration");
var se_IntelligentTieringFilter = /* @__PURE__ */ __name((input, context) => {
  const bn = new import_xml_builder.XmlNode(_ITF);
  bn.cc(input, _P);
  if (input[_Ta] != null) {
    bn.c(se_Tag(input[_Ta], context).n(_Ta));
  }
  if (input[_A] != null) {
    bn.c(se_IntelligentTieringAndOperator(input[_A], context).n(_A));
  }
  return bn;
}, "se_IntelligentTieringFilter");
var se_InventoryConfiguration = /* @__PURE__ */ __name((input, context) => {
  const bn = new import_xml_builder.XmlNode(_IC);
  if (input[_Des] != null) {
    bn.c(se_InventoryDestination(input[_Des], context).n(_Des));
  }
  if (input[_IE] != null) {
    bn.c(import_xml_builder.XmlNode.of(_IE, String(input[_IE])).n(_IE));
  }
  if (input[_F] != null) {
    bn.c(se_InventoryFilter(input[_F], context).n(_F));
  }
  if (input[_I] != null) {
    bn.c(import_xml_builder.XmlNode.of(_II, input[_I]).n(_I));
  }
  if (input[_IOV] != null) {
    bn.c(import_xml_builder.XmlNode.of(_IIOV, input[_IOV]).n(_IOV));
  }
  bn.lc(input, "OptionalFields", "OptionalFields", () => se_InventoryOptionalFields(input[_OF], context));
  if (input[_Sc] != null) {
    bn.c(se_InventorySchedule(input[_Sc], context).n(_Sc));
  }
  return bn;
}, "se_InventoryConfiguration");
var se_InventoryDestination = /* @__PURE__ */ __name((input, context) => {
  const bn = new import_xml_builder.XmlNode(_IDn);
  if (input[_SBD] != null) {
    bn.c(se_InventoryS3BucketDestination(input[_SBD], context).n(_SBD));
  }
  return bn;
}, "se_InventoryDestination");
var se_InventoryEncryption = /* @__PURE__ */ __name((input, context) => {
  const bn = new import_xml_builder.XmlNode(_IEn);
  if (input[_SSES] != null) {
    bn.c(se_SSES3(input[_SSES], context).n(_SS));
  }
  if (input[_SSEKMS] != null) {
    bn.c(se_SSEKMS(input[_SSEKMS], context).n(_SK));
  }
  return bn;
}, "se_InventoryEncryption");
var se_InventoryFilter = /* @__PURE__ */ __name((input, context) => {
  const bn = new import_xml_builder.XmlNode(_IF);
  bn.cc(input, _P);
  return bn;
}, "se_InventoryFilter");
var se_InventoryOptionalFields = /* @__PURE__ */ __name((input, context) => {
  return input.filter((e) => e != null).map((entry) => {
    const n = import_xml_builder.XmlNode.of(_IOF, entry);
    return n.n(_Fi);
  });
}, "se_InventoryOptionalFields");
var se_InventoryS3BucketDestination = /* @__PURE__ */ __name((input, context) => {
  const bn = new import_xml_builder.XmlNode(_ISBD);
  bn.cc(input, _AIc);
  if (input[_B] != null) {
    bn.c(import_xml_builder.XmlNode.of(_BN, input[_B]).n(_B));
  }
  if (input[_Fo] != null) {
    bn.c(import_xml_builder.XmlNode.of(_IFn, input[_Fo]).n(_Fo));
  }
  bn.cc(input, _P);
  if (input[_En] != null) {
    bn.c(se_InventoryEncryption(input[_En], context).n(_En));
  }
  return bn;
}, "se_InventoryS3BucketDestination");
var se_InventorySchedule = /* @__PURE__ */ __name((input, context) => {
  const bn = new import_xml_builder.XmlNode(_ISn);
  if (input[_Fr] != null) {
    bn.c(import_xml_builder.XmlNode.of(_IFnv, input[_Fr]).n(_Fr));
  }
  return bn;
}, "se_InventorySchedule");
var se_InventoryTableConfiguration = /* @__PURE__ */ __name((input, context) => {
  const bn = new import_xml_builder.XmlNode(_ITCn);
  if (input[_CSo] != null) {
    bn.c(import_xml_builder.XmlNode.of(_ICS, input[_CSo]).n(_CSo));
  }
  if (input[_ECn] != null) {
    bn.c(se_MetadataTableEncryptionConfiguration(input[_ECn], context).n(_ECn));
  }
  return bn;
}, "se_InventoryTableConfiguration");
var se_InventoryTableConfigurationUpdates = /* @__PURE__ */ __name((input, context) => {
  const bn = new import_xml_builder.XmlNode(_ITCU);
  if (input[_CSo] != null) {
    bn.c(import_xml_builder.XmlNode.of(_ICS, input[_CSo]).n(_CSo));
  }
  if (input[_ECn] != null) {
    bn.c(se_MetadataTableEncryptionConfiguration(input[_ECn], context).n(_ECn));
  }
  return bn;
}, "se_InventoryTableConfigurationUpdates");
var se_JournalTableConfiguration = /* @__PURE__ */ __name((input, context) => {
  const bn = new import_xml_builder.XmlNode(_JTC);
  if (input[_REe] != null) {
    bn.c(se_RecordExpiration(input[_REe], context).n(_REe));
  }
  if (input[_ECn] != null) {
    bn.c(se_MetadataTableEncryptionConfiguration(input[_ECn], context).n(_ECn));
  }
  return bn;
}, "se_JournalTableConfiguration");
var se_JournalTableConfigurationUpdates = /* @__PURE__ */ __name((input, context) => {
  const bn = new import_xml_builder.XmlNode(_JTCU);
  if (input[_REe] != null) {
    bn.c(se_RecordExpiration(input[_REe], context).n(_REe));
  }
  return bn;
}, "se_JournalTableConfigurationUpdates");
var se_JSONInput = /* @__PURE__ */ __name((input, context) => {
  const bn = new import_xml_builder.XmlNode(_JSONI);
  if (input[_Ty] != null) {
    bn.c(import_xml_builder.XmlNode.of(_JSONT, input[_Ty]).n(_Ty));
  }
  return bn;
}, "se_JSONInput");
var se_JSONOutput = /* @__PURE__ */ __name((input, context) => {
  const bn = new import_xml_builder.XmlNode(_JSONO);
  bn.cc(input, _RD);
  return bn;
}, "se_JSONOutput");
var se_LambdaFunctionConfiguration = /* @__PURE__ */ __name((input, context) => {
  const bn = new import_xml_builder.XmlNode(_LFCa);
  if (input[_I] != null) {
    bn.c(import_xml_builder.XmlNode.of(_NI, input[_I]).n(_I));
  }
  if (input[_LFA] != null) {
    bn.c(import_xml_builder.XmlNode.of(_LFA, input[_LFA]).n(_CF));
  }
  bn.l(input, "Events", "Event", () => se_EventList(input[_Eve], context));
  if (input[_F] != null) {
    bn.c(se_NotificationConfigurationFilter(input[_F], context).n(_F));
  }
  return bn;
}, "se_LambdaFunctionConfiguration");
var se_LambdaFunctionConfigurationList = /* @__PURE__ */ __name((input, context) => {
  return input.filter((e) => e != null).map((entry) => {
    const n = se_LambdaFunctionConfiguration(entry, context);
    return n.n(_me);
  });
}, "se_LambdaFunctionConfigurationList");
var se_LifecycleExpiration = /* @__PURE__ */ __name((input, context) => {
  const bn = new import_xml_builder.XmlNode(_LEi);
  if (input[_Dat] != null) {
    bn.c(import_xml_builder.XmlNode.of(_Dat, (0, import_smithy_client.serializeDateTime)(input[_Dat]).toString()).n(_Dat));
  }
  if (input[_Da] != null) {
    bn.c(import_xml_builder.XmlNode.of(_Da, String(input[_Da])).n(_Da));
  }
  if (input[_EODM] != null) {
    bn.c(import_xml_builder.XmlNode.of(_EODM, String(input[_EODM])).n(_EODM));
  }
  return bn;
}, "se_LifecycleExpiration");
var se_LifecycleRule = /* @__PURE__ */ __name((input, context) => {
  const bn = new import_xml_builder.XmlNode(_LR);
  if (input[_Exp] != null) {
    bn.c(se_LifecycleExpiration(input[_Exp], context).n(_Exp));
  }
  bn.cc(input, _ID_);
  bn.cc(input, _P);
  if (input[_F] != null) {
    bn.c(se_LifecycleRuleFilter(input[_F], context).n(_F));
  }
  if (input[_S] != null) {
    bn.c(import_xml_builder.XmlNode.of(_ESx, input[_S]).n(_S));
  }
  bn.l(input, "Transitions", "Transition", () => se_TransitionList(input[_Tr], context));
  bn.l(
    input,
    "NoncurrentVersionTransitions",
    "NoncurrentVersionTransition",
    () => se_NoncurrentVersionTransitionList(input[_NVT], context)
  );
  if (input[_NVE] != null) {
    bn.c(se_NoncurrentVersionExpiration(input[_NVE], context).n(_NVE));
  }
  if (input[_AIMU] != null) {
    bn.c(se_AbortIncompleteMultipartUpload(input[_AIMU], context).n(_AIMU));
  }
  return bn;
}, "se_LifecycleRule");
var se_LifecycleRuleAndOperator = /* @__PURE__ */ __name((input, context) => {
  const bn = new import_xml_builder.XmlNode(_LRAO);
  bn.cc(input, _P);
  bn.l(input, "Tags", "Tag", () => se_TagSet(input[_Tag], context));
  if (input[_OSGT] != null) {
    bn.c(import_xml_builder.XmlNode.of(_OSGTB, String(input[_OSGT])).n(_OSGT));
  }
  if (input[_OSLT] != null) {
    bn.c(import_xml_builder.XmlNode.of(_OSLTB, String(input[_OSLT])).n(_OSLT));
  }
  return bn;
}, "se_LifecycleRuleAndOperator");
var se_LifecycleRuleFilter = /* @__PURE__ */ __name((input, context) => {
  const bn = new import_xml_builder.XmlNode(_LRF);
  bn.cc(input, _P);
  if (input[_Ta] != null) {
    bn.c(se_Tag(input[_Ta], context).n(_Ta));
  }
  if (input[_OSGT] != null) {
    bn.c(import_xml_builder.XmlNode.of(_OSGTB, String(input[_OSGT])).n(_OSGT));
  }
  if (input[_OSLT] != null) {
    bn.c(import_xml_builder.XmlNode.of(_OSLTB, String(input[_OSLT])).n(_OSLT));
  }
  if (input[_A] != null) {
    bn.c(se_LifecycleRuleAndOperator(input[_A], context).n(_A));
  }
  return bn;
}, "se_LifecycleRuleFilter");
var se_LifecycleRules = /* @__PURE__ */ __name((input, context) => {
  return input.filter((e) => e != null).map((entry) => {
    const n = se_LifecycleRule(entry, context);
    return n.n(_me);
  });
}, "se_LifecycleRules");
var se_LocationInfo = /* @__PURE__ */ __name((input, context) => {
  const bn = new import_xml_builder.XmlNode(_LI);
  if (input[_Ty] != null) {
    bn.c(import_xml_builder.XmlNode.of(_LT, input[_Ty]).n(_Ty));
  }
  if (input[_N] != null) {
    bn.c(import_xml_builder.XmlNode.of(_LNAS, input[_N]).n(_N));
  }
  return bn;
}, "se_LocationInfo");
var se_LoggingEnabled = /* @__PURE__ */ __name((input, context) => {
  const bn = new import_xml_builder.XmlNode(_LE);
  bn.cc(input, _TB);
  bn.lc(input, "TargetGrants", "TargetGrants", () => se_TargetGrants(input[_TG], context));
  bn.cc(input, _TP);
  if (input[_TOKF] != null) {
    bn.c(se_TargetObjectKeyFormat(input[_TOKF], context).n(_TOKF));
  }
  return bn;
}, "se_LoggingEnabled");
var se_MetadataConfiguration = /* @__PURE__ */ __name((input, context) => {
  const bn = new import_xml_builder.XmlNode(_MCe);
  if (input[_JTC] != null) {
    bn.c(se_JournalTableConfiguration(input[_JTC], context).n(_JTC));
  }
  if (input[_ITCn] != null) {
    bn.c(se_InventoryTableConfiguration(input[_ITCn], context).n(_ITCn));
  }
  return bn;
}, "se_MetadataConfiguration");
var se_MetadataEntry = /* @__PURE__ */ __name((input, context) => {
  const bn = new import_xml_builder.XmlNode(_ME);
  if (input[_N] != null) {
    bn.c(import_xml_builder.XmlNode.of(_MKe, input[_N]).n(_N));
  }
  if (input[_Va] != null) {
    bn.c(import_xml_builder.XmlNode.of(_MV, input[_Va]).n(_Va));
  }
  return bn;
}, "se_MetadataEntry");
var se_MetadataTableConfiguration = /* @__PURE__ */ __name((input, context) => {
  const bn = new import_xml_builder.XmlNode(_MTC);
  if (input[_STD] != null) {
    bn.c(se_S3TablesDestination(input[_STD], context).n(_STD));
  }
  return bn;
}, "se_MetadataTableConfiguration");
var se_MetadataTableEncryptionConfiguration = /* @__PURE__ */ __name((input, context) => {
  const bn = new import_xml_builder.XmlNode(_MTEC);
  if (input[_SAs] != null) {
    bn.c(import_xml_builder.XmlNode.of(_TSA, input[_SAs]).n(_SAs));
  }
  bn.cc(input, _KKA);
  return bn;
}, "se_MetadataTableEncryptionConfiguration");
var se_Metrics = /* @__PURE__ */ __name((input, context) => {
  const bn = new import_xml_builder.XmlNode(_Me);
  if (input[_S] != null) {
    bn.c(import_xml_builder.XmlNode.of(_MS, input[_S]).n(_S));
  }
  if (input[_ETv] != null) {
    bn.c(se_ReplicationTimeValue(input[_ETv], context).n(_ETv));
  }
  return bn;
}, "se_Metrics");
var se_MetricsAndOperator = /* @__PURE__ */ __name((input, context) => {
  const bn = new import_xml_builder.XmlNode(_MAO);
  bn.cc(input, _P);
  bn.l(input, "Tags", "Tag", () => se_TagSet(input[_Tag], context));
  bn.cc(input, _APAc);
  return bn;
}, "se_MetricsAndOperator");
var se_MetricsConfiguration = /* @__PURE__ */ __name((input, context) => {
  const bn = new import_xml_builder.XmlNode(_MC);
  if (input[_I] != null) {
    bn.c(import_xml_builder.XmlNode.of(_MI, input[_I]).n(_I));
  }
  if (input[_F] != null) {
    bn.c(se_MetricsFilter(input[_F], context).n(_F));
  }
  return bn;
}, "se_MetricsConfiguration");
var se_MetricsFilter = /* @__PURE__ */ __name((input, context) => {
  const bn = new import_xml_builder.XmlNode(_MF);
  MetricsFilter.visit(input, {
    Prefix: /* @__PURE__ */ __name((value) => {
      if (input[_P] != null) {
        bn.c(import_xml_builder.XmlNode.of(_P, value).n(_P));
      }
    }, "Prefix"),
    Tag: /* @__PURE__ */ __name((value) => {
      if (input[_Ta] != null) {
        bn.c(se_Tag(value, context).n(_Ta));
      }
    }, "Tag"),
    AccessPointArn: /* @__PURE__ */ __name((value) => {
      if (input[_APAc] != null) {
        bn.c(import_xml_builder.XmlNode.of(_APAc, value).n(_APAc));
      }
    }, "AccessPointArn"),
    And: /* @__PURE__ */ __name((value) => {
      if (input[_A] != null) {
        bn.c(se_MetricsAndOperator(value, context).n(_A));
      }
    }, "And"),
    _: /* @__PURE__ */ __name((name, value) => {
      if (!(value instanceof import_xml_builder.XmlNode || value instanceof import_xml_builder.XmlText)) {
        throw new Error("Unable to serialize unknown union members in XML.");
      }
      bn.c(new import_xml_builder.XmlNode(name).c(value));
    }, "_")
  });
  return bn;
}, "se_MetricsFilter");
var se_NoncurrentVersionExpiration = /* @__PURE__ */ __name((input, context) => {
  const bn = new import_xml_builder.XmlNode(_NVE);
  if (input[_ND] != null) {
    bn.c(import_xml_builder.XmlNode.of(_Da, String(input[_ND])).n(_ND));
  }
  if (input[_NNV] != null) {
    bn.c(import_xml_builder.XmlNode.of(_VC, String(input[_NNV])).n(_NNV));
  }
  return bn;
}, "se_NoncurrentVersionExpiration");
var se_NoncurrentVersionTransition = /* @__PURE__ */ __name((input, context) => {
  const bn = new import_xml_builder.XmlNode(_NVTo);
  if (input[_ND] != null) {
    bn.c(import_xml_builder.XmlNode.of(_Da, String(input[_ND])).n(_ND));
  }
  if (input[_SC] != null) {
    bn.c(import_xml_builder.XmlNode.of(_TSC, input[_SC]).n(_SC));
  }
  if (input[_NNV] != null) {
    bn.c(import_xml_builder.XmlNode.of(_VC, String(input[_NNV])).n(_NNV));
  }
  return bn;
}, "se_NoncurrentVersionTransition");
var se_NoncurrentVersionTransitionList = /* @__PURE__ */ __name((input, context) => {
  return input.filter((e) => e != null).map((entry) => {
    const n = se_NoncurrentVersionTransition(entry, context);
    return n.n(_me);
  });
}, "se_NoncurrentVersionTransitionList");
var se_NotificationConfiguration = /* @__PURE__ */ __name((input, context) => {
  const bn = new import_xml_builder.XmlNode(_NC);
  bn.l(input, "TopicConfigurations", "TopicConfiguration", () => se_TopicConfigurationList(input[_TCop], context));
  bn.l(input, "QueueConfigurations", "QueueConfiguration", () => se_QueueConfigurationList(input[_QCu], context));
  bn.l(
    input,
    "LambdaFunctionConfigurations",
    "CloudFunctionConfiguration",
    () => se_LambdaFunctionConfigurationList(input[_LFC], context)
  );
  if (input[_EBC] != null) {
    bn.c(se_EventBridgeConfiguration(input[_EBC], context).n(_EBC));
  }
  return bn;
}, "se_NotificationConfiguration");
var se_NotificationConfigurationFilter = /* @__PURE__ */ __name((input, context) => {
  const bn = new import_xml_builder.XmlNode(_NCF);
  if (input[_K] != null) {
    bn.c(se_S3KeyFilter(input[_K], context).n(_SKe));
  }
  return bn;
}, "se_NotificationConfigurationFilter");
var se_ObjectIdentifier = /* @__PURE__ */ __name((input, context) => {
  const bn = new import_xml_builder.XmlNode(_OI);
  if (input[_K] != null) {
    bn.c(import_xml_builder.XmlNode.of(_OK, input[_K]).n(_K));
  }
  if (input[_VI] != null) {
    bn.c(import_xml_builder.XmlNode.of(_OVI, input[_VI]).n(_VI));
  }
  bn.cc(input, _ETa);
  if (input[_LMT] != null) {
    bn.c(import_xml_builder.XmlNode.of(_LMT, (0, import_smithy_client.dateToUtcString)(input[_LMT]).toString()).n(_LMT));
  }
  if (input[_Si] != null) {
    bn.c(import_xml_builder.XmlNode.of(_Si, String(input[_Si])).n(_Si));
  }
  return bn;
}, "se_ObjectIdentifier");
var se_ObjectIdentifierList = /* @__PURE__ */ __name((input, context) => {
  return input.filter((e) => e != null).map((entry) => {
    const n = se_ObjectIdentifier(entry, context);
    return n.n(_me);
  });
}, "se_ObjectIdentifierList");
var se_ObjectLockConfiguration = /* @__PURE__ */ __name((input, context) => {
  const bn = new import_xml_builder.XmlNode(_OLC);
  bn.cc(input, _OLE);
  if (input[_Ru] != null) {
    bn.c(se_ObjectLockRule(input[_Ru], context).n(_Ru));
  }
  return bn;
}, "se_ObjectLockConfiguration");
var se_ObjectLockLegalHold = /* @__PURE__ */ __name((input, context) => {
  const bn = new import_xml_builder.XmlNode(_OLLH);
  if (input[_S] != null) {
    bn.c(import_xml_builder.XmlNode.of(_OLLHS, input[_S]).n(_S));
  }
  return bn;
}, "se_ObjectLockLegalHold");
var se_ObjectLockRetention = /* @__PURE__ */ __name((input, context) => {
  const bn = new import_xml_builder.XmlNode(_OLR);
  if (input[_Mo] != null) {
    bn.c(import_xml_builder.XmlNode.of(_OLRM, input[_Mo]).n(_Mo));
  }
  if (input[_RUD] != null) {
    bn.c(import_xml_builder.XmlNode.of(_Dat, (0, import_smithy_client.serializeDateTime)(input[_RUD]).toString()).n(_RUD));
  }
  return bn;
}, "se_ObjectLockRetention");
var se_ObjectLockRule = /* @__PURE__ */ __name((input, context) => {
  const bn = new import_xml_builder.XmlNode(_OLRb);
  if (input[_DRe] != null) {
    bn.c(se_DefaultRetention(input[_DRe], context).n(_DRe));
  }
  return bn;
}, "se_ObjectLockRule");
var se_OutputLocation = /* @__PURE__ */ __name((input, context) => {
  const bn = new import_xml_builder.XmlNode(_OL);
  if (input[_S_] != null) {
    bn.c(se_S3Location(input[_S_], context).n(_S_));
  }
  return bn;
}, "se_OutputLocation");
var se_OutputSerialization = /* @__PURE__ */ __name((input, context) => {
  const bn = new import_xml_builder.XmlNode(_OS);
  if (input[_CSV] != null) {
    bn.c(se_CSVOutput(input[_CSV], context).n(_CSV));
  }
  if (input[_JSON] != null) {
    bn.c(se_JSONOutput(input[_JSON], context).n(_JSON));
  }
  return bn;
}, "se_OutputSerialization");
var se_Owner = /* @__PURE__ */ __name((input, context) => {
  const bn = new import_xml_builder.XmlNode(_O);
  bn.cc(input, _DN);
  bn.cc(input, _ID_);
  return bn;
}, "se_Owner");
var se_OwnershipControls = /* @__PURE__ */ __name((input, context) => {
  const bn = new import_xml_builder.XmlNode(_OC);
  bn.l(input, "Rules", "Rule", () => se_OwnershipControlsRules(input[_Rul], context));
  return bn;
}, "se_OwnershipControls");
var se_OwnershipControlsRule = /* @__PURE__ */ __name((input, context) => {
  const bn = new import_xml_builder.XmlNode(_OCR);
  bn.cc(input, _OO);
  return bn;
}, "se_OwnershipControlsRule");
var se_OwnershipControlsRules = /* @__PURE__ */ __name((input, context) => {
  return input.filter((e) => e != null).map((entry) => {
    const n = se_OwnershipControlsRule(entry, context);
    return n.n(_me);
  });
}, "se_OwnershipControlsRules");
var se_ParquetInput = /* @__PURE__ */ __name((input, context) => {
  const bn = new import_xml_builder.XmlNode(_PI);
  return bn;
}, "se_ParquetInput");
var se_PartitionedPrefix = /* @__PURE__ */ __name((input, context) => {
  const bn = new import_xml_builder.XmlNode(_PP);
  bn.cc(input, _PDS);
  return bn;
}, "se_PartitionedPrefix");
var se_PublicAccessBlockConfiguration = /* @__PURE__ */ __name((input, context) => {
  const bn = new import_xml_builder.XmlNode(_PABC);
  if (input[_BPA] != null) {
    bn.c(import_xml_builder.XmlNode.of(_Se, String(input[_BPA])).n(_BPA));
  }
  if (input[_IPA] != null) {
    bn.c(import_xml_builder.XmlNode.of(_Se, String(input[_IPA])).n(_IPA));
  }
  if (input[_BPP] != null) {
    bn.c(import_xml_builder.XmlNode.of(_Se, String(input[_BPP])).n(_BPP));
  }
  if (input[_RPB] != null) {
    bn.c(import_xml_builder.XmlNode.of(_Se, String(input[_RPB])).n(_RPB));
  }
  return bn;
}, "se_PublicAccessBlockConfiguration");
var se_QueueConfiguration = /* @__PURE__ */ __name((input, context) => {
  const bn = new import_xml_builder.XmlNode(_QC);
  if (input[_I] != null) {
    bn.c(import_xml_builder.XmlNode.of(_NI, input[_I]).n(_I));
  }
  if (input[_QA] != null) {
    bn.c(import_xml_builder.XmlNode.of(_QA, input[_QA]).n(_Qu));
  }
  bn.l(input, "Events", "Event", () => se_EventList(input[_Eve], context));
  if (input[_F] != null) {
    bn.c(se_NotificationConfigurationFilter(input[_F], context).n(_F));
  }
  return bn;
}, "se_QueueConfiguration");
var se_QueueConfigurationList = /* @__PURE__ */ __name((input, context) => {
  return input.filter((e) => e != null).map((entry) => {
    const n = se_QueueConfiguration(entry, context);
    return n.n(_me);
  });
}, "se_QueueConfigurationList");
var se_RecordExpiration = /* @__PURE__ */ __name((input, context) => {
  const bn = new import_xml_builder.XmlNode(_REe);
  if (input[_Exp] != null) {
    bn.c(import_xml_builder.XmlNode.of(_ESxp, input[_Exp]).n(_Exp));
  }
  if (input[_Da] != null) {
    bn.c(import_xml_builder.XmlNode.of(_RED, String(input[_Da])).n(_Da));
  }
  return bn;
}, "se_RecordExpiration");
var se_Redirect = /* @__PURE__ */ __name((input, context) => {
  const bn = new import_xml_builder.XmlNode(_Red);
  bn.cc(input, _HN);
  bn.cc(input, _HRC);
  bn.cc(input, _Pr);
  bn.cc(input, _RKPW);
  bn.cc(input, _RKW);
  return bn;
}, "se_Redirect");
var se_RedirectAllRequestsTo = /* @__PURE__ */ __name((input, context) => {
  const bn = new import_xml_builder.XmlNode(_RART);
  bn.cc(input, _HN);
  bn.cc(input, _Pr);
  return bn;
}, "se_RedirectAllRequestsTo");
var se_ReplicaModifications = /* @__PURE__ */ __name((input, context) => {
  const bn = new import_xml_builder.XmlNode(_RM);
  if (input[_S] != null) {
    bn.c(import_xml_builder.XmlNode.of(_RMS, input[_S]).n(_S));
  }
  return bn;
}, "se_ReplicaModifications");
var se_ReplicationConfiguration = /* @__PURE__ */ __name((input, context) => {
  const bn = new import_xml_builder.XmlNode(_RCe);
  bn.cc(input, _Ro);
  bn.l(input, "Rules", "Rule", () => se_ReplicationRules(input[_Rul], context));
  return bn;
}, "se_ReplicationConfiguration");
var se_ReplicationRule = /* @__PURE__ */ __name((input, context) => {
  const bn = new import_xml_builder.XmlNode(_RRe);
  bn.cc(input, _ID_);
  if (input[_Pri] != null) {
    bn.c(import_xml_builder.XmlNode.of(_Pri, String(input[_Pri])).n(_Pri));
  }
  bn.cc(input, _P);
  if (input[_F] != null) {
    bn.c(se_ReplicationRuleFilter(input[_F], context).n(_F));
  }
  if (input[_S] != null) {
    bn.c(import_xml_builder.XmlNode.of(_RRS, input[_S]).n(_S));
  }
  if (input[_SSC] != null) {
    bn.c(se_SourceSelectionCriteria(input[_SSC], context).n(_SSC));
  }
  if (input[_EOR] != null) {
    bn.c(se_ExistingObjectReplication(input[_EOR], context).n(_EOR));
  }
  if (input[_Des] != null) {
    bn.c(se_Destination(input[_Des], context).n(_Des));
  }
  if (input[_DMR] != null) {
    bn.c(se_DeleteMarkerReplication(input[_DMR], context).n(_DMR));
  }
  return bn;
}, "se_ReplicationRule");
var se_ReplicationRuleAndOperator = /* @__PURE__ */ __name((input, context) => {
  const bn = new import_xml_builder.XmlNode(_RRAO);
  bn.cc(input, _P);
  bn.l(input, "Tags", "Tag", () => se_TagSet(input[_Tag], context));
  return bn;
}, "se_ReplicationRuleAndOperator");
var se_ReplicationRuleFilter = /* @__PURE__ */ __name((input, context) => {
  const bn = new import_xml_builder.XmlNode(_RRF);
  bn.cc(input, _P);
  if (input[_Ta] != null) {
    bn.c(se_Tag(input[_Ta], context).n(_Ta));
  }
  if (input[_A] != null) {
    bn.c(se_ReplicationRuleAndOperator(input[_A], context).n(_A));
  }
  return bn;
}, "se_ReplicationRuleFilter");
var se_ReplicationRules = /* @__PURE__ */ __name((input, context) => {
  return input.filter((e) => e != null).map((entry) => {
    const n = se_ReplicationRule(entry, context);
    return n.n(_me);
  });
}, "se_ReplicationRules");
var se_ReplicationTime = /* @__PURE__ */ __name((input, context) => {
  const bn = new import_xml_builder.XmlNode(_RTe);
  if (input[_S] != null) {
    bn.c(import_xml_builder.XmlNode.of(_RTS, input[_S]).n(_S));
  }
  if (input[_Tim] != null) {
    bn.c(se_ReplicationTimeValue(input[_Tim], context).n(_Tim));
  }
  return bn;
}, "se_ReplicationTime");
var se_ReplicationTimeValue = /* @__PURE__ */ __name((input, context) => {
  const bn = new import_xml_builder.XmlNode(_RTV);
  if (input[_Mi] != null) {
    bn.c(import_xml_builder.XmlNode.of(_Mi, String(input[_Mi])).n(_Mi));
  }
  return bn;
}, "se_ReplicationTimeValue");
var se_RequestPaymentConfiguration = /* @__PURE__ */ __name((input, context) => {
  const bn = new import_xml_builder.XmlNode(_RPC);
  bn.cc(input, _Pa);
  return bn;
}, "se_RequestPaymentConfiguration");
var se_RequestProgress = /* @__PURE__ */ __name((input, context) => {
  const bn = new import_xml_builder.XmlNode(_RPe);
  if (input[_Ena] != null) {
    bn.c(import_xml_builder.XmlNode.of(_ERP, String(input[_Ena])).n(_Ena));
  }
  return bn;
}, "se_RequestProgress");
var se_RestoreRequest = /* @__PURE__ */ __name((input, context) => {
  const bn = new import_xml_builder.XmlNode(_RRes);
  if (input[_Da] != null) {
    bn.c(import_xml_builder.XmlNode.of(_Da, String(input[_Da])).n(_Da));
  }
  if (input[_GJP] != null) {
    bn.c(se_GlacierJobParameters(input[_GJP], context).n(_GJP));
  }
  if (input[_Ty] != null) {
    bn.c(import_xml_builder.XmlNode.of(_RRT, input[_Ty]).n(_Ty));
  }
  bn.cc(input, _Ti);
  bn.cc(input, _Desc);
  if (input[_SP] != null) {
    bn.c(se_SelectParameters(input[_SP], context).n(_SP));
  }
  if (input[_OL] != null) {
    bn.c(se_OutputLocation(input[_OL], context).n(_OL));
  }
  return bn;
}, "se_RestoreRequest");
var se_RoutingRule = /* @__PURE__ */ __name((input, context) => {
  const bn = new import_xml_builder.XmlNode(_RRou);
  if (input[_Con] != null) {
    bn.c(se_Condition(input[_Con], context).n(_Con));
  }
  if (input[_Red] != null) {
    bn.c(se_Redirect(input[_Red], context).n(_Red));
  }
  return bn;
}, "se_RoutingRule");
var se_RoutingRules = /* @__PURE__ */ __name((input, context) => {
  return input.filter((e) => e != null).map((entry) => {
    const n = se_RoutingRule(entry, context);
    return n.n(_RRou);
  });
}, "se_RoutingRules");
var se_S3KeyFilter = /* @__PURE__ */ __name((input, context) => {
  const bn = new import_xml_builder.XmlNode(_SKF);
  bn.l(input, "FilterRules", "FilterRule", () => se_FilterRuleList(input[_FRi], context));
  return bn;
}, "se_S3KeyFilter");
var se_S3Location = /* @__PURE__ */ __name((input, context) => {
  const bn = new import_xml_builder.XmlNode(_SL);
  bn.cc(input, _BN);
  if (input[_P] != null) {
    bn.c(import_xml_builder.XmlNode.of(_LP, input[_P]).n(_P));
  }
  if (input[_En] != null) {
    bn.c(se_Encryption(input[_En], context).n(_En));
  }
  if (input[_CACL] != null) {
    bn.c(import_xml_builder.XmlNode.of(_OCACL, input[_CACL]).n(_CACL));
  }
  bn.lc(input, "AccessControlList", "AccessControlList", () => se_Grants(input[_ACLc], context));
  if (input[_T] != null) {
    bn.c(se_Tagging(input[_T], context).n(_T));
  }
  bn.lc(input, "UserMetadata", "UserMetadata", () => se_UserMetadata(input[_UM], context));
  bn.cc(input, _SC);
  return bn;
}, "se_S3Location");
var se_S3TablesDestination = /* @__PURE__ */ __name((input, context) => {
  const bn = new import_xml_builder.XmlNode(_STD);
  if (input[_TBA] != null) {
    bn.c(import_xml_builder.XmlNode.of(_STBA, input[_TBA]).n(_TBA));
  }
  if (input[_TN] != null) {
    bn.c(import_xml_builder.XmlNode.of(_STN, input[_TN]).n(_TN));
  }
  return bn;
}, "se_S3TablesDestination");
var se_ScanRange = /* @__PURE__ */ __name((input, context) => {
  const bn = new import_xml_builder.XmlNode(_SR);
  if (input[_St] != null) {
    bn.c(import_xml_builder.XmlNode.of(_St, String(input[_St])).n(_St));
  }
  if (input[_End] != null) {
    bn.c(import_xml_builder.XmlNode.of(_End, String(input[_End])).n(_End));
  }
  return bn;
}, "se_ScanRange");
var se_SelectParameters = /* @__PURE__ */ __name((input, context) => {
  const bn = new import_xml_builder.XmlNode(_SP);
  if (input[_IS] != null) {
    bn.c(se_InputSerialization(input[_IS], context).n(_IS));
  }
  bn.cc(input, _ETx);
  bn.cc(input, _Ex);
  if (input[_OS] != null) {
    bn.c(se_OutputSerialization(input[_OS], context).n(_OS));
  }
  return bn;
}, "se_SelectParameters");
var se_ServerSideEncryptionByDefault = /* @__PURE__ */ __name((input, context) => {
  const bn = new import_xml_builder.XmlNode(_SSEBD);
  if (input[_SSEA] != null) {
    bn.c(import_xml_builder.XmlNode.of(_SSE, input[_SSEA]).n(_SSEA));
  }
  if (input[_KMSMKID] != null) {
    bn.c(import_xml_builder.XmlNode.of(_SSEKMSKI, input[_KMSMKID]).n(_KMSMKID));
  }
  return bn;
}, "se_ServerSideEncryptionByDefault");
var se_ServerSideEncryptionConfiguration = /* @__PURE__ */ __name((input, context) => {
  const bn = new import_xml_builder.XmlNode(_SSEC);
  bn.l(input, "Rules", "Rule", () => se_ServerSideEncryptionRules(input[_Rul], context));
  return bn;
}, "se_ServerSideEncryptionConfiguration");
var se_ServerSideEncryptionRule = /* @__PURE__ */ __name((input, context) => {
  const bn = new import_xml_builder.XmlNode(_SSER);
  if (input[_ASSEBD] != null) {
    bn.c(se_ServerSideEncryptionByDefault(input[_ASSEBD], context).n(_ASSEBD));
  }
  if (input[_BKE] != null) {
    bn.c(import_xml_builder.XmlNode.of(_BKE, String(input[_BKE])).n(_BKE));
  }
  return bn;
}, "se_ServerSideEncryptionRule");
var se_ServerSideEncryptionRules = /* @__PURE__ */ __name((input, context) => {
  return input.filter((e) => e != null).map((entry) => {
    const n = se_ServerSideEncryptionRule(entry, context);
    return n.n(_me);
  });
}, "se_ServerSideEncryptionRules");
var se_SimplePrefix = /* @__PURE__ */ __name((input, context) => {
  const bn = new import_xml_builder.XmlNode(_SPi);
  return bn;
}, "se_SimplePrefix");
var se_SourceSelectionCriteria = /* @__PURE__ */ __name((input, context) => {
  const bn = new import_xml_builder.XmlNode(_SSC);
  if (input[_SKEO] != null) {
    bn.c(se_SseKmsEncryptedObjects(input[_SKEO], context).n(_SKEO));
  }
  if (input[_RM] != null) {
    bn.c(se_ReplicaModifications(input[_RM], context).n(_RM));
  }
  return bn;
}, "se_SourceSelectionCriteria");
var se_SSEKMS = /* @__PURE__ */ __name((input, context) => {
  const bn = new import_xml_builder.XmlNode(_SK);
  if (input[_KI] != null) {
    bn.c(import_xml_builder.XmlNode.of(_SSEKMSKI, input[_KI]).n(_KI));
  }
  return bn;
}, "se_SSEKMS");
var se_SseKmsEncryptedObjects = /* @__PURE__ */ __name((input, context) => {
  const bn = new import_xml_builder.XmlNode(_SKEO);
  if (input[_S] != null) {
    bn.c(import_xml_builder.XmlNode.of(_SKEOS, input[_S]).n(_S));
  }
  return bn;
}, "se_SseKmsEncryptedObjects");
var se_SSES3 = /* @__PURE__ */ __name((input, context) => {
  const bn = new import_xml_builder.XmlNode(_SS);
  return bn;
}, "se_SSES3");
var se_StorageClassAnalysis = /* @__PURE__ */ __name((input, context) => {
  const bn = new import_xml_builder.XmlNode(_SCA);
  if (input[_DE] != null) {
    bn.c(se_StorageClassAnalysisDataExport(input[_DE], context).n(_DE));
  }
  return bn;
}, "se_StorageClassAnalysis");
var se_StorageClassAnalysisDataExport = /* @__PURE__ */ __name((input, context) => {
  const bn = new import_xml_builder.XmlNode(_SCADE);
  if (input[_OSV] != null) {
    bn.c(import_xml_builder.XmlNode.of(_SCASV, input[_OSV]).n(_OSV));
  }
  if (input[_Des] != null) {
    bn.c(se_AnalyticsExportDestination(input[_Des], context).n(_Des));
  }
  return bn;
}, "se_StorageClassAnalysisDataExport");
var se_Tag = /* @__PURE__ */ __name((input, context) => {
  const bn = new import_xml_builder.XmlNode(_Ta);
  if (input[_K] != null) {
    bn.c(import_xml_builder.XmlNode.of(_OK, input[_K]).n(_K));
  }
  bn.cc(input, _Va);
  return bn;
}, "se_Tag");
var se_Tagging = /* @__PURE__ */ __name((input, context) => {
  const bn = new import_xml_builder.XmlNode(_T);
  bn.lc(input, "TagSet", "TagSet", () => se_TagSet(input[_TS], context));
  return bn;
}, "se_Tagging");
var se_TagSet = /* @__PURE__ */ __name((input, context) => {
  return input.filter((e) => e != null).map((entry) => {
    const n = se_Tag(entry, context);
    return n.n(_Ta);
  });
}, "se_TagSet");
var se_TargetGrant = /* @__PURE__ */ __name((input, context) => {
  const bn = new import_xml_builder.XmlNode(_TGa);
  if (input[_Gra] != null) {
    const n = se_Grantee(input[_Gra], context).n(_Gra);
    n.a("xmlns:xsi", "http://www.w3.org/2001/XMLSchema-instance");
    bn.c(n);
  }
  if (input[_Pe] != null) {
    bn.c(import_xml_builder.XmlNode.of(_BLP, input[_Pe]).n(_Pe));
  }
  return bn;
}, "se_TargetGrant");
var se_TargetGrants = /* @__PURE__ */ __name((input, context) => {
  return input.filter((e) => e != null).map((entry) => {
    const n = se_TargetGrant(entry, context);
    return n.n(_G);
  });
}, "se_TargetGrants");
var se_TargetObjectKeyFormat = /* @__PURE__ */ __name((input, context) => {
  const bn = new import_xml_builder.XmlNode(_TOKF);
  if (input[_SPi] != null) {
    bn.c(se_SimplePrefix(input[_SPi], context).n(_SPi));
  }
  if (input[_PP] != null) {
    bn.c(se_PartitionedPrefix(input[_PP], context).n(_PP));
  }
  return bn;
}, "se_TargetObjectKeyFormat");
var se_Tiering = /* @__PURE__ */ __name((input, context) => {
  const bn = new import_xml_builder.XmlNode(_Tier);
  if (input[_Da] != null) {
    bn.c(import_xml_builder.XmlNode.of(_ITD, String(input[_Da])).n(_Da));
  }
  if (input[_AT] != null) {
    bn.c(import_xml_builder.XmlNode.of(_ITAT, input[_AT]).n(_AT));
  }
  return bn;
}, "se_Tiering");
var se_TieringList = /* @__PURE__ */ __name((input, context) => {
  return input.filter((e) => e != null).map((entry) => {
    const n = se_Tiering(entry, context);
    return n.n(_me);
  });
}, "se_TieringList");
var se_TopicConfiguration = /* @__PURE__ */ __name((input, context) => {
  const bn = new import_xml_builder.XmlNode(_TCo);
  if (input[_I] != null) {
    bn.c(import_xml_builder.XmlNode.of(_NI, input[_I]).n(_I));
  }
  if (input[_TA] != null) {
    bn.c(import_xml_builder.XmlNode.of(_TA, input[_TA]).n(_Top));
  }
  bn.l(input, "Events", "Event", () => se_EventList(input[_Eve], context));
  if (input[_F] != null) {
    bn.c(se_NotificationConfigurationFilter(input[_F], context).n(_F));
  }
  return bn;
}, "se_TopicConfiguration");
var se_TopicConfigurationList = /* @__PURE__ */ __name((input, context) => {
  return input.filter((e) => e != null).map((entry) => {
    const n = se_TopicConfiguration(entry, context);
    return n.n(_me);
  });
}, "se_TopicConfigurationList");
var se_Transition = /* @__PURE__ */ __name((input, context) => {
  const bn = new import_xml_builder.XmlNode(_Tra);
  if (input[_Dat] != null) {
    bn.c(import_xml_builder.XmlNode.of(_Dat, (0, import_smithy_client.serializeDateTime)(input[_Dat]).toString()).n(_Dat));
  }
  if (input[_Da] != null) {
    bn.c(import_xml_builder.XmlNode.of(_Da, String(input[_Da])).n(_Da));
  }
  if (input[_SC] != null) {
    bn.c(import_xml_builder.XmlNode.of(_TSC, input[_SC]).n(_SC));
  }
  return bn;
}, "se_Transition");
var se_TransitionList = /* @__PURE__ */ __name((input, context) => {
  return input.filter((e) => e != null).map((entry) => {
    const n = se_Transition(entry, context);
    return n.n(_me);
  });
}, "se_TransitionList");
var se_UserMetadata = /* @__PURE__ */ __name((input, context) => {
  return input.filter((e) => e != null).map((entry) => {
    const n = se_MetadataEntry(entry, context);
    return n.n(_ME);
  });
}, "se_UserMetadata");
var se_VersioningConfiguration = /* @__PURE__ */ __name((input, context) => {
  const bn = new import_xml_builder.XmlNode(_VCe);
  if (input[_MFAD] != null) {
    bn.c(import_xml_builder.XmlNode.of(_MFAD, input[_MFAD]).n(_MDf));
  }
  if (input[_S] != null) {
    bn.c(import_xml_builder.XmlNode.of(_BVS, input[_S]).n(_S));
  }
  return bn;
}, "se_VersioningConfiguration");
var se_WebsiteConfiguration = /* @__PURE__ */ __name((input, context) => {
  const bn = new import_xml_builder.XmlNode(_WC);
  if (input[_ED] != null) {
    bn.c(se_ErrorDocument(input[_ED], context).n(_ED));
  }
  if (input[_ID] != null) {
    bn.c(se_IndexDocument(input[_ID], context).n(_ID));
  }
  if (input[_RART] != null) {
    bn.c(se_RedirectAllRequestsTo(input[_RART], context).n(_RART));
  }
  bn.lc(input, "RoutingRules", "RoutingRules", () => se_RoutingRules(input[_RRo], context));
  return bn;
}, "se_WebsiteConfiguration");
var de_AbortIncompleteMultipartUpload = /* @__PURE__ */ __name((output, context) => {
  const contents = {};
  if (output[_DAI] != null) {
    contents[_DAI] = (0, import_smithy_client.strictParseInt32)(output[_DAI]);
  }
  return contents;
}, "de_AbortIncompleteMultipartUpload");
var de_AccessControlTranslation = /* @__PURE__ */ __name((output, context) => {
  const contents = {};
  if (output[_O] != null) {
    contents[_O] = (0, import_smithy_client.expectString)(output[_O]);
  }
  return contents;
}, "de_AccessControlTranslation");
var de_AllowedHeaders = /* @__PURE__ */ __name((output, context) => {
  return (output || []).filter((e) => e != null).map((entry) => {
    return (0, import_smithy_client.expectString)(entry);
  });
}, "de_AllowedHeaders");
var de_AllowedMethods = /* @__PURE__ */ __name((output, context) => {
  return (output || []).filter((e) => e != null).map((entry) => {
    return (0, import_smithy_client.expectString)(entry);
  });
}, "de_AllowedMethods");
var de_AllowedOrigins = /* @__PURE__ */ __name((output, context) => {
  return (output || []).filter((e) => e != null).map((entry) => {
    return (0, import_smithy_client.expectString)(entry);
  });
}, "de_AllowedOrigins");
var de_AnalyticsAndOperator = /* @__PURE__ */ __name((output, context) => {
  const contents = {};
  if (output[_P] != null) {
    contents[_P] = (0, import_smithy_client.expectString)(output[_P]);
  }
  if (output.Tag === "") {
    contents[_Tag] = [];
  } else if (output[_Ta] != null) {
    contents[_Tag] = de_TagSet((0, import_smithy_client.getArrayIfSingleItem)(output[_Ta]), context);
  }
  return contents;
}, "de_AnalyticsAndOperator");
var de_AnalyticsConfiguration = /* @__PURE__ */ __name((output, context) => {
  const contents = {};
  if (output[_I] != null) {
    contents[_I] = (0, import_smithy_client.expectString)(output[_I]);
  }
  if (output.Filter === "") {
  } else if (output[_F] != null) {
    contents[_F] = de_AnalyticsFilter((0, import_smithy_client.expectUnion)(output[_F]), context);
  }
  if (output[_SCA] != null) {
    contents[_SCA] = de_StorageClassAnalysis(output[_SCA], context);
  }
  return contents;
}, "de_AnalyticsConfiguration");
var de_AnalyticsConfigurationList = /* @__PURE__ */ __name((output, context) => {
  return (output || []).filter((e) => e != null).map((entry) => {
    return de_AnalyticsConfiguration(entry, context);
  });
}, "de_AnalyticsConfigurationList");
var de_AnalyticsExportDestination = /* @__PURE__ */ __name((output, context) => {
  const contents = {};
  if (output[_SBD] != null) {
    contents[_SBD] = de_AnalyticsS3BucketDestination(output[_SBD], context);
  }
  return contents;
}, "de_AnalyticsExportDestination");
var de_AnalyticsFilter = /* @__PURE__ */ __name((output, context) => {
  if (output[_P] != null) {
    return {
      Prefix: (0, import_smithy_client.expectString)(output[_P])
    };
  }
  if (output[_Ta] != null) {
    return {
      Tag: de_Tag(output[_Ta], context)
    };
  }
  if (output[_A] != null) {
    return {
      And: de_AnalyticsAndOperator(output[_A], context)
    };
  }
  return { $unknown: Object.entries(output)[0] };
}, "de_AnalyticsFilter");
var de_AnalyticsS3BucketDestination = /* @__PURE__ */ __name((output, context) => {
  const contents = {};
  if (output[_Fo] != null) {
    contents[_Fo] = (0, import_smithy_client.expectString)(output[_Fo]);
  }
  if (output[_BAI] != null) {
    contents[_BAI] = (0, import_smithy_client.expectString)(output[_BAI]);
  }
  if (output[_B] != null) {
    contents[_B] = (0, import_smithy_client.expectString)(output[_B]);
  }
  if (output[_P] != null) {
    contents[_P] = (0, import_smithy_client.expectString)(output[_P]);
  }
  return contents;
}, "de_AnalyticsS3BucketDestination");
var de_Bucket = /* @__PURE__ */ __name((output, context) => {
  const contents = {};
  if (output[_N] != null) {
    contents[_N] = (0, import_smithy_client.expectString)(output[_N]);
  }
  if (output[_CDr] != null) {
    contents[_CDr] = (0, import_smithy_client.expectNonNull)((0, import_smithy_client.parseRfc3339DateTimeWithOffset)(output[_CDr]));
  }
  if (output[_BR] != null) {
    contents[_BR] = (0, import_smithy_client.expectString)(output[_BR]);
  }
  if (output[_BA] != null) {
    contents[_BA] = (0, import_smithy_client.expectString)(output[_BA]);
  }
  return contents;
}, "de_Bucket");
var de_Buckets = /* @__PURE__ */ __name((output, context) => {
  return (output || []).filter((e) => e != null).map((entry) => {
    return de_Bucket(entry, context);
  });
}, "de_Buckets");
var de_Checksum = /* @__PURE__ */ __name((output, context) => {
  const contents = {};
  if (output[_CCRC] != null) {
    contents[_CCRC] = (0, import_smithy_client.expectString)(output[_CCRC]);
  }
  if (output[_CCRCC] != null) {
    contents[_CCRCC] = (0, import_smithy_client.expectString)(output[_CCRCC]);
  }
  if (output[_CCRCNVME] != null) {
    contents[_CCRCNVME] = (0, import_smithy_client.expectString)(output[_CCRCNVME]);
  }
  if (output[_CSHA] != null) {
    contents[_CSHA] = (0, import_smithy_client.expectString)(output[_CSHA]);
  }
  if (output[_CSHAh] != null) {
    contents[_CSHAh] = (0, import_smithy_client.expectString)(output[_CSHAh]);
  }
  if (output[_CT] != null) {
    contents[_CT] = (0, import_smithy_client.expectString)(output[_CT]);
  }
  return contents;
}, "de_Checksum");
var de_ChecksumAlgorithmList = /* @__PURE__ */ __name((output, context) => {
  return (output || []).filter((e) => e != null).map((entry) => {
    return (0, import_smithy_client.expectString)(entry);
  });
}, "de_ChecksumAlgorithmList");
var de_CommonPrefix = /* @__PURE__ */ __name((output, context) => {
  const contents = {};
  if (output[_P] != null) {
    contents[_P] = (0, import_smithy_client.expectString)(output[_P]);
  }
  return contents;
}, "de_CommonPrefix");
var de_CommonPrefixList = /* @__PURE__ */ __name((output, context) => {
  return (output || []).filter((e) => e != null).map((entry) => {
    return de_CommonPrefix(entry, context);
  });
}, "de_CommonPrefixList");
var de_Condition = /* @__PURE__ */ __name((output, context) => {
  const contents = {};
  if (output[_HECRE] != null) {
    contents[_HECRE] = (0, import_smithy_client.expectString)(output[_HECRE]);
  }
  if (output[_KPE] != null) {
    contents[_KPE] = (0, import_smithy_client.expectString)(output[_KPE]);
  }
  return contents;
}, "de_Condition");
var de_ContinuationEvent = /* @__PURE__ */ __name((output, context) => {
  const contents = {};
  return contents;
}, "de_ContinuationEvent");
var de_CopyObjectResult = /* @__PURE__ */ __name((output, context) => {
  const contents = {};
  if (output[_ETa] != null) {
    contents[_ETa] = (0, import_smithy_client.expectString)(output[_ETa]);
  }
  if (output[_LM] != null) {
    contents[_LM] = (0, import_smithy_client.expectNonNull)((0, import_smithy_client.parseRfc3339DateTimeWithOffset)(output[_LM]));
  }
  if (output[_CT] != null) {
    contents[_CT] = (0, import_smithy_client.expectString)(output[_CT]);
  }
  if (output[_CCRC] != null) {
    contents[_CCRC] = (0, import_smithy_client.expectString)(output[_CCRC]);
  }
  if (output[_CCRCC] != null) {
    contents[_CCRCC] = (0, import_smithy_client.expectString)(output[_CCRCC]);
  }
  if (output[_CCRCNVME] != null) {
    contents[_CCRCNVME] = (0, import_smithy_client.expectString)(output[_CCRCNVME]);
  }
  if (output[_CSHA] != null) {
    contents[_CSHA] = (0, import_smithy_client.expectString)(output[_CSHA]);
  }
  if (output[_CSHAh] != null) {
    contents[_CSHAh] = (0, import_smithy_client.expectString)(output[_CSHAh]);
  }
  return contents;
}, "de_CopyObjectResult");
var de_CopyPartResult = /* @__PURE__ */ __name((output, context) => {
  const contents = {};
  if (output[_ETa] != null) {
    contents[_ETa] = (0, import_smithy_client.expectString)(output[_ETa]);
  }
  if (output[_LM] != null) {
    contents[_LM] = (0, import_smithy_client.expectNonNull)((0, import_smithy_client.parseRfc3339DateTimeWithOffset)(output[_LM]));
  }
  if (output[_CCRC] != null) {
    contents[_CCRC] = (0, import_smithy_client.expectString)(output[_CCRC]);
  }
  if (output[_CCRCC] != null) {
    contents[_CCRCC] = (0, import_smithy_client.expectString)(output[_CCRCC]);
  }
  if (output[_CCRCNVME] != null) {
    contents[_CCRCNVME] = (0, import_smithy_client.expectString)(output[_CCRCNVME]);
  }
  if (output[_CSHA] != null) {
    contents[_CSHA] = (0, import_smithy_client.expectString)(output[_CSHA]);
  }
  if (output[_CSHAh] != null) {
    contents[_CSHAh] = (0, import_smithy_client.expectString)(output[_CSHAh]);
  }
  return contents;
}, "de_CopyPartResult");
var de_CORSRule = /* @__PURE__ */ __name((output, context) => {
  const contents = {};
  if (output[_ID_] != null) {
    contents[_ID_] = (0, import_smithy_client.expectString)(output[_ID_]);
  }
  if (output.AllowedHeader === "") {
    contents[_AHl] = [];
  } else if (output[_AH] != null) {
    contents[_AHl] = de_AllowedHeaders((0, import_smithy_client.getArrayIfSingleItem)(output[_AH]), context);
  }
  if (output.AllowedMethod === "") {
    contents[_AMl] = [];
  } else if (output[_AM] != null) {
    contents[_AMl] = de_AllowedMethods((0, import_smithy_client.getArrayIfSingleItem)(output[_AM]), context);
  }
  if (output.AllowedOrigin === "") {
    contents[_AOl] = [];
  } else if (output[_AO] != null) {
    contents[_AOl] = de_AllowedOrigins((0, import_smithy_client.getArrayIfSingleItem)(output[_AO]), context);
  }
  if (output.ExposeHeader === "") {
    contents[_EH] = [];
  } else if (output[_EHx] != null) {
    contents[_EH] = de_ExposeHeaders((0, import_smithy_client.getArrayIfSingleItem)(output[_EHx]), context);
  }
  if (output[_MAS] != null) {
    contents[_MAS] = (0, import_smithy_client.strictParseInt32)(output[_MAS]);
  }
  return contents;
}, "de_CORSRule");
var de_CORSRules = /* @__PURE__ */ __name((output, context) => {
  return (output || []).filter((e) => e != null).map((entry) => {
    return de_CORSRule(entry, context);
  });
}, "de_CORSRules");
var de_DefaultRetention = /* @__PURE__ */ __name((output, context) => {
  const contents = {};
  if (output[_Mo] != null) {
    contents[_Mo] = (0, import_smithy_client.expectString)(output[_Mo]);
  }
  if (output[_Da] != null) {
    contents[_Da] = (0, import_smithy_client.strictParseInt32)(output[_Da]);
  }
  if (output[_Y] != null) {
    contents[_Y] = (0, import_smithy_client.strictParseInt32)(output[_Y]);
  }
  return contents;
}, "de_DefaultRetention");
var de_DeletedObject = /* @__PURE__ */ __name((output, context) => {
  const contents = {};
  if (output[_K] != null) {
    contents[_K] = (0, import_smithy_client.expectString)(output[_K]);
  }
  if (output[_VI] != null) {
    contents[_VI] = (0, import_smithy_client.expectString)(output[_VI]);
  }
  if (output[_DM] != null) {
    contents[_DM] = (0, import_smithy_client.parseBoolean)(output[_DM]);
  }
  if (output[_DMVI] != null) {
    contents[_DMVI] = (0, import_smithy_client.expectString)(output[_DMVI]);
  }
  return contents;
}, "de_DeletedObject");
var de_DeletedObjects = /* @__PURE__ */ __name((output, context) => {
  return (output || []).filter((e) => e != null).map((entry) => {
    return de_DeletedObject(entry, context);
  });
}, "de_DeletedObjects");
var de_DeleteMarkerEntry = /* @__PURE__ */ __name((output, context) => {
  const contents = {};
  if (output[_O] != null) {
    contents[_O] = de_Owner(output[_O], context);
  }
  if (output[_K] != null) {
    contents[_K] = (0, import_smithy_client.expectString)(output[_K]);
  }
  if (output[_VI] != null) {
    contents[_VI] = (0, import_smithy_client.expectString)(output[_VI]);
  }
  if (output[_IL] != null) {
    contents[_IL] = (0, import_smithy_client.parseBoolean)(output[_IL]);
  }
  if (output[_LM] != null) {
    contents[_LM] = (0, import_smithy_client.expectNonNull)((0, import_smithy_client.parseRfc3339DateTimeWithOffset)(output[_LM]));
  }
  return contents;
}, "de_DeleteMarkerEntry");
var de_DeleteMarkerReplication = /* @__PURE__ */ __name((output, context) => {
  const contents = {};
  if (output[_S] != null) {
    contents[_S] = (0, import_smithy_client.expectString)(output[_S]);
  }
  return contents;
}, "de_DeleteMarkerReplication");
var de_DeleteMarkers = /* @__PURE__ */ __name((output, context) => {
  return (output || []).filter((e) => e != null).map((entry) => {
    return de_DeleteMarkerEntry(entry, context);
  });
}, "de_DeleteMarkers");
var de_Destination = /* @__PURE__ */ __name((output, context) => {
  const contents = {};
  if (output[_B] != null) {
    contents[_B] = (0, import_smithy_client.expectString)(output[_B]);
  }
  if (output[_Ac] != null) {
    contents[_Ac] = (0, import_smithy_client.expectString)(output[_Ac]);
  }
  if (output[_SC] != null) {
    contents[_SC] = (0, import_smithy_client.expectString)(output[_SC]);
  }
  if (output[_ACT] != null) {
    contents[_ACT] = de_AccessControlTranslation(output[_ACT], context);
  }
  if (output[_ECn] != null) {
    contents[_ECn] = de_EncryptionConfiguration(output[_ECn], context);
  }
  if (output[_RTe] != null) {
    contents[_RTe] = de_ReplicationTime(output[_RTe], context);
  }
  if (output[_Me] != null) {
    contents[_Me] = de_Metrics(output[_Me], context);
  }
  return contents;
}, "de_Destination");
var de_DestinationResult = /* @__PURE__ */ __name((output, context) => {
  const contents = {};
  if (output[_TBT] != null) {
    contents[_TBT] = (0, import_smithy_client.expectString)(output[_TBT]);
  }
  if (output[_TBA] != null) {
    contents[_TBA] = (0, import_smithy_client.expectString)(output[_TBA]);
  }
  if (output[_TNa] != null) {
    contents[_TNa] = (0, import_smithy_client.expectString)(output[_TNa]);
  }
  return contents;
}, "de_DestinationResult");
var de_EncryptionConfiguration = /* @__PURE__ */ __name((output, context) => {
  const contents = {};
  if (output[_RKKID] != null) {
    contents[_RKKID] = (0, import_smithy_client.expectString)(output[_RKKID]);
  }
  return contents;
}, "de_EncryptionConfiguration");
var de_EndEvent = /* @__PURE__ */ __name((output, context) => {
  const contents = {};
  return contents;
}, "de_EndEvent");
var de__Error = /* @__PURE__ */ __name((output, context) => {
  const contents = {};
  if (output[_K] != null) {
    contents[_K] = (0, import_smithy_client.expectString)(output[_K]);
  }
  if (output[_VI] != null) {
    contents[_VI] = (0, import_smithy_client.expectString)(output[_VI]);
  }
  if (output[_Cod] != null) {
    contents[_Cod] = (0, import_smithy_client.expectString)(output[_Cod]);
  }
  if (output[_Mes] != null) {
    contents[_Mes] = (0, import_smithy_client.expectString)(output[_Mes]);
  }
  return contents;
}, "de__Error");
var de_ErrorDetails = /* @__PURE__ */ __name((output, context) => {
  const contents = {};
  if (output[_EC] != null) {
    contents[_EC] = (0, import_smithy_client.expectString)(output[_EC]);
  }
  if (output[_EM] != null) {
    contents[_EM] = (0, import_smithy_client.expectString)(output[_EM]);
  }
  return contents;
}, "de_ErrorDetails");
var de_ErrorDocument = /* @__PURE__ */ __name((output, context) => {
  const contents = {};
  if (output[_K] != null) {
    contents[_K] = (0, import_smithy_client.expectString)(output[_K]);
  }
  return contents;
}, "de_ErrorDocument");
var de_Errors = /* @__PURE__ */ __name((output, context) => {
  return (output || []).filter((e) => e != null).map((entry) => {
    return de__Error(entry, context);
  });
}, "de_Errors");
var de_EventBridgeConfiguration = /* @__PURE__ */ __name((output, context) => {
  const contents = {};
  return contents;
}, "de_EventBridgeConfiguration");
var de_EventList = /* @__PURE__ */ __name((output, context) => {
  return (output || []).filter((e) => e != null).map((entry) => {
    return (0, import_smithy_client.expectString)(entry);
  });
}, "de_EventList");
var de_ExistingObjectReplication = /* @__PURE__ */ __name((output, context) => {
  const contents = {};
  if (output[_S] != null) {
    contents[_S] = (0, import_smithy_client.expectString)(output[_S]);
  }
  return contents;
}, "de_ExistingObjectReplication");
var de_ExposeHeaders = /* @__PURE__ */ __name((output, context) => {
  return (output || []).filter((e) => e != null).map((entry) => {
    return (0, import_smithy_client.expectString)(entry);
  });
}, "de_ExposeHeaders");
var de_FilterRule = /* @__PURE__ */ __name((output, context) => {
  const contents = {};
  if (output[_N] != null) {
    contents[_N] = (0, import_smithy_client.expectString)(output[_N]);
  }
  if (output[_Va] != null) {
    contents[_Va] = (0, import_smithy_client.expectString)(output[_Va]);
  }
  return contents;
}, "de_FilterRule");
var de_FilterRuleList = /* @__PURE__ */ __name((output, context) => {
  return (output || []).filter((e) => e != null).map((entry) => {
    return de_FilterRule(entry, context);
  });
}, "de_FilterRuleList");
var de_GetBucketMetadataConfigurationResult = /* @__PURE__ */ __name((output, context) => {
  const contents = {};
  if (output[_MCR] != null) {
    contents[_MCR] = de_MetadataConfigurationResult(output[_MCR], context);
  }
  return contents;
}, "de_GetBucketMetadataConfigurationResult");
var de_GetBucketMetadataTableConfigurationResult = /* @__PURE__ */ __name((output, context) => {
  const contents = {};
  if (output[_MTCR] != null) {
    contents[_MTCR] = de_MetadataTableConfigurationResult(output[_MTCR], context);
  }
  if (output[_S] != null) {
    contents[_S] = (0, import_smithy_client.expectString)(output[_S]);
  }
  if (output[_Er] != null) {
    contents[_Er] = de_ErrorDetails(output[_Er], context);
  }
  return contents;
}, "de_GetBucketMetadataTableConfigurationResult");
var de_GetObjectAttributesParts = /* @__PURE__ */ __name((output, context) => {
  const contents = {};
  if (output[_PC] != null) {
    contents[_TPC] = (0, import_smithy_client.strictParseInt32)(output[_PC]);
  }
  if (output[_PNM] != null) {
    contents[_PNM] = (0, import_smithy_client.expectString)(output[_PNM]);
  }
  if (output[_NPNM] != null) {
    contents[_NPNM] = (0, import_smithy_client.expectString)(output[_NPNM]);
  }
  if (output[_MP] != null) {
    contents[_MP] = (0, import_smithy_client.strictParseInt32)(output[_MP]);
  }
  if (output[_IT] != null) {
    contents[_IT] = (0, import_smithy_client.parseBoolean)(output[_IT]);
  }
  if (output.Part === "") {
    contents[_Part] = [];
  } else if (output[_Par] != null) {
    contents[_Part] = de_PartsList((0, import_smithy_client.getArrayIfSingleItem)(output[_Par]), context);
  }
  return contents;
}, "de_GetObjectAttributesParts");
var de_Grant = /* @__PURE__ */ __name((output, context) => {
  const contents = {};
  if (output[_Gra] != null) {
    contents[_Gra] = de_Grantee(output[_Gra], context);
  }
  if (output[_Pe] != null) {
    contents[_Pe] = (0, import_smithy_client.expectString)(output[_Pe]);
  }
  return contents;
}, "de_Grant");
var de_Grantee = /* @__PURE__ */ __name((output, context) => {
  const contents = {};
  if (output[_DN] != null) {
    contents[_DN] = (0, import_smithy_client.expectString)(output[_DN]);
  }
  if (output[_EA] != null) {
    contents[_EA] = (0, import_smithy_client.expectString)(output[_EA]);
  }
  if (output[_ID_] != null) {
    contents[_ID_] = (0, import_smithy_client.expectString)(output[_ID_]);
  }
  if (output[_URI] != null) {
    contents[_URI] = (0, import_smithy_client.expectString)(output[_URI]);
  }
  if (output[_x] != null) {
    contents[_Ty] = (0, import_smithy_client.expectString)(output[_x]);
  }
  return contents;
}, "de_Grantee");
var de_Grants = /* @__PURE__ */ __name((output, context) => {
  return (output || []).filter((e) => e != null).map((entry) => {
    return de_Grant(entry, context);
  });
}, "de_Grants");
var de_IndexDocument = /* @__PURE__ */ __name((output, context) => {
  const contents = {};
  if (output[_Su] != null) {
    contents[_Su] = (0, import_smithy_client.expectString)(output[_Su]);
  }
  return contents;
}, "de_IndexDocument");
var de_Initiator = /* @__PURE__ */ __name((output, context) => {
  const contents = {};
  if (output[_ID_] != null) {
    contents[_ID_] = (0, import_smithy_client.expectString)(output[_ID_]);
  }
  if (output[_DN] != null) {
    contents[_DN] = (0, import_smithy_client.expectString)(output[_DN]);
  }
  return contents;
}, "de_Initiator");
var de_IntelligentTieringAndOperator = /* @__PURE__ */ __name((output, context) => {
  const contents = {};
  if (output[_P] != null) {
    contents[_P] = (0, import_smithy_client.expectString)(output[_P]);
  }
  if (output.Tag === "") {
    contents[_Tag] = [];
  } else if (output[_Ta] != null) {
    contents[_Tag] = de_TagSet((0, import_smithy_client.getArrayIfSingleItem)(output[_Ta]), context);
  }
  return contents;
}, "de_IntelligentTieringAndOperator");
var de_IntelligentTieringConfiguration = /* @__PURE__ */ __name((output, context) => {
  const contents = {};
  if (output[_I] != null) {
    contents[_I] = (0, import_smithy_client.expectString)(output[_I]);
  }
  if (output[_F] != null) {
    contents[_F] = de_IntelligentTieringFilter(output[_F], context);
  }
  if (output[_S] != null) {
    contents[_S] = (0, import_smithy_client.expectString)(output[_S]);
  }
  if (output.Tiering === "") {
    contents[_Tie] = [];
  } else if (output[_Tier] != null) {
    contents[_Tie] = de_TieringList((0, import_smithy_client.getArrayIfSingleItem)(output[_Tier]), context);
  }
  return contents;
}, "de_IntelligentTieringConfiguration");
var de_IntelligentTieringConfigurationList = /* @__PURE__ */ __name((output, context) => {
  return (output || []).filter((e) => e != null).map((entry) => {
    return de_IntelligentTieringConfiguration(entry, context);
  });
}, "de_IntelligentTieringConfigurationList");
var de_IntelligentTieringFilter = /* @__PURE__ */ __name((output, context) => {
  const contents = {};
  if (output[_P] != null) {
    contents[_P] = (0, import_smithy_client.expectString)(output[_P]);
  }
  if (output[_Ta] != null) {
    contents[_Ta] = de_Tag(output[_Ta], context);
  }
  if (output[_A] != null) {
    contents[_A] = de_IntelligentTieringAndOperator(output[_A], context);
  }
  return contents;
}, "de_IntelligentTieringFilter");
var de_InventoryConfiguration = /* @__PURE__ */ __name((output, context) => {
  const contents = {};
  if (output[_Des] != null) {
    contents[_Des] = de_InventoryDestination(output[_Des], context);
  }
  if (output[_IE] != null) {
    contents[_IE] = (0, import_smithy_client.parseBoolean)(output[_IE]);
  }
  if (output[_F] != null) {
    contents[_F] = de_InventoryFilter(output[_F], context);
  }
  if (output[_I] != null) {
    contents[_I] = (0, import_smithy_client.expectString)(output[_I]);
  }
  if (output[_IOV] != null) {
    contents[_IOV] = (0, import_smithy_client.expectString)(output[_IOV]);
  }
  if (output.OptionalFields === "") {
    contents[_OF] = [];
  } else if (output[_OF] != null && output[_OF][_Fi] != null) {
    contents[_OF] = de_InventoryOptionalFields((0, import_smithy_client.getArrayIfSingleItem)(output[_OF][_Fi]), context);
  }
  if (output[_Sc] != null) {
    contents[_Sc] = de_InventorySchedule(output[_Sc], context);
  }
  return contents;
}, "de_InventoryConfiguration");
var de_InventoryConfigurationList = /* @__PURE__ */ __name((output, context) => {
  return (output || []).filter((e) => e != null).map((entry) => {
    return de_InventoryConfiguration(entry, context);
  });
}, "de_InventoryConfigurationList");
var de_InventoryDestination = /* @__PURE__ */ __name((output, context) => {
  const contents = {};
  if (output[_SBD] != null) {
    contents[_SBD] = de_InventoryS3BucketDestination(output[_SBD], context);
  }
  return contents;
}, "de_InventoryDestination");
var de_InventoryEncryption = /* @__PURE__ */ __name((output, context) => {
  const contents = {};
  if (output[_SS] != null) {
    contents[_SSES] = de_SSES3(output[_SS], context);
  }
  if (output[_SK] != null) {
    contents[_SSEKMS] = de_SSEKMS(output[_SK], context);
  }
  return contents;
}, "de_InventoryEncryption");
var de_InventoryFilter = /* @__PURE__ */ __name((output, context) => {
  const contents = {};
  if (output[_P] != null) {
    contents[_P] = (0, import_smithy_client.expectString)(output[_P]);
  }
  return contents;
}, "de_InventoryFilter");
var de_InventoryOptionalFields = /* @__PURE__ */ __name((output, context) => {
  return (output || []).filter((e) => e != null).map((entry) => {
    return (0, import_smithy_client.expectString)(entry);
  });
}, "de_InventoryOptionalFields");
var de_InventoryS3BucketDestination = /* @__PURE__ */ __name((output, context) => {
  const contents = {};
  if (output[_AIc] != null) {
    contents[_AIc] = (0, import_smithy_client.expectString)(output[_AIc]);
  }
  if (output[_B] != null) {
    contents[_B] = (0, import_smithy_client.expectString)(output[_B]);
  }
  if (output[_Fo] != null) {
    contents[_Fo] = (0, import_smithy_client.expectString)(output[_Fo]);
  }
  if (output[_P] != null) {
    contents[_P] = (0, import_smithy_client.expectString)(output[_P]);
  }
  if (output[_En] != null) {
    contents[_En] = de_InventoryEncryption(output[_En], context);
  }
  return contents;
}, "de_InventoryS3BucketDestination");
var de_InventorySchedule = /* @__PURE__ */ __name((output, context) => {
  const contents = {};
  if (output[_Fr] != null) {
    contents[_Fr] = (0, import_smithy_client.expectString)(output[_Fr]);
  }
  return contents;
}, "de_InventorySchedule");
var de_InventoryTableConfigurationResult = /* @__PURE__ */ __name((output, context) => {
  const contents = {};
  if (output[_CSo] != null) {
    contents[_CSo] = (0, import_smithy_client.expectString)(output[_CSo]);
  }
  if (output[_TSa] != null) {
    contents[_TSa] = (0, import_smithy_client.expectString)(output[_TSa]);
  }
  if (output[_Er] != null) {
    contents[_Er] = de_ErrorDetails(output[_Er], context);
  }
  if (output[_TN] != null) {
    contents[_TN] = (0, import_smithy_client.expectString)(output[_TN]);
  }
  if (output[_TAa] != null) {
    contents[_TAa] = (0, import_smithy_client.expectString)(output[_TAa]);
  }
  return contents;
}, "de_InventoryTableConfigurationResult");
var de_JournalTableConfigurationResult = /* @__PURE__ */ __name((output, context) => {
  const contents = {};
  if (output[_TSa] != null) {
    contents[_TSa] = (0, import_smithy_client.expectString)(output[_TSa]);
  }
  if (output[_Er] != null) {
    contents[_Er] = de_ErrorDetails(output[_Er], context);
  }
  if (output[_TN] != null) {
    contents[_TN] = (0, import_smithy_client.expectString)(output[_TN]);
  }
  if (output[_TAa] != null) {
    contents[_TAa] = (0, import_smithy_client.expectString)(output[_TAa]);
  }
  if (output[_REe] != null) {
    contents[_REe] = de_RecordExpiration(output[_REe], context);
  }
  return contents;
}, "de_JournalTableConfigurationResult");
var de_LambdaFunctionConfiguration = /* @__PURE__ */ __name((output, context) => {
  const contents = {};
  if (output[_I] != null) {
    contents[_I] = (0, import_smithy_client.expectString)(output[_I]);
  }
  if (output[_CF] != null) {
    contents[_LFA] = (0, import_smithy_client.expectString)(output[_CF]);
  }
  if (output.Event === "") {
    contents[_Eve] = [];
  } else if (output[_Ev] != null) {
    contents[_Eve] = de_EventList((0, import_smithy_client.getArrayIfSingleItem)(output[_Ev]), context);
  }
  if (output[_F] != null) {
    contents[_F] = de_NotificationConfigurationFilter(output[_F], context);
  }
  return contents;
}, "de_LambdaFunctionConfiguration");
var de_LambdaFunctionConfigurationList = /* @__PURE__ */ __name((output, context) => {
  return (output || []).filter((e) => e != null).map((entry) => {
    return de_LambdaFunctionConfiguration(entry, context);
  });
}, "de_LambdaFunctionConfigurationList");
var de_LifecycleExpiration = /* @__PURE__ */ __name((output, context) => {
  const contents = {};
  if (output[_Dat] != null) {
    contents[_Dat] = (0, import_smithy_client.expectNonNull)((0, import_smithy_client.parseRfc3339DateTimeWithOffset)(output[_Dat]));
  }
  if (output[_Da] != null) {
    contents[_Da] = (0, import_smithy_client.strictParseInt32)(output[_Da]);
  }
  if (output[_EODM] != null) {
    contents[_EODM] = (0, import_smithy_client.parseBoolean)(output[_EODM]);
  }
  return contents;
}, "de_LifecycleExpiration");
var de_LifecycleRule = /* @__PURE__ */ __name((output, context) => {
  const contents = {};
  if (output[_Exp] != null) {
    contents[_Exp] = de_LifecycleExpiration(output[_Exp], context);
  }
  if (output[_ID_] != null) {
    contents[_ID_] = (0, import_smithy_client.expectString)(output[_ID_]);
  }
  if (output[_P] != null) {
    contents[_P] = (0, import_smithy_client.expectString)(output[_P]);
  }
  if (output[_F] != null) {
    contents[_F] = de_LifecycleRuleFilter(output[_F], context);
  }
  if (output[_S] != null) {
    contents[_S] = (0, import_smithy_client.expectString)(output[_S]);
  }
  if (output.Transition === "") {
    contents[_Tr] = [];
  } else if (output[_Tra] != null) {
    contents[_Tr] = de_TransitionList((0, import_smithy_client.getArrayIfSingleItem)(output[_Tra]), context);
  }
  if (output.NoncurrentVersionTransition === "") {
    contents[_NVT] = [];
  } else if (output[_NVTo] != null) {
    contents[_NVT] = de_NoncurrentVersionTransitionList((0, import_smithy_client.getArrayIfSingleItem)(output[_NVTo]), context);
  }
  if (output[_NVE] != null) {
    contents[_NVE] = de_NoncurrentVersionExpiration(output[_NVE], context);
  }
  if (output[_AIMU] != null) {
    contents[_AIMU] = de_AbortIncompleteMultipartUpload(output[_AIMU], context);
  }
  return contents;
}, "de_LifecycleRule");
var de_LifecycleRuleAndOperator = /* @__PURE__ */ __name((output, context) => {
  const contents = {};
  if (output[_P] != null) {
    contents[_P] = (0, import_smithy_client.expectString)(output[_P]);
  }
  if (output.Tag === "") {
    contents[_Tag] = [];
  } else if (output[_Ta] != null) {
    contents[_Tag] = de_TagSet((0, import_smithy_client.getArrayIfSingleItem)(output[_Ta]), context);
  }
  if (output[_OSGT] != null) {
    contents[_OSGT] = (0, import_smithy_client.strictParseLong)(output[_OSGT]);
  }
  if (output[_OSLT] != null) {
    contents[_OSLT] = (0, import_smithy_client.strictParseLong)(output[_OSLT]);
  }
  return contents;
}, "de_LifecycleRuleAndOperator");
var de_LifecycleRuleFilter = /* @__PURE__ */ __name((output, context) => {
  const contents = {};
  if (output[_P] != null) {
    contents[_P] = (0, import_smithy_client.expectString)(output[_P]);
  }
  if (output[_Ta] != null) {
    contents[_Ta] = de_Tag(output[_Ta], context);
  }
  if (output[_OSGT] != null) {
    contents[_OSGT] = (0, import_smithy_client.strictParseLong)(output[_OSGT]);
  }
  if (output[_OSLT] != null) {
    contents[_OSLT] = (0, import_smithy_client.strictParseLong)(output[_OSLT]);
  }
  if (output[_A] != null) {
    contents[_A] = de_LifecycleRuleAndOperator(output[_A], context);
  }
  return contents;
}, "de_LifecycleRuleFilter");
var de_LifecycleRules = /* @__PURE__ */ __name((output, context) => {
  return (output || []).filter((e) => e != null).map((entry) => {
    return de_LifecycleRule(entry, context);
  });
}, "de_LifecycleRules");
var de_LoggingEnabled = /* @__PURE__ */ __name((output, context) => {
  const contents = {};
  if (output[_TB] != null) {
    contents[_TB] = (0, import_smithy_client.expectString)(output[_TB]);
  }
  if (output.TargetGrants === "") {
    contents[_TG] = [];
  } else if (output[_TG] != null && output[_TG][_G] != null) {
    contents[_TG] = de_TargetGrants((0, import_smithy_client.getArrayIfSingleItem)(output[_TG][_G]), context);
  }
  if (output[_TP] != null) {
    contents[_TP] = (0, import_smithy_client.expectString)(output[_TP]);
  }
  if (output[_TOKF] != null) {
    contents[_TOKF] = de_TargetObjectKeyFormat(output[_TOKF], context);
  }
  return contents;
}, "de_LoggingEnabled");
var de_MetadataConfigurationResult = /* @__PURE__ */ __name((output, context) => {
  const contents = {};
  if (output[_DRes] != null) {
    contents[_DRes] = de_DestinationResult(output[_DRes], context);
  }
  if (output[_JTCR] != null) {
    contents[_JTCR] = de_JournalTableConfigurationResult(output[_JTCR], context);
  }
  if (output[_ITCR] != null) {
    contents[_ITCR] = de_InventoryTableConfigurationResult(output[_ITCR], context);
  }
  return contents;
}, "de_MetadataConfigurationResult");
var de_MetadataTableConfigurationResult = /* @__PURE__ */ __name((output, context) => {
  const contents = {};
  if (output[_STDR] != null) {
    contents[_STDR] = de_S3TablesDestinationResult(output[_STDR], context);
  }
  return contents;
}, "de_MetadataTableConfigurationResult");
var de_Metrics = /* @__PURE__ */ __name((output, context) => {
  const contents = {};
  if (output[_S] != null) {
    contents[_S] = (0, import_smithy_client.expectString)(output[_S]);
  }
  if (output[_ETv] != null) {
    contents[_ETv] = de_ReplicationTimeValue(output[_ETv], context);
  }
  return contents;
}, "de_Metrics");
var de_MetricsAndOperator = /* @__PURE__ */ __name((output, context) => {
  const contents = {};
  if (output[_P] != null) {
    contents[_P] = (0, import_smithy_client.expectString)(output[_P]);
  }
  if (output.Tag === "") {
    contents[_Tag] = [];
  } else if (output[_Ta] != null) {
    contents[_Tag] = de_TagSet((0, import_smithy_client.getArrayIfSingleItem)(output[_Ta]), context);
  }
  if (output[_APAc] != null) {
    contents[_APAc] = (0, import_smithy_client.expectString)(output[_APAc]);
  }
  return contents;
}, "de_MetricsAndOperator");
var de_MetricsConfiguration = /* @__PURE__ */ __name((output, context) => {
  const contents = {};
  if (output[_I] != null) {
    contents[_I] = (0, import_smithy_client.expectString)(output[_I]);
  }
  if (output.Filter === "") {
  } else if (output[_F] != null) {
    contents[_F] = de_MetricsFilter((0, import_smithy_client.expectUnion)(output[_F]), context);
  }
  return contents;
}, "de_MetricsConfiguration");
var de_MetricsConfigurationList = /* @__PURE__ */ __name((output, context) => {
  return (output || []).filter((e) => e != null).map((entry) => {
    return de_MetricsConfiguration(entry, context);
  });
}, "de_MetricsConfigurationList");
var de_MetricsFilter = /* @__PURE__ */ __name((output, context) => {
  if (output[_P] != null) {
    return {
      Prefix: (0, import_smithy_client.expectString)(output[_P])
    };
  }
  if (output[_Ta] != null) {
    return {
      Tag: de_Tag(output[_Ta], context)
    };
  }
  if (output[_APAc] != null) {
    return {
      AccessPointArn: (0, import_smithy_client.expectString)(output[_APAc])
    };
  }
  if (output[_A] != null) {
    return {
      And: de_MetricsAndOperator(output[_A], context)
    };
  }
  return { $unknown: Object.entries(output)[0] };
}, "de_MetricsFilter");
var de_MultipartUpload = /* @__PURE__ */ __name((output, context) => {
  const contents = {};
  if (output[_UI] != null) {
    contents[_UI] = (0, import_smithy_client.expectString)(output[_UI]);
  }
  if (output[_K] != null) {
    contents[_K] = (0, import_smithy_client.expectString)(output[_K]);
  }
  if (output[_Ini] != null) {
    contents[_Ini] = (0, import_smithy_client.expectNonNull)((0, import_smithy_client.parseRfc3339DateTimeWithOffset)(output[_Ini]));
  }
  if (output[_SC] != null) {
    contents[_SC] = (0, import_smithy_client.expectString)(output[_SC]);
  }
  if (output[_O] != null) {
    contents[_O] = de_Owner(output[_O], context);
  }
  if (output[_In] != null) {
    contents[_In] = de_Initiator(output[_In], context);
  }
  if (output[_CA] != null) {
    contents[_CA] = (0, import_smithy_client.expectString)(output[_CA]);
  }
  if (output[_CT] != null) {
    contents[_CT] = (0, import_smithy_client.expectString)(output[_CT]);
  }
  return contents;
}, "de_MultipartUpload");
var de_MultipartUploadList = /* @__PURE__ */ __name((output, context) => {
  return (output || []).filter((e) => e != null).map((entry) => {
    return de_MultipartUpload(entry, context);
  });
}, "de_MultipartUploadList");
var de_NoncurrentVersionExpiration = /* @__PURE__ */ __name((output, context) => {
  const contents = {};
  if (output[_ND] != null) {
    contents[_ND] = (0, import_smithy_client.strictParseInt32)(output[_ND]);
  }
  if (output[_NNV] != null) {
    contents[_NNV] = (0, import_smithy_client.strictParseInt32)(output[_NNV]);
  }
  return contents;
}, "de_NoncurrentVersionExpiration");
var de_NoncurrentVersionTransition = /* @__PURE__ */ __name((output, context) => {
  const contents = {};
  if (output[_ND] != null) {
    contents[_ND] = (0, import_smithy_client.strictParseInt32)(output[_ND]);
  }
  if (output[_SC] != null) {
    contents[_SC] = (0, import_smithy_client.expectString)(output[_SC]);
  }
  if (output[_NNV] != null) {
    contents[_NNV] = (0, import_smithy_client.strictParseInt32)(output[_NNV]);
  }
  return contents;
}, "de_NoncurrentVersionTransition");
var de_NoncurrentVersionTransitionList = /* @__PURE__ */ __name((output, context) => {
  return (output || []).filter((e) => e != null).map((entry) => {
    return de_NoncurrentVersionTransition(entry, context);
  });
}, "de_NoncurrentVersionTransitionList");
var de_NotificationConfigurationFilter = /* @__PURE__ */ __name((output, context) => {
  const contents = {};
  if (output[_SKe] != null) {
    contents[_K] = de_S3KeyFilter(output[_SKe], context);
  }
  return contents;
}, "de_NotificationConfigurationFilter");
var de__Object = /* @__PURE__ */ __name((output, context) => {
  const contents = {};
  if (output[_K] != null) {
    contents[_K] = (0, import_smithy_client.expectString)(output[_K]);
  }
  if (output[_LM] != null) {
    contents[_LM] = (0, import_smithy_client.expectNonNull)((0, import_smithy_client.parseRfc3339DateTimeWithOffset)(output[_LM]));
  }
  if (output[_ETa] != null) {
    contents[_ETa] = (0, import_smithy_client.expectString)(output[_ETa]);
  }
  if (output.ChecksumAlgorithm === "") {
    contents[_CA] = [];
  } else if (output[_CA] != null) {
    contents[_CA] = de_ChecksumAlgorithmList((0, import_smithy_client.getArrayIfSingleItem)(output[_CA]), context);
  }
  if (output[_CT] != null) {
    contents[_CT] = (0, import_smithy_client.expectString)(output[_CT]);
  }
  if (output[_Si] != null) {
    contents[_Si] = (0, import_smithy_client.strictParseLong)(output[_Si]);
  }
  if (output[_SC] != null) {
    contents[_SC] = (0, import_smithy_client.expectString)(output[_SC]);
  }
  if (output[_O] != null) {
    contents[_O] = de_Owner(output[_O], context);
  }
  if (output[_RSes] != null) {
    contents[_RSes] = de_RestoreStatus(output[_RSes], context);
  }
  return contents;
}, "de__Object");
var de_ObjectList = /* @__PURE__ */ __name((output, context) => {
  return (output || []).filter((e) => e != null).map((entry) => {
    return de__Object(entry, context);
  });
}, "de_ObjectList");
var de_ObjectLockConfiguration = /* @__PURE__ */ __name((output, context) => {
  const contents = {};
  if (output[_OLE] != null) {
    contents[_OLE] = (0, import_smithy_client.expectString)(output[_OLE]);
  }
  if (output[_Ru] != null) {
    contents[_Ru] = de_ObjectLockRule(output[_Ru], context);
  }
  return contents;
}, "de_ObjectLockConfiguration");
var de_ObjectLockLegalHold = /* @__PURE__ */ __name((output, context) => {
  const contents = {};
  if (output[_S] != null) {
    contents[_S] = (0, import_smithy_client.expectString)(output[_S]);
  }
  return contents;
}, "de_ObjectLockLegalHold");
var de_ObjectLockRetention = /* @__PURE__ */ __name((output, context) => {
  const contents = {};
  if (output[_Mo] != null) {
    contents[_Mo] = (0, import_smithy_client.expectString)(output[_Mo]);
  }
  if (output[_RUD] != null) {
    contents[_RUD] = (0, import_smithy_client.expectNonNull)((0, import_smithy_client.parseRfc3339DateTimeWithOffset)(output[_RUD]));
  }
  return contents;
}, "de_ObjectLockRetention");
var de_ObjectLockRule = /* @__PURE__ */ __name((output, context) => {
  const contents = {};
  if (output[_DRe] != null) {
    contents[_DRe] = de_DefaultRetention(output[_DRe], context);
  }
  return contents;
}, "de_ObjectLockRule");
var de_ObjectPart = /* @__PURE__ */ __name((output, context) => {
  const contents = {};
  if (output[_PN] != null) {
    contents[_PN] = (0, import_smithy_client.strictParseInt32)(output[_PN]);
  }
  if (output[_Si] != null) {
    contents[_Si] = (0, import_smithy_client.strictParseLong)(output[_Si]);
  }
  if (output[_CCRC] != null) {
    contents[_CCRC] = (0, import_smithy_client.expectString)(output[_CCRC]);
  }
  if (output[_CCRCC] != null) {
    contents[_CCRCC] = (0, import_smithy_client.expectString)(output[_CCRCC]);
  }
  if (output[_CCRCNVME] != null) {
    contents[_CCRCNVME] = (0, import_smithy_client.expectString)(output[_CCRCNVME]);
  }
  if (output[_CSHA] != null) {
    contents[_CSHA] = (0, import_smithy_client.expectString)(output[_CSHA]);
  }
  if (output[_CSHAh] != null) {
    contents[_CSHAh] = (0, import_smithy_client.expectString)(output[_CSHAh]);
  }
  return contents;
}, "de_ObjectPart");
var de_ObjectVersion = /* @__PURE__ */ __name((output, context) => {
  const contents = {};
  if (output[_ETa] != null) {
    contents[_ETa] = (0, import_smithy_client.expectString)(output[_ETa]);
  }
  if (output.ChecksumAlgorithm === "") {
    contents[_CA] = [];
  } else if (output[_CA] != null) {
    contents[_CA] = de_ChecksumAlgorithmList((0, import_smithy_client.getArrayIfSingleItem)(output[_CA]), context);
  }
  if (output[_CT] != null) {
    contents[_CT] = (0, import_smithy_client.expectString)(output[_CT]);
  }
  if (output[_Si] != null) {
    contents[_Si] = (0, import_smithy_client.strictParseLong)(output[_Si]);
  }
  if (output[_SC] != null) {
    contents[_SC] = (0, import_smithy_client.expectString)(output[_SC]);
  }
  if (output[_K] != null) {
    contents[_K] = (0, import_smithy_client.expectString)(output[_K]);
  }
  if (output[_VI] != null) {
    contents[_VI] = (0, import_smithy_client.expectString)(output[_VI]);
  }
  if (output[_IL] != null) {
    contents[_IL] = (0, import_smithy_client.parseBoolean)(output[_IL]);
  }
  if (output[_LM] != null) {
    contents[_LM] = (0, import_smithy_client.expectNonNull)((0, import_smithy_client.parseRfc3339DateTimeWithOffset)(output[_LM]));
  }
  if (output[_O] != null) {
    contents[_O] = de_Owner(output[_O], context);
  }
  if (output[_RSes] != null) {
    contents[_RSes] = de_RestoreStatus(output[_RSes], context);
  }
  return contents;
}, "de_ObjectVersion");
var de_ObjectVersionList = /* @__PURE__ */ __name((output, context) => {
  return (output || []).filter((e) => e != null).map((entry) => {
    return de_ObjectVersion(entry, context);
  });
}, "de_ObjectVersionList");
var de_Owner = /* @__PURE__ */ __name((output, context) => {
  const contents = {};
  if (output[_DN] != null) {
    contents[_DN] = (0, import_smithy_client.expectString)(output[_DN]);
  }
  if (output[_ID_] != null) {
    contents[_ID_] = (0, import_smithy_client.expectString)(output[_ID_]);
  }
  return contents;
}, "de_Owner");
var de_OwnershipControls = /* @__PURE__ */ __name((output, context) => {
  const contents = {};
  if (output.Rule === "") {
    contents[_Rul] = [];
  } else if (output[_Ru] != null) {
    contents[_Rul] = de_OwnershipControlsRules((0, import_smithy_client.getArrayIfSingleItem)(output[_Ru]), context);
  }
  return contents;
}, "de_OwnershipControls");
var de_OwnershipControlsRule = /* @__PURE__ */ __name((output, context) => {
  const contents = {};
  if (output[_OO] != null) {
    contents[_OO] = (0, import_smithy_client.expectString)(output[_OO]);
  }
  return contents;
}, "de_OwnershipControlsRule");
var de_OwnershipControlsRules = /* @__PURE__ */ __name((output, context) => {
  return (output || []).filter((e) => e != null).map((entry) => {
    return de_OwnershipControlsRule(entry, context);
  });
}, "de_OwnershipControlsRules");
var de_Part = /* @__PURE__ */ __name((output, context) => {
  const contents = {};
  if (output[_PN] != null) {
    contents[_PN] = (0, import_smithy_client.strictParseInt32)(output[_PN]);
  }
  if (output[_LM] != null) {
    contents[_LM] = (0, import_smithy_client.expectNonNull)((0, import_smithy_client.parseRfc3339DateTimeWithOffset)(output[_LM]));
  }
  if (output[_ETa] != null) {
    contents[_ETa] = (0, import_smithy_client.expectString)(output[_ETa]);
  }
  if (output[_Si] != null) {
    contents[_Si] = (0, import_smithy_client.strictParseLong)(output[_Si]);
  }
  if (output[_CCRC] != null) {
    contents[_CCRC] = (0, import_smithy_client.expectString)(output[_CCRC]);
  }
  if (output[_CCRCC] != null) {
    contents[_CCRCC] = (0, import_smithy_client.expectString)(output[_CCRCC]);
  }
  if (output[_CCRCNVME] != null) {
    contents[_CCRCNVME] = (0, import_smithy_client.expectString)(output[_CCRCNVME]);
  }
  if (output[_CSHA] != null) {
    contents[_CSHA] = (0, import_smithy_client.expectString)(output[_CSHA]);
  }
  if (output[_CSHAh] != null) {
    contents[_CSHAh] = (0, import_smithy_client.expectString)(output[_CSHAh]);
  }
  return contents;
}, "de_Part");
var de_PartitionedPrefix = /* @__PURE__ */ __name((output, context) => {
  const contents = {};
  if (output[_PDS] != null) {
    contents[_PDS] = (0, import_smithy_client.expectString)(output[_PDS]);
  }
  return contents;
}, "de_PartitionedPrefix");
var de_Parts = /* @__PURE__ */ __name((output, context) => {
  return (output || []).filter((e) => e != null).map((entry) => {
    return de_Part(entry, context);
  });
}, "de_Parts");
var de_PartsList = /* @__PURE__ */ __name((output, context) => {
  return (output || []).filter((e) => e != null).map((entry) => {
    return de_ObjectPart(entry, context);
  });
}, "de_PartsList");
var de_PolicyStatus = /* @__PURE__ */ __name((output, context) => {
  const contents = {};
  if (output[_IP] != null) {
    contents[_IP] = (0, import_smithy_client.parseBoolean)(output[_IP]);
  }
  return contents;
}, "de_PolicyStatus");
var de_Progress = /* @__PURE__ */ __name((output, context) => {
  const contents = {};
  if (output[_BS] != null) {
    contents[_BS] = (0, import_smithy_client.strictParseLong)(output[_BS]);
  }
  if (output[_BP] != null) {
    contents[_BP] = (0, import_smithy_client.strictParseLong)(output[_BP]);
  }
  if (output[_BRy] != null) {
    contents[_BRy] = (0, import_smithy_client.strictParseLong)(output[_BRy]);
  }
  return contents;
}, "de_Progress");
var de_PublicAccessBlockConfiguration = /* @__PURE__ */ __name((output, context) => {
  const contents = {};
  if (output[_BPA] != null) {
    contents[_BPA] = (0, import_smithy_client.parseBoolean)(output[_BPA]);
  }
  if (output[_IPA] != null) {
    contents[_IPA] = (0, import_smithy_client.parseBoolean)(output[_IPA]);
  }
  if (output[_BPP] != null) {
    contents[_BPP] = (0, import_smithy_client.parseBoolean)(output[_BPP]);
  }
  if (output[_RPB] != null) {
    contents[_RPB] = (0, import_smithy_client.parseBoolean)(output[_RPB]);
  }
  return contents;
}, "de_PublicAccessBlockConfiguration");
var de_QueueConfiguration = /* @__PURE__ */ __name((output, context) => {
  const contents = {};
  if (output[_I] != null) {
    contents[_I] = (0, import_smithy_client.expectString)(output[_I]);
  }
  if (output[_Qu] != null) {
    contents[_QA] = (0, import_smithy_client.expectString)(output[_Qu]);
  }
  if (output.Event === "") {
    contents[_Eve] = [];
  } else if (output[_Ev] != null) {
    contents[_Eve] = de_EventList((0, import_smithy_client.getArrayIfSingleItem)(output[_Ev]), context);
  }
  if (output[_F] != null) {
    contents[_F] = de_NotificationConfigurationFilter(output[_F], context);
  }
  return contents;
}, "de_QueueConfiguration");
var de_QueueConfigurationList = /* @__PURE__ */ __name((output, context) => {
  return (output || []).filter((e) => e != null).map((entry) => {
    return de_QueueConfiguration(entry, context);
  });
}, "de_QueueConfigurationList");
var de_RecordExpiration = /* @__PURE__ */ __name((output, context) => {
  const contents = {};
  if (output[_Exp] != null) {
    contents[_Exp] = (0, import_smithy_client.expectString)(output[_Exp]);
  }
  if (output[_Da] != null) {
    contents[_Da] = (0, import_smithy_client.strictParseInt32)(output[_Da]);
  }
  return contents;
}, "de_RecordExpiration");
var de_Redirect = /* @__PURE__ */ __name((output, context) => {
  const contents = {};
  if (output[_HN] != null) {
    contents[_HN] = (0, import_smithy_client.expectString)(output[_HN]);
  }
  if (output[_HRC] != null) {
    contents[_HRC] = (0, import_smithy_client.expectString)(output[_HRC]);
  }
  if (output[_Pr] != null) {
    contents[_Pr] = (0, import_smithy_client.expectString)(output[_Pr]);
  }
  if (output[_RKPW] != null) {
    contents[_RKPW] = (0, import_smithy_client.expectString)(output[_RKPW]);
  }
  if (output[_RKW] != null) {
    contents[_RKW] = (0, import_smithy_client.expectString)(output[_RKW]);
  }
  return contents;
}, "de_Redirect");
var de_RedirectAllRequestsTo = /* @__PURE__ */ __name((output, context) => {
  const contents = {};
  if (output[_HN] != null) {
    contents[_HN] = (0, import_smithy_client.expectString)(output[_HN]);
  }
  if (output[_Pr] != null) {
    contents[_Pr] = (0, import_smithy_client.expectString)(output[_Pr]);
  }
  return contents;
}, "de_RedirectAllRequestsTo");
var de_ReplicaModifications = /* @__PURE__ */ __name((output, context) => {
  const contents = {};
  if (output[_S] != null) {
    contents[_S] = (0, import_smithy_client.expectString)(output[_S]);
  }
  return contents;
}, "de_ReplicaModifications");
var de_ReplicationConfiguration = /* @__PURE__ */ __name((output, context) => {
  const contents = {};
  if (output[_Ro] != null) {
    contents[_Ro] = (0, import_smithy_client.expectString)(output[_Ro]);
  }
  if (output.Rule === "") {
    contents[_Rul] = [];
  } else if (output[_Ru] != null) {
    contents[_Rul] = de_ReplicationRules((0, import_smithy_client.getArrayIfSingleItem)(output[_Ru]), context);
  }
  return contents;
}, "de_ReplicationConfiguration");
var de_ReplicationRule = /* @__PURE__ */ __name((output, context) => {
  const contents = {};
  if (output[_ID_] != null) {
    contents[_ID_] = (0, import_smithy_client.expectString)(output[_ID_]);
  }
  if (output[_Pri] != null) {
    contents[_Pri] = (0, import_smithy_client.strictParseInt32)(output[_Pri]);
  }
  if (output[_P] != null) {
    contents[_P] = (0, import_smithy_client.expectString)(output[_P]);
  }
  if (output[_F] != null) {
    contents[_F] = de_ReplicationRuleFilter(output[_F], context);
  }
  if (output[_S] != null) {
    contents[_S] = (0, import_smithy_client.expectString)(output[_S]);
  }
  if (output[_SSC] != null) {
    contents[_SSC] = de_SourceSelectionCriteria(output[_SSC], context);
  }
  if (output[_EOR] != null) {
    contents[_EOR] = de_ExistingObjectReplication(output[_EOR], context);
  }
  if (output[_Des] != null) {
    contents[_Des] = de_Destination(output[_Des], context);
  }
  if (output[_DMR] != null) {
    contents[_DMR] = de_DeleteMarkerReplication(output[_DMR], context);
  }
  return contents;
}, "de_ReplicationRule");
var de_ReplicationRuleAndOperator = /* @__PURE__ */ __name((output, context) => {
  const contents = {};
  if (output[_P] != null) {
    contents[_P] = (0, import_smithy_client.expectString)(output[_P]);
  }
  if (output.Tag === "") {
    contents[_Tag] = [];
  } else if (output[_Ta] != null) {
    contents[_Tag] = de_TagSet((0, import_smithy_client.getArrayIfSingleItem)(output[_Ta]), context);
  }
  return contents;
}, "de_ReplicationRuleAndOperator");
var de_ReplicationRuleFilter = /* @__PURE__ */ __name((output, context) => {
  const contents = {};
  if (output[_P] != null) {
    contents[_P] = (0, import_smithy_client.expectString)(output[_P]);
  }
  if (output[_Ta] != null) {
    contents[_Ta] = de_Tag(output[_Ta], context);
  }
  if (output[_A] != null) {
    contents[_A] = de_ReplicationRuleAndOperator(output[_A], context);
  }
  return contents;
}, "de_ReplicationRuleFilter");
var de_ReplicationRules = /* @__PURE__ */ __name((output, context) => {
  return (output || []).filter((e) => e != null).map((entry) => {
    return de_ReplicationRule(entry, context);
  });
}, "de_ReplicationRules");
var de_ReplicationTime = /* @__PURE__ */ __name((output, context) => {
  const contents = {};
  if (output[_S] != null) {
    contents[_S] = (0, import_smithy_client.expectString)(output[_S]);
  }
  if (output[_Tim] != null) {
    contents[_Tim] = de_ReplicationTimeValue(output[_Tim], context);
  }
  return contents;
}, "de_ReplicationTime");
var de_ReplicationTimeValue = /* @__PURE__ */ __name((output, context) => {
  const contents = {};
  if (output[_Mi] != null) {
    contents[_Mi] = (0, import_smithy_client.strictParseInt32)(output[_Mi]);
  }
  return contents;
}, "de_ReplicationTimeValue");
var de_RestoreStatus = /* @__PURE__ */ __name((output, context) => {
  const contents = {};
  if (output[_IRIP] != null) {
    contents[_IRIP] = (0, import_smithy_client.parseBoolean)(output[_IRIP]);
  }
  if (output[_REDe] != null) {
    contents[_REDe] = (0, import_smithy_client.expectNonNull)((0, import_smithy_client.parseRfc3339DateTimeWithOffset)(output[_REDe]));
  }
  return contents;
}, "de_RestoreStatus");
var de_RoutingRule = /* @__PURE__ */ __name((output, context) => {
  const contents = {};
  if (output[_Con] != null) {
    contents[_Con] = de_Condition(output[_Con], context);
  }
  if (output[_Red] != null) {
    contents[_Red] = de_Redirect(output[_Red], context);
  }
  return contents;
}, "de_RoutingRule");
var de_RoutingRules = /* @__PURE__ */ __name((output, context) => {
  return (output || []).filter((e) => e != null).map((entry) => {
    return de_RoutingRule(entry, context);
  });
}, "de_RoutingRules");
var de_S3KeyFilter = /* @__PURE__ */ __name((output, context) => {
  const contents = {};
  if (output.FilterRule === "") {
    contents[_FRi] = [];
  } else if (output[_FR] != null) {
    contents[_FRi] = de_FilterRuleList((0, import_smithy_client.getArrayIfSingleItem)(output[_FR]), context);
  }
  return contents;
}, "de_S3KeyFilter");
var de_S3TablesDestinationResult = /* @__PURE__ */ __name((output, context) => {
  const contents = {};
  if (output[_TBA] != null) {
    contents[_TBA] = (0, import_smithy_client.expectString)(output[_TBA]);
  }
  if (output[_TN] != null) {
    contents[_TN] = (0, import_smithy_client.expectString)(output[_TN]);
  }
  if (output[_TAa] != null) {
    contents[_TAa] = (0, import_smithy_client.expectString)(output[_TAa]);
  }
  if (output[_TNa] != null) {
    contents[_TNa] = (0, import_smithy_client.expectString)(output[_TNa]);
  }
  return contents;
}, "de_S3TablesDestinationResult");
var de_ServerSideEncryptionByDefault = /* @__PURE__ */ __name((output, context) => {
  const contents = {};
  if (output[_SSEA] != null) {
    contents[_SSEA] = (0, import_smithy_client.expectString)(output[_SSEA]);
  }
  if (output[_KMSMKID] != null) {
    contents[_KMSMKID] = (0, import_smithy_client.expectString)(output[_KMSMKID]);
  }
  return contents;
}, "de_ServerSideEncryptionByDefault");
var de_ServerSideEncryptionConfiguration = /* @__PURE__ */ __name((output, context) => {
  const contents = {};
  if (output.Rule === "") {
    contents[_Rul] = [];
  } else if (output[_Ru] != null) {
    contents[_Rul] = de_ServerSideEncryptionRules((0, import_smithy_client.getArrayIfSingleItem)(output[_Ru]), context);
  }
  return contents;
}, "de_ServerSideEncryptionConfiguration");
var de_ServerSideEncryptionRule = /* @__PURE__ */ __name((output, context) => {
  const contents = {};
  if (output[_ASSEBD] != null) {
    contents[_ASSEBD] = de_ServerSideEncryptionByDefault(output[_ASSEBD], context);
  }
  if (output[_BKE] != null) {
    contents[_BKE] = (0, import_smithy_client.parseBoolean)(output[_BKE]);
  }
  return contents;
}, "de_ServerSideEncryptionRule");
var de_ServerSideEncryptionRules = /* @__PURE__ */ __name((output, context) => {
  return (output || []).filter((e) => e != null).map((entry) => {
    return de_ServerSideEncryptionRule(entry, context);
  });
}, "de_ServerSideEncryptionRules");
var de_SessionCredentials = /* @__PURE__ */ __name((output, context) => {
  const contents = {};
  if (output[_AKI] != null) {
    contents[_AKI] = (0, import_smithy_client.expectString)(output[_AKI]);
  }
  if (output[_SAK] != null) {
    contents[_SAK] = (0, import_smithy_client.expectString)(output[_SAK]);
  }
  if (output[_ST] != null) {
    contents[_ST] = (0, import_smithy_client.expectString)(output[_ST]);
  }
  if (output[_Exp] != null) {
    contents[_Exp] = (0, import_smithy_client.expectNonNull)((0, import_smithy_client.parseRfc3339DateTimeWithOffset)(output[_Exp]));
  }
  return contents;
}, "de_SessionCredentials");
var de_SimplePrefix = /* @__PURE__ */ __name((output, context) => {
  const contents = {};
  return contents;
}, "de_SimplePrefix");
var de_SourceSelectionCriteria = /* @__PURE__ */ __name((output, context) => {
  const contents = {};
  if (output[_SKEO] != null) {
    contents[_SKEO] = de_SseKmsEncryptedObjects(output[_SKEO], context);
  }
  if (output[_RM] != null) {
    contents[_RM] = de_ReplicaModifications(output[_RM], context);
  }
  return contents;
}, "de_SourceSelectionCriteria");
var de_SSEKMS = /* @__PURE__ */ __name((output, context) => {
  const contents = {};
  if (output[_KI] != null) {
    contents[_KI] = (0, import_smithy_client.expectString)(output[_KI]);
  }
  return contents;
}, "de_SSEKMS");
var de_SseKmsEncryptedObjects = /* @__PURE__ */ __name((output, context) => {
  const contents = {};
  if (output[_S] != null) {
    contents[_S] = (0, import_smithy_client.expectString)(output[_S]);
  }
  return contents;
}, "de_SseKmsEncryptedObjects");
var de_SSES3 = /* @__PURE__ */ __name((output, context) => {
  const contents = {};
  return contents;
}, "de_SSES3");
var de_Stats = /* @__PURE__ */ __name((output, context) => {
  const contents = {};
  if (output[_BS] != null) {
    contents[_BS] = (0, import_smithy_client.strictParseLong)(output[_BS]);
  }
  if (output[_BP] != null) {
    contents[_BP] = (0, import_smithy_client.strictParseLong)(output[_BP]);
  }
  if (output[_BRy] != null) {
    contents[_BRy] = (0, import_smithy_client.strictParseLong)(output[_BRy]);
  }
  return contents;
}, "de_Stats");
var de_StorageClassAnalysis = /* @__PURE__ */ __name((output, context) => {
  const contents = {};
  if (output[_DE] != null) {
    contents[_DE] = de_StorageClassAnalysisDataExport(output[_DE], context);
  }
  return contents;
}, "de_StorageClassAnalysis");
var de_StorageClassAnalysisDataExport = /* @__PURE__ */ __name((output, context) => {
  const contents = {};
  if (output[_OSV] != null) {
    contents[_OSV] = (0, import_smithy_client.expectString)(output[_OSV]);
  }
  if (output[_Des] != null) {
    contents[_Des] = de_AnalyticsExportDestination(output[_Des], context);
  }
  return contents;
}, "de_StorageClassAnalysisDataExport");
var de_Tag = /* @__PURE__ */ __name((output, context) => {
  const contents = {};
  if (output[_K] != null) {
    contents[_K] = (0, import_smithy_client.expectString)(output[_K]);
  }
  if (output[_Va] != null) {
    contents[_Va] = (0, import_smithy_client.expectString)(output[_Va]);
  }
  return contents;
}, "de_Tag");
var de_TagSet = /* @__PURE__ */ __name((output, context) => {
  return (output || []).filter((e) => e != null).map((entry) => {
    return de_Tag(entry, context);
  });
}, "de_TagSet");
var de_TargetGrant = /* @__PURE__ */ __name((output, context) => {
  const contents = {};
  if (output[_Gra] != null) {
    contents[_Gra] = de_Grantee(output[_Gra], context);
  }
  if (output[_Pe] != null) {
    contents[_Pe] = (0, import_smithy_client.expectString)(output[_Pe]);
  }
  return contents;
}, "de_TargetGrant");
var de_TargetGrants = /* @__PURE__ */ __name((output, context) => {
  return (output || []).filter((e) => e != null).map((entry) => {
    return de_TargetGrant(entry, context);
  });
}, "de_TargetGrants");
var de_TargetObjectKeyFormat = /* @__PURE__ */ __name((output, context) => {
  const contents = {};
  if (output[_SPi] != null) {
    contents[_SPi] = de_SimplePrefix(output[_SPi], context);
  }
  if (output[_PP] != null) {
    contents[_PP] = de_PartitionedPrefix(output[_PP], context);
  }
  return contents;
}, "de_TargetObjectKeyFormat");
var de_Tiering = /* @__PURE__ */ __name((output, context) => {
  const contents = {};
  if (output[_Da] != null) {
    contents[_Da] = (0, import_smithy_client.strictParseInt32)(output[_Da]);
  }
  if (output[_AT] != null) {
    contents[_AT] = (0, import_smithy_client.expectString)(output[_AT]);
  }
  return contents;
}, "de_Tiering");
var de_TieringList = /* @__PURE__ */ __name((output, context) => {
  return (output || []).filter((e) => e != null).map((entry) => {
    return de_Tiering(entry, context);
  });
}, "de_TieringList");
var de_TopicConfiguration = /* @__PURE__ */ __name((output, context) => {
  const contents = {};
  if (output[_I] != null) {
    contents[_I] = (0, import_smithy_client.expectString)(output[_I]);
  }
  if (output[_Top] != null) {
    contents[_TA] = (0, import_smithy_client.expectString)(output[_Top]);
  }
  if (output.Event === "") {
    contents[_Eve] = [];
  } else if (output[_Ev] != null) {
    contents[_Eve] = de_EventList((0, import_smithy_client.getArrayIfSingleItem)(output[_Ev]), context);
  }
  if (output[_F] != null) {
    contents[_F] = de_NotificationConfigurationFilter(output[_F], context);
  }
  return contents;
}, "de_TopicConfiguration");
var de_TopicConfigurationList = /* @__PURE__ */ __name((output, context) => {
  return (output || []).filter((e) => e != null).map((entry) => {
    return de_TopicConfiguration(entry, context);
  });
}, "de_TopicConfigurationList");
var de_Transition = /* @__PURE__ */ __name((output, context) => {
  const contents = {};
  if (output[_Dat] != null) {
    contents[_Dat] = (0, import_smithy_client.expectNonNull)((0, import_smithy_client.parseRfc3339DateTimeWithOffset)(output[_Dat]));
  }
  if (output[_Da] != null) {
    contents[_Da] = (0, import_smithy_client.strictParseInt32)(output[_Da]);
  }
  if (output[_SC] != null) {
    contents[_SC] = (0, import_smithy_client.expectString)(output[_SC]);
  }
  return contents;
}, "de_Transition");
var de_TransitionList = /* @__PURE__ */ __name((output, context) => {
  return (output || []).filter((e) => e != null).map((entry) => {
    return de_Transition(entry, context);
  });
}, "de_TransitionList");
var deserializeMetadata = /* @__PURE__ */ __name((output) => ({
  httpStatusCode: output.statusCode,
  requestId: output.headers["x-amzn-requestid"] ?? output.headers["x-amzn-request-id"] ?? output.headers["x-amz-request-id"],
  extendedRequestId: output.headers["x-amz-id-2"],
  cfId: output.headers["x-amz-cf-id"]
}), "deserializeMetadata");
var collectBodyString = /* @__PURE__ */ __name((streamBody, context) => (0, import_smithy_client.collectBody)(streamBody, context).then((body) => context.utf8Encoder(body)), "collectBodyString");
var _A = "And";
var _AAO = "AnalyticsAndOperator";
var _AC = "AnalyticsConfiguration";
var _ACL = "ACL";
var _ACLc = "AccessControlList";
var _ACLn = "AnalyticsConfigurationList";
var _ACP = "AccessControlPolicy";
var _ACT = "AccessControlTranslation";
var _ACc = "AccelerateConfiguration";
var _AD = "AbortDate";
var _AED = "AnalyticsExportDestination";
var _AF = "AnalyticsFilter";
var _AH = "AllowedHeader";
var _AHl = "AllowedHeaders";
var _AI = "AnalyticsId";
var _AIMU = "AbortIncompleteMultipartUpload";
var _AIc = "AccountId";
var _AKI = "AccessKeyId";
var _AM = "AllowedMethod";
var _AMl = "AllowedMethods";
var _AO = "AllowedOrigin";
var _AOl = "AllowedOrigins";
var _APA = "AccessPointAlias";
var _APAc = "AccessPointArn";
var _AQRD = "AllowQuotedRecordDelimiter";
var _AR = "AcceptRanges";
var _ARI = "AbortRuleId";
var _AS = "ArchiveStatus";
var _ASBD = "AnalyticsS3BucketDestination";
var _ASEFF = "AnalyticsS3ExportFileFormat";
var _ASSEBD = "ApplyServerSideEncryptionByDefault";
var _AT = "AccessTier";
var _Ac = "Account";
var _B = "Bucket";
var _BA = "BucketArn";
var _BAI = "BucketAccountId";
var _BAS = "BucketAccelerateStatus";
var _BGR = "BypassGovernanceRetention";
var _BI = "BucketInfo";
var _BKE = "BucketKeyEnabled";
var _BLC = "BucketLifecycleConfiguration";
var _BLCu = "BucketLocationConstraint";
var _BLN = "BucketLocationName";
var _BLP = "BucketLogsPermission";
var _BLS = "BucketLoggingStatus";
var _BLT = "BucketLocationType";
var _BN = "BucketName";
var _BP = "BytesProcessed";
var _BPA = "BlockPublicAcls";
var _BPP = "BlockPublicPolicy";
var _BR = "BucketRegion";
var _BRy = "BytesReturned";
var _BS = "BytesScanned";
var _BT = "BucketType";
var _BVS = "BucketVersioningStatus";
var _Bu = "Buckets";
var _C = "Credentials";
var _CA = "ChecksumAlgorithm";
var _CACL = "CannedACL";
var _CBC = "CreateBucketConfiguration";
var _CC = "CacheControl";
var _CCRC = "ChecksumCRC32";
var _CCRCC = "ChecksumCRC32C";
var _CCRCNVME = "ChecksumCRC64NVME";
var _CD = "ContentDisposition";
var _CDr = "CreationDate";
var _CE = "ContentEncoding";
var _CF = "CloudFunction";
var _CFC = "CloudFunctionConfiguration";
var _CL = "ContentLanguage";
var _CLo = "ContentLength";
var _CM = "ChecksumMode";
var _CMD = "ContentMD5";
var _CMU = "CompletedMultipartUpload";
var _CORSC = "CORSConfiguration";
var _CORSR = "CORSRule";
var _CORSRu = "CORSRules";
var _CP = "CommonPrefixes";
var _CPo = "CompletedPart";
var _CR = "ContentRange";
var _CRSBA = "ConfirmRemoveSelfBucketAccess";
var _CS = "CopySource";
var _CSHA = "ChecksumSHA1";
var _CSHAh = "ChecksumSHA256";
var _CSIM = "CopySourceIfMatch";
var _CSIMS = "CopySourceIfModifiedSince";
var _CSINM = "CopySourceIfNoneMatch";
var _CSIUS = "CopySourceIfUnmodifiedSince";
var _CSR = "CopySourceRange";
var _CSSSECA = "CopySourceSSECustomerAlgorithm";
var _CSSSECK = "CopySourceSSECustomerKey";
var _CSSSECKMD = "CopySourceSSECustomerKeyMD5";
var _CSV = "CSV";
var _CSVI = "CopySourceVersionId";
var _CSVIn = "CSVInput";
var _CSVO = "CSVOutput";
var _CSo = "ConfigurationState";
var _CT = "ChecksumType";
var _CTl = "ClientToken";
var _CTo = "ContentType";
var _CTom = "CompressionType";
var _CTon = "ContinuationToken";
var _Ch = "Checksum";
var _Co = "Contents";
var _Cod = "Code";
var _Com = "Comments";
var _Con = "Condition";
var _D = "Delimiter";
var _DAI = "DaysAfterInitiation";
var _DE = "DataExport";
var _DIM = "DestinationIfMatch";
var _DIMS = "DestinationIfModifiedSince";
var _DINM = "DestinationIfNoneMatch";
var _DIUS = "DestinationIfUnmodifiedSince";
var _DM = "DeleteMarker";
var _DMR = "DeleteMarkerReplication";
var _DMRS = "DeleteMarkerReplicationStatus";
var _DMVI = "DeleteMarkerVersionId";
var _DMe = "DeleteMarkers";
var _DN = "DisplayName";
var _DR = "DataRedundancy";
var _DRe = "DefaultRetention";
var _DRes = "DestinationResult";
var _Da = "Days";
var _Dat = "Date";
var _De = "Deleted";
var _Del = "Delete";
var _Des = "Destination";
var _Desc = "Description";
var _E = "Expires";
var _EA = "EmailAddress";
var _EBC = "EventBridgeConfiguration";
var _EBO = "ExpectedBucketOwner";
var _EC = "ErrorCode";
var _ECn = "EncryptionConfiguration";
var _ED = "ErrorDocument";
var _EH = "ExposeHeaders";
var _EHx = "ExposeHeader";
var _EM = "ErrorMessage";
var _EODM = "ExpiredObjectDeleteMarker";
var _EOR = "ExistingObjectReplication";
var _EORS = "ExistingObjectReplicationStatus";
var _ERP = "EnableRequestProgress";
var _ES = "ExpiresString";
var _ESBO = "ExpectedSourceBucketOwner";
var _ESx = "ExpirationStatus";
var _ESxp = "ExpirationState";
var _ET = "EncodingType";
var _ETa = "ETag";
var _ETn = "EncryptionType";
var _ETv = "EventThreshold";
var _ETx = "ExpressionType";
var _En = "Encryption";
var _Ena = "Enabled";
var _End = "End";
var _Er = "Error";
var _Err = "Errors";
var _Ev = "Event";
var _Eve = "Events";
var _Ex = "Expression";
var _Exp = "Expiration";
var _F = "Filter";
var _FD = "FieldDelimiter";
var _FHI = "FileHeaderInfo";
var _FO = "FetchOwner";
var _FR = "FilterRule";
var _FRN = "FilterRuleName";
var _FRV = "FilterRuleValue";
var _FRi = "FilterRules";
var _Fi = "Field";
var _Fo = "Format";
var _Fr = "Frequency";
var _G = "Grant";
var _GFC = "GrantFullControl";
var _GJP = "GlacierJobParameters";
var _GR = "GrantRead";
var _GRACP = "GrantReadACP";
var _GW = "GrantWrite";
var _GWACP = "GrantWriteACP";
var _Gr = "Grants";
var _Gra = "Grantee";
var _HECRE = "HttpErrorCodeReturnedEquals";
var _HN = "HostName";
var _HRC = "HttpRedirectCode";
var _I = "Id";
var _IC = "InventoryConfiguration";
var _ICL = "InventoryConfigurationList";
var _ICS = "InventoryConfigurationState";
var _ID = "IndexDocument";
var _ID_ = "ID";
var _IDn = "InventoryDestination";
var _IE = "IsEnabled";
var _IEn = "InventoryEncryption";
var _IF = "InventoryFilter";
var _IFn = "InventoryFormat";
var _IFnv = "InventoryFrequency";
var _II = "InventoryId";
var _IIOV = "InventoryIncludedObjectVersions";
var _IL = "IsLatest";
var _IM = "IfMatch";
var _IMIT = "IfMatchInitiatedTime";
var _IMLMT = "IfMatchLastModifiedTime";
var _IMS = "IfMatchSize";
var _IMSf = "IfModifiedSince";
var _INM = "IfNoneMatch";
var _IOF = "InventoryOptionalField";
var _IOV = "IncludedObjectVersions";
var _IP = "IsPublic";
var _IPA = "IgnorePublicAcls";
var _IRIP = "IsRestoreInProgress";
var _IS = "InputSerialization";
var _ISBD = "InventoryS3BucketDestination";
var _ISn = "InventorySchedule";
var _IT = "IsTruncated";
var _ITAO = "IntelligentTieringAndOperator";
var _ITAT = "IntelligentTieringAccessTier";
var _ITC = "IntelligentTieringConfiguration";
var _ITCL = "IntelligentTieringConfigurationList";
var _ITCR = "InventoryTableConfigurationResult";
var _ITCU = "InventoryTableConfigurationUpdates";
var _ITCn = "InventoryTableConfiguration";
var _ITD = "IntelligentTieringDays";
var _ITF = "IntelligentTieringFilter";
var _ITI = "IntelligentTieringId";
var _ITS = "IntelligentTieringStatus";
var _IUS = "IfUnmodifiedSince";
var _In = "Initiator";
var _Ini = "Initiated";
var _JSON = "JSON";
var _JSONI = "JSONInput";
var _JSONO = "JSONOutput";
var _JSONT = "JSONType";
var _JTC = "JournalTableConfiguration";
var _JTCR = "JournalTableConfigurationResult";
var _JTCU = "JournalTableConfigurationUpdates";
var _K = "Key";
var _KC = "KeyCount";
var _KI = "KeyId";
var _KKA = "KmsKeyArn";
var _KM = "KeyMarker";
var _KMSC = "KMSContext";
var _KMSKI = "KMSKeyId";
var _KMSMKID = "KMSMasterKeyID";
var _KPE = "KeyPrefixEquals";
var _L = "Location";
var _LC = "LocationConstraint";
var _LE = "LoggingEnabled";
var _LEi = "LifecycleExpiration";
var _LFA = "LambdaFunctionArn";
var _LFC = "LambdaFunctionConfigurations";
var _LFCa = "LambdaFunctionConfiguration";
var _LI = "LocationInfo";
var _LM = "LastModified";
var _LMT = "LastModifiedTime";
var _LNAS = "LocationNameAsString";
var _LP = "LocationPrefix";
var _LR = "LifecycleRule";
var _LRAO = "LifecycleRuleAndOperator";
var _LRF = "LifecycleRuleFilter";
var _LT = "LocationType";
var _M = "Marker";
var _MAO = "MetricsAndOperator";
var _MAS = "MaxAgeSeconds";
var _MB = "MaxBuckets";
var _MC = "MetricsConfiguration";
var _MCL = "MetricsConfigurationList";
var _MCR = "MetadataConfigurationResult";
var _MCe = "MetadataConfiguration";
var _MD = "MetadataDirective";
var _MDB = "MaxDirectoryBuckets";
var _MDf = "MfaDelete";
var _ME = "MetadataEntry";
var _MF = "MetricsFilter";
var _MFA = "MFA";
var _MFAD = "MFADelete";
var _MI = "MetricsId";
var _MK = "MaxKeys";
var _MKe = "MetadataKey";
var _MM = "MissingMeta";
var _MOS = "MpuObjectSize";
var _MP = "MaxParts";
var _MS = "MetricsStatus";
var _MTC = "MetadataTableConfiguration";
var _MTCR = "MetadataTableConfigurationResult";
var _MTEC = "MetadataTableEncryptionConfiguration";
var _MU = "MaxUploads";
var _MV = "MetadataValue";
var _Me = "Metrics";
var _Mes = "Message";
var _Mi = "Minutes";
var _Mo = "Mode";
var _N = "Name";
var _NC = "NotificationConfiguration";
var _NCF = "NotificationConfigurationFilter";
var _NCT = "NextContinuationToken";
var _ND = "NoncurrentDays";
var _NI = "NotificationId";
var _NKM = "NextKeyMarker";
var _NM = "NextMarker";
var _NNV = "NewerNoncurrentVersions";
var _NPNM = "NextPartNumberMarker";
var _NUIM = "NextUploadIdMarker";
var _NVE = "NoncurrentVersionExpiration";
var _NVIM = "NextVersionIdMarker";
var _NVT = "NoncurrentVersionTransitions";
var _NVTo = "NoncurrentVersionTransition";
var _O = "Owner";
var _OA = "ObjectAttributes";
var _OC = "OwnershipControls";
var _OCACL = "ObjectCannedACL";
var _OCR = "OwnershipControlsRule";
var _OF = "OptionalFields";
var _OI = "ObjectIdentifier";
var _OK = "ObjectKey";
var _OL = "OutputLocation";
var _OLC = "ObjectLockConfiguration";
var _OLE = "ObjectLockEnabled";
var _OLEFB = "ObjectLockEnabledForBucket";
var _OLLH = "ObjectLockLegalHold";
var _OLLHS = "ObjectLockLegalHoldStatus";
var _OLM = "ObjectLockMode";
var _OLR = "ObjectLockRetention";
var _OLRM = "ObjectLockRetentionMode";
var _OLRUD = "ObjectLockRetainUntilDate";
var _OLRb = "ObjectLockRule";
var _OO = "ObjectOwnership";
var _OOA = "OptionalObjectAttributes";
var _OOw = "OwnerOverride";
var _OP = "ObjectParts";
var _OS = "OutputSerialization";
var _OSGT = "ObjectSizeGreaterThan";
var _OSGTB = "ObjectSizeGreaterThanBytes";
var _OSLT = "ObjectSizeLessThan";
var _OSLTB = "ObjectSizeLessThanBytes";
var _OSV = "OutputSchemaVersion";
var _OSb = "ObjectSize";
var _OVI = "ObjectVersionId";
var _Ob = "Objects";
var _P = "Prefix";
var _PABC = "PublicAccessBlockConfiguration";
var _PC = "PartsCount";
var _PDS = "PartitionDateSource";
var _PI = "ParquetInput";
var _PN = "PartNumber";
var _PNM = "PartNumberMarker";
var _PP = "PartitionedPrefix";
var _Pa = "Payer";
var _Par = "Part";
var _Parq = "Parquet";
var _Part = "Parts";
var _Pe = "Permission";
var _Pr = "Protocol";
var _Pri = "Priority";
var _Q = "Quiet";
var _QA = "QueueArn";
var _QC = "QueueConfiguration";
var _QCu = "QueueConfigurations";
var _QCuo = "QuoteCharacter";
var _QEC = "QuoteEscapeCharacter";
var _QF = "QuoteFields";
var _Qu = "Queue";
var _R = "Range";
var _RART = "RedirectAllRequestsTo";
var _RC = "RequestCharged";
var _RCC = "ResponseCacheControl";
var _RCD = "ResponseContentDisposition";
var _RCE = "ResponseContentEncoding";
var _RCL = "ResponseContentLanguage";
var _RCT = "ResponseContentType";
var _RCe = "ReplicationConfiguration";
var _RD = "RecordDelimiter";
var _RE = "ResponseExpires";
var _RED = "RecordExpirationDays";
var _REDe = "RestoreExpiryDate";
var _REe = "RecordExpiration";
var _RKKID = "ReplicaKmsKeyID";
var _RKPW = "ReplaceKeyPrefixWith";
var _RKW = "ReplaceKeyWith";
var _RM = "ReplicaModifications";
var _RMS = "ReplicaModificationsStatus";
var _ROP = "RestoreOutputPath";
var _RP = "RequestPayer";
var _RPB = "RestrictPublicBuckets";
var _RPC = "RequestPaymentConfiguration";
var _RPe = "RequestProgress";
var _RR = "RequestRoute";
var _RRAO = "ReplicationRuleAndOperator";
var _RRF = "ReplicationRuleFilter";
var _RRS = "ReplicationRuleStatus";
var _RRT = "RestoreRequestType";
var _RRe = "ReplicationRule";
var _RRes = "RestoreRequest";
var _RRo = "RoutingRules";
var _RRou = "RoutingRule";
var _RS = "RenameSource";
var _RSe = "ReplicationStatus";
var _RSes = "RestoreStatus";
var _RT = "RequestToken";
var _RTS = "ReplicationTimeStatus";
var _RTV = "ReplicationTimeValue";
var _RTe = "ReplicationTime";
var _RUD = "RetainUntilDate";
var _Re = "Restore";
var _Red = "Redirect";
var _Ro = "Role";
var _Ru = "Rule";
var _Rul = "Rules";
var _S = "Status";
var _SA = "StartAfter";
var _SAK = "SecretAccessKey";
var _SAs = "SseAlgorithm";
var _SBD = "S3BucketDestination";
var _SC = "StorageClass";
var _SCA = "StorageClassAnalysis";
var _SCADE = "StorageClassAnalysisDataExport";
var _SCASV = "StorageClassAnalysisSchemaVersion";
var _SCt = "StatusCode";
var _SDV = "SkipDestinationValidation";
var _SIM = "SourceIfMatch";
var _SIMS = "SourceIfModifiedSince";
var _SINM = "SourceIfNoneMatch";
var _SIUS = "SourceIfUnmodifiedSince";
var _SK = "SSE-KMS";
var _SKEO = "SseKmsEncryptedObjects";
var _SKEOS = "SseKmsEncryptedObjectsStatus";
var _SKF = "S3KeyFilter";
var _SKe = "S3Key";
var _SL = "S3Location";
var _SM = "SessionMode";
var _SOCR = "SelectObjectContentRequest";
var _SP = "SelectParameters";
var _SPi = "SimplePrefix";
var _SR = "ScanRange";
var _SS = "SSE-S3";
var _SSC = "SourceSelectionCriteria";
var _SSE = "ServerSideEncryption";
var _SSEA = "SSEAlgorithm";
var _SSEBD = "ServerSideEncryptionByDefault";
var _SSEC = "ServerSideEncryptionConfiguration";
var _SSECA = "SSECustomerAlgorithm";
var _SSECK = "SSECustomerKey";
var _SSECKMD = "SSECustomerKeyMD5";
var _SSEKMS = "SSEKMS";
var _SSEKMSEC = "SSEKMSEncryptionContext";
var _SSEKMSKI = "SSEKMSKeyId";
var _SSER = "ServerSideEncryptionRule";
var _SSES = "SSES3";
var _ST = "SessionToken";
var _STBA = "S3TablesBucketArn";
var _STD = "S3TablesDestination";
var _STDR = "S3TablesDestinationResult";
var _STN = "S3TablesName";
var _S_ = "S3";
var _Sc = "Schedule";
var _Se = "Setting";
var _Si = "Size";
var _St = "Start";
var _Su = "Suffix";
var _T = "Tagging";
var _TA = "TopicArn";
var _TAa = "TableArn";
var _TB = "TargetBucket";
var _TBA = "TableBucketArn";
var _TBT = "TableBucketType";
var _TC = "TagCount";
var _TCo = "TopicConfiguration";
var _TCop = "TopicConfigurations";
var _TD = "TaggingDirective";
var _TDMOS = "TransitionDefaultMinimumObjectSize";
var _TG = "TargetGrants";
var _TGa = "TargetGrant";
var _TN = "TableName";
var _TNa = "TableNamespace";
var _TOKF = "TargetObjectKeyFormat";
var _TP = "TargetPrefix";
var _TPC = "TotalPartsCount";
var _TS = "TagSet";
var _TSA = "TableSseAlgorithm";
var _TSC = "TransitionStorageClass";
var _TSa = "TableStatus";
var _Ta = "Tag";
var _Tag = "Tags";
var _Ti = "Tier";
var _Tie = "Tierings";
var _Tier = "Tiering";
var _Tim = "Time";
var _To = "Token";
var _Top = "Topic";
var _Tr = "Transitions";
var _Tra = "Transition";
var _Ty = "Type";
var _U = "Upload";
var _UI = "UploadId";
var _UIM = "UploadIdMarker";
var _UM = "UserMetadata";
var _URI = "URI";
var _Up = "Uploads";
var _V = "Version";
var _VC = "VersionCount";
var _VCe = "VersioningConfiguration";
var _VI = "VersionId";
var _VIM = "VersionIdMarker";
var _Va = "Value";
var _Ve = "Versions";
var _WC = "WebsiteConfiguration";
var _WOB = "WriteOffsetBytes";
var _WRL = "WebsiteRedirectLocation";
var _Y = "Years";
var _a = "analytics";
var _ac = "accelerate";
var _acl = "acl";
var _ar = "accept-ranges";
var _at = "attributes";
var _br = "bucket-region";
var _c = "cors";
var _cc = "cache-control";
var _cd = "content-disposition";
var _ce = "content-encoding";
var _cl = "content-language";
var _cl_ = "content-length";
var _cm = "content-md5";
var _cr = "content-range";
var _ct = "content-type";
var _ct_ = "continuation-token";
var _d = "delete";
var _de = "delimiter";
var _e = "expires";
var _en = "encryption";
var _et = "encoding-type";
var _eta = "etag";
var _ex = "expiresstring";
var _fo = "fetch-owner";
var _i = "id";
var _im = "if-match";
var _ims = "if-modified-since";
var _in = "inventory";
var _inm = "if-none-match";
var _it = "intelligent-tiering";
var _ius = "if-unmodified-since";
var _km = "key-marker";
var _l = "lifecycle";
var _lh = "legal-hold";
var _lm = "last-modified";
var _lo = "location";
var _log = "logging";
var _lt = "list-type";
var _m = "metrics";
var _mC = "metadataConfiguration";
var _mIT = "metadataInventoryTable";
var _mJT = "metadataJournalTable";
var _mT = "metadataTable";
var _ma = "marker";
var _mb = "max-buckets";
var _mdb = "max-directory-buckets";
var _me = "member";
var _mk = "max-keys";
var _mp = "max-parts";
var _mu = "max-uploads";
var _n = "notification";
var _oC = "ownershipControls";
var _ol = "object-lock";
var _p = "policy";
var _pAB = "publicAccessBlock";
var _pN = "partNumber";
var _pS = "policyStatus";
var _pnm = "part-number-marker";
var _pr = "prefix";
var _r = "replication";
var _rO = "renameObject";
var _rP = "requestPayment";
var _ra = "range";
var _rcc = "response-cache-control";
var _rcd = "response-content-disposition";
var _rce = "response-content-encoding";
var _rcl = "response-content-language";
var _rct = "response-content-type";
var _re = "response-expires";
var _res = "restore";
var _ret = "retention";
var _s = "session";
var _sa = "start-after";
var _se = "select";
var _st = "select-type";
var _t = "tagging";
var _to = "torrent";
var _u = "uploads";
var _uI = "uploadId";
var _uim = "upload-id-marker";
var _v = "versioning";
var _vI = "versionId";
var _ve = '<?xml version="1.0" encoding="UTF-8"?>';
var _ver = "versions";
var _vim = "version-id-marker";
var _w = "website";
var _x = "xsi:type";
var _xaa = "x-amz-acl";
var _xaad = "x-amz-abort-date";
var _xaapa = "x-amz-access-point-alias";
var _xaari = "x-amz-abort-rule-id";
var _xaas = "x-amz-archive-status";
var _xaba = "x-amz-bucket-arn";
var _xabgr = "x-amz-bypass-governance-retention";
var _xabln = "x-amz-bucket-location-name";
var _xablt = "x-amz-bucket-location-type";
var _xabole = "x-amz-bucket-object-lock-enabled";
var _xabolt = "x-amz-bucket-object-lock-token";
var _xabr = "x-amz-bucket-region";
var _xaca = "x-amz-checksum-algorithm";
var _xacc = "x-amz-checksum-crc32";
var _xacc_ = "x-amz-checksum-crc32c";
var _xacc__ = "x-amz-checksum-crc64nvme";
var _xacm = "x-amz-checksum-mode";
var _xacrsba = "x-amz-confirm-remove-self-bucket-access";
var _xacs = "x-amz-checksum-sha1";
var _xacs_ = "x-amz-checksum-sha256";
var _xacs__ = "x-amz-copy-source";
var _xacsim = "x-amz-copy-source-if-match";
var _xacsims = "x-amz-copy-source-if-modified-since";
var _xacsinm = "x-amz-copy-source-if-none-match";
var _xacsius = "x-amz-copy-source-if-unmodified-since";
var _xacsm = "x-amz-create-session-mode";
var _xacsr = "x-amz-copy-source-range";
var _xacssseca = "x-amz-copy-source-server-side-encryption-customer-algorithm";
var _xacssseck = "x-amz-copy-source-server-side-encryption-customer-key";
var _xacssseckm = "x-amz-copy-source-server-side-encryption-customer-key-md5";
var _xacsvi = "x-amz-copy-source-version-id";
var _xact = "x-amz-checksum-type";
var _xact_ = "x-amz-client-token";
var _xadm = "x-amz-delete-marker";
var _xae = "x-amz-expiration";
var _xaebo = "x-amz-expected-bucket-owner";
var _xafec = "x-amz-fwd-error-code";
var _xafem = "x-amz-fwd-error-message";
var _xafhar = "x-amz-fwd-header-accept-ranges";
var _xafhcc = "x-amz-fwd-header-cache-control";
var _xafhcd = "x-amz-fwd-header-content-disposition";
var _xafhce = "x-amz-fwd-header-content-encoding";
var _xafhcl = "x-amz-fwd-header-content-language";
var _xafhcr = "x-amz-fwd-header-content-range";
var _xafhct = "x-amz-fwd-header-content-type";
var _xafhe = "x-amz-fwd-header-etag";
var _xafhe_ = "x-amz-fwd-header-expires";
var _xafhlm = "x-amz-fwd-header-last-modified";
var _xafhxacc = "x-amz-fwd-header-x-amz-checksum-crc32";
var _xafhxacc_ = "x-amz-fwd-header-x-amz-checksum-crc32c";
var _xafhxacc__ = "x-amz-fwd-header-x-amz-checksum-crc64nvme";
var _xafhxacs = "x-amz-fwd-header-x-amz-checksum-sha1";
var _xafhxacs_ = "x-amz-fwd-header-x-amz-checksum-sha256";
var _xafhxadm = "x-amz-fwd-header-x-amz-delete-marker";
var _xafhxae = "x-amz-fwd-header-x-amz-expiration";
var _xafhxamm = "x-amz-fwd-header-x-amz-missing-meta";
var _xafhxampc = "x-amz-fwd-header-x-amz-mp-parts-count";
var _xafhxaollh = "x-amz-fwd-header-x-amz-object-lock-legal-hold";
var _xafhxaolm = "x-amz-fwd-header-x-amz-object-lock-mode";
var _xafhxaolrud = "x-amz-fwd-header-x-amz-object-lock-retain-until-date";
var _xafhxar = "x-amz-fwd-header-x-amz-restore";
var _xafhxarc = "x-amz-fwd-header-x-amz-request-charged";
var _xafhxars = "x-amz-fwd-header-x-amz-replication-status";
var _xafhxasc = "x-amz-fwd-header-x-amz-storage-class";
var _xafhxasse = "x-amz-fwd-header-x-amz-server-side-encryption";
var _xafhxasseakki = "x-amz-fwd-header-x-amz-server-side-encryption-aws-kms-key-id";
var _xafhxassebke = "x-amz-fwd-header-x-amz-server-side-encryption-bucket-key-enabled";
var _xafhxasseca = "x-amz-fwd-header-x-amz-server-side-encryption-customer-algorithm";
var _xafhxasseckm = "x-amz-fwd-header-x-amz-server-side-encryption-customer-key-md5";
var _xafhxatc = "x-amz-fwd-header-x-amz-tagging-count";
var _xafhxavi = "x-amz-fwd-header-x-amz-version-id";
var _xafs = "x-amz-fwd-status";
var _xagfc = "x-amz-grant-full-control";
var _xagr = "x-amz-grant-read";
var _xagra = "x-amz-grant-read-acp";
var _xagw = "x-amz-grant-write";
var _xagwa = "x-amz-grant-write-acp";
var _xaimit = "x-amz-if-match-initiated-time";
var _xaimlmt = "x-amz-if-match-last-modified-time";
var _xaims = "x-amz-if-match-size";
var _xam = "x-amz-mfa";
var _xamd = "x-amz-metadata-directive";
var _xamm = "x-amz-missing-meta";
var _xamos = "x-amz-mp-object-size";
var _xamp = "x-amz-max-parts";
var _xampc = "x-amz-mp-parts-count";
var _xaoa = "x-amz-object-attributes";
var _xaollh = "x-amz-object-lock-legal-hold";
var _xaolm = "x-amz-object-lock-mode";
var _xaolrud = "x-amz-object-lock-retain-until-date";
var _xaoo = "x-amz-object-ownership";
var _xaooa = "x-amz-optional-object-attributes";
var _xaos = "x-amz-object-size";
var _xapnm = "x-amz-part-number-marker";
var _xar = "x-amz-restore";
var _xarc = "x-amz-request-charged";
var _xarop = "x-amz-restore-output-path";
var _xarp = "x-amz-request-payer";
var _xarr = "x-amz-request-route";
var _xars = "x-amz-rename-source";
var _xars_ = "x-amz-replication-status";
var _xarsim = "x-amz-rename-source-if-match";
var _xarsims = "x-amz-rename-source-if-modified-since";
var _xarsinm = "x-amz-rename-source-if-none-match";
var _xarsius = "x-amz-rename-source-if-unmodified-since";
var _xart = "x-amz-request-token";
var _xasc = "x-amz-storage-class";
var _xasca = "x-amz-sdk-checksum-algorithm";
var _xasdv = "x-amz-skip-destination-validation";
var _xasebo = "x-amz-source-expected-bucket-owner";
var _xasse = "x-amz-server-side-encryption";
var _xasseakki = "x-amz-server-side-encryption-aws-kms-key-id";
var _xassebke = "x-amz-server-side-encryption-bucket-key-enabled";
var _xassec = "x-amz-server-side-encryption-context";
var _xasseca = "x-amz-server-side-encryption-customer-algorithm";
var _xasseck = "x-amz-server-side-encryption-customer-key";
var _xasseckm = "x-amz-server-side-encryption-customer-key-md5";
var _xat = "x-amz-tagging";
var _xatc = "x-amz-tagging-count";
var _xatd = "x-amz-tagging-directive";
var _xatdmos = "x-amz-transition-default-minimum-object-size";
var _xavi = "x-amz-version-id";
var _xawob = "x-amz-write-offset-bytes";
var _xawrl = "x-amz-website-redirect-location";
var _xi = "x-id";

// src/commands/CreateSessionCommand.ts
var CreateSessionCommand = class extends import_smithy_client.Command.classBuilder().ep({
  ...commonParams,
  DisableS3ExpressSessionAuth: { type: "staticContextParams", value: true },
  Bucket: { type: "contextParams", name: "Bucket" }
}).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions()),
    (0, import_middleware_sdk_s3.getThrow200ExceptionsPlugin)(config)
  ];
}).s("AmazonS3", "CreateSession", {}).n("S3Client", "CreateSessionCommand").f(CreateSessionRequestFilterSensitiveLog, CreateSessionOutputFilterSensitiveLog).ser(se_CreateSessionCommand).de(de_CreateSessionCommand).build() {
  static {
    __name(this, "CreateSessionCommand");
  }
};

// src/S3Client.ts
var import_runtimeConfig = require("././runtimeConfig");

// src/runtimeExtensions.ts
var import_region_config_resolver = require("@aws-sdk/region-config-resolver");



// src/auth/httpAuthExtensionConfiguration.ts
var getHttpAuthExtensionConfiguration = /* @__PURE__ */ __name((runtimeConfig) => {
  const _httpAuthSchemes = runtimeConfig.httpAuthSchemes;
  let _httpAuthSchemeProvider = runtimeConfig.httpAuthSchemeProvider;
  let _credentials = runtimeConfig.credentials;
  return {
    setHttpAuthScheme(httpAuthScheme) {
      const index = _httpAuthSchemes.findIndex((scheme) => scheme.schemeId === httpAuthScheme.schemeId);
      if (index === -1) {
        _httpAuthSchemes.push(httpAuthScheme);
      } else {
        _httpAuthSchemes.splice(index, 1, httpAuthScheme);
      }
    },
    httpAuthSchemes() {
      return _httpAuthSchemes;
    },
    setHttpAuthSchemeProvider(httpAuthSchemeProvider) {
      _httpAuthSchemeProvider = httpAuthSchemeProvider;
    },
    httpAuthSchemeProvider() {
      return _httpAuthSchemeProvider;
    },
    setCredentials(credentials) {
      _credentials = credentials;
    },
    credentials() {
      return _credentials;
    }
  };
}, "getHttpAuthExtensionConfiguration");
var resolveHttpAuthRuntimeConfig = /* @__PURE__ */ __name((config) => {
  return {
    httpAuthSchemes: config.httpAuthSchemes(),
    httpAuthSchemeProvider: config.httpAuthSchemeProvider(),
    credentials: config.credentials()
  };
}, "resolveHttpAuthRuntimeConfig");

// src/runtimeExtensions.ts
var resolveRuntimeExtensions = /* @__PURE__ */ __name((runtimeConfig, extensions) => {
  const extensionConfiguration = Object.assign(
    (0, import_region_config_resolver.getAwsRegionExtensionConfiguration)(runtimeConfig),
    (0, import_smithy_client.getDefaultExtensionConfiguration)(runtimeConfig),
    (0, import_protocol_http.getHttpHandlerExtensionConfiguration)(runtimeConfig),
    getHttpAuthExtensionConfiguration(runtimeConfig)
  );
  extensions.forEach((extension) => extension.configure(extensionConfiguration));
  return Object.assign(
    runtimeConfig,
    (0, import_region_config_resolver.resolveAwsRegionExtensionConfiguration)(extensionConfiguration),
    (0, import_smithy_client.resolveDefaultRuntimeConfig)(extensionConfiguration),
    (0, import_protocol_http.resolveHttpHandlerRuntimeConfig)(extensionConfiguration),
    resolveHttpAuthRuntimeConfig(extensionConfiguration)
  );
}, "resolveRuntimeExtensions");

// src/S3Client.ts
var S3Client = class extends import_smithy_client.Client {
  static {
    __name(this, "S3Client");
  }
  /**
   * The resolved configuration of S3Client class. This is resolved and normalized from the {@link S3ClientConfig | constructor configuration interface}.
   */
  config;
  constructor(...[configuration]) {
    const _config_0 = (0, import_runtimeConfig.getRuntimeConfig)(configuration || {});
    super(_config_0);
    this.initConfig = _config_0;
    const _config_1 = resolveClientEndpointParameters(_config_0);
    const _config_2 = (0, import_middleware_user_agent.resolveUserAgentConfig)(_config_1);
    const _config_3 = (0, import_middleware_flexible_checksums.resolveFlexibleChecksumsConfig)(_config_2);
    const _config_4 = (0, import_middleware_retry.resolveRetryConfig)(_config_3);
    const _config_5 = (0, import_config_resolver.resolveRegionConfig)(_config_4);
    const _config_6 = (0, import_middleware_host_header.resolveHostHeaderConfig)(_config_5);
    const _config_7 = (0, import_middleware_endpoint.resolveEndpointConfig)(_config_6);
    const _config_8 = (0, import_eventstream_serde_config_resolver.resolveEventStreamSerdeConfig)(_config_7);
    const _config_9 = (0, import_httpAuthSchemeProvider.resolveHttpAuthSchemeConfig)(_config_8);
    const _config_10 = (0, import_middleware_sdk_s32.resolveS3Config)(_config_9, { session: [() => this, CreateSessionCommand] });
    const _config_11 = resolveRuntimeExtensions(_config_10, configuration?.extensions || []);
    this.config = _config_11;
    this.middlewareStack.use((0, import_middleware_user_agent.getUserAgentPlugin)(this.config));
    this.middlewareStack.use((0, import_middleware_retry.getRetryPlugin)(this.config));
    this.middlewareStack.use((0, import_middleware_content_length.getContentLengthPlugin)(this.config));
    this.middlewareStack.use((0, import_middleware_host_header.getHostHeaderPlugin)(this.config));
    this.middlewareStack.use((0, import_middleware_logger.getLoggerPlugin)(this.config));
    this.middlewareStack.use((0, import_middleware_recursion_detection.getRecursionDetectionPlugin)(this.config));
    this.middlewareStack.use(
      (0, import_core3.getHttpAuthSchemeEndpointRuleSetPlugin)(this.config, {
        httpAuthSchemeParametersProvider: import_httpAuthSchemeProvider.defaultS3HttpAuthSchemeParametersProvider,
        identityProviderConfigProvider: /* @__PURE__ */ __name(async (config) => new import_core3.DefaultIdentityProviderConfig({
          "aws.auth#sigv4": config.credentials,
          "aws.auth#sigv4a": config.credentials
        }), "identityProviderConfigProvider")
      })
    );
    this.middlewareStack.use((0, import_core3.getHttpSigningPlugin)(this.config));
    this.middlewareStack.use((0, import_middleware_sdk_s32.getValidateBucketNamePlugin)(this.config));
    this.middlewareStack.use((0, import_middleware_expect_continue.getAddExpectContinuePlugin)(this.config));
    this.middlewareStack.use((0, import_middleware_sdk_s32.getRegionRedirectMiddlewarePlugin)(this.config));
    this.middlewareStack.use((0, import_middleware_sdk_s32.getS3ExpressPlugin)(this.config));
    this.middlewareStack.use((0, import_middleware_sdk_s32.getS3ExpressHttpSigningPlugin)(this.config));
  }
  /**
   * Destroy underlying resources, like sockets. It's usually not necessary to do this.
   * However in Node.js, it's best to explicitly shut down the client's agent when it is no longer needed.
   * Otherwise, sockets might stay open for quite a long time before the server terminates them.
   */
  destroy() {
    super.destroy();
  }
};

// src/S3.ts


// src/commands/AbortMultipartUploadCommand.ts
var import_middleware_sdk_s33 = require("@aws-sdk/middleware-sdk-s3");



var AbortMultipartUploadCommand = class extends import_smithy_client.Command.classBuilder().ep({
  ...commonParams,
  Bucket: { type: "contextParams", name: "Bucket" },
  Key: { type: "contextParams", name: "Key" }
}).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions()),
    (0, import_middleware_sdk_s33.getThrow200ExceptionsPlugin)(config)
  ];
}).s("AmazonS3", "AbortMultipartUpload", {}).n("S3Client", "AbortMultipartUploadCommand").f(void 0, void 0).ser(se_AbortMultipartUploadCommand).de(de_AbortMultipartUploadCommand).build() {
  static {
    __name(this, "AbortMultipartUploadCommand");
  }
};

// src/commands/CompleteMultipartUploadCommand.ts
var import_middleware_sdk_s34 = require("@aws-sdk/middleware-sdk-s3");
var import_middleware_ssec = require("@aws-sdk/middleware-ssec");



var CompleteMultipartUploadCommand = class extends import_smithy_client.Command.classBuilder().ep({
  ...commonParams,
  Bucket: { type: "contextParams", name: "Bucket" },
  Key: { type: "contextParams", name: "Key" }
}).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions()),
    (0, import_middleware_sdk_s34.getThrow200ExceptionsPlugin)(config),
    (0, import_middleware_ssec.getSsecPlugin)(config)
  ];
}).s("AmazonS3", "CompleteMultipartUpload", {}).n("S3Client", "CompleteMultipartUploadCommand").f(CompleteMultipartUploadRequestFilterSensitiveLog, CompleteMultipartUploadOutputFilterSensitiveLog).ser(se_CompleteMultipartUploadCommand).de(de_CompleteMultipartUploadCommand).build() {
  static {
    __name(this, "CompleteMultipartUploadCommand");
  }
};

// src/commands/CopyObjectCommand.ts
var import_middleware_sdk_s35 = require("@aws-sdk/middleware-sdk-s3");




var CopyObjectCommand = class extends import_smithy_client.Command.classBuilder().ep({
  ...commonParams,
  DisableS3ExpressSessionAuth: { type: "staticContextParams", value: true },
  Bucket: { type: "contextParams", name: "Bucket" },
  Key: { type: "contextParams", name: "Key" },
  CopySource: { type: "contextParams", name: "CopySource" }
}).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions()),
    (0, import_middleware_sdk_s35.getThrow200ExceptionsPlugin)(config),
    (0, import_middleware_ssec.getSsecPlugin)(config)
  ];
}).s("AmazonS3", "CopyObject", {}).n("S3Client", "CopyObjectCommand").f(CopyObjectRequestFilterSensitiveLog, CopyObjectOutputFilterSensitiveLog).ser(se_CopyObjectCommand).de(de_CopyObjectCommand).build() {
  static {
    __name(this, "CopyObjectCommand");
  }
};

// src/commands/CreateBucketCommand.ts
var import_middleware_location_constraint = require("@aws-sdk/middleware-location-constraint");
var import_middleware_sdk_s36 = require("@aws-sdk/middleware-sdk-s3");



var CreateBucketCommand = class extends import_smithy_client.Command.classBuilder().ep({
  ...commonParams,
  UseS3ExpressControlEndpoint: { type: "staticContextParams", value: true },
  DisableAccessPoints: { type: "staticContextParams", value: true },
  Bucket: { type: "contextParams", name: "Bucket" }
}).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions()),
    (0, import_middleware_sdk_s36.getThrow200ExceptionsPlugin)(config),
    (0, import_middleware_location_constraint.getLocationConstraintPlugin)(config)
  ];
}).s("AmazonS3", "CreateBucket", {}).n("S3Client", "CreateBucketCommand").f(void 0, void 0).ser(se_CreateBucketCommand).de(de_CreateBucketCommand).build() {
  static {
    __name(this, "CreateBucketCommand");
  }
};

// src/commands/CreateBucketMetadataConfigurationCommand.ts




var CreateBucketMetadataConfigurationCommand = class extends import_smithy_client.Command.classBuilder().ep({
  ...commonParams,
  UseS3ExpressControlEndpoint: { type: "staticContextParams", value: true },
  Bucket: { type: "contextParams", name: "Bucket" }
}).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions()),
    (0, import_middleware_flexible_checksums.getFlexibleChecksumsPlugin)(config, {
      requestAlgorithmMember: { httpHeader: "x-amz-sdk-checksum-algorithm", name: "ChecksumAlgorithm" },
      requestChecksumRequired: true
    })
  ];
}).s("AmazonS3", "CreateBucketMetadataConfiguration", {}).n("S3Client", "CreateBucketMetadataConfigurationCommand").f(void 0, void 0).ser(se_CreateBucketMetadataConfigurationCommand).de(de_CreateBucketMetadataConfigurationCommand).build() {
  static {
    __name(this, "CreateBucketMetadataConfigurationCommand");
  }
};

// src/commands/CreateBucketMetadataTableConfigurationCommand.ts




var CreateBucketMetadataTableConfigurationCommand = class extends import_smithy_client.Command.classBuilder().ep({
  ...commonParams,
  UseS3ExpressControlEndpoint: { type: "staticContextParams", value: true },
  Bucket: { type: "contextParams", name: "Bucket" }
}).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions()),
    (0, import_middleware_flexible_checksums.getFlexibleChecksumsPlugin)(config, {
      requestAlgorithmMember: { httpHeader: "x-amz-sdk-checksum-algorithm", name: "ChecksumAlgorithm" },
      requestChecksumRequired: true
    })
  ];
}).s("AmazonS3", "CreateBucketMetadataTableConfiguration", {}).n("S3Client", "CreateBucketMetadataTableConfigurationCommand").f(void 0, void 0).ser(se_CreateBucketMetadataTableConfigurationCommand).de(de_CreateBucketMetadataTableConfigurationCommand).build() {
  static {
    __name(this, "CreateBucketMetadataTableConfigurationCommand");
  }
};

// src/commands/CreateMultipartUploadCommand.ts
var import_middleware_sdk_s37 = require("@aws-sdk/middleware-sdk-s3");




var CreateMultipartUploadCommand = class extends import_smithy_client.Command.classBuilder().ep({
  ...commonParams,
  Bucket: { type: "contextParams", name: "Bucket" },
  Key: { type: "contextParams", name: "Key" }
}).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions()),
    (0, import_middleware_sdk_s37.getThrow200ExceptionsPlugin)(config),
    (0, import_middleware_ssec.getSsecPlugin)(config)
  ];
}).s("AmazonS3", "CreateMultipartUpload", {}).n("S3Client", "CreateMultipartUploadCommand").f(CreateMultipartUploadRequestFilterSensitiveLog, CreateMultipartUploadOutputFilterSensitiveLog).ser(se_CreateMultipartUploadCommand).de(de_CreateMultipartUploadCommand).build() {
  static {
    __name(this, "CreateMultipartUploadCommand");
  }
};

// src/commands/DeleteBucketAnalyticsConfigurationCommand.ts



var DeleteBucketAnalyticsConfigurationCommand = class extends import_smithy_client.Command.classBuilder().ep({
  ...commonParams,
  UseS3ExpressControlEndpoint: { type: "staticContextParams", value: true },
  Bucket: { type: "contextParams", name: "Bucket" }
}).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AmazonS3", "DeleteBucketAnalyticsConfiguration", {}).n("S3Client", "DeleteBucketAnalyticsConfigurationCommand").f(void 0, void 0).ser(se_DeleteBucketAnalyticsConfigurationCommand).de(de_DeleteBucketAnalyticsConfigurationCommand).build() {
  static {
    __name(this, "DeleteBucketAnalyticsConfigurationCommand");
  }
};

// src/commands/DeleteBucketCommand.ts



var DeleteBucketCommand = class extends import_smithy_client.Command.classBuilder().ep({
  ...commonParams,
  UseS3ExpressControlEndpoint: { type: "staticContextParams", value: true },
  Bucket: { type: "contextParams", name: "Bucket" }
}).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AmazonS3", "DeleteBucket", {}).n("S3Client", "DeleteBucketCommand").f(void 0, void 0).ser(se_DeleteBucketCommand).de(de_DeleteBucketCommand).build() {
  static {
    __name(this, "DeleteBucketCommand");
  }
};

// src/commands/DeleteBucketCorsCommand.ts



var DeleteBucketCorsCommand = class extends import_smithy_client.Command.classBuilder().ep({
  ...commonParams,
  UseS3ExpressControlEndpoint: { type: "staticContextParams", value: true },
  Bucket: { type: "contextParams", name: "Bucket" }
}).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AmazonS3", "DeleteBucketCors", {}).n("S3Client", "DeleteBucketCorsCommand").f(void 0, void 0).ser(se_DeleteBucketCorsCommand).de(de_DeleteBucketCorsCommand).build() {
  static {
    __name(this, "DeleteBucketCorsCommand");
  }
};

// src/commands/DeleteBucketEncryptionCommand.ts



var DeleteBucketEncryptionCommand = class extends import_smithy_client.Command.classBuilder().ep({
  ...commonParams,
  UseS3ExpressControlEndpoint: { type: "staticContextParams", value: true },
  Bucket: { type: "contextParams", name: "Bucket" }
}).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AmazonS3", "DeleteBucketEncryption", {}).n("S3Client", "DeleteBucketEncryptionCommand").f(void 0, void 0).ser(se_DeleteBucketEncryptionCommand).de(de_DeleteBucketEncryptionCommand).build() {
  static {
    __name(this, "DeleteBucketEncryptionCommand");
  }
};

// src/commands/DeleteBucketIntelligentTieringConfigurationCommand.ts



var DeleteBucketIntelligentTieringConfigurationCommand = class extends import_smithy_client.Command.classBuilder().ep({
  ...commonParams,
  UseS3ExpressControlEndpoint: { type: "staticContextParams", value: true },
  Bucket: { type: "contextParams", name: "Bucket" }
}).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AmazonS3", "DeleteBucketIntelligentTieringConfiguration", {}).n("S3Client", "DeleteBucketIntelligentTieringConfigurationCommand").f(void 0, void 0).ser(se_DeleteBucketIntelligentTieringConfigurationCommand).de(de_DeleteBucketIntelligentTieringConfigurationCommand).build() {
  static {
    __name(this, "DeleteBucketIntelligentTieringConfigurationCommand");
  }
};

// src/commands/DeleteBucketInventoryConfigurationCommand.ts



var DeleteBucketInventoryConfigurationCommand = class extends import_smithy_client.Command.classBuilder().ep({
  ...commonParams,
  UseS3ExpressControlEndpoint: { type: "staticContextParams", value: true },
  Bucket: { type: "contextParams", name: "Bucket" }
}).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AmazonS3", "DeleteBucketInventoryConfiguration", {}).n("S3Client", "DeleteBucketInventoryConfigurationCommand").f(void 0, void 0).ser(se_DeleteBucketInventoryConfigurationCommand).de(de_DeleteBucketInventoryConfigurationCommand).build() {
  static {
    __name(this, "DeleteBucketInventoryConfigurationCommand");
  }
};

// src/commands/DeleteBucketLifecycleCommand.ts



var DeleteBucketLifecycleCommand = class extends import_smithy_client.Command.classBuilder().ep({
  ...commonParams,
  UseS3ExpressControlEndpoint: { type: "staticContextParams", value: true },
  Bucket: { type: "contextParams", name: "Bucket" }
}).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AmazonS3", "DeleteBucketLifecycle", {}).n("S3Client", "DeleteBucketLifecycleCommand").f(void 0, void 0).ser(se_DeleteBucketLifecycleCommand).de(de_DeleteBucketLifecycleCommand).build() {
  static {
    __name(this, "DeleteBucketLifecycleCommand");
  }
};

// src/commands/DeleteBucketMetadataConfigurationCommand.ts



var DeleteBucketMetadataConfigurationCommand = class extends import_smithy_client.Command.classBuilder().ep({
  ...commonParams,
  UseS3ExpressControlEndpoint: { type: "staticContextParams", value: true },
  Bucket: { type: "contextParams", name: "Bucket" }
}).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AmazonS3", "DeleteBucketMetadataConfiguration", {}).n("S3Client", "DeleteBucketMetadataConfigurationCommand").f(void 0, void 0).ser(se_DeleteBucketMetadataConfigurationCommand).de(de_DeleteBucketMetadataConfigurationCommand).build() {
  static {
    __name(this, "DeleteBucketMetadataConfigurationCommand");
  }
};

// src/commands/DeleteBucketMetadataTableConfigurationCommand.ts



var DeleteBucketMetadataTableConfigurationCommand = class extends import_smithy_client.Command.classBuilder().ep({
  ...commonParams,
  UseS3ExpressControlEndpoint: { type: "staticContextParams", value: true },
  Bucket: { type: "contextParams", name: "Bucket" }
}).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AmazonS3", "DeleteBucketMetadataTableConfiguration", {}).n("S3Client", "DeleteBucketMetadataTableConfigurationCommand").f(void 0, void 0).ser(se_DeleteBucketMetadataTableConfigurationCommand).de(de_DeleteBucketMetadataTableConfigurationCommand).build() {
  static {
    __name(this, "DeleteBucketMetadataTableConfigurationCommand");
  }
};

// src/commands/DeleteBucketMetricsConfigurationCommand.ts



var DeleteBucketMetricsConfigurationCommand = class extends import_smithy_client.Command.classBuilder().ep({
  ...commonParams,
  UseS3ExpressControlEndpoint: { type: "staticContextParams", value: true },
  Bucket: { type: "contextParams", name: "Bucket" }
}).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AmazonS3", "DeleteBucketMetricsConfiguration", {}).n("S3Client", "DeleteBucketMetricsConfigurationCommand").f(void 0, void 0).ser(se_DeleteBucketMetricsConfigurationCommand).de(de_DeleteBucketMetricsConfigurationCommand).build() {
  static {
    __name(this, "DeleteBucketMetricsConfigurationCommand");
  }
};

// src/commands/DeleteBucketOwnershipControlsCommand.ts



var DeleteBucketOwnershipControlsCommand = class extends import_smithy_client.Command.classBuilder().ep({
  ...commonParams,
  UseS3ExpressControlEndpoint: { type: "staticContextParams", value: true },
  Bucket: { type: "contextParams", name: "Bucket" }
}).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AmazonS3", "DeleteBucketOwnershipControls", {}).n("S3Client", "DeleteBucketOwnershipControlsCommand").f(void 0, void 0).ser(se_DeleteBucketOwnershipControlsCommand).de(de_DeleteBucketOwnershipControlsCommand).build() {
  static {
    __name(this, "DeleteBucketOwnershipControlsCommand");
  }
};

// src/commands/DeleteBucketPolicyCommand.ts



var DeleteBucketPolicyCommand = class extends import_smithy_client.Command.classBuilder().ep({
  ...commonParams,
  UseS3ExpressControlEndpoint: { type: "staticContextParams", value: true },
  Bucket: { type: "contextParams", name: "Bucket" }
}).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AmazonS3", "DeleteBucketPolicy", {}).n("S3Client", "DeleteBucketPolicyCommand").f(void 0, void 0).ser(se_DeleteBucketPolicyCommand).de(de_DeleteBucketPolicyCommand).build() {
  static {
    __name(this, "DeleteBucketPolicyCommand");
  }
};

// src/commands/DeleteBucketReplicationCommand.ts



var DeleteBucketReplicationCommand = class extends import_smithy_client.Command.classBuilder().ep({
  ...commonParams,
  UseS3ExpressControlEndpoint: { type: "staticContextParams", value: true },
  Bucket: { type: "contextParams", name: "Bucket" }
}).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AmazonS3", "DeleteBucketReplication", {}).n("S3Client", "DeleteBucketReplicationCommand").f(void 0, void 0).ser(se_DeleteBucketReplicationCommand).de(de_DeleteBucketReplicationCommand).build() {
  static {
    __name(this, "DeleteBucketReplicationCommand");
  }
};

// src/commands/DeleteBucketTaggingCommand.ts



var DeleteBucketTaggingCommand = class extends import_smithy_client.Command.classBuilder().ep({
  ...commonParams,
  UseS3ExpressControlEndpoint: { type: "staticContextParams", value: true },
  Bucket: { type: "contextParams", name: "Bucket" }
}).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AmazonS3", "DeleteBucketTagging", {}).n("S3Client", "DeleteBucketTaggingCommand").f(void 0, void 0).ser(se_DeleteBucketTaggingCommand).de(de_DeleteBucketTaggingCommand).build() {
  static {
    __name(this, "DeleteBucketTaggingCommand");
  }
};

// src/commands/DeleteBucketWebsiteCommand.ts



var DeleteBucketWebsiteCommand = class extends import_smithy_client.Command.classBuilder().ep({
  ...commonParams,
  UseS3ExpressControlEndpoint: { type: "staticContextParams", value: true },
  Bucket: { type: "contextParams", name: "Bucket" }
}).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AmazonS3", "DeleteBucketWebsite", {}).n("S3Client", "DeleteBucketWebsiteCommand").f(void 0, void 0).ser(se_DeleteBucketWebsiteCommand).de(de_DeleteBucketWebsiteCommand).build() {
  static {
    __name(this, "DeleteBucketWebsiteCommand");
  }
};

// src/commands/DeleteObjectCommand.ts
var import_middleware_sdk_s38 = require("@aws-sdk/middleware-sdk-s3");



var DeleteObjectCommand = class extends import_smithy_client.Command.classBuilder().ep({
  ...commonParams,
  Bucket: { type: "contextParams", name: "Bucket" },
  Key: { type: "contextParams", name: "Key" }
}).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions()),
    (0, import_middleware_sdk_s38.getThrow200ExceptionsPlugin)(config)
  ];
}).s("AmazonS3", "DeleteObject", {}).n("S3Client", "DeleteObjectCommand").f(void 0, void 0).ser(se_DeleteObjectCommand).de(de_DeleteObjectCommand).build() {
  static {
    __name(this, "DeleteObjectCommand");
  }
};

// src/commands/DeleteObjectsCommand.ts

var import_middleware_sdk_s39 = require("@aws-sdk/middleware-sdk-s3");



var DeleteObjectsCommand = class extends import_smithy_client.Command.classBuilder().ep({
  ...commonParams,
  Bucket: { type: "contextParams", name: "Bucket" }
}).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions()),
    (0, import_middleware_flexible_checksums.getFlexibleChecksumsPlugin)(config, {
      requestAlgorithmMember: { httpHeader: "x-amz-sdk-checksum-algorithm", name: "ChecksumAlgorithm" },
      requestChecksumRequired: true
    }),
    (0, import_middleware_sdk_s39.getThrow200ExceptionsPlugin)(config)
  ];
}).s("AmazonS3", "DeleteObjects", {}).n("S3Client", "DeleteObjectsCommand").f(void 0, void 0).ser(se_DeleteObjectsCommand).de(de_DeleteObjectsCommand).build() {
  static {
    __name(this, "DeleteObjectsCommand");
  }
};

// src/commands/DeleteObjectTaggingCommand.ts
var import_middleware_sdk_s310 = require("@aws-sdk/middleware-sdk-s3");



var DeleteObjectTaggingCommand = class extends import_smithy_client.Command.classBuilder().ep({
  ...commonParams,
  Bucket: { type: "contextParams", name: "Bucket" }
}).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions()),
    (0, import_middleware_sdk_s310.getThrow200ExceptionsPlugin)(config)
  ];
}).s("AmazonS3", "DeleteObjectTagging", {}).n("S3Client", "DeleteObjectTaggingCommand").f(void 0, void 0).ser(se_DeleteObjectTaggingCommand).de(de_DeleteObjectTaggingCommand).build() {
  static {
    __name(this, "DeleteObjectTaggingCommand");
  }
};

// src/commands/DeletePublicAccessBlockCommand.ts



var DeletePublicAccessBlockCommand = class extends import_smithy_client.Command.classBuilder().ep({
  ...commonParams,
  UseS3ExpressControlEndpoint: { type: "staticContextParams", value: true },
  Bucket: { type: "contextParams", name: "Bucket" }
}).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AmazonS3", "DeletePublicAccessBlock", {}).n("S3Client", "DeletePublicAccessBlockCommand").f(void 0, void 0).ser(se_DeletePublicAccessBlockCommand).de(de_DeletePublicAccessBlockCommand).build() {
  static {
    __name(this, "DeletePublicAccessBlockCommand");
  }
};

// src/commands/GetBucketAccelerateConfigurationCommand.ts
var import_middleware_sdk_s311 = require("@aws-sdk/middleware-sdk-s3");



var GetBucketAccelerateConfigurationCommand = class extends import_smithy_client.Command.classBuilder().ep({
  ...commonParams,
  UseS3ExpressControlEndpoint: { type: "staticContextParams", value: true },
  Bucket: { type: "contextParams", name: "Bucket" }
}).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions()),
    (0, import_middleware_sdk_s311.getThrow200ExceptionsPlugin)(config)
  ];
}).s("AmazonS3", "GetBucketAccelerateConfiguration", {}).n("S3Client", "GetBucketAccelerateConfigurationCommand").f(void 0, void 0).ser(se_GetBucketAccelerateConfigurationCommand).de(de_GetBucketAccelerateConfigurationCommand).build() {
  static {
    __name(this, "GetBucketAccelerateConfigurationCommand");
  }
};

// src/commands/GetBucketAclCommand.ts
var import_middleware_sdk_s312 = require("@aws-sdk/middleware-sdk-s3");



var GetBucketAclCommand = class extends import_smithy_client.Command.classBuilder().ep({
  ...commonParams,
  UseS3ExpressControlEndpoint: { type: "staticContextParams", value: true },
  Bucket: { type: "contextParams", name: "Bucket" }
}).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions()),
    (0, import_middleware_sdk_s312.getThrow200ExceptionsPlugin)(config)
  ];
}).s("AmazonS3", "GetBucketAcl", {}).n("S3Client", "GetBucketAclCommand").f(void 0, void 0).ser(se_GetBucketAclCommand).de(de_GetBucketAclCommand).build() {
  static {
    __name(this, "GetBucketAclCommand");
  }
};

// src/commands/GetBucketAnalyticsConfigurationCommand.ts
var import_middleware_sdk_s313 = require("@aws-sdk/middleware-sdk-s3");



var GetBucketAnalyticsConfigurationCommand = class extends import_smithy_client.Command.classBuilder().ep({
  ...commonParams,
  UseS3ExpressControlEndpoint: { type: "staticContextParams", value: true },
  Bucket: { type: "contextParams", name: "Bucket" }
}).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions()),
    (0, import_middleware_sdk_s313.getThrow200ExceptionsPlugin)(config)
  ];
}).s("AmazonS3", "GetBucketAnalyticsConfiguration", {}).n("S3Client", "GetBucketAnalyticsConfigurationCommand").f(void 0, void 0).ser(se_GetBucketAnalyticsConfigurationCommand).de(de_GetBucketAnalyticsConfigurationCommand).build() {
  static {
    __name(this, "GetBucketAnalyticsConfigurationCommand");
  }
};

// src/commands/GetBucketCorsCommand.ts
var import_middleware_sdk_s314 = require("@aws-sdk/middleware-sdk-s3");



var GetBucketCorsCommand = class extends import_smithy_client.Command.classBuilder().ep({
  ...commonParams,
  UseS3ExpressControlEndpoint: { type: "staticContextParams", value: true },
  Bucket: { type: "contextParams", name: "Bucket" }
}).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions()),
    (0, import_middleware_sdk_s314.getThrow200ExceptionsPlugin)(config)
  ];
}).s("AmazonS3", "GetBucketCors", {}).n("S3Client", "GetBucketCorsCommand").f(void 0, void 0).ser(se_GetBucketCorsCommand).de(de_GetBucketCorsCommand).build() {
  static {
    __name(this, "GetBucketCorsCommand");
  }
};

// src/commands/GetBucketEncryptionCommand.ts
var import_middleware_sdk_s315 = require("@aws-sdk/middleware-sdk-s3");



var GetBucketEncryptionCommand = class extends import_smithy_client.Command.classBuilder().ep({
  ...commonParams,
  UseS3ExpressControlEndpoint: { type: "staticContextParams", value: true },
  Bucket: { type: "contextParams", name: "Bucket" }
}).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions()),
    (0, import_middleware_sdk_s315.getThrow200ExceptionsPlugin)(config)
  ];
}).s("AmazonS3", "GetBucketEncryption", {}).n("S3Client", "GetBucketEncryptionCommand").f(void 0, GetBucketEncryptionOutputFilterSensitiveLog).ser(se_GetBucketEncryptionCommand).de(de_GetBucketEncryptionCommand).build() {
  static {
    __name(this, "GetBucketEncryptionCommand");
  }
};

// src/commands/GetBucketIntelligentTieringConfigurationCommand.ts
var import_middleware_sdk_s316 = require("@aws-sdk/middleware-sdk-s3");



var GetBucketIntelligentTieringConfigurationCommand = class extends import_smithy_client.Command.classBuilder().ep({
  ...commonParams,
  UseS3ExpressControlEndpoint: { type: "staticContextParams", value: true },
  Bucket: { type: "contextParams", name: "Bucket" }
}).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions()),
    (0, import_middleware_sdk_s316.getThrow200ExceptionsPlugin)(config)
  ];
}).s("AmazonS3", "GetBucketIntelligentTieringConfiguration", {}).n("S3Client", "GetBucketIntelligentTieringConfigurationCommand").f(void 0, void 0).ser(se_GetBucketIntelligentTieringConfigurationCommand).de(de_GetBucketIntelligentTieringConfigurationCommand).build() {
  static {
    __name(this, "GetBucketIntelligentTieringConfigurationCommand");
  }
};

// src/commands/GetBucketInventoryConfigurationCommand.ts
var import_middleware_sdk_s317 = require("@aws-sdk/middleware-sdk-s3");



var GetBucketInventoryConfigurationCommand = class extends import_smithy_client.Command.classBuilder().ep({
  ...commonParams,
  UseS3ExpressControlEndpoint: { type: "staticContextParams", value: true },
  Bucket: { type: "contextParams", name: "Bucket" }
}).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions()),
    (0, import_middleware_sdk_s317.getThrow200ExceptionsPlugin)(config)
  ];
}).s("AmazonS3", "GetBucketInventoryConfiguration", {}).n("S3Client", "GetBucketInventoryConfigurationCommand").f(void 0, GetBucketInventoryConfigurationOutputFilterSensitiveLog).ser(se_GetBucketInventoryConfigurationCommand).de(de_GetBucketInventoryConfigurationCommand).build() {
  static {
    __name(this, "GetBucketInventoryConfigurationCommand");
  }
};

// src/commands/GetBucketLifecycleConfigurationCommand.ts
var import_middleware_sdk_s318 = require("@aws-sdk/middleware-sdk-s3");



var GetBucketLifecycleConfigurationCommand = class extends import_smithy_client.Command.classBuilder().ep({
  ...commonParams,
  UseS3ExpressControlEndpoint: { type: "staticContextParams", value: true },
  Bucket: { type: "contextParams", name: "Bucket" }
}).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions()),
    (0, import_middleware_sdk_s318.getThrow200ExceptionsPlugin)(config)
  ];
}).s("AmazonS3", "GetBucketLifecycleConfiguration", {}).n("S3Client", "GetBucketLifecycleConfigurationCommand").f(void 0, void 0).ser(se_GetBucketLifecycleConfigurationCommand).de(de_GetBucketLifecycleConfigurationCommand).build() {
  static {
    __name(this, "GetBucketLifecycleConfigurationCommand");
  }
};

// src/commands/GetBucketLocationCommand.ts
var import_middleware_sdk_s319 = require("@aws-sdk/middleware-sdk-s3");



var GetBucketLocationCommand = class extends import_smithy_client.Command.classBuilder().ep({
  ...commonParams,
  UseS3ExpressControlEndpoint: { type: "staticContextParams", value: true },
  Bucket: { type: "contextParams", name: "Bucket" }
}).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions()),
    (0, import_middleware_sdk_s319.getThrow200ExceptionsPlugin)(config)
  ];
}).s("AmazonS3", "GetBucketLocation", {}).n("S3Client", "GetBucketLocationCommand").f(void 0, void 0).ser(se_GetBucketLocationCommand).de(de_GetBucketLocationCommand).build() {
  static {
    __name(this, "GetBucketLocationCommand");
  }
};

// src/commands/GetBucketLoggingCommand.ts
var import_middleware_sdk_s320 = require("@aws-sdk/middleware-sdk-s3");



var GetBucketLoggingCommand = class extends import_smithy_client.Command.classBuilder().ep({
  ...commonParams,
  UseS3ExpressControlEndpoint: { type: "staticContextParams", value: true },
  Bucket: { type: "contextParams", name: "Bucket" }
}).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions()),
    (0, import_middleware_sdk_s320.getThrow200ExceptionsPlugin)(config)
  ];
}).s("AmazonS3", "GetBucketLogging", {}).n("S3Client", "GetBucketLoggingCommand").f(void 0, void 0).ser(se_GetBucketLoggingCommand).de(de_GetBucketLoggingCommand).build() {
  static {
    __name(this, "GetBucketLoggingCommand");
  }
};

// src/commands/GetBucketMetadataConfigurationCommand.ts
var import_middleware_sdk_s321 = require("@aws-sdk/middleware-sdk-s3");



var GetBucketMetadataConfigurationCommand = class extends import_smithy_client.Command.classBuilder().ep({
  ...commonParams,
  UseS3ExpressControlEndpoint: { type: "staticContextParams", value: true },
  Bucket: { type: "contextParams", name: "Bucket" }
}).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions()),
    (0, import_middleware_sdk_s321.getThrow200ExceptionsPlugin)(config)
  ];
}).s("AmazonS3", "GetBucketMetadataConfiguration", {}).n("S3Client", "GetBucketMetadataConfigurationCommand").f(void 0, void 0).ser(se_GetBucketMetadataConfigurationCommand).de(de_GetBucketMetadataConfigurationCommand).build() {
  static {
    __name(this, "GetBucketMetadataConfigurationCommand");
  }
};

// src/commands/GetBucketMetadataTableConfigurationCommand.ts
var import_middleware_sdk_s322 = require("@aws-sdk/middleware-sdk-s3");



var GetBucketMetadataTableConfigurationCommand = class extends import_smithy_client.Command.classBuilder().ep({
  ...commonParams,
  UseS3ExpressControlEndpoint: { type: "staticContextParams", value: true },
  Bucket: { type: "contextParams", name: "Bucket" }
}).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions()),
    (0, import_middleware_sdk_s322.getThrow200ExceptionsPlugin)(config)
  ];
}).s("AmazonS3", "GetBucketMetadataTableConfiguration", {}).n("S3Client", "GetBucketMetadataTableConfigurationCommand").f(void 0, void 0).ser(se_GetBucketMetadataTableConfigurationCommand).de(de_GetBucketMetadataTableConfigurationCommand).build() {
  static {
    __name(this, "GetBucketMetadataTableConfigurationCommand");
  }
};

// src/commands/GetBucketMetricsConfigurationCommand.ts
var import_middleware_sdk_s323 = require("@aws-sdk/middleware-sdk-s3");



var GetBucketMetricsConfigurationCommand = class extends import_smithy_client.Command.classBuilder().ep({
  ...commonParams,
  UseS3ExpressControlEndpoint: { type: "staticContextParams", value: true },
  Bucket: { type: "contextParams", name: "Bucket" }
}).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions()),
    (0, import_middleware_sdk_s323.getThrow200ExceptionsPlugin)(config)
  ];
}).s("AmazonS3", "GetBucketMetricsConfiguration", {}).n("S3Client", "GetBucketMetricsConfigurationCommand").f(void 0, void 0).ser(se_GetBucketMetricsConfigurationCommand).de(de_GetBucketMetricsConfigurationCommand).build() {
  static {
    __name(this, "GetBucketMetricsConfigurationCommand");
  }
};

// src/commands/GetBucketNotificationConfigurationCommand.ts
var import_middleware_sdk_s324 = require("@aws-sdk/middleware-sdk-s3");



var GetBucketNotificationConfigurationCommand = class extends import_smithy_client.Command.classBuilder().ep({
  ...commonParams,
  UseS3ExpressControlEndpoint: { type: "staticContextParams", value: true },
  Bucket: { type: "contextParams", name: "Bucket" }
}).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions()),
    (0, import_middleware_sdk_s324.getThrow200ExceptionsPlugin)(config)
  ];
}).s("AmazonS3", "GetBucketNotificationConfiguration", {}).n("S3Client", "GetBucketNotificationConfigurationCommand").f(void 0, void 0).ser(se_GetBucketNotificationConfigurationCommand).de(de_GetBucketNotificationConfigurationCommand).build() {
  static {
    __name(this, "GetBucketNotificationConfigurationCommand");
  }
};

// src/commands/GetBucketOwnershipControlsCommand.ts
var import_middleware_sdk_s325 = require("@aws-sdk/middleware-sdk-s3");



var GetBucketOwnershipControlsCommand = class extends import_smithy_client.Command.classBuilder().ep({
  ...commonParams,
  UseS3ExpressControlEndpoint: { type: "staticContextParams", value: true },
  Bucket: { type: "contextParams", name: "Bucket" }
}).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions()),
    (0, import_middleware_sdk_s325.getThrow200ExceptionsPlugin)(config)
  ];
}).s("AmazonS3", "GetBucketOwnershipControls", {}).n("S3Client", "GetBucketOwnershipControlsCommand").f(void 0, void 0).ser(se_GetBucketOwnershipControlsCommand).de(de_GetBucketOwnershipControlsCommand).build() {
  static {
    __name(this, "GetBucketOwnershipControlsCommand");
  }
};

// src/commands/GetBucketPolicyCommand.ts
var import_middleware_sdk_s326 = require("@aws-sdk/middleware-sdk-s3");



var GetBucketPolicyCommand = class extends import_smithy_client.Command.classBuilder().ep({
  ...commonParams,
  UseS3ExpressControlEndpoint: { type: "staticContextParams", value: true },
  Bucket: { type: "contextParams", name: "Bucket" }
}).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions()),
    (0, import_middleware_sdk_s326.getThrow200ExceptionsPlugin)(config)
  ];
}).s("AmazonS3", "GetBucketPolicy", {}).n("S3Client", "GetBucketPolicyCommand").f(void 0, void 0).ser(se_GetBucketPolicyCommand).de(de_GetBucketPolicyCommand).build() {
  static {
    __name(this, "GetBucketPolicyCommand");
  }
};

// src/commands/GetBucketPolicyStatusCommand.ts
var import_middleware_sdk_s327 = require("@aws-sdk/middleware-sdk-s3");



var GetBucketPolicyStatusCommand = class extends import_smithy_client.Command.classBuilder().ep({
  ...commonParams,
  UseS3ExpressControlEndpoint: { type: "staticContextParams", value: true },
  Bucket: { type: "contextParams", name: "Bucket" }
}).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions()),
    (0, import_middleware_sdk_s327.getThrow200ExceptionsPlugin)(config)
  ];
}).s("AmazonS3", "GetBucketPolicyStatus", {}).n("S3Client", "GetBucketPolicyStatusCommand").f(void 0, void 0).ser(se_GetBucketPolicyStatusCommand).de(de_GetBucketPolicyStatusCommand).build() {
  static {
    __name(this, "GetBucketPolicyStatusCommand");
  }
};

// src/commands/GetBucketReplicationCommand.ts
var import_middleware_sdk_s328 = require("@aws-sdk/middleware-sdk-s3");



var GetBucketReplicationCommand = class extends import_smithy_client.Command.classBuilder().ep({
  ...commonParams,
  UseS3ExpressControlEndpoint: { type: "staticContextParams", value: true },
  Bucket: { type: "contextParams", name: "Bucket" }
}).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions()),
    (0, import_middleware_sdk_s328.getThrow200ExceptionsPlugin)(config)
  ];
}).s("AmazonS3", "GetBucketReplication", {}).n("S3Client", "GetBucketReplicationCommand").f(void 0, void 0).ser(se_GetBucketReplicationCommand).de(de_GetBucketReplicationCommand).build() {
  static {
    __name(this, "GetBucketReplicationCommand");
  }
};

// src/commands/GetBucketRequestPaymentCommand.ts
var import_middleware_sdk_s329 = require("@aws-sdk/middleware-sdk-s3");



var GetBucketRequestPaymentCommand = class extends import_smithy_client.Command.classBuilder().ep({
  ...commonParams,
  UseS3ExpressControlEndpoint: { type: "staticContextParams", value: true },
  Bucket: { type: "contextParams", name: "Bucket" }
}).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions()),
    (0, import_middleware_sdk_s329.getThrow200ExceptionsPlugin)(config)
  ];
}).s("AmazonS3", "GetBucketRequestPayment", {}).n("S3Client", "GetBucketRequestPaymentCommand").f(void 0, void 0).ser(se_GetBucketRequestPaymentCommand).de(de_GetBucketRequestPaymentCommand).build() {
  static {
    __name(this, "GetBucketRequestPaymentCommand");
  }
};

// src/commands/GetBucketTaggingCommand.ts
var import_middleware_sdk_s330 = require("@aws-sdk/middleware-sdk-s3");



var GetBucketTaggingCommand = class extends import_smithy_client.Command.classBuilder().ep({
  ...commonParams,
  UseS3ExpressControlEndpoint: { type: "staticContextParams", value: true },
  Bucket: { type: "contextParams", name: "Bucket" }
}).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions()),
    (0, import_middleware_sdk_s330.getThrow200ExceptionsPlugin)(config)
  ];
}).s("AmazonS3", "GetBucketTagging", {}).n("S3Client", "GetBucketTaggingCommand").f(void 0, void 0).ser(se_GetBucketTaggingCommand).de(de_GetBucketTaggingCommand).build() {
  static {
    __name(this, "GetBucketTaggingCommand");
  }
};

// src/commands/GetBucketVersioningCommand.ts
var import_middleware_sdk_s331 = require("@aws-sdk/middleware-sdk-s3");



var GetBucketVersioningCommand = class extends import_smithy_client.Command.classBuilder().ep({
  ...commonParams,
  UseS3ExpressControlEndpoint: { type: "staticContextParams", value: true },
  Bucket: { type: "contextParams", name: "Bucket" }
}).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions()),
    (0, import_middleware_sdk_s331.getThrow200ExceptionsPlugin)(config)
  ];
}).s("AmazonS3", "GetBucketVersioning", {}).n("S3Client", "GetBucketVersioningCommand").f(void 0, void 0).ser(se_GetBucketVersioningCommand).de(de_GetBucketVersioningCommand).build() {
  static {
    __name(this, "GetBucketVersioningCommand");
  }
};

// src/commands/GetBucketWebsiteCommand.ts
var import_middleware_sdk_s332 = require("@aws-sdk/middleware-sdk-s3");



var GetBucketWebsiteCommand = class extends import_smithy_client.Command.classBuilder().ep({
  ...commonParams,
  UseS3ExpressControlEndpoint: { type: "staticContextParams", value: true },
  Bucket: { type: "contextParams", name: "Bucket" }
}).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions()),
    (0, import_middleware_sdk_s332.getThrow200ExceptionsPlugin)(config)
  ];
}).s("AmazonS3", "GetBucketWebsite", {}).n("S3Client", "GetBucketWebsiteCommand").f(void 0, void 0).ser(se_GetBucketWebsiteCommand).de(de_GetBucketWebsiteCommand).build() {
  static {
    __name(this, "GetBucketWebsiteCommand");
  }
};

// src/commands/GetObjectAclCommand.ts
var import_middleware_sdk_s333 = require("@aws-sdk/middleware-sdk-s3");



var GetObjectAclCommand = class extends import_smithy_client.Command.classBuilder().ep({
  ...commonParams,
  Bucket: { type: "contextParams", name: "Bucket" },
  Key: { type: "contextParams", name: "Key" }
}).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions()),
    (0, import_middleware_sdk_s333.getThrow200ExceptionsPlugin)(config)
  ];
}).s("AmazonS3", "GetObjectAcl", {}).n("S3Client", "GetObjectAclCommand").f(void 0, void 0).ser(se_GetObjectAclCommand).de(de_GetObjectAclCommand).build() {
  static {
    __name(this, "GetObjectAclCommand");
  }
};

// src/commands/GetObjectAttributesCommand.ts
var import_middleware_sdk_s334 = require("@aws-sdk/middleware-sdk-s3");




var GetObjectAttributesCommand = class extends import_smithy_client.Command.classBuilder().ep({
  ...commonParams,
  Bucket: { type: "contextParams", name: "Bucket" }
}).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions()),
    (0, import_middleware_sdk_s334.getThrow200ExceptionsPlugin)(config),
    (0, import_middleware_ssec.getSsecPlugin)(config)
  ];
}).s("AmazonS3", "GetObjectAttributes", {}).n("S3Client", "GetObjectAttributesCommand").f(GetObjectAttributesRequestFilterSensitiveLog, void 0).ser(se_GetObjectAttributesCommand).de(de_GetObjectAttributesCommand).build() {
  static {
    __name(this, "GetObjectAttributesCommand");
  }
};

// src/commands/GetObjectCommand.ts

var import_middleware_sdk_s335 = require("@aws-sdk/middleware-sdk-s3");




var GetObjectCommand = class extends import_smithy_client.Command.classBuilder().ep({
  ...commonParams,
  Bucket: { type: "contextParams", name: "Bucket" },
  Key: { type: "contextParams", name: "Key" }
}).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions()),
    (0, import_middleware_flexible_checksums.getFlexibleChecksumsPlugin)(config, {
      requestChecksumRequired: false,
      requestValidationModeMember: "ChecksumMode",
      responseAlgorithms: ["CRC64NVME", "CRC32", "CRC32C", "SHA256", "SHA1"]
    }),
    (0, import_middleware_ssec.getSsecPlugin)(config),
    (0, import_middleware_sdk_s335.getS3ExpiresMiddlewarePlugin)(config)
  ];
}).s("AmazonS3", "GetObject", {}).n("S3Client", "GetObjectCommand").f(GetObjectRequestFilterSensitiveLog, GetObjectOutputFilterSensitiveLog).ser(se_GetObjectCommand).de(de_GetObjectCommand).build() {
  static {
    __name(this, "GetObjectCommand");
  }
};

// src/commands/GetObjectLegalHoldCommand.ts
var import_middleware_sdk_s336 = require("@aws-sdk/middleware-sdk-s3");



var GetObjectLegalHoldCommand = class extends import_smithy_client.Command.classBuilder().ep({
  ...commonParams,
  Bucket: { type: "contextParams", name: "Bucket" }
}).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions()),
    (0, import_middleware_sdk_s336.getThrow200ExceptionsPlugin)(config)
  ];
}).s("AmazonS3", "GetObjectLegalHold", {}).n("S3Client", "GetObjectLegalHoldCommand").f(void 0, void 0).ser(se_GetObjectLegalHoldCommand).de(de_GetObjectLegalHoldCommand).build() {
  static {
    __name(this, "GetObjectLegalHoldCommand");
  }
};

// src/commands/GetObjectLockConfigurationCommand.ts
var import_middleware_sdk_s337 = require("@aws-sdk/middleware-sdk-s3");



var GetObjectLockConfigurationCommand = class extends import_smithy_client.Command.classBuilder().ep({
  ...commonParams,
  Bucket: { type: "contextParams", name: "Bucket" }
}).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions()),
    (0, import_middleware_sdk_s337.getThrow200ExceptionsPlugin)(config)
  ];
}).s("AmazonS3", "GetObjectLockConfiguration", {}).n("S3Client", "GetObjectLockConfigurationCommand").f(void 0, void 0).ser(se_GetObjectLockConfigurationCommand).de(de_GetObjectLockConfigurationCommand).build() {
  static {
    __name(this, "GetObjectLockConfigurationCommand");
  }
};

// src/commands/GetObjectRetentionCommand.ts
var import_middleware_sdk_s338 = require("@aws-sdk/middleware-sdk-s3");



var GetObjectRetentionCommand = class extends import_smithy_client.Command.classBuilder().ep({
  ...commonParams,
  Bucket: { type: "contextParams", name: "Bucket" }
}).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions()),
    (0, import_middleware_sdk_s338.getThrow200ExceptionsPlugin)(config)
  ];
}).s("AmazonS3", "GetObjectRetention", {}).n("S3Client", "GetObjectRetentionCommand").f(void 0, void 0).ser(se_GetObjectRetentionCommand).de(de_GetObjectRetentionCommand).build() {
  static {
    __name(this, "GetObjectRetentionCommand");
  }
};

// src/commands/GetObjectTaggingCommand.ts
var import_middleware_sdk_s339 = require("@aws-sdk/middleware-sdk-s3");



var GetObjectTaggingCommand = class extends import_smithy_client.Command.classBuilder().ep({
  ...commonParams,
  Bucket: { type: "contextParams", name: "Bucket" }
}).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions()),
    (0, import_middleware_sdk_s339.getThrow200ExceptionsPlugin)(config)
  ];
}).s("AmazonS3", "GetObjectTagging", {}).n("S3Client", "GetObjectTaggingCommand").f(void 0, void 0).ser(se_GetObjectTaggingCommand).de(de_GetObjectTaggingCommand).build() {
  static {
    __name(this, "GetObjectTaggingCommand");
  }
};

// src/commands/GetObjectTorrentCommand.ts



var GetObjectTorrentCommand = class extends import_smithy_client.Command.classBuilder().ep({
  ...commonParams,
  Bucket: { type: "contextParams", name: "Bucket" }
}).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AmazonS3", "GetObjectTorrent", {}).n("S3Client", "GetObjectTorrentCommand").f(void 0, GetObjectTorrentOutputFilterSensitiveLog).ser(se_GetObjectTorrentCommand).de(de_GetObjectTorrentCommand).build() {
  static {
    __name(this, "GetObjectTorrentCommand");
  }
};

// src/commands/GetPublicAccessBlockCommand.ts
var import_middleware_sdk_s340 = require("@aws-sdk/middleware-sdk-s3");



var GetPublicAccessBlockCommand = class extends import_smithy_client.Command.classBuilder().ep({
  ...commonParams,
  UseS3ExpressControlEndpoint: { type: "staticContextParams", value: true },
  Bucket: { type: "contextParams", name: "Bucket" }
}).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions()),
    (0, import_middleware_sdk_s340.getThrow200ExceptionsPlugin)(config)
  ];
}).s("AmazonS3", "GetPublicAccessBlock", {}).n("S3Client", "GetPublicAccessBlockCommand").f(void 0, void 0).ser(se_GetPublicAccessBlockCommand).de(de_GetPublicAccessBlockCommand).build() {
  static {
    __name(this, "GetPublicAccessBlockCommand");
  }
};

// src/commands/HeadBucketCommand.ts
var import_middleware_sdk_s341 = require("@aws-sdk/middleware-sdk-s3");



var HeadBucketCommand = class extends import_smithy_client.Command.classBuilder().ep({
  ...commonParams,
  Bucket: { type: "contextParams", name: "Bucket" }
}).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions()),
    (0, import_middleware_sdk_s341.getThrow200ExceptionsPlugin)(config)
  ];
}).s("AmazonS3", "HeadBucket", {}).n("S3Client", "HeadBucketCommand").f(void 0, void 0).ser(se_HeadBucketCommand).de(de_HeadBucketCommand).build() {
  static {
    __name(this, "HeadBucketCommand");
  }
};

// src/commands/HeadObjectCommand.ts
var import_middleware_sdk_s342 = require("@aws-sdk/middleware-sdk-s3");




var HeadObjectCommand = class extends import_smithy_client.Command.classBuilder().ep({
  ...commonParams,
  Bucket: { type: "contextParams", name: "Bucket" },
  Key: { type: "contextParams", name: "Key" }
}).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions()),
    (0, import_middleware_sdk_s342.getThrow200ExceptionsPlugin)(config),
    (0, import_middleware_ssec.getSsecPlugin)(config),
    (0, import_middleware_sdk_s342.getS3ExpiresMiddlewarePlugin)(config)
  ];
}).s("AmazonS3", "HeadObject", {}).n("S3Client", "HeadObjectCommand").f(HeadObjectRequestFilterSensitiveLog, HeadObjectOutputFilterSensitiveLog).ser(se_HeadObjectCommand).de(de_HeadObjectCommand).build() {
  static {
    __name(this, "HeadObjectCommand");
  }
};

// src/commands/ListBucketAnalyticsConfigurationsCommand.ts
var import_middleware_sdk_s343 = require("@aws-sdk/middleware-sdk-s3");



var ListBucketAnalyticsConfigurationsCommand = class extends import_smithy_client.Command.classBuilder().ep({
  ...commonParams,
  UseS3ExpressControlEndpoint: { type: "staticContextParams", value: true },
  Bucket: { type: "contextParams", name: "Bucket" }
}).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions()),
    (0, import_middleware_sdk_s343.getThrow200ExceptionsPlugin)(config)
  ];
}).s("AmazonS3", "ListBucketAnalyticsConfigurations", {}).n("S3Client", "ListBucketAnalyticsConfigurationsCommand").f(void 0, void 0).ser(se_ListBucketAnalyticsConfigurationsCommand).de(de_ListBucketAnalyticsConfigurationsCommand).build() {
  static {
    __name(this, "ListBucketAnalyticsConfigurationsCommand");
  }
};

// src/commands/ListBucketIntelligentTieringConfigurationsCommand.ts
var import_middleware_sdk_s344 = require("@aws-sdk/middleware-sdk-s3");



var ListBucketIntelligentTieringConfigurationsCommand = class extends import_smithy_client.Command.classBuilder().ep({
  ...commonParams,
  UseS3ExpressControlEndpoint: { type: "staticContextParams", value: true },
  Bucket: { type: "contextParams", name: "Bucket" }
}).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions()),
    (0, import_middleware_sdk_s344.getThrow200ExceptionsPlugin)(config)
  ];
}).s("AmazonS3", "ListBucketIntelligentTieringConfigurations", {}).n("S3Client", "ListBucketIntelligentTieringConfigurationsCommand").f(void 0, void 0).ser(se_ListBucketIntelligentTieringConfigurationsCommand).de(de_ListBucketIntelligentTieringConfigurationsCommand).build() {
  static {
    __name(this, "ListBucketIntelligentTieringConfigurationsCommand");
  }
};

// src/commands/ListBucketInventoryConfigurationsCommand.ts
var import_middleware_sdk_s345 = require("@aws-sdk/middleware-sdk-s3");



var ListBucketInventoryConfigurationsCommand = class extends import_smithy_client.Command.classBuilder().ep({
  ...commonParams,
  UseS3ExpressControlEndpoint: { type: "staticContextParams", value: true },
  Bucket: { type: "contextParams", name: "Bucket" }
}).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions()),
    (0, import_middleware_sdk_s345.getThrow200ExceptionsPlugin)(config)
  ];
}).s("AmazonS3", "ListBucketInventoryConfigurations", {}).n("S3Client", "ListBucketInventoryConfigurationsCommand").f(void 0, ListBucketInventoryConfigurationsOutputFilterSensitiveLog).ser(se_ListBucketInventoryConfigurationsCommand).de(de_ListBucketInventoryConfigurationsCommand).build() {
  static {
    __name(this, "ListBucketInventoryConfigurationsCommand");
  }
};

// src/commands/ListBucketMetricsConfigurationsCommand.ts
var import_middleware_sdk_s346 = require("@aws-sdk/middleware-sdk-s3");



var ListBucketMetricsConfigurationsCommand = class extends import_smithy_client.Command.classBuilder().ep({
  ...commonParams,
  Bucket: { type: "contextParams", name: "Bucket" }
}).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions()),
    (0, import_middleware_sdk_s346.getThrow200ExceptionsPlugin)(config)
  ];
}).s("AmazonS3", "ListBucketMetricsConfigurations", {}).n("S3Client", "ListBucketMetricsConfigurationsCommand").f(void 0, void 0).ser(se_ListBucketMetricsConfigurationsCommand).de(de_ListBucketMetricsConfigurationsCommand).build() {
  static {
    __name(this, "ListBucketMetricsConfigurationsCommand");
  }
};

// src/commands/ListBucketsCommand.ts
var import_middleware_sdk_s347 = require("@aws-sdk/middleware-sdk-s3");



var ListBucketsCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions()),
    (0, import_middleware_sdk_s347.getThrow200ExceptionsPlugin)(config)
  ];
}).s("AmazonS3", "ListBuckets", {}).n("S3Client", "ListBucketsCommand").f(void 0, void 0).ser(se_ListBucketsCommand).de(de_ListBucketsCommand).build() {
  static {
    __name(this, "ListBucketsCommand");
  }
};

// src/commands/ListDirectoryBucketsCommand.ts
var import_middleware_sdk_s348 = require("@aws-sdk/middleware-sdk-s3");



var ListDirectoryBucketsCommand = class extends import_smithy_client.Command.classBuilder().ep({
  ...commonParams,
  UseS3ExpressControlEndpoint: { type: "staticContextParams", value: true }
}).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions()),
    (0, import_middleware_sdk_s348.getThrow200ExceptionsPlugin)(config)
  ];
}).s("AmazonS3", "ListDirectoryBuckets", {}).n("S3Client", "ListDirectoryBucketsCommand").f(void 0, void 0).ser(se_ListDirectoryBucketsCommand).de(de_ListDirectoryBucketsCommand).build() {
  static {
    __name(this, "ListDirectoryBucketsCommand");
  }
};

// src/commands/ListMultipartUploadsCommand.ts
var import_middleware_sdk_s349 = require("@aws-sdk/middleware-sdk-s3");



var ListMultipartUploadsCommand = class extends import_smithy_client.Command.classBuilder().ep({
  ...commonParams,
  Bucket: { type: "contextParams", name: "Bucket" },
  Prefix: { type: "contextParams", name: "Prefix" }
}).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions()),
    (0, import_middleware_sdk_s349.getThrow200ExceptionsPlugin)(config)
  ];
}).s("AmazonS3", "ListMultipartUploads", {}).n("S3Client", "ListMultipartUploadsCommand").f(void 0, void 0).ser(se_ListMultipartUploadsCommand).de(de_ListMultipartUploadsCommand).build() {
  static {
    __name(this, "ListMultipartUploadsCommand");
  }
};

// src/commands/ListObjectsCommand.ts
var import_middleware_sdk_s350 = require("@aws-sdk/middleware-sdk-s3");



var ListObjectsCommand = class extends import_smithy_client.Command.classBuilder().ep({
  ...commonParams,
  Bucket: { type: "contextParams", name: "Bucket" },
  Prefix: { type: "contextParams", name: "Prefix" }
}).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions()),
    (0, import_middleware_sdk_s350.getThrow200ExceptionsPlugin)(config)
  ];
}).s("AmazonS3", "ListObjects", {}).n("S3Client", "ListObjectsCommand").f(void 0, void 0).ser(se_ListObjectsCommand).de(de_ListObjectsCommand).build() {
  static {
    __name(this, "ListObjectsCommand");
  }
};

// src/commands/ListObjectsV2Command.ts
var import_middleware_sdk_s351 = require("@aws-sdk/middleware-sdk-s3");



var ListObjectsV2Command = class extends import_smithy_client.Command.classBuilder().ep({
  ...commonParams,
  Bucket: { type: "contextParams", name: "Bucket" },
  Prefix: { type: "contextParams", name: "Prefix" }
}).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions()),
    (0, import_middleware_sdk_s351.getThrow200ExceptionsPlugin)(config)
  ];
}).s("AmazonS3", "ListObjectsV2", {}).n("S3Client", "ListObjectsV2Command").f(void 0, void 0).ser(se_ListObjectsV2Command).de(de_ListObjectsV2Command).build() {
  static {
    __name(this, "ListObjectsV2Command");
  }
};

// src/commands/ListObjectVersionsCommand.ts
var import_middleware_sdk_s352 = require("@aws-sdk/middleware-sdk-s3");



var ListObjectVersionsCommand = class extends import_smithy_client.Command.classBuilder().ep({
  ...commonParams,
  Bucket: { type: "contextParams", name: "Bucket" },
  Prefix: { type: "contextParams", name: "Prefix" }
}).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions()),
    (0, import_middleware_sdk_s352.getThrow200ExceptionsPlugin)(config)
  ];
}).s("AmazonS3", "ListObjectVersions", {}).n("S3Client", "ListObjectVersionsCommand").f(void 0, void 0).ser(se_ListObjectVersionsCommand).de(de_ListObjectVersionsCommand).build() {
  static {
    __name(this, "ListObjectVersionsCommand");
  }
};

// src/commands/ListPartsCommand.ts
var import_middleware_sdk_s353 = require("@aws-sdk/middleware-sdk-s3");




var ListPartsCommand = class extends import_smithy_client.Command.classBuilder().ep({
  ...commonParams,
  Bucket: { type: "contextParams", name: "Bucket" },
  Key: { type: "contextParams", name: "Key" }
}).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions()),
    (0, import_middleware_sdk_s353.getThrow200ExceptionsPlugin)(config),
    (0, import_middleware_ssec.getSsecPlugin)(config)
  ];
}).s("AmazonS3", "ListParts", {}).n("S3Client", "ListPartsCommand").f(ListPartsRequestFilterSensitiveLog, void 0).ser(se_ListPartsCommand).de(de_ListPartsCommand).build() {
  static {
    __name(this, "ListPartsCommand");
  }
};

// src/commands/PutBucketAccelerateConfigurationCommand.ts




var PutBucketAccelerateConfigurationCommand = class extends import_smithy_client.Command.classBuilder().ep({
  ...commonParams,
  UseS3ExpressControlEndpoint: { type: "staticContextParams", value: true },
  Bucket: { type: "contextParams", name: "Bucket" }
}).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions()),
    (0, import_middleware_flexible_checksums.getFlexibleChecksumsPlugin)(config, {
      requestAlgorithmMember: { httpHeader: "x-amz-sdk-checksum-algorithm", name: "ChecksumAlgorithm" },
      requestChecksumRequired: false
    })
  ];
}).s("AmazonS3", "PutBucketAccelerateConfiguration", {}).n("S3Client", "PutBucketAccelerateConfigurationCommand").f(void 0, void 0).ser(se_PutBucketAccelerateConfigurationCommand).de(de_PutBucketAccelerateConfigurationCommand).build() {
  static {
    __name(this, "PutBucketAccelerateConfigurationCommand");
  }
};

// src/commands/PutBucketAclCommand.ts




var PutBucketAclCommand = class extends import_smithy_client.Command.classBuilder().ep({
  ...commonParams,
  UseS3ExpressControlEndpoint: { type: "staticContextParams", value: true },
  Bucket: { type: "contextParams", name: "Bucket" }
}).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions()),
    (0, import_middleware_flexible_checksums.getFlexibleChecksumsPlugin)(config, {
      requestAlgorithmMember: { httpHeader: "x-amz-sdk-checksum-algorithm", name: "ChecksumAlgorithm" },
      requestChecksumRequired: true
    })
  ];
}).s("AmazonS3", "PutBucketAcl", {}).n("S3Client", "PutBucketAclCommand").f(void 0, void 0).ser(se_PutBucketAclCommand).de(de_PutBucketAclCommand).build() {
  static {
    __name(this, "PutBucketAclCommand");
  }
};

// src/commands/PutBucketAnalyticsConfigurationCommand.ts



var PutBucketAnalyticsConfigurationCommand = class extends import_smithy_client.Command.classBuilder().ep({
  ...commonParams,
  UseS3ExpressControlEndpoint: { type: "staticContextParams", value: true },
  Bucket: { type: "contextParams", name: "Bucket" }
}).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AmazonS3", "PutBucketAnalyticsConfiguration", {}).n("S3Client", "PutBucketAnalyticsConfigurationCommand").f(void 0, void 0).ser(se_PutBucketAnalyticsConfigurationCommand).de(de_PutBucketAnalyticsConfigurationCommand).build() {
  static {
    __name(this, "PutBucketAnalyticsConfigurationCommand");
  }
};

// src/commands/PutBucketCorsCommand.ts




var PutBucketCorsCommand = class extends import_smithy_client.Command.classBuilder().ep({
  ...commonParams,
  UseS3ExpressControlEndpoint: { type: "staticContextParams", value: true },
  Bucket: { type: "contextParams", name: "Bucket" }
}).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions()),
    (0, import_middleware_flexible_checksums.getFlexibleChecksumsPlugin)(config, {
      requestAlgorithmMember: { httpHeader: "x-amz-sdk-checksum-algorithm", name: "ChecksumAlgorithm" },
      requestChecksumRequired: true
    })
  ];
}).s("AmazonS3", "PutBucketCors", {}).n("S3Client", "PutBucketCorsCommand").f(void 0, void 0).ser(se_PutBucketCorsCommand).de(de_PutBucketCorsCommand).build() {
  static {
    __name(this, "PutBucketCorsCommand");
  }
};

// src/commands/PutBucketEncryptionCommand.ts




var PutBucketEncryptionCommand = class extends import_smithy_client.Command.classBuilder().ep({
  ...commonParams,
  UseS3ExpressControlEndpoint: { type: "staticContextParams", value: true },
  Bucket: { type: "contextParams", name: "Bucket" }
}).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions()),
    (0, import_middleware_flexible_checksums.getFlexibleChecksumsPlugin)(config, {
      requestAlgorithmMember: { httpHeader: "x-amz-sdk-checksum-algorithm", name: "ChecksumAlgorithm" },
      requestChecksumRequired: true
    })
  ];
}).s("AmazonS3", "PutBucketEncryption", {}).n("S3Client", "PutBucketEncryptionCommand").f(PutBucketEncryptionRequestFilterSensitiveLog, void 0).ser(se_PutBucketEncryptionCommand).de(de_PutBucketEncryptionCommand).build() {
  static {
    __name(this, "PutBucketEncryptionCommand");
  }
};

// src/commands/PutBucketIntelligentTieringConfigurationCommand.ts



var PutBucketIntelligentTieringConfigurationCommand = class extends import_smithy_client.Command.classBuilder().ep({
  ...commonParams,
  UseS3ExpressControlEndpoint: { type: "staticContextParams", value: true },
  Bucket: { type: "contextParams", name: "Bucket" }
}).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AmazonS3", "PutBucketIntelligentTieringConfiguration", {}).n("S3Client", "PutBucketIntelligentTieringConfigurationCommand").f(void 0, void 0).ser(se_PutBucketIntelligentTieringConfigurationCommand).de(de_PutBucketIntelligentTieringConfigurationCommand).build() {
  static {
    __name(this, "PutBucketIntelligentTieringConfigurationCommand");
  }
};

// src/commands/PutBucketInventoryConfigurationCommand.ts



var PutBucketInventoryConfigurationCommand = class extends import_smithy_client.Command.classBuilder().ep({
  ...commonParams,
  UseS3ExpressControlEndpoint: { type: "staticContextParams", value: true },
  Bucket: { type: "contextParams", name: "Bucket" }
}).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AmazonS3", "PutBucketInventoryConfiguration", {}).n("S3Client", "PutBucketInventoryConfigurationCommand").f(PutBucketInventoryConfigurationRequestFilterSensitiveLog, void 0).ser(se_PutBucketInventoryConfigurationCommand).de(de_PutBucketInventoryConfigurationCommand).build() {
  static {
    __name(this, "PutBucketInventoryConfigurationCommand");
  }
};

// src/commands/PutBucketLifecycleConfigurationCommand.ts

var import_middleware_sdk_s354 = require("@aws-sdk/middleware-sdk-s3");



var PutBucketLifecycleConfigurationCommand = class extends import_smithy_client.Command.classBuilder().ep({
  ...commonParams,
  UseS3ExpressControlEndpoint: { type: "staticContextParams", value: true },
  Bucket: { type: "contextParams", name: "Bucket" }
}).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions()),
    (0, import_middleware_flexible_checksums.getFlexibleChecksumsPlugin)(config, {
      requestAlgorithmMember: { httpHeader: "x-amz-sdk-checksum-algorithm", name: "ChecksumAlgorithm" },
      requestChecksumRequired: true
    }),
    (0, import_middleware_sdk_s354.getThrow200ExceptionsPlugin)(config)
  ];
}).s("AmazonS3", "PutBucketLifecycleConfiguration", {}).n("S3Client", "PutBucketLifecycleConfigurationCommand").f(void 0, void 0).ser(se_PutBucketLifecycleConfigurationCommand).de(de_PutBucketLifecycleConfigurationCommand).build() {
  static {
    __name(this, "PutBucketLifecycleConfigurationCommand");
  }
};

// src/commands/PutBucketLoggingCommand.ts




var PutBucketLoggingCommand = class extends import_smithy_client.Command.classBuilder().ep({
  ...commonParams,
  UseS3ExpressControlEndpoint: { type: "staticContextParams", value: true },
  Bucket: { type: "contextParams", name: "Bucket" }
}).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions()),
    (0, import_middleware_flexible_checksums.getFlexibleChecksumsPlugin)(config, {
      requestAlgorithmMember: { httpHeader: "x-amz-sdk-checksum-algorithm", name: "ChecksumAlgorithm" },
      requestChecksumRequired: true
    })
  ];
}).s("AmazonS3", "PutBucketLogging", {}).n("S3Client", "PutBucketLoggingCommand").f(void 0, void 0).ser(se_PutBucketLoggingCommand).de(de_PutBucketLoggingCommand).build() {
  static {
    __name(this, "PutBucketLoggingCommand");
  }
};

// src/commands/PutBucketMetricsConfigurationCommand.ts



var PutBucketMetricsConfigurationCommand = class extends import_smithy_client.Command.classBuilder().ep({
  ...commonParams,
  UseS3ExpressControlEndpoint: { type: "staticContextParams", value: true },
  Bucket: { type: "contextParams", name: "Bucket" }
}).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AmazonS3", "PutBucketMetricsConfiguration", {}).n("S3Client", "PutBucketMetricsConfigurationCommand").f(void 0, void 0).ser(se_PutBucketMetricsConfigurationCommand).de(de_PutBucketMetricsConfigurationCommand).build() {
  static {
    __name(this, "PutBucketMetricsConfigurationCommand");
  }
};

// src/commands/PutBucketNotificationConfigurationCommand.ts



var PutBucketNotificationConfigurationCommand = class extends import_smithy_client.Command.classBuilder().ep({
  ...commonParams,
  UseS3ExpressControlEndpoint: { type: "staticContextParams", value: true },
  Bucket: { type: "contextParams", name: "Bucket" }
}).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AmazonS3", "PutBucketNotificationConfiguration", {}).n("S3Client", "PutBucketNotificationConfigurationCommand").f(void 0, void 0).ser(se_PutBucketNotificationConfigurationCommand).de(de_PutBucketNotificationConfigurationCommand).build() {
  static {
    __name(this, "PutBucketNotificationConfigurationCommand");
  }
};

// src/commands/PutBucketOwnershipControlsCommand.ts




var PutBucketOwnershipControlsCommand = class extends import_smithy_client.Command.classBuilder().ep({
  ...commonParams,
  UseS3ExpressControlEndpoint: { type: "staticContextParams", value: true },
  Bucket: { type: "contextParams", name: "Bucket" }
}).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions()),
    (0, import_middleware_flexible_checksums.getFlexibleChecksumsPlugin)(config, {
      requestAlgorithmMember: { httpHeader: "x-amz-sdk-checksum-algorithm", name: "ChecksumAlgorithm" },
      requestChecksumRequired: true
    })
  ];
}).s("AmazonS3", "PutBucketOwnershipControls", {}).n("S3Client", "PutBucketOwnershipControlsCommand").f(void 0, void 0).ser(se_PutBucketOwnershipControlsCommand).de(de_PutBucketOwnershipControlsCommand).build() {
  static {
    __name(this, "PutBucketOwnershipControlsCommand");
  }
};

// src/commands/PutBucketPolicyCommand.ts




var PutBucketPolicyCommand = class extends import_smithy_client.Command.classBuilder().ep({
  ...commonParams,
  UseS3ExpressControlEndpoint: { type: "staticContextParams", value: true },
  Bucket: { type: "contextParams", name: "Bucket" }
}).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions()),
    (0, import_middleware_flexible_checksums.getFlexibleChecksumsPlugin)(config, {
      requestAlgorithmMember: { httpHeader: "x-amz-sdk-checksum-algorithm", name: "ChecksumAlgorithm" },
      requestChecksumRequired: true
    })
  ];
}).s("AmazonS3", "PutBucketPolicy", {}).n("S3Client", "PutBucketPolicyCommand").f(void 0, void 0).ser(se_PutBucketPolicyCommand).de(de_PutBucketPolicyCommand).build() {
  static {
    __name(this, "PutBucketPolicyCommand");
  }
};

// src/commands/PutBucketReplicationCommand.ts




var PutBucketReplicationCommand = class extends import_smithy_client.Command.classBuilder().ep({
  ...commonParams,
  UseS3ExpressControlEndpoint: { type: "staticContextParams", value: true },
  Bucket: { type: "contextParams", name: "Bucket" }
}).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions()),
    (0, import_middleware_flexible_checksums.getFlexibleChecksumsPlugin)(config, {
      requestAlgorithmMember: { httpHeader: "x-amz-sdk-checksum-algorithm", name: "ChecksumAlgorithm" },
      requestChecksumRequired: true
    })
  ];
}).s("AmazonS3", "PutBucketReplication", {}).n("S3Client", "PutBucketReplicationCommand").f(void 0, void 0).ser(se_PutBucketReplicationCommand).de(de_PutBucketReplicationCommand).build() {
  static {
    __name(this, "PutBucketReplicationCommand");
  }
};

// src/commands/PutBucketRequestPaymentCommand.ts




var PutBucketRequestPaymentCommand = class extends import_smithy_client.Command.classBuilder().ep({
  ...commonParams,
  UseS3ExpressControlEndpoint: { type: "staticContextParams", value: true },
  Bucket: { type: "contextParams", name: "Bucket" }
}).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions()),
    (0, import_middleware_flexible_checksums.getFlexibleChecksumsPlugin)(config, {
      requestAlgorithmMember: { httpHeader: "x-amz-sdk-checksum-algorithm", name: "ChecksumAlgorithm" },
      requestChecksumRequired: true
    })
  ];
}).s("AmazonS3", "PutBucketRequestPayment", {}).n("S3Client", "PutBucketRequestPaymentCommand").f(void 0, void 0).ser(se_PutBucketRequestPaymentCommand).de(de_PutBucketRequestPaymentCommand).build() {
  static {
    __name(this, "PutBucketRequestPaymentCommand");
  }
};

// src/commands/PutBucketTaggingCommand.ts




var PutBucketTaggingCommand = class extends import_smithy_client.Command.classBuilder().ep({
  ...commonParams,
  UseS3ExpressControlEndpoint: { type: "staticContextParams", value: true },
  Bucket: { type: "contextParams", name: "Bucket" }
}).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions()),
    (0, import_middleware_flexible_checksums.getFlexibleChecksumsPlugin)(config, {
      requestAlgorithmMember: { httpHeader: "x-amz-sdk-checksum-algorithm", name: "ChecksumAlgorithm" },
      requestChecksumRequired: true
    })
  ];
}).s("AmazonS3", "PutBucketTagging", {}).n("S3Client", "PutBucketTaggingCommand").f(void 0, void 0).ser(se_PutBucketTaggingCommand).de(de_PutBucketTaggingCommand).build() {
  static {
    __name(this, "PutBucketTaggingCommand");
  }
};

// src/commands/PutBucketVersioningCommand.ts




var PutBucketVersioningCommand = class extends import_smithy_client.Command.classBuilder().ep({
  ...commonParams,
  UseS3ExpressControlEndpoint: { type: "staticContextParams", value: true },
  Bucket: { type: "contextParams", name: "Bucket" }
}).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions()),
    (0, import_middleware_flexible_checksums.getFlexibleChecksumsPlugin)(config, {
      requestAlgorithmMember: { httpHeader: "x-amz-sdk-checksum-algorithm", name: "ChecksumAlgorithm" },
      requestChecksumRequired: true
    })
  ];
}).s("AmazonS3", "PutBucketVersioning", {}).n("S3Client", "PutBucketVersioningCommand").f(void 0, void 0).ser(se_PutBucketVersioningCommand).de(de_PutBucketVersioningCommand).build() {
  static {
    __name(this, "PutBucketVersioningCommand");
  }
};

// src/commands/PutBucketWebsiteCommand.ts




var PutBucketWebsiteCommand = class extends import_smithy_client.Command.classBuilder().ep({
  ...commonParams,
  UseS3ExpressControlEndpoint: { type: "staticContextParams", value: true },
  Bucket: { type: "contextParams", name: "Bucket" }
}).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions()),
    (0, import_middleware_flexible_checksums.getFlexibleChecksumsPlugin)(config, {
      requestAlgorithmMember: { httpHeader: "x-amz-sdk-checksum-algorithm", name: "ChecksumAlgorithm" },
      requestChecksumRequired: true
    })
  ];
}).s("AmazonS3", "PutBucketWebsite", {}).n("S3Client", "PutBucketWebsiteCommand").f(void 0, void 0).ser(se_PutBucketWebsiteCommand).de(de_PutBucketWebsiteCommand).build() {
  static {
    __name(this, "PutBucketWebsiteCommand");
  }
};

// src/commands/PutObjectAclCommand.ts

var import_middleware_sdk_s355 = require("@aws-sdk/middleware-sdk-s3");



var PutObjectAclCommand = class extends import_smithy_client.Command.classBuilder().ep({
  ...commonParams,
  Bucket: { type: "contextParams", name: "Bucket" },
  Key: { type: "contextParams", name: "Key" }
}).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions()),
    (0, import_middleware_flexible_checksums.getFlexibleChecksumsPlugin)(config, {
      requestAlgorithmMember: { httpHeader: "x-amz-sdk-checksum-algorithm", name: "ChecksumAlgorithm" },
      requestChecksumRequired: true
    }),
    (0, import_middleware_sdk_s355.getThrow200ExceptionsPlugin)(config)
  ];
}).s("AmazonS3", "PutObjectAcl", {}).n("S3Client", "PutObjectAclCommand").f(void 0, void 0).ser(se_PutObjectAclCommand).de(de_PutObjectAclCommand).build() {
  static {
    __name(this, "PutObjectAclCommand");
  }
};

// src/commands/PutObjectCommand.ts

var import_middleware_sdk_s356 = require("@aws-sdk/middleware-sdk-s3");




var PutObjectCommand = class extends import_smithy_client.Command.classBuilder().ep({
  ...commonParams,
  Bucket: { type: "contextParams", name: "Bucket" },
  Key: { type: "contextParams", name: "Key" }
}).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions()),
    (0, import_middleware_flexible_checksums.getFlexibleChecksumsPlugin)(config, {
      requestAlgorithmMember: { httpHeader: "x-amz-sdk-checksum-algorithm", name: "ChecksumAlgorithm" },
      requestChecksumRequired: false
    }),
    (0, import_middleware_sdk_s356.getCheckContentLengthHeaderPlugin)(config),
    (0, import_middleware_sdk_s356.getThrow200ExceptionsPlugin)(config),
    (0, import_middleware_ssec.getSsecPlugin)(config)
  ];
}).s("AmazonS3", "PutObject", {}).n("S3Client", "PutObjectCommand").f(PutObjectRequestFilterSensitiveLog, PutObjectOutputFilterSensitiveLog).ser(se_PutObjectCommand).de(de_PutObjectCommand).build() {
  static {
    __name(this, "PutObjectCommand");
  }
};

// src/commands/PutObjectLegalHoldCommand.ts

var import_middleware_sdk_s357 = require("@aws-sdk/middleware-sdk-s3");



var PutObjectLegalHoldCommand = class extends import_smithy_client.Command.classBuilder().ep({
  ...commonParams,
  Bucket: { type: "contextParams", name: "Bucket" }
}).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions()),
    (0, import_middleware_flexible_checksums.getFlexibleChecksumsPlugin)(config, {
      requestAlgorithmMember: { httpHeader: "x-amz-sdk-checksum-algorithm", name: "ChecksumAlgorithm" },
      requestChecksumRequired: true
    }),
    (0, import_middleware_sdk_s357.getThrow200ExceptionsPlugin)(config)
  ];
}).s("AmazonS3", "PutObjectLegalHold", {}).n("S3Client", "PutObjectLegalHoldCommand").f(void 0, void 0).ser(se_PutObjectLegalHoldCommand).de(de_PutObjectLegalHoldCommand).build() {
  static {
    __name(this, "PutObjectLegalHoldCommand");
  }
};

// src/commands/PutObjectLockConfigurationCommand.ts

var import_middleware_sdk_s358 = require("@aws-sdk/middleware-sdk-s3");



var PutObjectLockConfigurationCommand = class extends import_smithy_client.Command.classBuilder().ep({
  ...commonParams,
  Bucket: { type: "contextParams", name: "Bucket" }
}).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions()),
    (0, import_middleware_flexible_checksums.getFlexibleChecksumsPlugin)(config, {
      requestAlgorithmMember: { httpHeader: "x-amz-sdk-checksum-algorithm", name: "ChecksumAlgorithm" },
      requestChecksumRequired: true
    }),
    (0, import_middleware_sdk_s358.getThrow200ExceptionsPlugin)(config)
  ];
}).s("AmazonS3", "PutObjectLockConfiguration", {}).n("S3Client", "PutObjectLockConfigurationCommand").f(void 0, void 0).ser(se_PutObjectLockConfigurationCommand).de(de_PutObjectLockConfigurationCommand).build() {
  static {
    __name(this, "PutObjectLockConfigurationCommand");
  }
};

// src/commands/PutObjectRetentionCommand.ts

var import_middleware_sdk_s359 = require("@aws-sdk/middleware-sdk-s3");



var PutObjectRetentionCommand = class extends import_smithy_client.Command.classBuilder().ep({
  ...commonParams,
  Bucket: { type: "contextParams", name: "Bucket" }
}).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions()),
    (0, import_middleware_flexible_checksums.getFlexibleChecksumsPlugin)(config, {
      requestAlgorithmMember: { httpHeader: "x-amz-sdk-checksum-algorithm", name: "ChecksumAlgorithm" },
      requestChecksumRequired: true
    }),
    (0, import_middleware_sdk_s359.getThrow200ExceptionsPlugin)(config)
  ];
}).s("AmazonS3", "PutObjectRetention", {}).n("S3Client", "PutObjectRetentionCommand").f(void 0, void 0).ser(se_PutObjectRetentionCommand).de(de_PutObjectRetentionCommand).build() {
  static {
    __name(this, "PutObjectRetentionCommand");
  }
};

// src/commands/PutObjectTaggingCommand.ts

var import_middleware_sdk_s360 = require("@aws-sdk/middleware-sdk-s3");



var PutObjectTaggingCommand = class extends import_smithy_client.Command.classBuilder().ep({
  ...commonParams,
  Bucket: { type: "contextParams", name: "Bucket" }
}).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions()),
    (0, import_middleware_flexible_checksums.getFlexibleChecksumsPlugin)(config, {
      requestAlgorithmMember: { httpHeader: "x-amz-sdk-checksum-algorithm", name: "ChecksumAlgorithm" },
      requestChecksumRequired: true
    }),
    (0, import_middleware_sdk_s360.getThrow200ExceptionsPlugin)(config)
  ];
}).s("AmazonS3", "PutObjectTagging", {}).n("S3Client", "PutObjectTaggingCommand").f(void 0, void 0).ser(se_PutObjectTaggingCommand).de(de_PutObjectTaggingCommand).build() {
  static {
    __name(this, "PutObjectTaggingCommand");
  }
};

// src/commands/PutPublicAccessBlockCommand.ts




var PutPublicAccessBlockCommand = class extends import_smithy_client.Command.classBuilder().ep({
  ...commonParams,
  UseS3ExpressControlEndpoint: { type: "staticContextParams", value: true },
  Bucket: { type: "contextParams", name: "Bucket" }
}).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions()),
    (0, import_middleware_flexible_checksums.getFlexibleChecksumsPlugin)(config, {
      requestAlgorithmMember: { httpHeader: "x-amz-sdk-checksum-algorithm", name: "ChecksumAlgorithm" },
      requestChecksumRequired: true
    })
  ];
}).s("AmazonS3", "PutPublicAccessBlock", {}).n("S3Client", "PutPublicAccessBlockCommand").f(void 0, void 0).ser(se_PutPublicAccessBlockCommand).de(de_PutPublicAccessBlockCommand).build() {
  static {
    __name(this, "PutPublicAccessBlockCommand");
  }
};

// src/commands/RenameObjectCommand.ts
var import_middleware_sdk_s361 = require("@aws-sdk/middleware-sdk-s3");



var RenameObjectCommand = class extends import_smithy_client.Command.classBuilder().ep({
  ...commonParams,
  Bucket: { type: "contextParams", name: "Bucket" },
  Key: { type: "contextParams", name: "Key" }
}).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions()),
    (0, import_middleware_sdk_s361.getThrow200ExceptionsPlugin)(config)
  ];
}).s("AmazonS3", "RenameObject", {}).n("S3Client", "RenameObjectCommand").f(void 0, void 0).ser(se_RenameObjectCommand).de(de_RenameObjectCommand).build() {
  static {
    __name(this, "RenameObjectCommand");
  }
};

// src/commands/RestoreObjectCommand.ts

var import_middleware_sdk_s362 = require("@aws-sdk/middleware-sdk-s3");



var RestoreObjectCommand = class extends import_smithy_client.Command.classBuilder().ep({
  ...commonParams,
  Bucket: { type: "contextParams", name: "Bucket" }
}).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions()),
    (0, import_middleware_flexible_checksums.getFlexibleChecksumsPlugin)(config, {
      requestAlgorithmMember: { httpHeader: "x-amz-sdk-checksum-algorithm", name: "ChecksumAlgorithm" },
      requestChecksumRequired: false
    }),
    (0, import_middleware_sdk_s362.getThrow200ExceptionsPlugin)(config)
  ];
}).s("AmazonS3", "RestoreObject", {}).n("S3Client", "RestoreObjectCommand").f(RestoreObjectRequestFilterSensitiveLog, void 0).ser(se_RestoreObjectCommand).de(de_RestoreObjectCommand).build() {
  static {
    __name(this, "RestoreObjectCommand");
  }
};

// src/commands/SelectObjectContentCommand.ts
var import_middleware_sdk_s363 = require("@aws-sdk/middleware-sdk-s3");




var SelectObjectContentCommand = class extends import_smithy_client.Command.classBuilder().ep({
  ...commonParams,
  Bucket: { type: "contextParams", name: "Bucket" }
}).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions()),
    (0, import_middleware_sdk_s363.getThrow200ExceptionsPlugin)(config),
    (0, import_middleware_ssec.getSsecPlugin)(config)
  ];
}).s("AmazonS3", "SelectObjectContent", {
  /**
   * @internal
   */
  eventStream: {
    output: true
  }
}).n("S3Client", "SelectObjectContentCommand").f(SelectObjectContentRequestFilterSensitiveLog, SelectObjectContentOutputFilterSensitiveLog).ser(se_SelectObjectContentCommand).de(de_SelectObjectContentCommand).build() {
  static {
    __name(this, "SelectObjectContentCommand");
  }
};

// src/commands/UpdateBucketMetadataInventoryTableConfigurationCommand.ts




var UpdateBucketMetadataInventoryTableConfigurationCommand = class extends import_smithy_client.Command.classBuilder().ep({
  ...commonParams,
  UseS3ExpressControlEndpoint: { type: "staticContextParams", value: true },
  Bucket: { type: "contextParams", name: "Bucket" }
}).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions()),
    (0, import_middleware_flexible_checksums.getFlexibleChecksumsPlugin)(config, {
      requestAlgorithmMember: { httpHeader: "x-amz-sdk-checksum-algorithm", name: "ChecksumAlgorithm" },
      requestChecksumRequired: true
    })
  ];
}).s("AmazonS3", "UpdateBucketMetadataInventoryTableConfiguration", {}).n("S3Client", "UpdateBucketMetadataInventoryTableConfigurationCommand").f(void 0, void 0).ser(se_UpdateBucketMetadataInventoryTableConfigurationCommand).de(de_UpdateBucketMetadataInventoryTableConfigurationCommand).build() {
  static {
    __name(this, "UpdateBucketMetadataInventoryTableConfigurationCommand");
  }
};

// src/commands/UpdateBucketMetadataJournalTableConfigurationCommand.ts




var UpdateBucketMetadataJournalTableConfigurationCommand = class extends import_smithy_client.Command.classBuilder().ep({
  ...commonParams,
  UseS3ExpressControlEndpoint: { type: "staticContextParams", value: true },
  Bucket: { type: "contextParams", name: "Bucket" }
}).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions()),
    (0, import_middleware_flexible_checksums.getFlexibleChecksumsPlugin)(config, {
      requestAlgorithmMember: { httpHeader: "x-amz-sdk-checksum-algorithm", name: "ChecksumAlgorithm" },
      requestChecksumRequired: true
    })
  ];
}).s("AmazonS3", "UpdateBucketMetadataJournalTableConfiguration", {}).n("S3Client", "UpdateBucketMetadataJournalTableConfigurationCommand").f(void 0, void 0).ser(se_UpdateBucketMetadataJournalTableConfigurationCommand).de(de_UpdateBucketMetadataJournalTableConfigurationCommand).build() {
  static {
    __name(this, "UpdateBucketMetadataJournalTableConfigurationCommand");
  }
};

// src/commands/UploadPartCommand.ts

var import_middleware_sdk_s364 = require("@aws-sdk/middleware-sdk-s3");




var UploadPartCommand = class extends import_smithy_client.Command.classBuilder().ep({
  ...commonParams,
  Bucket: { type: "contextParams", name: "Bucket" },
  Key: { type: "contextParams", name: "Key" }
}).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions()),
    (0, import_middleware_flexible_checksums.getFlexibleChecksumsPlugin)(config, {
      requestAlgorithmMember: { httpHeader: "x-amz-sdk-checksum-algorithm", name: "ChecksumAlgorithm" },
      requestChecksumRequired: false
    }),
    (0, import_middleware_sdk_s364.getThrow200ExceptionsPlugin)(config),
    (0, import_middleware_ssec.getSsecPlugin)(config)
  ];
}).s("AmazonS3", "UploadPart", {}).n("S3Client", "UploadPartCommand").f(UploadPartRequestFilterSensitiveLog, UploadPartOutputFilterSensitiveLog).ser(se_UploadPartCommand).de(de_UploadPartCommand).build() {
  static {
    __name(this, "UploadPartCommand");
  }
};

// src/commands/UploadPartCopyCommand.ts
var import_middleware_sdk_s365 = require("@aws-sdk/middleware-sdk-s3");




var UploadPartCopyCommand = class extends import_smithy_client.Command.classBuilder().ep({
  ...commonParams,
  DisableS3ExpressSessionAuth: { type: "staticContextParams", value: true },
  Bucket: { type: "contextParams", name: "Bucket" }
}).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions()),
    (0, import_middleware_sdk_s365.getThrow200ExceptionsPlugin)(config),
    (0, import_middleware_ssec.getSsecPlugin)(config)
  ];
}).s("AmazonS3", "UploadPartCopy", {}).n("S3Client", "UploadPartCopyCommand").f(UploadPartCopyRequestFilterSensitiveLog, UploadPartCopyOutputFilterSensitiveLog).ser(se_UploadPartCopyCommand).de(de_UploadPartCopyCommand).build() {
  static {
    __name(this, "UploadPartCopyCommand");
  }
};

// src/commands/WriteGetObjectResponseCommand.ts



var WriteGetObjectResponseCommand = class extends import_smithy_client.Command.classBuilder().ep({
  ...commonParams,
  UseObjectLambdaEndpoint: { type: "staticContextParams", value: true }
}).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("AmazonS3", "WriteGetObjectResponse", {}).n("S3Client", "WriteGetObjectResponseCommand").f(WriteGetObjectResponseRequestFilterSensitiveLog, void 0).ser(se_WriteGetObjectResponseCommand).de(de_WriteGetObjectResponseCommand).build() {
  static {
    __name(this, "WriteGetObjectResponseCommand");
  }
};

// src/S3.ts
var commands = {
  AbortMultipartUploadCommand,
  CompleteMultipartUploadCommand,
  CopyObjectCommand,
  CreateBucketCommand,
  CreateBucketMetadataConfigurationCommand,
  CreateBucketMetadataTableConfigurationCommand,
  CreateMultipartUploadCommand,
  CreateSessionCommand,
  DeleteBucketCommand,
  DeleteBucketAnalyticsConfigurationCommand,
  DeleteBucketCorsCommand,
  DeleteBucketEncryptionCommand,
  DeleteBucketIntelligentTieringConfigurationCommand,
  DeleteBucketInventoryConfigurationCommand,
  DeleteBucketLifecycleCommand,
  DeleteBucketMetadataConfigurationCommand,
  DeleteBucketMetadataTableConfigurationCommand,
  DeleteBucketMetricsConfigurationCommand,
  DeleteBucketOwnershipControlsCommand,
  DeleteBucketPolicyCommand,
  DeleteBucketReplicationCommand,
  DeleteBucketTaggingCommand,
  DeleteBucketWebsiteCommand,
  DeleteObjectCommand,
  DeleteObjectsCommand,
  DeleteObjectTaggingCommand,
  DeletePublicAccessBlockCommand,
  GetBucketAccelerateConfigurationCommand,
  GetBucketAclCommand,
  GetBucketAnalyticsConfigurationCommand,
  GetBucketCorsCommand,
  GetBucketEncryptionCommand,
  GetBucketIntelligentTieringConfigurationCommand,
  GetBucketInventoryConfigurationCommand,
  GetBucketLifecycleConfigurationCommand,
  GetBucketLocationCommand,
  GetBucketLoggingCommand,
  GetBucketMetadataConfigurationCommand,
  GetBucketMetadataTableConfigurationCommand,
  GetBucketMetricsConfigurationCommand,
  GetBucketNotificationConfigurationCommand,
  GetBucketOwnershipControlsCommand,
  GetBucketPolicyCommand,
  GetBucketPolicyStatusCommand,
  GetBucketReplicationCommand,
  GetBucketRequestPaymentCommand,
  GetBucketTaggingCommand,
  GetBucketVersioningCommand,
  GetBucketWebsiteCommand,
  GetObjectCommand,
  GetObjectAclCommand,
  GetObjectAttributesCommand,
  GetObjectLegalHoldCommand,
  GetObjectLockConfigurationCommand,
  GetObjectRetentionCommand,
  GetObjectTaggingCommand,
  GetObjectTorrentCommand,
  GetPublicAccessBlockCommand,
  HeadBucketCommand,
  HeadObjectCommand,
  ListBucketAnalyticsConfigurationsCommand,
  ListBucketIntelligentTieringConfigurationsCommand,
  ListBucketInventoryConfigurationsCommand,
  ListBucketMetricsConfigurationsCommand,
  ListBucketsCommand,
  ListDirectoryBucketsCommand,
  ListMultipartUploadsCommand,
  ListObjectsCommand,
  ListObjectsV2Command,
  ListObjectVersionsCommand,
  ListPartsCommand,
  PutBucketAccelerateConfigurationCommand,
  PutBucketAclCommand,
  PutBucketAnalyticsConfigurationCommand,
  PutBucketCorsCommand,
  PutBucketEncryptionCommand,
  PutBucketIntelligentTieringConfigurationCommand,
  PutBucketInventoryConfigurationCommand,
  PutBucketLifecycleConfigurationCommand,
  PutBucketLoggingCommand,
  PutBucketMetricsConfigurationCommand,
  PutBucketNotificationConfigurationCommand,
  PutBucketOwnershipControlsCommand,
  PutBucketPolicyCommand,
  PutBucketReplicationCommand,
  PutBucketRequestPaymentCommand,
  PutBucketTaggingCommand,
  PutBucketVersioningCommand,
  PutBucketWebsiteCommand,
  PutObjectCommand,
  PutObjectAclCommand,
  PutObjectLegalHoldCommand,
  PutObjectLockConfigurationCommand,
  PutObjectRetentionCommand,
  PutObjectTaggingCommand,
  PutPublicAccessBlockCommand,
  RenameObjectCommand,
  RestoreObjectCommand,
  SelectObjectContentCommand,
  UpdateBucketMetadataInventoryTableConfigurationCommand,
  UpdateBucketMetadataJournalTableConfigurationCommand,
  UploadPartCommand,
  UploadPartCopyCommand,
  WriteGetObjectResponseCommand
};
var S3 = class extends S3Client {
  static {
    __name(this, "S3");
  }
};
(0, import_smithy_client.createAggregatedClient)(commands, S3);

// src/pagination/ListBucketsPaginator.ts
var import_core4 = require("@smithy/core");
var paginateListBuckets = (0, import_core4.createPaginator)(S3Client, ListBucketsCommand, "ContinuationToken", "ContinuationToken", "MaxBuckets");

// src/pagination/ListDirectoryBucketsPaginator.ts
var import_core5 = require("@smithy/core");
var paginateListDirectoryBuckets = (0, import_core5.createPaginator)(S3Client, ListDirectoryBucketsCommand, "ContinuationToken", "ContinuationToken", "MaxDirectoryBuckets");

// src/pagination/ListObjectsV2Paginator.ts
var import_core6 = require("@smithy/core");
var paginateListObjectsV2 = (0, import_core6.createPaginator)(S3Client, ListObjectsV2Command, "ContinuationToken", "NextContinuationToken", "MaxKeys");

// src/pagination/ListPartsPaginator.ts
var import_core7 = require("@smithy/core");
var paginateListParts = (0, import_core7.createPaginator)(S3Client, ListPartsCommand, "PartNumberMarker", "NextPartNumberMarker", "MaxParts");

// src/waiters/waitForBucketExists.ts
var import_util_waiter = require("@smithy/util-waiter");
var checkState = /* @__PURE__ */ __name(async (client, input) => {
  let reason;
  try {
    const result = await client.send(new HeadBucketCommand(input));
    reason = result;
    return { state: import_util_waiter.WaiterState.SUCCESS, reason };
  } catch (exception) {
    reason = exception;
    if (exception.name && exception.name == "NotFound") {
      return { state: import_util_waiter.WaiterState.RETRY, reason };
    }
  }
  return { state: import_util_waiter.WaiterState.RETRY, reason };
}, "checkState");
var waitForBucketExists = /* @__PURE__ */ __name(async (params, input) => {
  const serviceDefaults = { minDelay: 5, maxDelay: 120 };
  return (0, import_util_waiter.createWaiter)({ ...serviceDefaults, ...params }, input, checkState);
}, "waitForBucketExists");
var waitUntilBucketExists = /* @__PURE__ */ __name(async (params, input) => {
  const serviceDefaults = { minDelay: 5, maxDelay: 120 };
  const result = await (0, import_util_waiter.createWaiter)({ ...serviceDefaults, ...params }, input, checkState);
  return (0, import_util_waiter.checkExceptions)(result);
}, "waitUntilBucketExists");

// src/waiters/waitForBucketNotExists.ts

var checkState2 = /* @__PURE__ */ __name(async (client, input) => {
  let reason;
  try {
    const result = await client.send(new HeadBucketCommand(input));
    reason = result;
  } catch (exception) {
    reason = exception;
    if (exception.name && exception.name == "NotFound") {
      return { state: import_util_waiter.WaiterState.SUCCESS, reason };
    }
  }
  return { state: import_util_waiter.WaiterState.RETRY, reason };
}, "checkState");
var waitForBucketNotExists = /* @__PURE__ */ __name(async (params, input) => {
  const serviceDefaults = { minDelay: 5, maxDelay: 120 };
  return (0, import_util_waiter.createWaiter)({ ...serviceDefaults, ...params }, input, checkState2);
}, "waitForBucketNotExists");
var waitUntilBucketNotExists = /* @__PURE__ */ __name(async (params, input) => {
  const serviceDefaults = { minDelay: 5, maxDelay: 120 };
  const result = await (0, import_util_waiter.createWaiter)({ ...serviceDefaults, ...params }, input, checkState2);
  return (0, import_util_waiter.checkExceptions)(result);
}, "waitUntilBucketNotExists");

// src/waiters/waitForObjectExists.ts

var checkState3 = /* @__PURE__ */ __name(async (client, input) => {
  let reason;
  try {
    const result = await client.send(new HeadObjectCommand(input));
    reason = result;
    return { state: import_util_waiter.WaiterState.SUCCESS, reason };
  } catch (exception) {
    reason = exception;
    if (exception.name && exception.name == "NotFound") {
      return { state: import_util_waiter.WaiterState.RETRY, reason };
    }
  }
  return { state: import_util_waiter.WaiterState.RETRY, reason };
}, "checkState");
var waitForObjectExists = /* @__PURE__ */ __name(async (params, input) => {
  const serviceDefaults = { minDelay: 5, maxDelay: 120 };
  return (0, import_util_waiter.createWaiter)({ ...serviceDefaults, ...params }, input, checkState3);
}, "waitForObjectExists");
var waitUntilObjectExists = /* @__PURE__ */ __name(async (params, input) => {
  const serviceDefaults = { minDelay: 5, maxDelay: 120 };
  const result = await (0, import_util_waiter.createWaiter)({ ...serviceDefaults, ...params }, input, checkState3);
  return (0, import_util_waiter.checkExceptions)(result);
}, "waitUntilObjectExists");

// src/waiters/waitForObjectNotExists.ts

var checkState4 = /* @__PURE__ */ __name(async (client, input) => {
  let reason;
  try {
    const result = await client.send(new HeadObjectCommand(input));
    reason = result;
  } catch (exception) {
    reason = exception;
    if (exception.name && exception.name == "NotFound") {
      return { state: import_util_waiter.WaiterState.SUCCESS, reason };
    }
  }
  return { state: import_util_waiter.WaiterState.RETRY, reason };
}, "checkState");
var waitForObjectNotExists = /* @__PURE__ */ __name(async (params, input) => {
  const serviceDefaults = { minDelay: 5, maxDelay: 120 };
  return (0, import_util_waiter.createWaiter)({ ...serviceDefaults, ...params }, input, checkState4);
}, "waitForObjectNotExists");
var waitUntilObjectNotExists = /* @__PURE__ */ __name(async (params, input) => {
  const serviceDefaults = { minDelay: 5, maxDelay: 120 };
  const result = await (0, import_util_waiter.createWaiter)({ ...serviceDefaults, ...params }, input, checkState4);
  return (0, import_util_waiter.checkExceptions)(result);
}, "waitUntilObjectNotExists");
// Annotate the CommonJS export names for ESM import in node:

0 && (module.exports = {
  S3ServiceException,
  __Client,
  S3Client,
  S3,
  $Command,
  AbortMultipartUploadCommand,
  CompleteMultipartUploadCommand,
  CopyObjectCommand,
  CreateBucketCommand,
  CreateBucketMetadataConfigurationCommand,
  CreateBucketMetadataTableConfigurationCommand,
  CreateMultipartUploadCommand,
  CreateSessionCommand,
  DeleteBucketAnalyticsConfigurationCommand,
  DeleteBucketCommand,
  DeleteBucketCorsCommand,
  DeleteBucketEncryptionCommand,
  DeleteBucketIntelligentTieringConfigurationCommand,
  DeleteBucketInventoryConfigurationCommand,
  DeleteBucketLifecycleCommand,
  DeleteBucketMetadataConfigurationCommand,
  DeleteBucketMetadataTableConfigurationCommand,
  DeleteBucketMetricsConfigurationCommand,
  DeleteBucketOwnershipControlsCommand,
  DeleteBucketPolicyCommand,
  DeleteBucketReplicationCommand,
  DeleteBucketTaggingCommand,
  DeleteBucketWebsiteCommand,
  DeleteObjectCommand,
  DeleteObjectTaggingCommand,
  DeleteObjectsCommand,
  DeletePublicAccessBlockCommand,
  GetBucketAccelerateConfigurationCommand,
  GetBucketAclCommand,
  GetBucketAnalyticsConfigurationCommand,
  GetBucketCorsCommand,
  GetBucketEncryptionCommand,
  GetBucketIntelligentTieringConfigurationCommand,
  GetBucketInventoryConfigurationCommand,
  GetBucketLifecycleConfigurationCommand,
  GetBucketLocationCommand,
  GetBucketLoggingCommand,
  GetBucketMetadataConfigurationCommand,
  GetBucketMetadataTableConfigurationCommand,
  GetBucketMetricsConfigurationCommand,
  GetBucketNotificationConfigurationCommand,
  GetBucketOwnershipControlsCommand,
  GetBucketPolicyCommand,
  GetBucketPolicyStatusCommand,
  GetBucketReplicationCommand,
  GetBucketRequestPaymentCommand,
  GetBucketTaggingCommand,
  GetBucketVersioningCommand,
  GetBucketWebsiteCommand,
  GetObjectAclCommand,
  GetObjectAttributesCommand,
  GetObjectCommand,
  GetObjectLegalHoldCommand,
  GetObjectLockConfigurationCommand,
  GetObjectRetentionCommand,
  GetObjectTaggingCommand,
  GetObjectTorrentCommand,
  GetPublicAccessBlockCommand,
  HeadBucketCommand,
  HeadObjectCommand,
  ListBucketAnalyticsConfigurationsCommand,
  ListBucketIntelligentTieringConfigurationsCommand,
  ListBucketInventoryConfigurationsCommand,
  ListBucketMetricsConfigurationsCommand,
  ListBucketsCommand,
  ListDirectoryBucketsCommand,
  ListMultipartUploadsCommand,
  ListObjectVersionsCommand,
  ListObjectsCommand,
  ListObjectsV2Command,
  ListPartsCommand,
  PutBucketAccelerateConfigurationCommand,
  PutBucketAclCommand,
  PutBucketAnalyticsConfigurationCommand,
  PutBucketCorsCommand,
  PutBucketEncryptionCommand,
  PutBucketIntelligentTieringConfigurationCommand,
  PutBucketInventoryConfigurationCommand,
  PutBucketLifecycleConfigurationCommand,
  PutBucketLoggingCommand,
  PutBucketMetricsConfigurationCommand,
  PutBucketNotificationConfigurationCommand,
  PutBucketOwnershipControlsCommand,
  PutBucketPolicyCommand,
  PutBucketReplicationCommand,
  PutBucketRequestPaymentCommand,
  PutBucketTaggingCommand,
  PutBucketVersioningCommand,
  PutBucketWebsiteCommand,
  PutObjectAclCommand,
  PutObjectCommand,
  PutObjectLegalHoldCommand,
  PutObjectLockConfigurationCommand,
  PutObjectRetentionCommand,
  PutObjectTaggingCommand,
  PutPublicAccessBlockCommand,
  RenameObjectCommand,
  RestoreObjectCommand,
  SelectObjectContentCommand,
  UpdateBucketMetadataInventoryTableConfigurationCommand,
  UpdateBucketMetadataJournalTableConfigurationCommand,
  UploadPartCommand,
  UploadPartCopyCommand,
  WriteGetObjectResponseCommand,
  paginateListBuckets,
  paginateListDirectoryBuckets,
  paginateListObjectsV2,
  paginateListParts,
  waitForBucketExists,
  waitUntilBucketExists,
  waitForBucketNotExists,
  waitUntilBucketNotExists,
  waitForObjectExists,
  waitUntilObjectExists,
  waitForObjectNotExists,
  waitUntilObjectNotExists,
  RequestCharged,
  RequestPayer,
  NoSuchUpload,
  BucketAccelerateStatus,
  Type,
  Permission,
  OwnerOverride,
  ChecksumType,
  ServerSideEncryption,
  ObjectCannedACL,
  ChecksumAlgorithm,
  MetadataDirective,
  ObjectLockLegalHoldStatus,
  ObjectLockMode,
  StorageClass,
  TaggingDirective,
  ObjectNotInActiveTierError,
  BucketAlreadyExists,
  BucketAlreadyOwnedByYou,
  BucketCannedACL,
  DataRedundancy,
  BucketType,
  LocationType,
  BucketLocationConstraint,
  ObjectOwnership,
  InventoryConfigurationState,
  TableSseAlgorithm,
  ExpirationState,
  SessionMode,
  NoSuchBucket,
  AnalyticsFilter,
  AnalyticsS3ExportFileFormat,
  StorageClassAnalysisSchemaVersion,
  IntelligentTieringStatus,
  IntelligentTieringAccessTier,
  InventoryFormat,
  InventoryIncludedObjectVersions,
  InventoryOptionalField,
  InventoryFrequency,
  TransitionStorageClass,
  ExpirationStatus,
  TransitionDefaultMinimumObjectSize,
  BucketLogsPermission,
  PartitionDateSource,
  S3TablesBucketType,
  MetricsFilter,
  Event,
  FilterRuleName,
  DeleteMarkerReplicationStatus,
  MetricsStatus,
  ReplicationTimeStatus,
  ExistingObjectReplicationStatus,
  ReplicaModificationsStatus,
  SseKmsEncryptedObjectsStatus,
  ReplicationRuleStatus,
  Payer,
  MFADeleteStatus,
  BucketVersioningStatus,
  Protocol,
  ReplicationStatus,
  ChecksumMode,
  InvalidObjectState,
  NoSuchKey,
  ObjectAttributes,
  ObjectLockEnabled,
  ObjectLockRetentionMode,
  NotFound,
  ArchiveStatus,
  EncodingType,
  CompleteMultipartUploadOutputFilterSensitiveLog,
  CompleteMultipartUploadRequestFilterSensitiveLog,
  CopyObjectOutputFilterSensitiveLog,
  CopyObjectRequestFilterSensitiveLog,
  CreateMultipartUploadOutputFilterSensitiveLog,
  CreateMultipartUploadRequestFilterSensitiveLog,
  SessionCredentialsFilterSensitiveLog,
  CreateSessionOutputFilterSensitiveLog,
  CreateSessionRequestFilterSensitiveLog,
  ServerSideEncryptionByDefaultFilterSensitiveLog,
  ServerSideEncryptionRuleFilterSensitiveLog,
  ServerSideEncryptionConfigurationFilterSensitiveLog,
  GetBucketEncryptionOutputFilterSensitiveLog,
  SSEKMSFilterSensitiveLog,
  InventoryEncryptionFilterSensitiveLog,
  InventoryS3BucketDestinationFilterSensitiveLog,
  InventoryDestinationFilterSensitiveLog,
  InventoryConfigurationFilterSensitiveLog,
  GetBucketInventoryConfigurationOutputFilterSensitiveLog,
  GetObjectOutputFilterSensitiveLog,
  GetObjectRequestFilterSensitiveLog,
  GetObjectAttributesRequestFilterSensitiveLog,
  GetObjectTorrentOutputFilterSensitiveLog,
  HeadObjectOutputFilterSensitiveLog,
  HeadObjectRequestFilterSensitiveLog,
  ListBucketInventoryConfigurationsOutputFilterSensitiveLog,
  ObjectStorageClass,
  OptionalObjectAttributes,
  ObjectVersionStorageClass,
  MFADelete,
  EncryptionTypeMismatch,
  InvalidRequest,
  InvalidWriteOffset,
  TooManyParts,
  IdempotencyParameterMismatch,
  ObjectAlreadyInActiveTierError,
  Tier,
  ExpressionType,
  CompressionType,
  FileHeaderInfo,
  JSONType,
  QuoteFields,
  RestoreRequestType,
  SelectObjectContentEventStream,
  ListPartsRequestFilterSensitiveLog,
  PutBucketEncryptionRequestFilterSensitiveLog,
  PutBucketInventoryConfigurationRequestFilterSensitiveLog,
  PutObjectOutputFilterSensitiveLog,
  PutObjectRequestFilterSensitiveLog,
  EncryptionFilterSensitiveLog,
  S3LocationFilterSensitiveLog,
  OutputLocationFilterSensitiveLog,
  RestoreRequestFilterSensitiveLog,
  RestoreObjectRequestFilterSensitiveLog,
  SelectObjectContentEventStreamFilterSensitiveLog,
  SelectObjectContentOutputFilterSensitiveLog,
  SelectObjectContentRequestFilterSensitiveLog,
  UploadPartOutputFilterSensitiveLog,
  UploadPartRequestFilterSensitiveLog,
  UploadPartCopyOutputFilterSensitiveLog,
  UploadPartCopyRequestFilterSensitiveLog,
  WriteGetObjectResponseRequestFilterSensitiveLog
});

