{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Akshay\\\\LOOP_PROJECTS\\\\shade_cms\\\\dashboard\\\\src\\\\features\\\\Resources\\\\components\\\\AllForOneManager.jsx\",\n  _s = $RefreshSig$();\n// import for content manager\nimport HomeManager from \"./contentmanager/HomeManager\";\nimport SolutionManager from \"./contentmanager/SolutionManager\";\nimport AboutManager from \"./contentmanager/AboutManager\";\nimport MarketManager from \"./contentmanager/MarketManager\";\nimport ProjectContentManager from \"./contentmanager/ProjectContentManager\";\nimport CareersManager from \"./contentmanager/CareersManager\";\nimport NewsManager from \"./contentmanager/NewsManager\";\nimport FooterManager from \"./contentmanager/CMforSubParts/FooterManager\";\nimport HeaderManager from \"./contentmanager/CMforSubParts/HeaderManager\";\nimport ProjectDetailManager from \"./contentmanager/CMforDetails/ProjectDetailManager\";\nimport CareerDetailManager from \"./contentmanager/CMforDetails/CareerDetailManager\";\nimport NewsDetailManager from \"./contentmanager/CMforDetails/NewsDetailsManager\";\nimport TestimonyManager from \"./contentmanager/CMforSubParts/TestimonyManager\";\nimport ServiceManager from \"./contentmanager/ServiceManager\";\nimport ServiceDetailsManager from \"./contentmanager/CMforDetails/ServiceDetailsManager\";\nimport SubServiceDetailManager from \"./contentmanager/subDetailsManagement/SubServiceDetailManagement\";\nimport { useEffect } from \"react\";\nimport { useDispatch } from \"react-redux\";\nimport { updateMainContent } from \"../../common/homeContentSlice\";\nimport SnRManager from \"./contentmanager/SnRManager\";\nimport SnRPoliciesManager from \"./contentmanager/SnRPolicyManager\";\nimport HistoryManager from \"./contentmanager/HistoryManager\";\nimport VisionManager from \"./contentmanager/VisionManager\";\nimport HSnEManager from \"./contentmanager/HSnEManager\";\nimport MarketDetailsManager from \"./contentmanager/CMforDetails/MarketDetailsManager\";\nimport AffiliatesManager from \"./contentmanager/AffiliatesManager\";\nimport OrganizationManager from \"./contentmanager/OrganizationManager\";\nimport TemplateOneManager from \"./contentmanager/TemplateOneManager\";\nimport TemplateTwoManager from \"./contentmanager/TemplateTwoManager\";\nimport TemplateThreeManager from \"./contentmanager/TemplateThreeManager\";\nimport TemplateFourManager from \"./contentmanager/TemplateFourManager\";\nimport ContactUsManager from \"./contentmanager/ContactUsManager\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AllForOneManager = ({\n  currentPath,\n  language,\n  subPath,\n  deepPath,\n  content,\n  contentIndex,\n  outOfEditing\n}) => {\n  _s();\n  const dispatch = useDispatch();\n  let manager = null;\n  switch (currentPath) {\n    case \"home\":\n      manager = /*#__PURE__*/_jsxDEV(HomeManager, {\n        outOfEditing: outOfEditing,\n        language: language,\n        content: content,\n        indexes: contentIndex,\n        currentPath: currentPath\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 43,\n        columnNumber: 23\n      }, this);\n      break;\n    case \"solution\":\n      manager = /*#__PURE__*/_jsxDEV(SolutionManager, {\n        outOfEditing: outOfEditing,\n        language: language,\n        currentContent: content,\n        currentPath: currentPath,\n        indexes: contentIndex\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 47,\n        columnNumber: 23\n      }, this);\n      break;\n    case \"about-us\":\n      manager = /*#__PURE__*/_jsxDEV(AboutManager, {\n        outOfEditing: outOfEditing,\n        language: language,\n        content: content,\n        indexes: contentIndex,\n        currentPath: currentPath\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 51,\n        columnNumber: 23\n      }, this);\n      break;\n    case \"service\":\n      manager = subPath ? deepPath ? /*#__PURE__*/_jsxDEV(SubServiceDetailManager, {\n        outOfEditing: outOfEditing,\n        serviceId: subPath,\n        deepPath: deepPath,\n        language: language,\n        content: content,\n        indexes: contentIndex,\n        currentPath: \"subOfsubService\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 56,\n        columnNumber: 17\n      }, this) : /*#__PURE__*/_jsxDEV(ServiceDetailsManager, {\n        outOfEditing: outOfEditing,\n        serviceId: subPath,\n        language: language,\n        content: content,\n        indexes: contentIndex,\n        currentPath: \"serviceDetails\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 57,\n        columnNumber: 17\n      }, this) : /*#__PURE__*/_jsxDEV(ServiceManager, {\n        outOfEditing: outOfEditing,\n        language: language,\n        currentContent: content,\n        currentPath: currentPath,\n        indexes: contentIndex\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 58,\n        columnNumber: 17\n      }, this);\n      break;\n    case \"service\":\n      manager = subPath && /*#__PURE__*/_jsxDEV(ServiceDetailsManager, {\n        outOfEditing: outOfEditing,\n        serviceId: subPath,\n        language: language,\n        currentContent: content.serviceDetails,\n        indexes: contentIndex,\n        currentPath: \"serviceDetails\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 63,\n        columnNumber: 17\n      }, this);\n      break;\n    case \"market\":\n      manager = subPath ? /*#__PURE__*/_jsxDEV(MarketDetailsManager, {\n        outOfEditing: outOfEditing,\n        language: language,\n        indexes: contentIndex,\n        content: content,\n        currentPath: currentPath\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 68,\n        columnNumber: 17\n      }, this) : /*#__PURE__*/_jsxDEV(MarketManager, {\n        outOfEditing: outOfEditing,\n        language: language,\n        currentContent: content,\n        indexes: contentIndex,\n        currentPath: currentPath\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 69,\n        columnNumber: 19\n      }, this);\n      break;\n    case \"project\":\n      manager = subPath ? /*#__PURE__*/_jsxDEV(ProjectDetailManager, {\n        outOfEditing: outOfEditing,\n        projectId: subPath,\n        language: language,\n        indexes: contentIndex,\n        currentContent: content,\n        currentPath: \"projectDetail\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 75,\n        columnNumber: 21\n      }, this) : /*#__PURE__*/_jsxDEV(ProjectContentManager, {\n        outOfEditing: outOfEditing,\n        language: language,\n        currentContent: content,\n        indexes: contentIndex,\n        currentPath: currentPath\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 76,\n        columnNumber: 21\n      }, this);\n      break;\n    case \"careers\":\n      manager = /*#__PURE__*/_jsxDEV(NewsManager, {\n        outOfEditing: outOfEditing,\n        language: language,\n        content: content,\n        indexes: contentIndex,\n        currentPath: \"newsBlogs\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 80,\n        columnNumber: 23\n      }, this);\n      break;\n    case \"career\":\n      manager = /*#__PURE__*/_jsxDEV(CareerDetailManager, {\n        outOfEditing: outOfEditing,\n        careerId: subPath,\n        language: language,\n        indexes: contentIndex,\n        currentContent: content,\n        currentPath: \"careerDetails\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 83,\n        columnNumber: 23\n      }, this);\n      break;\n    case \"news-blogs\":\n      manager = /*#__PURE__*/_jsxDEV(NewsManager, {\n        outOfEditing: outOfEditing,\n        language: language,\n        content: content,\n        indexes: contentIndex,\n        currentPath: \"newsBlogs\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 87,\n        columnNumber: 23\n      }, this);\n      break;\n    case \"news\":\n      manager = /*#__PURE__*/_jsxDEV(NewsDetailManager, {\n        outOfEditing: outOfEditing,\n        newsId: subPath,\n        language: language,\n        indexes: contentIndex,\n        content: content,\n        currentPath: \"newsBlogsDetails\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 91,\n        columnNumber: 23\n      }, this);\n      break;\n    case \"footer\":\n      manager = /*#__PURE__*/_jsxDEV(FooterManager, {\n        outOfEditing: outOfEditing,\n        language: language,\n        currentContent: content,\n        indexes: contentIndex,\n        currentPath: currentPath\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 95,\n        columnNumber: 23\n      }, this);\n      break;\n    case \"header\":\n      manager = /*#__PURE__*/_jsxDEV(HeaderManager, {\n        outOfEditing: outOfEditing,\n        language: language,\n        currentContent: content,\n        indexes: contentIndex,\n        currentPath: currentPath\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 99,\n        columnNumber: 23\n      }, this);\n      break;\n    case \"testimonial\":\n      manager = /*#__PURE__*/_jsxDEV(TestimonyManager, {\n        outOfEditing: outOfEditing,\n        language: language,\n        currentContent: content,\n        indexes: contentIndex,\n        testimonyId: subPath,\n        currentPath: \"testimonialSection\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 103,\n        columnNumber: 23\n      }, this);\n      break;\n    case \"safety\":\n    case \"safety_responsibility\":\n      manager = subPath ? /*#__PURE__*/_jsxDEV(SnRPoliciesManager, {\n        outOfEditing: outOfEditing,\n        language: language,\n        content: content,\n        indexes: contentIndex,\n        currentPath: currentPath\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 109,\n        columnNumber: 17\n      }, this) : /*#__PURE__*/_jsxDEV(SnRManager, {\n        outOfEditing: outOfEditing,\n        language: language,\n        content: content,\n        indexes: contentIndex,\n        currentPath: currentPath\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 110,\n        columnNumber: 17\n      }, this);\n      break;\n    case \"history\":\n      manager = /*#__PURE__*/_jsxDEV(HistoryManager, {\n        outOfEditing: outOfEditing,\n        language: language,\n        content: content,\n        indexes: contentIndex,\n        currentPath: currentPath\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 114,\n        columnNumber: 23\n      }, this);\n      break;\n    case \"vision\":\n      manager = /*#__PURE__*/_jsxDEV(VisionManager, {\n        outOfEditing: outOfEditing,\n        language: language,\n        content: content,\n        indexes: contentIndex,\n        currentPath: currentPath\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 118,\n        columnNumber: 23\n      }, this);\n      break;\n    case \"affiliates\":\n      manager = /*#__PURE__*/_jsxDEV(AffiliatesManager, {\n        outOfEditing: outOfEditing,\n        language: language,\n        content: content,\n        indexes: contentIndex,\n        currentPath: currentPath\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 122,\n        columnNumber: 23\n      }, this);\n      break;\n    case \"organization\":\n      manager = /*#__PURE__*/_jsxDEV(OrganizationManager, {\n        outOfEditing: outOfEditing,\n        language: language,\n        content: content,\n        indexes: contentIndex,\n        currentPath: currentPath\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 126,\n        columnNumber: 23\n      }, this);\n      break;\n    case \"hse\":\n      manager = /*#__PURE__*/_jsxDEV(HSnEManager, {\n        outOfEditing: outOfEditing,\n        language: language,\n        content: content,\n        indexes: contentIndex,\n        currentPath: currentPath\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 130,\n        columnNumber: 23\n      }, this);\n      break;\n    case \"temp-1\":\n      manager = /*#__PURE__*/_jsxDEV(TemplateOneManager, {\n        outOfEditing: outOfEditing,\n        language: language,\n        content: content,\n        indexes: contentIndex,\n        currentPath: currentPath\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 134,\n        columnNumber: 23\n      }, this);\n      break;\n    case \"temp-2\":\n      manager = /*#__PURE__*/_jsxDEV(TemplateTwoManager, {\n        outOfEditing: outOfEditing,\n        language: language,\n        content: content,\n        indexes: contentIndex,\n        currentPath: currentPath\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 138,\n        columnNumber: 23\n      }, this);\n      break;\n    case \"temp-3\":\n      manager = /*#__PURE__*/_jsxDEV(TemplateThreeManager, {\n        outOfEditing: outOfEditing,\n        language: language,\n        content: content,\n        indexes: contentIndex,\n        currentPath: currentPath\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 142,\n        columnNumber: 23\n      }, this);\n      break;\n    case \"temp-4\":\n      manager = /*#__PURE__*/_jsxDEV(TemplateFourManager, {\n        outOfEditing: outOfEditing,\n        language: language,\n        content: content,\n        indexes: contentIndex,\n        currentPath: currentPath\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 146,\n        columnNumber: 23\n      }, this);\n      break;\n    case \"contact-us\":\n      manager = /*#__PURE__*/_jsxDEV(ContactUsManager, {\n        outOfEditing: outOfEditing,\n        language: language,\n        content: content,\n        indexes: contentIndex,\n        currentPath: currentPath\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 150,\n        columnNumber: 23\n      }, this);\n      break;\n    default:\n  }\n  useEffect(() => {\n    return () => dispatch(updateMainContent({\n      currentPath: \"content\",\n      payload: undefined\n    }));\n  }, []);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"\",\n    children: manager\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 162,\n    columnNumber: 9\n  }, this);\n};\n_s(AllForOneManager, \"rAh3tY+Iv6hWC9AI4Dm+rCbkwNE=\", false, function () {\n  return [useDispatch];\n});\n_c = AllForOneManager;\nexport default AllForOneManager;\nvar _c;\n$RefreshReg$(_c, \"AllForOneManager\");", "map": {"version": 3, "names": ["HomeManager", "SolutionManager", "AboutManager", "MarketManager", "ProjectContentManager", "CareersManager", "NewsManager", "FooterManager", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ProjectDetailManager", "CareerDetailManager", "NewsDetailManager", "TestimonyManager", "ServiceManager", "ServiceDetailsManager", "SubServiceDetailManager", "useEffect", "useDispatch", "update<PERSON>ain<PERSON><PERSON>nt", "SnRManager", "SnRPoliciesManager", "HistoryManager", "VisionManager", "HSnEManager", "MarketDetailsManager", "Affiliates<PERSON>anager", "OrganizationManager", "TemplateOneManager", "TemplateTwoManager", "TemplateThr<PERSON>Manager", "TemplateFourManager", "ContactUsManager", "jsxDEV", "_jsxDEV", "AllForOneManager", "currentPath", "language", "subPath", "<PERSON><PERSON><PERSON>", "content", "contentIndex", "outOfEditing", "_s", "dispatch", "manager", "indexes", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "currentC<PERSON>nt", "serviceId", "serviceDetails", "projectId", "careerId", "newsId", "testimonyId", "payload", "undefined", "className", "children", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Akshay/LOOP_PROJECTS/shade_cms/dashboard/src/features/Resources/components/AllForOneManager.jsx"], "sourcesContent": ["// import for content manager\nimport HomeManager from \"./contentmanager/HomeManager\";\nimport SolutionManager from \"./contentmanager/SolutionManager\";\nimport AboutManager from \"./contentmanager/AboutManager\";\nimport MarketManager from \"./contentmanager/MarketManager\";\nimport ProjectContentManager from \"./contentmanager/ProjectContentManager\";\nimport CareersManager from \"./contentmanager/CareersManager\";\nimport NewsManager from \"./contentmanager/NewsManager\";\nimport FooterManager from \"./contentmanager/CMforSubParts/FooterManager\";\nimport HeaderManager from \"./contentmanager/CMforSubParts/HeaderManager\";\nimport ProjectDetailManager from \"./contentmanager/CMforDetails/ProjectDetailManager\";\nimport CareerDetailManager from \"./contentmanager/CMforDetails/CareerDetailManager\";\nimport NewsDetailManager from \"./contentmanager/CMforDetails/NewsDetailsManager\";\nimport TestimonyManager from \"./contentmanager/CMforSubParts/TestimonyManager\";\nimport ServiceManager from \"./contentmanager/ServiceManager\";\nimport ServiceDetailsManager from \"./contentmanager/CMforDetails/ServiceDetailsManager\";\nimport SubServiceDetailManager from \"./contentmanager/subDetailsManagement/SubServiceDetailManagement\";\nimport { useEffect } from \"react\";\nimport { useDispatch } from \"react-redux\";\nimport { updateMainContent } from \"../../common/homeContentSlice\";\nimport SnRManager from \"./contentmanager/SnRManager\";\nimport SnRPoliciesManager from \"./contentmanager/SnRPolicyManager\";\nimport HistoryManager from \"./contentmanager/HistoryManager\";\nimport VisionManager from \"./contentmanager/VisionManager\";\nimport HSnEManager from \"./contentmanager/HSnEManager\";\nimport MarketDetailsManager from \"./contentmanager/CMforDetails/MarketDetailsManager\";\nimport AffiliatesManager from \"./contentmanager/AffiliatesManager\";\nimport OrganizationManager from \"./contentmanager/OrganizationManager\";\nimport TemplateOneManager from \"./contentmanager/TemplateOneManager\";\nimport TemplateTwoManager from \"./contentmanager/TemplateTwoManager\";\nimport TemplateThreeManager from \"./contentmanager/TemplateThreeManager\";\nimport TemplateFourManager from \"./contentmanager/TemplateFourManager\";\nimport ContactUsManager from \"./contentmanager/ContactUsManager\";\n\n\nconst AllForOneManager = ({ currentPath, language, subPath, deepPath, content, contentIndex, outOfEditing }) => {\n    const dispatch = useDispatch()\n\n    let manager = null\n\n    switch (currentPath) {\n        case \"home\":\n            manager = <HomeManager outOfEditing={outOfEditing} language={language} content={content} indexes={contentIndex} currentPath={currentPath} />\n            break;\n\n        case \"solution\":\n            manager = <SolutionManager outOfEditing={outOfEditing} language={language} currentContent={content} currentPath={currentPath} indexes={contentIndex} />\n            break;\n\n        case \"about-us\":\n            manager = <AboutManager outOfEditing={outOfEditing} language={language} content={content} indexes={contentIndex} currentPath={currentPath} />\n            break;\n\n        case \"service\":\n            manager = subPath ? deepPath ?\n                <SubServiceDetailManager outOfEditing={outOfEditing} serviceId={subPath} deepPath={deepPath} language={language} content={content} indexes={contentIndex} currentPath={\"subOfsubService\"} /> :\n                <ServiceDetailsManager outOfEditing={outOfEditing} serviceId={subPath} language={language} content={content} indexes={contentIndex} currentPath={\"serviceDetails\"} /> :\n                <ServiceManager outOfEditing={outOfEditing} language={language} currentContent={content} currentPath={currentPath} indexes={contentIndex} />\n            break;\n\n        case \"service\":\n            manager = subPath &&\n                <ServiceDetailsManager outOfEditing={outOfEditing} serviceId={subPath} language={language} currentContent={content.serviceDetails} indexes={contentIndex} currentPath={\"serviceDetails\"} />\n            break;\n\n        case \"market\":\n            manager = subPath ?\n                <MarketDetailsManager outOfEditing={outOfEditing} language={language} indexes={contentIndex} content={content} currentPath={currentPath} />\n                : <MarketManager outOfEditing={outOfEditing} language={language} currentContent={content} indexes={contentIndex} currentPath={currentPath} />\n            break;\n\n        case \"project\":\n            manager =\n                subPath ?\n                    <ProjectDetailManager outOfEditing={outOfEditing} projectId={subPath} language={language} indexes={contentIndex} currentContent={content} currentPath={\"projectDetail\"} /> :\n                    <ProjectContentManager outOfEditing={outOfEditing} language={language} currentContent={content} indexes={contentIndex} currentPath={currentPath} />\n            break;\n\n        case \"careers\":\n            manager = <NewsManager outOfEditing={outOfEditing} language={language} content={content} indexes={contentIndex} currentPath={\"newsBlogs\"} />\n            break;\n        case \"career\":\n            manager = <CareerDetailManager outOfEditing={outOfEditing} careerId={subPath} language={language} indexes={contentIndex} currentContent={content} currentPath={\"careerDetails\"} />\n            break;\n\n        case \"news-blogs\":\n            manager = <NewsManager outOfEditing={outOfEditing} language={language} content={content} indexes={contentIndex} currentPath={\"newsBlogs\"} />\n            break;\n\n        case \"news\":\n            manager = <NewsDetailManager outOfEditing={outOfEditing} newsId={subPath} language={language} indexes={contentIndex} content={content} currentPath={\"newsBlogsDetails\"} />\n            break;\n\n        case \"footer\":\n            manager = <FooterManager outOfEditing={outOfEditing} language={language} currentContent={content} indexes={contentIndex} currentPath={currentPath} />\n            break;\n\n        case \"header\":\n            manager = <HeaderManager outOfEditing={outOfEditing} language={language} currentContent={content} indexes={contentIndex} currentPath={currentPath} />\n            break;\n\n        case \"testimonial\":\n            manager = <TestimonyManager outOfEditing={outOfEditing} language={language} currentContent={content} indexes={contentIndex} testimonyId={subPath} currentPath={\"testimonialSection\"} />\n            break;\n\n        case \"safety\":\n        case \"safety_responsibility\":\n            manager = subPath ?\n                <SnRPoliciesManager outOfEditing={outOfEditing} language={language} content={content} indexes={contentIndex} currentPath={currentPath} /> :\n                <SnRManager outOfEditing={outOfEditing} language={language} content={content} indexes={contentIndex} currentPath={currentPath} />\n            break;\n\n        case \"history\":\n            manager = <HistoryManager outOfEditing={outOfEditing} language={language} content={content} indexes={contentIndex} currentPath={currentPath} />\n            break;\n\n        case \"vision\":\n            manager = <VisionManager outOfEditing={outOfEditing} language={language} content={content} indexes={contentIndex} currentPath={currentPath} />\n            break;\n\n        case \"affiliates\":\n            manager = <AffiliatesManager outOfEditing={outOfEditing} language={language} content={content} indexes={contentIndex} currentPath={currentPath} />\n            break;\n\n        case \"organization\":\n            manager = <OrganizationManager outOfEditing={outOfEditing} language={language} content={content} indexes={contentIndex} currentPath={currentPath} />\n            break;\n\n        case \"hse\":\n            manager = <HSnEManager outOfEditing={outOfEditing} language={language} content={content} indexes={contentIndex} currentPath={currentPath} />\n            break;\n\n        case \"temp-1\":\n            manager = <TemplateOneManager outOfEditing={outOfEditing} language={language} content={content} indexes={contentIndex} currentPath={currentPath} />\n            break;\n\n        case \"temp-2\":\n            manager = <TemplateTwoManager outOfEditing={outOfEditing} language={language} content={content} indexes={contentIndex} currentPath={currentPath} />\n            break;\n\n        case \"temp-3\":\n            manager = <TemplateThreeManager outOfEditing={outOfEditing} language={language} content={content} indexes={contentIndex} currentPath={currentPath} />\n            break;\n\n        case \"temp-4\":\n            manager = <TemplateFourManager outOfEditing={outOfEditing} language={language} content={content} indexes={contentIndex} currentPath={currentPath} />\n            break;\n\n        case \"contact-us\":\n            manager = <ContactUsManager outOfEditing={outOfEditing} language={language} content={content} indexes={contentIndex} currentPath={currentPath} />\n            break;\n\n        default:\n    }\n\n    useEffect(() => {\n        return () => dispatch(updateMainContent({ currentPath: \"content\", payload: undefined }))\n    }, [])\n\n\n    return (\n        <div className=\"\">\n            {manager}\n        </div>\n    )\n}\n\nexport default AllForOneManager"], "mappings": ";;AAAA;AACA,OAAOA,WAAW,MAAM,8BAA8B;AACtD,OAAOC,eAAe,MAAM,kCAAkC;AAC9D,OAAOC,YAAY,MAAM,+BAA+B;AACxD,OAAOC,aAAa,MAAM,gCAAgC;AAC1D,OAAOC,qBAAqB,MAAM,wCAAwC;AAC1E,OAAOC,cAAc,MAAM,iCAAiC;AAC5D,OAAOC,WAAW,MAAM,8BAA8B;AACtD,OAAOC,aAAa,MAAM,8CAA8C;AACxE,OAAOC,aAAa,MAAM,8CAA8C;AACxE,OAAOC,oBAAoB,MAAM,oDAAoD;AACrF,OAAOC,mBAAmB,MAAM,mDAAmD;AACnF,OAAOC,iBAAiB,MAAM,kDAAkD;AAChF,OAAOC,gBAAgB,MAAM,iDAAiD;AAC9E,OAAOC,cAAc,MAAM,iCAAiC;AAC5D,OAAOC,qBAAqB,MAAM,qDAAqD;AACvF,OAAOC,uBAAuB,MAAM,kEAAkE;AACtG,SAASC,SAAS,QAAQ,OAAO;AACjC,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,iBAAiB,QAAQ,+BAA+B;AACjE,OAAOC,UAAU,MAAM,6BAA6B;AACpD,OAAOC,kBAAkB,MAAM,mCAAmC;AAClE,OAAOC,cAAc,MAAM,iCAAiC;AAC5D,OAAOC,aAAa,MAAM,gCAAgC;AAC1D,OAAOC,WAAW,MAAM,8BAA8B;AACtD,OAAOC,oBAAoB,MAAM,oDAAoD;AACrF,OAAOC,iBAAiB,MAAM,oCAAoC;AAClE,OAAOC,mBAAmB,MAAM,sCAAsC;AACtE,OAAOC,kBAAkB,MAAM,qCAAqC;AACpE,OAAOC,kBAAkB,MAAM,qCAAqC;AACpE,OAAOC,oBAAoB,MAAM,uCAAuC;AACxE,OAAOC,mBAAmB,MAAM,sCAAsC;AACtE,OAAOC,gBAAgB,MAAM,mCAAmC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAGjE,MAAMC,gBAAgB,GAAGA,CAAC;EAAEC,WAAW;EAAEC,QAAQ;EAAEC,OAAO;EAAEC,QAAQ;EAAEC,OAAO;EAAEC,YAAY;EAAEC;AAAa,CAAC,KAAK;EAAAC,EAAA;EAC5G,MAAMC,QAAQ,GAAG1B,WAAW,CAAC,CAAC;EAE9B,IAAI2B,OAAO,GAAG,IAAI;EAElB,QAAQT,WAAW;IACf,KAAK,MAAM;MACPS,OAAO,gBAAGX,OAAA,CAACjC,WAAW;QAACyC,YAAY,EAAEA,YAAa;QAACL,QAAQ,EAAEA,QAAS;QAACG,OAAO,EAAEA,OAAQ;QAACM,OAAO,EAAEL,YAAa;QAACL,WAAW,EAAEA;MAAY;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MAC5I;IAEJ,KAAK,UAAU;MACXL,OAAO,gBAAGX,OAAA,CAAChC,eAAe;QAACwC,YAAY,EAAEA,YAAa;QAACL,QAAQ,EAAEA,QAAS;QAACc,cAAc,EAAEX,OAAQ;QAACJ,WAAW,EAAEA,WAAY;QAACU,OAAO,EAAEL;MAAa;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACvJ;IAEJ,KAAK,UAAU;MACXL,OAAO,gBAAGX,OAAA,CAAC/B,YAAY;QAACuC,YAAY,EAAEA,YAAa;QAACL,QAAQ,EAAEA,QAAS;QAACG,OAAO,EAAEA,OAAQ;QAACM,OAAO,EAAEL,YAAa;QAACL,WAAW,EAAEA;MAAY;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MAC7I;IAEJ,KAAK,SAAS;MACVL,OAAO,GAAGP,OAAO,GAAGC,QAAQ,gBACxBL,OAAA,CAAClB,uBAAuB;QAAC0B,YAAY,EAAEA,YAAa;QAACU,SAAS,EAAEd,OAAQ;QAACC,QAAQ,EAAEA,QAAS;QAACF,QAAQ,EAAEA,QAAS;QAACG,OAAO,EAAEA,OAAQ;QAACM,OAAO,EAAEL,YAAa;QAACL,WAAW,EAAE;MAAkB;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,gBAC5LhB,OAAA,CAACnB,qBAAqB;QAAC2B,YAAY,EAAEA,YAAa;QAACU,SAAS,EAAEd,OAAQ;QAACD,QAAQ,EAAEA,QAAS;QAACG,OAAO,EAAEA,OAAQ;QAACM,OAAO,EAAEL,YAAa;QAACL,WAAW,EAAE;MAAiB;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,gBACrKhB,OAAA,CAACpB,cAAc;QAAC4B,YAAY,EAAEA,YAAa;QAACL,QAAQ,EAAEA,QAAS;QAACc,cAAc,EAAEX,OAAQ;QAACJ,WAAW,EAAEA,WAAY;QAACU,OAAO,EAAEL;MAAa;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MAChJ;IAEJ,KAAK,SAAS;MACVL,OAAO,GAAGP,OAAO,iBACbJ,OAAA,CAACnB,qBAAqB;QAAC2B,YAAY,EAAEA,YAAa;QAACU,SAAS,EAAEd,OAAQ;QAACD,QAAQ,EAAEA,QAAS;QAACc,cAAc,EAAEX,OAAO,CAACa,cAAe;QAACP,OAAO,EAAEL,YAAa;QAACL,WAAW,EAAE;MAAiB;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MAC/L;IAEJ,KAAK,QAAQ;MACTL,OAAO,GAAGP,OAAO,gBACbJ,OAAA,CAACT,oBAAoB;QAACiB,YAAY,EAAEA,YAAa;QAACL,QAAQ,EAAEA,QAAS;QAACS,OAAO,EAAEL,YAAa;QAACD,OAAO,EAAEA,OAAQ;QAACJ,WAAW,EAAEA;MAAY;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,gBACzIhB,OAAA,CAAC9B,aAAa;QAACsC,YAAY,EAAEA,YAAa;QAACL,QAAQ,EAAEA,QAAS;QAACc,cAAc,EAAEX,OAAQ;QAACM,OAAO,EAAEL,YAAa;QAACL,WAAW,EAAEA;MAAY;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACjJ;IAEJ,KAAK,SAAS;MACVL,OAAO,GACHP,OAAO,gBACHJ,OAAA,CAACxB,oBAAoB;QAACgC,YAAY,EAAEA,YAAa;QAACY,SAAS,EAAEhB,OAAQ;QAACD,QAAQ,EAAEA,QAAS;QAACS,OAAO,EAAEL,YAAa;QAACU,cAAc,EAAEX,OAAQ;QAACJ,WAAW,EAAE;MAAgB;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,gBAC1KhB,OAAA,CAAC7B,qBAAqB;QAACqC,YAAY,EAAEA,YAAa;QAACL,QAAQ,EAAEA,QAAS;QAACc,cAAc,EAAEX,OAAQ;QAACM,OAAO,EAAEL,YAAa;QAACL,WAAW,EAAEA;MAAY;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MAC3J;IAEJ,KAAK,SAAS;MACVL,OAAO,gBAAGX,OAAA,CAAC3B,WAAW;QAACmC,YAAY,EAAEA,YAAa;QAACL,QAAQ,EAAEA,QAAS;QAACG,OAAO,EAAEA,OAAQ;QAACM,OAAO,EAAEL,YAAa;QAACL,WAAW,EAAE;MAAY;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MAC5I;IACJ,KAAK,QAAQ;MACTL,OAAO,gBAAGX,OAAA,CAACvB,mBAAmB;QAAC+B,YAAY,EAAEA,YAAa;QAACa,QAAQ,EAAEjB,OAAQ;QAACD,QAAQ,EAAEA,QAAS;QAACS,OAAO,EAAEL,YAAa;QAACU,cAAc,EAAEX,OAAQ;QAACJ,WAAW,EAAE;MAAgB;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MAClL;IAEJ,KAAK,YAAY;MACbL,OAAO,gBAAGX,OAAA,CAAC3B,WAAW;QAACmC,YAAY,EAAEA,YAAa;QAACL,QAAQ,EAAEA,QAAS;QAACG,OAAO,EAAEA,OAAQ;QAACM,OAAO,EAAEL,YAAa;QAACL,WAAW,EAAE;MAAY;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MAC5I;IAEJ,KAAK,MAAM;MACPL,OAAO,gBAAGX,OAAA,CAACtB,iBAAiB;QAAC8B,YAAY,EAAEA,YAAa;QAACc,MAAM,EAAElB,OAAQ;QAACD,QAAQ,EAAEA,QAAS;QAACS,OAAO,EAAEL,YAAa;QAACD,OAAO,EAAEA,OAAQ;QAACJ,WAAW,EAAE;MAAmB;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MAC1K;IAEJ,KAAK,QAAQ;MACTL,OAAO,gBAAGX,OAAA,CAAC1B,aAAa;QAACkC,YAAY,EAAEA,YAAa;QAACL,QAAQ,EAAEA,QAAS;QAACc,cAAc,EAAEX,OAAQ;QAACM,OAAO,EAAEL,YAAa;QAACL,WAAW,EAAEA;MAAY;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACrJ;IAEJ,KAAK,QAAQ;MACTL,OAAO,gBAAGX,OAAA,CAACzB,aAAa;QAACiC,YAAY,EAAEA,YAAa;QAACL,QAAQ,EAAEA,QAAS;QAACc,cAAc,EAAEX,OAAQ;QAACM,OAAO,EAAEL,YAAa;QAACL,WAAW,EAAEA;MAAY;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACrJ;IAEJ,KAAK,aAAa;MACdL,OAAO,gBAAGX,OAAA,CAACrB,gBAAgB;QAAC6B,YAAY,EAAEA,YAAa;QAACL,QAAQ,EAAEA,QAAS;QAACc,cAAc,EAAEX,OAAQ;QAACM,OAAO,EAAEL,YAAa;QAACgB,WAAW,EAAEnB,OAAQ;QAACF,WAAW,EAAE;MAAqB;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACvL;IAEJ,KAAK,QAAQ;IACb,KAAK,uBAAuB;MACxBL,OAAO,GAAGP,OAAO,gBACbJ,OAAA,CAACb,kBAAkB;QAACqB,YAAY,EAAEA,YAAa;QAACL,QAAQ,EAAEA,QAAS;QAACG,OAAO,EAAEA,OAAQ;QAACM,OAAO,EAAEL,YAAa;QAACL,WAAW,EAAEA;MAAY;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,gBACzIhB,OAAA,CAACd,UAAU;QAACsB,YAAY,EAAEA,YAAa;QAACL,QAAQ,EAAEA,QAAS;QAACG,OAAO,EAAEA,OAAQ;QAACM,OAAO,EAAEL,YAAa;QAACL,WAAW,EAAEA;MAAY;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACrI;IAEJ,KAAK,SAAS;MACVL,OAAO,gBAAGX,OAAA,CAACZ,cAAc;QAACoB,YAAY,EAAEA,YAAa;QAACL,QAAQ,EAAEA,QAAS;QAACG,OAAO,EAAEA,OAAQ;QAACM,OAAO,EAAEL,YAAa;QAACL,WAAW,EAAEA;MAAY;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MAC/I;IAEJ,KAAK,QAAQ;MACTL,OAAO,gBAAGX,OAAA,CAACX,aAAa;QAACmB,YAAY,EAAEA,YAAa;QAACL,QAAQ,EAAEA,QAAS;QAACG,OAAO,EAAEA,OAAQ;QAACM,OAAO,EAAEL,YAAa;QAACL,WAAW,EAAEA;MAAY;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MAC9I;IAEJ,KAAK,YAAY;MACbL,OAAO,gBAAGX,OAAA,CAACR,iBAAiB;QAACgB,YAAY,EAAEA,YAAa;QAACL,QAAQ,EAAEA,QAAS;QAACG,OAAO,EAAEA,OAAQ;QAACM,OAAO,EAAEL,YAAa;QAACL,WAAW,EAAEA;MAAY;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MAClJ;IAEJ,KAAK,cAAc;MACfL,OAAO,gBAAGX,OAAA,CAACP,mBAAmB;QAACe,YAAY,EAAEA,YAAa;QAACL,QAAQ,EAAEA,QAAS;QAACG,OAAO,EAAEA,OAAQ;QAACM,OAAO,EAAEL,YAAa;QAACL,WAAW,EAAEA;MAAY;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACpJ;IAEJ,KAAK,KAAK;MACNL,OAAO,gBAAGX,OAAA,CAACV,WAAW;QAACkB,YAAY,EAAEA,YAAa;QAACL,QAAQ,EAAEA,QAAS;QAACG,OAAO,EAAEA,OAAQ;QAACM,OAAO,EAAEL,YAAa;QAACL,WAAW,EAAEA;MAAY;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MAC5I;IAEJ,KAAK,QAAQ;MACTL,OAAO,gBAAGX,OAAA,CAACN,kBAAkB;QAACc,YAAY,EAAEA,YAAa;QAACL,QAAQ,EAAEA,QAAS;QAACG,OAAO,EAAEA,OAAQ;QAACM,OAAO,EAAEL,YAAa;QAACL,WAAW,EAAEA;MAAY;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACnJ;IAEJ,KAAK,QAAQ;MACTL,OAAO,gBAAGX,OAAA,CAACL,kBAAkB;QAACa,YAAY,EAAEA,YAAa;QAACL,QAAQ,EAAEA,QAAS;QAACG,OAAO,EAAEA,OAAQ;QAACM,OAAO,EAAEL,YAAa;QAACL,WAAW,EAAEA;MAAY;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACnJ;IAEJ,KAAK,QAAQ;MACTL,OAAO,gBAAGX,OAAA,CAACJ,oBAAoB;QAACY,YAAY,EAAEA,YAAa;QAACL,QAAQ,EAAEA,QAAS;QAACG,OAAO,EAAEA,OAAQ;QAACM,OAAO,EAAEL,YAAa;QAACL,WAAW,EAAEA;MAAY;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACrJ;IAEJ,KAAK,QAAQ;MACTL,OAAO,gBAAGX,OAAA,CAACH,mBAAmB;QAACW,YAAY,EAAEA,YAAa;QAACL,QAAQ,EAAEA,QAAS;QAACG,OAAO,EAAEA,OAAQ;QAACM,OAAO,EAAEL,YAAa;QAACL,WAAW,EAAEA;MAAY;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACpJ;IAEJ,KAAK,YAAY;MACbL,OAAO,gBAAGX,OAAA,CAACF,gBAAgB;QAACU,YAAY,EAAEA,YAAa;QAACL,QAAQ,EAAEA,QAAS;QAACG,OAAO,EAAEA,OAAQ;QAACM,OAAO,EAAEL,YAAa;QAACL,WAAW,EAAEA;MAAY;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACjJ;IAEJ;EACJ;EAEAjC,SAAS,CAAC,MAAM;IACZ,OAAO,MAAM2B,QAAQ,CAACzB,iBAAiB,CAAC;MAAEiB,WAAW,EAAE,SAAS;MAAEsB,OAAO,EAAEC;IAAU,CAAC,CAAC,CAAC;EAC5F,CAAC,EAAE,EAAE,CAAC;EAGN,oBACIzB,OAAA;IAAK0B,SAAS,EAAC,EAAE;IAAAC,QAAA,EACZhB;EAAO;IAAAE,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEd,CAAC;AAAAP,EAAA,CAlIKR,gBAAgB;EAAA,QACDjB,WAAW;AAAA;AAAA4C,EAAA,GAD1B3B,gBAAgB;AAoItB,eAAeA,gBAAgB;AAAA,IAAA2B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}