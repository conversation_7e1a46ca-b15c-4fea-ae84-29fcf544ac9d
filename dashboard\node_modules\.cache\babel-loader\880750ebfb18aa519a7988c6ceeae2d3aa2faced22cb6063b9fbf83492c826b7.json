{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Akshay\\\\LOOP_PROJECTS\\\\shade_cms\\\\dashboard\\\\src\\\\features\\\\Resources\\\\components\\\\contentmanager\\\\CMforDetails\\\\CareerDetailManager.jsx\",\n  _s = $RefreshSig$();\nimport { useEffect, useState } from \"react\";\nimport FileUploader from \"../../../../../components/Input/InputFileUploader\";\nimport ContentSection from \"../../breakUI/ContentSections\";\nimport { useDispatch } from \"react-redux\";\nimport { updateMainContent, updateTheProjectSummaryList } from \"../../../../common/homeContentSlice\";\nimport content from \"../../websiteComponent/content.json\";\nimport DynamicContentSection from \"../../breakUI/DynamicContentSection\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CareerDetailManager = ({\n  careerId,\n  currentContent,\n  currentPath,\n  language\n}) => {\n  _s();\n  var _currentContent$caree, _currentContent$caree2, _currentContent$caree3, _currentContent$caree4, _currentContent$caree5, _currentContent$caree6, _currentContent$caree7, _currentContent$caree8;\n  const dispatch = useDispatch();\n  const careerIndex = Array.isArray(currentContent) ? currentContent.findIndex(e => e.id == careerId) : -1;\n\n  // Validation states\n  const [validationErrors, setValidationErrors] = useState({});\n  const [isValidating, setIsValidating] = useState(false);\n\n  // Function to clear specific validation error\n  const clearValidationError = errorKey => {\n    setValidationErrors(prev => {\n      const newErrors = {\n        ...prev\n      };\n      delete newErrors[errorKey];\n      return newErrors;\n    });\n  };\n\n  // Validation functions\n  const validateField = (value, fieldName) => {\n    if (!value || typeof value === 'string' && value.trim() === '') {\n      return \"Required\";\n    }\n    return null;\n  };\n  const validateImage = (imageUrl, fieldName) => {\n    if (!imageUrl || imageUrl.trim() === '') {\n      return \"Required\";\n    }\n    return null;\n  };\n  const validateArray = (array, fieldName, minLength = 1) => {\n    if (!array || array.length < minLength) {\n      return \"Required\";\n    }\n    return null;\n  };\n\n  // Comprehensive validation function\n  const validateAllFields = () => {\n    var _career$banner, _career$banner$title, _career$banner2, _career$banner2$subTi, _career$banner3, _career$banner3$butto, _career$banner4, _career$banner4$image, _career$banner4$image2, _career$jobDetails, _career$jobDetails$le, _career$jobDetails2, _career$jobDetails2$r, _career$jobDetails2$r2, _career$jobDetails3, _career$jobDetails3$r, _career$jobDetails3$r2, _career$jobDetails4, _career$jobDetails4$r, _career$jobDetails5, _career$jobDetails5$r, _career$jobDetails5$r2, _career$jobDetails5$r3, _career$jobDetails6, _career$jobDetails6$r, _career$jobDetails6$r2, _career$jobDetails6$r3, _career$jobDetails7, _career$jobDetails7$b;\n    setIsValidating(true);\n    const errors = {};\n    const career = currentContent === null || currentContent === void 0 ? void 0 : currentContent[careerIndex];\n\n    // Banner section validation\n    const bannerTitle = career === null || career === void 0 ? void 0 : (_career$banner = career.banner) === null || _career$banner === void 0 ? void 0 : (_career$banner$title = _career$banner.title) === null || _career$banner$title === void 0 ? void 0 : _career$banner$title[language];\n    const bannerSubTitle = career === null || career === void 0 ? void 0 : (_career$banner2 = career.banner) === null || _career$banner2 === void 0 ? void 0 : (_career$banner2$subTi = _career$banner2.subTitle) === null || _career$banner2$subTi === void 0 ? void 0 : _career$banner2$subTi[language];\n    const bannerButton = career === null || career === void 0 ? void 0 : (_career$banner3 = career.banner) === null || _career$banner3 === void 0 ? void 0 : (_career$banner3$butto = _career$banner3.button) === null || _career$banner3$butto === void 0 ? void 0 : _career$banner3$butto[language];\n    const bannerImage = career === null || career === void 0 ? void 0 : (_career$banner4 = career.banner) === null || _career$banner4 === void 0 ? void 0 : (_career$banner4$image = _career$banner4.images) === null || _career$banner4$image === void 0 ? void 0 : (_career$banner4$image2 = _career$banner4$image[0]) === null || _career$banner4$image2 === void 0 ? void 0 : _career$banner4$image2.url;\n    const bannerTitleError = validateField(bannerTitle, \"Banner Title\");\n    if (bannerTitleError) errors[\"banner_title\"] = bannerTitleError;\n    const bannerSubTitleError = validateField(bannerSubTitle, \"Banner Sub Title\");\n    if (bannerSubTitleError) errors[\"banner_subtitle\"] = bannerSubTitleError;\n    const bannerButtonError = validateField(bannerButton, \"Banner Button\");\n    if (bannerButtonError) errors[\"banner_button\"] = bannerButtonError;\n    const bannerImageError = validateImage(bannerImage, \"Banner Image\");\n    if (bannerImageError) errors[\"banner_image\"] = bannerImageError;\n\n    // Left panel sections validation\n    const leftPanelSections = career === null || career === void 0 ? void 0 : (_career$jobDetails = career.jobDetails) === null || _career$jobDetails === void 0 ? void 0 : (_career$jobDetails$le = _career$jobDetails.leftPanel) === null || _career$jobDetails$le === void 0 ? void 0 : _career$jobDetails$le.sections;\n    if (leftPanelSections && leftPanelSections.length > 0) {\n      leftPanelSections.forEach((section, index) => {\n        var _section$title, _section$content;\n        const sectionTitle = section === null || section === void 0 ? void 0 : (_section$title = section.title) === null || _section$title === void 0 ? void 0 : _section$title[language];\n        const sectionContent = section === null || section === void 0 ? void 0 : (_section$content = section.content) === null || _section$content === void 0 ? void 0 : _section$content[language];\n        const sectionTitleError = validateField(sectionTitle, `Left Panel Section ${index + 1} Title`);\n        if (sectionTitleError) errors[`left_section_${index}_title`] = sectionTitleError;\n        const sectionContentError = validateField(sectionContent, `Left Panel Section ${index + 1} Content`);\n        if (sectionContentError) errors[`left_section_${index}_content`] = sectionContentError;\n      });\n    }\n\n    // Right panel top section validation\n    const rightPanelTitle = career === null || career === void 0 ? void 0 : (_career$jobDetails2 = career.jobDetails) === null || _career$jobDetails2 === void 0 ? void 0 : (_career$jobDetails2$r = _career$jobDetails2.rightPanel) === null || _career$jobDetails2$r === void 0 ? void 0 : (_career$jobDetails2$r2 = _career$jobDetails2$r.title) === null || _career$jobDetails2$r2 === void 0 ? void 0 : _career$jobDetails2$r2[language];\n    const rightPanelButton = career === null || career === void 0 ? void 0 : (_career$jobDetails3 = career.jobDetails) === null || _career$jobDetails3 === void 0 ? void 0 : (_career$jobDetails3$r = _career$jobDetails3.rightPanel) === null || _career$jobDetails3$r === void 0 ? void 0 : (_career$jobDetails3$r2 = _career$jobDetails3$r.button) === null || _career$jobDetails3$r2 === void 0 ? void 0 : _career$jobDetails3$r2[language];\n    const rightPanelTitleError = validateField(rightPanelTitle, \"Right Panel Title\");\n    if (rightPanelTitleError) errors[\"right_panel_title\"] = rightPanelTitleError;\n    const rightPanelButtonError = validateField(rightPanelButton, \"Right Panel Button\");\n    if (rightPanelButtonError) errors[\"right_panel_button\"] = rightPanelButtonError;\n\n    // Right panel tailwraps validation\n    const tailwraps = career === null || career === void 0 ? void 0 : (_career$jobDetails4 = career.jobDetails) === null || _career$jobDetails4 === void 0 ? void 0 : (_career$jobDetails4$r = _career$jobDetails4.rightPanel) === null || _career$jobDetails4$r === void 0 ? void 0 : _career$jobDetails4$r.tailwraps;\n    if (tailwraps && tailwraps.length > 0) {\n      tailwraps.forEach((tailwrap, index) => {\n        var _tailwrap$title, _tailwrap$description, _tailwrap$images, _tailwrap$images$;\n        const tailwrapTitle = tailwrap === null || tailwrap === void 0 ? void 0 : (_tailwrap$title = tailwrap.title) === null || _tailwrap$title === void 0 ? void 0 : _tailwrap$title[language];\n        const tailwrapDescription = tailwrap === null || tailwrap === void 0 ? void 0 : (_tailwrap$description = tailwrap.description) === null || _tailwrap$description === void 0 ? void 0 : _tailwrap$description[language];\n        const tailwrapIcon = tailwrap === null || tailwrap === void 0 ? void 0 : (_tailwrap$images = tailwrap.images) === null || _tailwrap$images === void 0 ? void 0 : (_tailwrap$images$ = _tailwrap$images[0]) === null || _tailwrap$images$ === void 0 ? void 0 : _tailwrap$images$.url;\n        const tailwrapTitleError = validateField(tailwrapTitle, `Tailwrap ${index + 1} Title`);\n        if (tailwrapTitleError) errors[`tailwrap_${index}_title`] = tailwrapTitleError;\n        const tailwrapDescriptionError = validateField(tailwrapDescription, `Tailwrap ${index + 1} Description`);\n        if (tailwrapDescriptionError) errors[`tailwrap_${index}_description`] = tailwrapDescriptionError;\n        const tailwrapIconError = validateImage(tailwrapIcon, `Tailwrap ${index + 1} Icon`);\n        if (tailwrapIconError) errors[`tailwrap_${index}_icon`] = tailwrapIconError;\n      });\n    }\n\n    // Right panel view all button validation\n    const viewAllText = career === null || career === void 0 ? void 0 : (_career$jobDetails5 = career.jobDetails) === null || _career$jobDetails5 === void 0 ? void 0 : (_career$jobDetails5$r = _career$jobDetails5.rightPanel) === null || _career$jobDetails5$r === void 0 ? void 0 : (_career$jobDetails5$r2 = _career$jobDetails5$r.viewAllButton) === null || _career$jobDetails5$r2 === void 0 ? void 0 : (_career$jobDetails5$r3 = _career$jobDetails5$r2.text) === null || _career$jobDetails5$r3 === void 0 ? void 0 : _career$jobDetails5$r3[language];\n    const viewAllLink = career === null || career === void 0 ? void 0 : (_career$jobDetails6 = career.jobDetails) === null || _career$jobDetails6 === void 0 ? void 0 : (_career$jobDetails6$r = _career$jobDetails6.rightPanel) === null || _career$jobDetails6$r === void 0 ? void 0 : (_career$jobDetails6$r2 = _career$jobDetails6$r.viewAllButton) === null || _career$jobDetails6$r2 === void 0 ? void 0 : (_career$jobDetails6$r3 = _career$jobDetails6$r2.link) === null || _career$jobDetails6$r3 === void 0 ? void 0 : _career$jobDetails6$r3[language];\n    const viewAllTextError = validateField(viewAllText, \"View All Text\");\n    if (viewAllTextError) errors[\"view_all_text\"] = viewAllTextError;\n    const viewAllLinkError = validateField(viewAllLink, \"View All Link\");\n    if (viewAllLinkError) errors[\"view_all_link\"] = viewAllLinkError;\n\n    // Bottom button validation\n    const bottomButton = career === null || career === void 0 ? void 0 : (_career$jobDetails7 = career.jobDetails) === null || _career$jobDetails7 === void 0 ? void 0 : (_career$jobDetails7$b = _career$jobDetails7.button) === null || _career$jobDetails7$b === void 0 ? void 0 : _career$jobDetails7$b[language];\n    const bottomButtonError = validateField(bottomButton, \"Bottom Button\");\n    if (bottomButtonError) errors[\"bottom_button\"] = bottomButtonError;\n    setValidationErrors(errors);\n    setIsValidating(false);\n    return Object.keys(errors).length === 0;\n  };\n\n  // Expose validation function globally\n  useEffect(() => {\n    window.validateCareerDetailContent = validateAllFields;\n    return () => {\n      delete window.validateCareerDetailContent;\n    };\n  }, [currentContent, language, careerIndex]);\n  const addExtraSummary = () => {\n    // dispatch(updateCardAndItemsArray(\n    //     {\n    //         insert: {\n    //             title: {\n    //                 ar: \"\",\n    //                 en: \"\"\n    //             },\n    //             content: {\n    //                 ar: \"\",\n    //                 en: \"\"\n    //             }\n    //         },\n    //         careerIndex,\n    //         context: \"careerDetails\",\n    //         operation: 'add'\n    //     }\n    // ))\n  };\n  useEffect(() => {\n    dispatch(updateMainContent({\n      currentPath: \"careerDetails\",\n      payload: content === null || content === void 0 ? void 0 : content.careerDetails\n    }));\n  }, []);\n\n  // If no careerId is provided or career not found, show error message\n  if (!careerId) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-4 text-center\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-red-500 font-semibold\",\n        children: \"No Career ID provided\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 174,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-600\",\n        children: \"Please select a specific career to edit.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 175,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 173,\n      columnNumber: 13\n    }, this);\n  }\n  if (careerIndex === -1) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-4 text-center\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-red-500 font-semibold\",\n        children: \"Career not found\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 183,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-600\",\n        children: [\"The career with ID \\\"\", careerId, \"\\\" was not found.\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 184,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 182,\n      columnNumber: 13\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(FileUploader, {\n      id: \"CareerDetailsIDReference\" + careerId,\n      label: \"Rerference doc\",\n      fileName: \"Upload your file...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 191,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(ContentSection, {\n      currentPath: currentPath,\n      Heading: \"Banner\",\n      inputs: [{\n        input: \"input\",\n        label: \"Heading/title\",\n        updateType: \"title\",\n        errorMessage: validationErrors[\"banner_title\"],\n        errorKey: \"banner_title\"\n      }, {\n        input: \"input\",\n        label: \"Description\",\n        updateType: \"subTitle\",\n        maxLength: 23,\n        errorMessage: validationErrors[\"banner_subtitle\"],\n        errorKey: \"banner_subtitle\"\n      }, {\n        input: \"input\",\n        label: \"Button Text\",\n        updateType: \"button\",\n        errorMessage: validationErrors[\"banner_button\"],\n        errorKey: \"banner_button\"\n      }\n      // { input: \"input\", label: \"Url\", updateType: \"url\" },\n      ],\n      inputFiles: [{\n        label: \"Backround Image\",\n        id: \"careerBanner/\" + careerId,\n        errorMessage: validationErrors[\"banner_image\"],\n        errorKey: \"banner_image\"\n      }],\n      section: \"banner\",\n      language: language,\n      currentContent: currentContent,\n      projectId: careerIndex + 1,\n      clearValidationError: clearValidationError\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 193,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-4 border-b\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: `font-semibold text-[1.25rem] mb-4`,\n        children: \"Job Details Left Panel\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 212,\n        columnNumber: 17\n      }, this), currentContent === null || currentContent === void 0 ? void 0 : (_currentContent$caree = currentContent[careerIndex]) === null || _currentContent$caree === void 0 ? void 0 : (_currentContent$caree2 = _currentContent$caree.jobDetails) === null || _currentContent$caree2 === void 0 ? void 0 : (_currentContent$caree3 = _currentContent$caree2.leftPanel) === null || _currentContent$caree3 === void 0 ? void 0 : (_currentContent$caree4 = _currentContent$caree3.sections) === null || _currentContent$caree4 === void 0 ? void 0 : _currentContent$caree4.map((element, index, a) => {\n        const isLast = index === a.length - 1;\n        return /*#__PURE__*/_jsxDEV(DynamicContentSection, {\n          currentPath: currentPath,\n          subHeading: \"Section \" + (index + 1),\n          inputs: [{\n            input: \"input\",\n            label: \"Title\",\n            updateType: \"title\",\n            errorMessage: validationErrors[`left_section_${index}_title`],\n            errorKey: `left_section_${index}_title`\n          }, {\n            input: \"textarea\",\n            label: \"Description\",\n            updateType: \"content\",\n            errorMessage: validationErrors[`left_section_${index}_content`],\n            errorKey: `left_section_${index}_content`\n          }],\n          section: \"jobDetails\",\n          subSection: \"leftPanel\",\n          subSectionsProMax: \"sections\",\n          index: index,\n          language: language,\n          currentContent: currentContent,\n          projectId: careerIndex + 1,\n          careerIndex: careerIndex,\n          careerId: careerId,\n          allowRemoval: true,\n          isBorder: false,\n          clearValidationError: clearValidationError\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 217,\n          columnNumber: 29\n        }, this);\n      }), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"text-blue-500 cursor-pointer mb-3\",\n        onClick: addExtraSummary,\n        children: \"Add More Section...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 240,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 211,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-4 border-b\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: `font-semibold text-[1.25rem] mb-4`,\n        children: \"Job Details Right Panel\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 247,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-6\",\n        children: /*#__PURE__*/_jsxDEV(ContentSection, {\n          currentPath: currentPath,\n          subHeading: \"Top Section\",\n          inputs: [{\n            input: \"input\",\n            label: \"Heading/title\",\n            updateType: \"title\",\n            maxLength: 15,\n            errorMessage: validationErrors[\"right_panel_title\"],\n            errorKey: \"right_panel_title\"\n          }, {\n            input: \"input\",\n            label: \"Button Text\",\n            updateType: \"button\",\n            maxLength: 18,\n            errorMessage: validationErrors[\"right_panel_button\"],\n            errorKey: \"right_panel_button\"\n          }\n          // { input: \"input\", label: \"Url\", updateType: \"url\" },\n          ],\n          section: \"jobDetails\",\n          subSection: \"rightPanel\",\n          language: language,\n          currentContent: currentContent,\n          projectId: careerIndex + 1,\n          careerId: careerId,\n          clearValidationError: clearValidationError\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 249,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 248,\n        columnNumber: 17\n      }, this), currentContent === null || currentContent === void 0 ? void 0 : (_currentContent$caree5 = currentContent[careerIndex]) === null || _currentContent$caree5 === void 0 ? void 0 : (_currentContent$caree6 = _currentContent$caree5.jobDetails) === null || _currentContent$caree6 === void 0 ? void 0 : (_currentContent$caree7 = _currentContent$caree6.rightPanel) === null || _currentContent$caree7 === void 0 ? void 0 : (_currentContent$caree8 = _currentContent$caree7.tailwraps) === null || _currentContent$caree8 === void 0 ? void 0 : _currentContent$caree8.map((element, index, a) => {\n        const isLast = index === a.length - 1;\n        return /*#__PURE__*/_jsxDEV(DynamicContentSection, {\n          currentPath: currentPath,\n          subHeading: \"Section \" + (index + 1),\n          inputs: [{\n            input: \"input\",\n            label: \"Title\",\n            updateType: \"title\",\n            maxLength: 15,\n            errorMessage: validationErrors[`tailwrap_${index}_title`],\n            errorKey: `tailwrap_${index}_title`\n          }, {\n            input: \"input\",\n            label: \"Description\",\n            updateType: \"description\",\n            maxLength: 19,\n            errorMessage: validationErrors[`tailwrap_${index}_description`],\n            errorKey: `tailwrap_${index}_description`\n          }],\n          inputFiles: [{\n            label: \"icon\",\n            id: `careerRightPanel/${careerId}/${index}`,\n            errorMessage: validationErrors[`tailwrap_${index}_icon`],\n            errorKey: `tailwrap_${index}_icon`\n          }],\n          section: \"jobDetails\",\n          subSection: \"rightPanel\",\n          subSectionsProMax: \"tailwraps\",\n          index: index,\n          language: language,\n          currentContent: currentContent,\n          projectId: careerIndex + 1,\n          careerIndex: careerIndex,\n          careerId: careerId,\n          isBorder: false,\n          clearValidationError: clearValidationError\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 270,\n          columnNumber: 29\n        }, this);\n      }), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: /*#__PURE__*/_jsxDEV(ContentSection, {\n          currentPath: currentPath,\n          subHeading: \"Right panel redirection\",\n          inputs: [{\n            input: \"input\",\n            label: \"Heading/title\",\n            updateType: \"text\",\n            errorMessage: validationErrors[\"view_all_text\"],\n            errorKey: \"view_all_text\"\n          }, {\n            input: \"input\",\n            label: \"Link\",\n            updateType: \"link\",\n            errorMessage: validationErrors[\"view_all_link\"],\n            errorKey: \"view_all_link\"\n          }\n          // { input: \"input\", label: \"Url\", updateType: \"url\" },\n          ],\n          section: \"jobDetails\",\n          subSection: \"rightPanel\",\n          subSectionsProMax: \"viewAllButton\",\n          language: language,\n          currentContent: currentContent,\n          projectId: careerIndex + 1,\n          careerId: careerId,\n          clearValidationError: clearValidationError\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 296,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 295,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 246,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(ContentSection, {\n      currentPath: currentPath,\n      subHeading: \"Bottom Button\",\n      inputs: [{\n        input: \"input\",\n        label: \"Button\",\n        updateType: \"button\",\n        errorMessage: validationErrors[\"bottom_button\"],\n        errorKey: \"bottom_button\"\n      }],\n      section: \"jobDetails\",\n      language: language,\n      currentContent: currentContent,\n      projectId: careerIndex + 1,\n      careerId: careerId,\n      clearValidationError: clearValidationError\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 317,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 190,\n    columnNumber: 9\n  }, this);\n};\n_s(CareerDetailManager, \"OaPJ6jUxWJZDObpS0MtCaUWGwFE=\", false, function () {\n  return [useDispatch];\n});\n_c = CareerDetailManager;\nexport default CareerDetailManager;\nvar _c;\n$RefreshReg$(_c, \"CareerDetailManager\");", "map": {"version": 3, "names": ["useEffect", "useState", "FileUploader", "ContentSection", "useDispatch", "update<PERSON>ain<PERSON><PERSON>nt", "updateTheProjectSummaryList", "content", "DynamicContentSection", "jsxDEV", "_jsxDEV", "CareerDetailManager", "careerId", "currentC<PERSON>nt", "currentPath", "language", "_s", "_currentContent$caree", "_currentContent$caree2", "_currentContent$caree3", "_currentContent$caree4", "_currentContent$caree5", "_currentContent$caree6", "_currentContent$caree7", "_currentContent$caree8", "dispatch", "careerIndex", "Array", "isArray", "findIndex", "e", "id", "validationErrors", "setValidationErrors", "isValidating", "setIsValidating", "clearValidationError", "<PERSON><PERSON><PERSON>", "prev", "newErrors", "validateField", "value", "fieldName", "trim", "validateImage", "imageUrl", "validate<PERSON><PERSON>y", "array", "<PERSON><PERSON><PERSON><PERSON>", "length", "validate<PERSON>ll<PERSON>ields", "_career$banner", "_career$banner$title", "_career$banner2", "_career$banner2$subTi", "_career$banner3", "_career$banner3$butto", "_career$banner4", "_career$banner4$image", "_career$banner4$image2", "_career$jobDetails", "_career$jobDetails$le", "_career$jobDetails2", "_career$jobDetails2$r", "_career$jobDetails2$r2", "_career$jobDetails3", "_career$jobDetails3$r", "_career$jobDetails3$r2", "_career$jobDetails4", "_career$jobDetails4$r", "_career$jobDetails5", "_career$jobDetails5$r", "_career$jobDetails5$r2", "_career$jobDetails5$r3", "_career$jobDetails6", "_career$jobDetails6$r", "_career$jobDetails6$r2", "_career$jobDetails6$r3", "_career$jobDetails7", "_career$jobDetails7$b", "errors", "career", "bannerTitle", "banner", "title", "bannerSubTitle", "subTitle", "bannerButton", "button", "bannerImage", "images", "url", "bannerTitleError", "bannerSubTitleError", "bannerButtonError", "bannerImageError", "leftPanelSections", "jobDetails", "leftPanel", "sections", "for<PERSON>ach", "section", "index", "_section$title", "_section$content", "sectionTitle", "sectionContent", "sectionTitleError", "sectionContentError", "rightPanelTitle", "rightPanel", "rightPanelButton", "rightPanelTitleError", "rightPanelButtonError", "tailwraps", "tailwrap", "_tailwrap$title", "_tailwrap$description", "_tailwrap$images", "_tailwrap$images$", "tailwrapTitle", "tailwrapDescription", "description", "tailwrapIcon", "tailwrapTitleError", "tailwrapDescriptionError", "tailwrapIconError", "viewAllText", "viewAllButton", "text", "viewAllLink", "link", "viewAllTextError", "viewAllLinkError", "bottomButton", "bottomButtonError", "Object", "keys", "window", "validateCareerDetailContent", "addExtraSummary", "payload", "careerDetails", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "label", "Heading", "inputs", "input", "updateType", "errorMessage", "max<PERSON><PERSON><PERSON>", "inputFiles", "projectId", "map", "element", "a", "isLast", "subHeading", "subSection", "subSectionsProMax", "allowRemoval", "isBorder", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Akshay/LOOP_PROJECTS/shade_cms/dashboard/src/features/Resources/components/contentmanager/CMforDetails/CareerDetailManager.jsx"], "sourcesContent": ["import { useEffect, useState } from \"react\"\nimport FileUploader from \"../../../../../components/Input/InputFileUploader\"\nimport ContentSection from \"../../breakUI/ContentSections\"\nimport { useDispatch } from \"react-redux\"\nimport { updateMainContent, updateTheProjectSummaryList } from \"../../../../common/homeContentSlice\"\nimport content from \"../../websiteComponent/content.json\"\nimport DynamicContentSection from \"../../breakUI/DynamicContentSection\"\n\nconst CareerDetailManager = ({ careerId, currentContent, currentPath, language }) => {\n    const dispatch = useDispatch();\n    const careerIndex = Array.isArray(currentContent) ? currentContent.findIndex(e => e.id == careerId) : -1\n\n    // Validation states\n    const [validationErrors, setValidationErrors] = useState({})\n    const [isValidating, setIsValidating] = useState(false)\n\n    // Function to clear specific validation error\n    const clearValidationError = (errorKey) => {\n        setValidationErrors(prev => {\n            const newErrors = { ...prev }\n            delete newErrors[errorKey]\n            return newErrors\n        })\n    }\n\n    // Validation functions\n    const validateField = (value, fieldName) => {\n        if (!value || (typeof value === 'string' && value.trim() === '')) {\n            return \"Required\"\n        }\n        return null\n    }\n\n    const validateImage = (imageUrl, fieldName) => {\n        if (!imageUrl || imageUrl.trim() === '') {\n            return \"Required\"\n        }\n        return null\n    }\n\n    const validateArray = (array, fieldName, minLength = 1) => {\n        if (!array || array.length < minLength) {\n            return \"Required\"\n        }\n        return null\n    }\n\n    // Comprehensive validation function\n    const validateAllFields = () => {\n        setIsValidating(true)\n        const errors = {}\n\n        const career = currentContent?.[careerIndex]\n\n        // Banner section validation\n        const bannerTitle = career?.banner?.title?.[language]\n        const bannerSubTitle = career?.banner?.subTitle?.[language]\n        const bannerButton = career?.banner?.button?.[language]\n        const bannerImage = career?.banner?.images?.[0]?.url\n\n        const bannerTitleError = validateField(bannerTitle, \"Banner Title\")\n        if (bannerTitleError) errors[\"banner_title\"] = bannerTitleError\n\n        const bannerSubTitleError = validateField(bannerSubTitle, \"Banner Sub Title\")\n        if (bannerSubTitleError) errors[\"banner_subtitle\"] = bannerSubTitleError\n\n        const bannerButtonError = validateField(bannerButton, \"Banner Button\")\n        if (bannerButtonError) errors[\"banner_button\"] = bannerButtonError\n\n        const bannerImageError = validateImage(bannerImage, \"Banner Image\")\n        if (bannerImageError) errors[\"banner_image\"] = bannerImageError\n\n        // Left panel sections validation\n        const leftPanelSections = career?.jobDetails?.leftPanel?.sections\n        if (leftPanelSections && leftPanelSections.length > 0) {\n            leftPanelSections.forEach((section, index) => {\n                const sectionTitle = section?.title?.[language]\n                const sectionContent = section?.content?.[language]\n\n                const sectionTitleError = validateField(sectionTitle, `Left Panel Section ${index + 1} Title`)\n                if (sectionTitleError) errors[`left_section_${index}_title`] = sectionTitleError\n\n                const sectionContentError = validateField(sectionContent, `Left Panel Section ${index + 1} Content`)\n                if (sectionContentError) errors[`left_section_${index}_content`] = sectionContentError\n            })\n        }\n\n        // Right panel top section validation\n        const rightPanelTitle = career?.jobDetails?.rightPanel?.title?.[language]\n        const rightPanelButton = career?.jobDetails?.rightPanel?.button?.[language]\n\n        const rightPanelTitleError = validateField(rightPanelTitle, \"Right Panel Title\")\n        if (rightPanelTitleError) errors[\"right_panel_title\"] = rightPanelTitleError\n\n        const rightPanelButtonError = validateField(rightPanelButton, \"Right Panel Button\")\n        if (rightPanelButtonError) errors[\"right_panel_button\"] = rightPanelButtonError\n\n        // Right panel tailwraps validation\n        const tailwraps = career?.jobDetails?.rightPanel?.tailwraps\n        if (tailwraps && tailwraps.length > 0) {\n            tailwraps.forEach((tailwrap, index) => {\n                const tailwrapTitle = tailwrap?.title?.[language]\n                const tailwrapDescription = tailwrap?.description?.[language]\n                const tailwrapIcon = tailwrap?.images?.[0]?.url\n\n                const tailwrapTitleError = validateField(tailwrapTitle, `Tailwrap ${index + 1} Title`)\n                if (tailwrapTitleError) errors[`tailwrap_${index}_title`] = tailwrapTitleError\n\n                const tailwrapDescriptionError = validateField(tailwrapDescription, `Tailwrap ${index + 1} Description`)\n                if (tailwrapDescriptionError) errors[`tailwrap_${index}_description`] = tailwrapDescriptionError\n\n                const tailwrapIconError = validateImage(tailwrapIcon, `Tailwrap ${index + 1} Icon`)\n                if (tailwrapIconError) errors[`tailwrap_${index}_icon`] = tailwrapIconError\n            })\n        }\n\n        // Right panel view all button validation\n        const viewAllText = career?.jobDetails?.rightPanel?.viewAllButton?.text?.[language]\n        const viewAllLink = career?.jobDetails?.rightPanel?.viewAllButton?.link?.[language]\n\n        const viewAllTextError = validateField(viewAllText, \"View All Text\")\n        if (viewAllTextError) errors[\"view_all_text\"] = viewAllTextError\n\n        const viewAllLinkError = validateField(viewAllLink, \"View All Link\")\n        if (viewAllLinkError) errors[\"view_all_link\"] = viewAllLinkError\n\n        // Bottom button validation\n        const bottomButton = career?.jobDetails?.button?.[language]\n        const bottomButtonError = validateField(bottomButton, \"Bottom Button\")\n        if (bottomButtonError) errors[\"bottom_button\"] = bottomButtonError\n\n        setValidationErrors(errors)\n        setIsValidating(false)\n        return Object.keys(errors).length === 0\n    }\n\n    // Expose validation function globally\n    useEffect(() => {\n        window.validateCareerDetailContent = validateAllFields;\n        return () => {\n            delete window.validateCareerDetailContent;\n        };\n    }, [currentContent, language, careerIndex]);\n\n    const addExtraSummary = () => {\n        // dispatch(updateCardAndItemsArray(\n        //     {\n        //         insert: {\n        //             title: {\n        //                 ar: \"\",\n        //                 en: \"\"\n        //             },\n        //             content: {\n        //                 ar: \"\",\n        //                 en: \"\"\n        //             }\n        //         },\n        //         careerIndex,\n        //         context: \"careerDetails\",\n        //         operation: 'add'\n        //     }\n        // ))\n    }\n\n    useEffect(() => {\n\n        dispatch(updateMainContent({ currentPath: \"careerDetails\", payload: (content?.careerDetails) }))\n    }, [])\n\n    // If no careerId is provided or career not found, show error message\n    if (!careerId) {\n        return (\n            <div className=\"p-4 text-center\">\n                <h3 className=\"text-red-500 font-semibold\">No Career ID provided</h3>\n                <p className=\"text-gray-600\">Please select a specific career to edit.</p>\n            </div>\n        )\n    }\n\n    if (careerIndex === -1) {\n        return (\n            <div className=\"p-4 text-center\">\n                <h3 className=\"text-red-500 font-semibold\">Career not found</h3>\n                <p className=\"text-gray-600\">The career with ID \"{careerId}\" was not found.</p>\n            </div>\n        )\n    }\n\n    return (\n        <div>\n            <FileUploader id={\"CareerDetailsIDReference\" + careerId} label={\"Rerference doc\"} fileName={\"Upload your file...\"} />\n            {/** Hero Banner */}\n            <ContentSection\n                currentPath={currentPath}\n                Heading={\"Banner\"}\n                inputs={[\n                    { input: \"input\", label: \"Heading/title\", updateType: \"title\", errorMessage: validationErrors[\"banner_title\"], errorKey: \"banner_title\" },\n                    { input: \"input\", label: \"Description\", updateType: \"subTitle\", maxLength: 23, errorMessage: validationErrors[\"banner_subtitle\"], errorKey: \"banner_subtitle\" },\n                    { input: \"input\", label: \"Button Text\", updateType: \"button\", errorMessage: validationErrors[\"banner_button\"], errorKey: \"banner_button\" },\n                    // { input: \"input\", label: \"Url\", updateType: \"url\" },\n                ]}\n                inputFiles={[{ label: \"Backround Image\", id: \"careerBanner/\" + (careerId), errorMessage: validationErrors[\"banner_image\"], errorKey: \"banner_image\" }]}\n                section={\"banner\"}\n                language={language}\n                currentContent={currentContent}\n                projectId={careerIndex + 1}\n                clearValidationError={clearValidationError}\n            />\n\n            {/* left panel */}\n            <div className=\"mt-4 border-b\">\n                <h3 className={`font-semibold text-[1.25rem] mb-4`}>Job Details Left Panel</h3>\n                {\n                    currentContent?.[careerIndex]?.jobDetails?.leftPanel?.sections?.map((element, index, a) => {\n                        const isLast = index === a.length - 1\n                        return (\n                            <DynamicContentSection key={index}\n                                currentPath={currentPath}\n                                subHeading={\"Section \" + (index + 1)}\n                                inputs={[\n                                    { input: \"input\", label: \"Title\", updateType: \"title\", errorMessage: validationErrors[`left_section_${index}_title`], errorKey: `left_section_${index}_title` },\n                                    { input: \"textarea\", label: \"Description\", updateType: \"content\", errorMessage: validationErrors[`left_section_${index}_content`], errorKey: `left_section_${index}_content` },\n                                ]}\n                                section={\"jobDetails\"}\n                                subSection={\"leftPanel\"}\n                                subSectionsProMax={\"sections\"}\n                                index={index}\n                                language={language}\n                                currentContent={currentContent}\n                                projectId={careerIndex + 1}\n                                careerIndex={careerIndex}\n                                careerId={careerId}\n                                allowRemoval={true}\n                                isBorder={false}\n                                clearValidationError={clearValidationError}\n                            />\n                        )\n                    })\n                }\n                <button className=\"text-blue-500 cursor-pointer mb-3\"\n                    onClick={addExtraSummary}\n                >Add More Section...</button>\n            </div>\n\n            {/* right panel */}\n            <div className=\"mt-4 border-b\">\n                <h3 className={`font-semibold text-[1.25rem] mb-4`}>Job Details Right Panel</h3>\n                <div className=\"mb-6\">\n                    <ContentSection\n                        currentPath={currentPath}\n                        subHeading={\"Top Section\"}\n                        inputs={[\n                            { input: \"input\", label: \"Heading/title\", updateType: \"title\", maxLength: 15, errorMessage: validationErrors[\"right_panel_title\"], errorKey: \"right_panel_title\" },\n                            { input: \"input\", label: \"Button Text\", updateType: \"button\", maxLength: 18, errorMessage: validationErrors[\"right_panel_button\"], errorKey: \"right_panel_button\" },\n                            // { input: \"input\", label: \"Url\", updateType: \"url\" },\n                        ]}\n                        section={\"jobDetails\"}\n                        subSection={\"rightPanel\"}\n                        language={language}\n                        currentContent={currentContent}\n                        projectId={careerIndex + 1}\n                        careerId={careerId}\n                        clearValidationError={clearValidationError}\n                    />\n                </div>\n                {\n                    currentContent?.[careerIndex]?.jobDetails?.rightPanel?.tailwraps?.map((element, index, a) => {\n                        const isLast = index === a.length - 1\n                        return (\n                            <DynamicContentSection key={index}\n                                currentPath={currentPath}\n                                subHeading={\"Section \" + (index + 1)}\n                                inputs={[\n                                    { input: \"input\", label: \"Title\", updateType: \"title\", maxLength: 15, errorMessage: validationErrors[`tailwrap_${index}_title`], errorKey: `tailwrap_${index}_title` },\n                                    { input: \"input\", label: \"Description\", updateType: \"description\", maxLength: 19, errorMessage: validationErrors[`tailwrap_${index}_description`], errorKey: `tailwrap_${index}_description` },\n                                ]}\n                                inputFiles={[{ label: \"icon\", id: `careerRightPanel/${careerId}/${index}`, errorMessage: validationErrors[`tailwrap_${index}_icon`], errorKey: `tailwrap_${index}_icon` }]}\n                                section={\"jobDetails\"}\n                                subSection={\"rightPanel\"}\n                                subSectionsProMax={\"tailwraps\"}\n                                index={index}\n                                language={language}\n                                currentContent={currentContent}\n                                projectId={careerIndex + 1}\n                                careerIndex={careerIndex}\n                                careerId={careerId}\n                                isBorder={false}\n                                clearValidationError={clearValidationError}\n                            />\n                        )\n                    })\n                }\n\n                {/* right panel redirection link */}\n                <div>\n                    <ContentSection\n                        currentPath={currentPath}\n                        subHeading={\"Right panel redirection\"}\n                        inputs={[\n                            { input: \"input\", label: \"Heading/title\", updateType: \"text\", errorMessage: validationErrors[\"view_all_text\"], errorKey: \"view_all_text\" },\n                            { input: \"input\", label: \"Link\", updateType: \"link\", errorMessage: validationErrors[\"view_all_link\"], errorKey: \"view_all_link\" },\n                            // { input: \"input\", label: \"Url\", updateType: \"url\" },\n                        ]}\n                        section={\"jobDetails\"}\n                        subSection={\"rightPanel\"}\n                        subSectionsProMax={\"viewAllButton\"}\n                        language={language}\n                        currentContent={currentContent}\n                        projectId={careerIndex + 1}\n                        careerId={careerId}\n                        clearValidationError={clearValidationError}\n                    />\n                </div>\n            </div>\n\n            {/* the last button */}\n            <ContentSection\n                currentPath={currentPath}\n                subHeading={\"Bottom Button\"}\n                inputs={[\n                    { input: \"input\", label: \"Button\", updateType: \"button\", errorMessage: validationErrors[\"bottom_button\"], errorKey: \"bottom_button\" },\n                ]}\n                section={\"jobDetails\"}\n                language={language}\n                currentContent={currentContent}\n                projectId={careerIndex + 1}\n                careerId={careerId}\n                clearValidationError={clearValidationError}\n            />\n\n        </div>\n    )\n}\n\nexport default CareerDetailManager"], "mappings": ";;AAAA,SAASA,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC3C,OAAOC,YAAY,MAAM,mDAAmD;AAC5E,OAAOC,cAAc,MAAM,+BAA+B;AAC1D,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,iBAAiB,EAAEC,2BAA2B,QAAQ,qCAAqC;AACpG,OAAOC,OAAO,MAAM,qCAAqC;AACzD,OAAOC,qBAAqB,MAAM,qCAAqC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAEvE,MAAMC,mBAAmB,GAAGA,CAAC;EAAEC,QAAQ;EAAEC,cAAc;EAAEC,WAAW;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;EACjF,MAAMC,QAAQ,GAAGrB,WAAW,CAAC,CAAC;EAC9B,MAAMsB,WAAW,GAAGC,KAAK,CAACC,OAAO,CAACf,cAAc,CAAC,GAAGA,cAAc,CAACgB,SAAS,CAACC,CAAC,IAAIA,CAAC,CAACC,EAAE,IAAInB,QAAQ,CAAC,GAAG,CAAC,CAAC;;EAExG;EACA,MAAM,CAACoB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGhC,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC5D,MAAM,CAACiC,YAAY,EAAEC,eAAe,CAAC,GAAGlC,QAAQ,CAAC,KAAK,CAAC;;EAEvD;EACA,MAAMmC,oBAAoB,GAAIC,QAAQ,IAAK;IACvCJ,mBAAmB,CAACK,IAAI,IAAI;MACxB,MAAMC,SAAS,GAAG;QAAE,GAAGD;MAAK,CAAC;MAC7B,OAAOC,SAAS,CAACF,QAAQ,CAAC;MAC1B,OAAOE,SAAS;IACpB,CAAC,CAAC;EACN,CAAC;;EAED;EACA,MAAMC,aAAa,GAAGA,CAACC,KAAK,EAAEC,SAAS,KAAK;IACxC,IAAI,CAACD,KAAK,IAAK,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACE,IAAI,CAAC,CAAC,KAAK,EAAG,EAAE;MAC9D,OAAO,UAAU;IACrB;IACA,OAAO,IAAI;EACf,CAAC;EAED,MAAMC,aAAa,GAAGA,CAACC,QAAQ,EAAEH,SAAS,KAAK;IAC3C,IAAI,CAACG,QAAQ,IAAIA,QAAQ,CAACF,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MACrC,OAAO,UAAU;IACrB;IACA,OAAO,IAAI;EACf,CAAC;EAED,MAAMG,aAAa,GAAGA,CAACC,KAAK,EAAEL,SAAS,EAAEM,SAAS,GAAG,CAAC,KAAK;IACvD,IAAI,CAACD,KAAK,IAAIA,KAAK,CAACE,MAAM,GAAGD,SAAS,EAAE;MACpC,OAAO,UAAU;IACrB;IACA,OAAO,IAAI;EACf,CAAC;;EAED;EACA,MAAME,iBAAiB,GAAGA,CAAA,KAAM;IAAA,IAAAC,cAAA,EAAAC,oBAAA,EAAAC,eAAA,EAAAC,qBAAA,EAAAC,eAAA,EAAAC,qBAAA,EAAAC,eAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,kBAAA,EAAAC,qBAAA,EAAAC,mBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,mBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,mBAAA,EAAAC,qBAAA,EAAAC,mBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,mBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,mBAAA,EAAAC,qBAAA;IAC5B5C,eAAe,CAAC,IAAI,CAAC;IACrB,MAAM6C,MAAM,GAAG,CAAC,CAAC;IAEjB,MAAMC,MAAM,GAAGpE,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAGa,WAAW,CAAC;;IAE5C;IACA,MAAMwD,WAAW,GAAGD,MAAM,aAANA,MAAM,wBAAA9B,cAAA,GAAN8B,MAAM,CAAEE,MAAM,cAAAhC,cAAA,wBAAAC,oBAAA,GAAdD,cAAA,CAAgBiC,KAAK,cAAAhC,oBAAA,uBAArBA,oBAAA,CAAwBrC,QAAQ,CAAC;IACrD,MAAMsE,cAAc,GAAGJ,MAAM,aAANA,MAAM,wBAAA5B,eAAA,GAAN4B,MAAM,CAAEE,MAAM,cAAA9B,eAAA,wBAAAC,qBAAA,GAAdD,eAAA,CAAgBiC,QAAQ,cAAAhC,qBAAA,uBAAxBA,qBAAA,CAA2BvC,QAAQ,CAAC;IAC3D,MAAMwE,YAAY,GAAGN,MAAM,aAANA,MAAM,wBAAA1B,eAAA,GAAN0B,MAAM,CAAEE,MAAM,cAAA5B,eAAA,wBAAAC,qBAAA,GAAdD,eAAA,CAAgBiC,MAAM,cAAAhC,qBAAA,uBAAtBA,qBAAA,CAAyBzC,QAAQ,CAAC;IACvD,MAAM0E,WAAW,GAAGR,MAAM,aAANA,MAAM,wBAAAxB,eAAA,GAANwB,MAAM,CAAEE,MAAM,cAAA1B,eAAA,wBAAAC,qBAAA,GAAdD,eAAA,CAAgBiC,MAAM,cAAAhC,qBAAA,wBAAAC,sBAAA,GAAtBD,qBAAA,CAAyB,CAAC,CAAC,cAAAC,sBAAA,uBAA3BA,sBAAA,CAA6BgC,GAAG;IAEpD,MAAMC,gBAAgB,GAAGpD,aAAa,CAAC0C,WAAW,EAAE,cAAc,CAAC;IACnE,IAAIU,gBAAgB,EAAEZ,MAAM,CAAC,cAAc,CAAC,GAAGY,gBAAgB;IAE/D,MAAMC,mBAAmB,GAAGrD,aAAa,CAAC6C,cAAc,EAAE,kBAAkB,CAAC;IAC7E,IAAIQ,mBAAmB,EAAEb,MAAM,CAAC,iBAAiB,CAAC,GAAGa,mBAAmB;IAExE,MAAMC,iBAAiB,GAAGtD,aAAa,CAAC+C,YAAY,EAAE,eAAe,CAAC;IACtE,IAAIO,iBAAiB,EAAEd,MAAM,CAAC,eAAe,CAAC,GAAGc,iBAAiB;IAElE,MAAMC,gBAAgB,GAAGnD,aAAa,CAAC6C,WAAW,EAAE,cAAc,CAAC;IACnE,IAAIM,gBAAgB,EAAEf,MAAM,CAAC,cAAc,CAAC,GAAGe,gBAAgB;;IAE/D;IACA,MAAMC,iBAAiB,GAAGf,MAAM,aAANA,MAAM,wBAAArB,kBAAA,GAANqB,MAAM,CAAEgB,UAAU,cAAArC,kBAAA,wBAAAC,qBAAA,GAAlBD,kBAAA,CAAoBsC,SAAS,cAAArC,qBAAA,uBAA7BA,qBAAA,CAA+BsC,QAAQ;IACjE,IAAIH,iBAAiB,IAAIA,iBAAiB,CAAC/C,MAAM,GAAG,CAAC,EAAE;MACnD+C,iBAAiB,CAACI,OAAO,CAAC,CAACC,OAAO,EAAEC,KAAK,KAAK;QAAA,IAAAC,cAAA,EAAAC,gBAAA;QAC1C,MAAMC,YAAY,GAAGJ,OAAO,aAAPA,OAAO,wBAAAE,cAAA,GAAPF,OAAO,CAAEjB,KAAK,cAAAmB,cAAA,uBAAdA,cAAA,CAAiBxF,QAAQ,CAAC;QAC/C,MAAM2F,cAAc,GAAGL,OAAO,aAAPA,OAAO,wBAAAG,gBAAA,GAAPH,OAAO,CAAE9F,OAAO,cAAAiG,gBAAA,uBAAhBA,gBAAA,CAAmBzF,QAAQ,CAAC;QAEnD,MAAM4F,iBAAiB,GAAGnE,aAAa,CAACiE,YAAY,EAAE,sBAAsBH,KAAK,GAAG,CAAC,QAAQ,CAAC;QAC9F,IAAIK,iBAAiB,EAAE3B,MAAM,CAAC,gBAAgBsB,KAAK,QAAQ,CAAC,GAAGK,iBAAiB;QAEhF,MAAMC,mBAAmB,GAAGpE,aAAa,CAACkE,cAAc,EAAE,sBAAsBJ,KAAK,GAAG,CAAC,UAAU,CAAC;QACpG,IAAIM,mBAAmB,EAAE5B,MAAM,CAAC,gBAAgBsB,KAAK,UAAU,CAAC,GAAGM,mBAAmB;MAC1F,CAAC,CAAC;IACN;;IAEA;IACA,MAAMC,eAAe,GAAG5B,MAAM,aAANA,MAAM,wBAAAnB,mBAAA,GAANmB,MAAM,CAAEgB,UAAU,cAAAnC,mBAAA,wBAAAC,qBAAA,GAAlBD,mBAAA,CAAoBgD,UAAU,cAAA/C,qBAAA,wBAAAC,sBAAA,GAA9BD,qBAAA,CAAgCqB,KAAK,cAAApB,sBAAA,uBAArCA,sBAAA,CAAwCjD,QAAQ,CAAC;IACzE,MAAMgG,gBAAgB,GAAG9B,MAAM,aAANA,MAAM,wBAAAhB,mBAAA,GAANgB,MAAM,CAAEgB,UAAU,cAAAhC,mBAAA,wBAAAC,qBAAA,GAAlBD,mBAAA,CAAoB6C,UAAU,cAAA5C,qBAAA,wBAAAC,sBAAA,GAA9BD,qBAAA,CAAgCsB,MAAM,cAAArB,sBAAA,uBAAtCA,sBAAA,CAAyCpD,QAAQ,CAAC;IAE3E,MAAMiG,oBAAoB,GAAGxE,aAAa,CAACqE,eAAe,EAAE,mBAAmB,CAAC;IAChF,IAAIG,oBAAoB,EAAEhC,MAAM,CAAC,mBAAmB,CAAC,GAAGgC,oBAAoB;IAE5E,MAAMC,qBAAqB,GAAGzE,aAAa,CAACuE,gBAAgB,EAAE,oBAAoB,CAAC;IACnF,IAAIE,qBAAqB,EAAEjC,MAAM,CAAC,oBAAoB,CAAC,GAAGiC,qBAAqB;;IAE/E;IACA,MAAMC,SAAS,GAAGjC,MAAM,aAANA,MAAM,wBAAAb,mBAAA,GAANa,MAAM,CAAEgB,UAAU,cAAA7B,mBAAA,wBAAAC,qBAAA,GAAlBD,mBAAA,CAAoB0C,UAAU,cAAAzC,qBAAA,uBAA9BA,qBAAA,CAAgC6C,SAAS;IAC3D,IAAIA,SAAS,IAAIA,SAAS,CAACjE,MAAM,GAAG,CAAC,EAAE;MACnCiE,SAAS,CAACd,OAAO,CAAC,CAACe,QAAQ,EAAEb,KAAK,KAAK;QAAA,IAAAc,eAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,iBAAA;QACnC,MAAMC,aAAa,GAAGL,QAAQ,aAARA,QAAQ,wBAAAC,eAAA,GAARD,QAAQ,CAAE/B,KAAK,cAAAgC,eAAA,uBAAfA,eAAA,CAAkBrG,QAAQ,CAAC;QACjD,MAAM0G,mBAAmB,GAAGN,QAAQ,aAARA,QAAQ,wBAAAE,qBAAA,GAARF,QAAQ,CAAEO,WAAW,cAAAL,qBAAA,uBAArBA,qBAAA,CAAwBtG,QAAQ,CAAC;QAC7D,MAAM4G,YAAY,GAAGR,QAAQ,aAARA,QAAQ,wBAAAG,gBAAA,GAARH,QAAQ,CAAEzB,MAAM,cAAA4B,gBAAA,wBAAAC,iBAAA,GAAhBD,gBAAA,CAAmB,CAAC,CAAC,cAAAC,iBAAA,uBAArBA,iBAAA,CAAuB5B,GAAG;QAE/C,MAAMiC,kBAAkB,GAAGpF,aAAa,CAACgF,aAAa,EAAE,YAAYlB,KAAK,GAAG,CAAC,QAAQ,CAAC;QACtF,IAAIsB,kBAAkB,EAAE5C,MAAM,CAAC,YAAYsB,KAAK,QAAQ,CAAC,GAAGsB,kBAAkB;QAE9E,MAAMC,wBAAwB,GAAGrF,aAAa,CAACiF,mBAAmB,EAAE,YAAYnB,KAAK,GAAG,CAAC,cAAc,CAAC;QACxG,IAAIuB,wBAAwB,EAAE7C,MAAM,CAAC,YAAYsB,KAAK,cAAc,CAAC,GAAGuB,wBAAwB;QAEhG,MAAMC,iBAAiB,GAAGlF,aAAa,CAAC+E,YAAY,EAAE,YAAYrB,KAAK,GAAG,CAAC,OAAO,CAAC;QACnF,IAAIwB,iBAAiB,EAAE9C,MAAM,CAAC,YAAYsB,KAAK,OAAO,CAAC,GAAGwB,iBAAiB;MAC/E,CAAC,CAAC;IACN;;IAEA;IACA,MAAMC,WAAW,GAAG9C,MAAM,aAANA,MAAM,wBAAAX,mBAAA,GAANW,MAAM,CAAEgB,UAAU,cAAA3B,mBAAA,wBAAAC,qBAAA,GAAlBD,mBAAA,CAAoBwC,UAAU,cAAAvC,qBAAA,wBAAAC,sBAAA,GAA9BD,qBAAA,CAAgCyD,aAAa,cAAAxD,sBAAA,wBAAAC,sBAAA,GAA7CD,sBAAA,CAA+CyD,IAAI,cAAAxD,sBAAA,uBAAnDA,sBAAA,CAAsD1D,QAAQ,CAAC;IACnF,MAAMmH,WAAW,GAAGjD,MAAM,aAANA,MAAM,wBAAAP,mBAAA,GAANO,MAAM,CAAEgB,UAAU,cAAAvB,mBAAA,wBAAAC,qBAAA,GAAlBD,mBAAA,CAAoBoC,UAAU,cAAAnC,qBAAA,wBAAAC,sBAAA,GAA9BD,qBAAA,CAAgCqD,aAAa,cAAApD,sBAAA,wBAAAC,sBAAA,GAA7CD,sBAAA,CAA+CuD,IAAI,cAAAtD,sBAAA,uBAAnDA,sBAAA,CAAsD9D,QAAQ,CAAC;IAEnF,MAAMqH,gBAAgB,GAAG5F,aAAa,CAACuF,WAAW,EAAE,eAAe,CAAC;IACpE,IAAIK,gBAAgB,EAAEpD,MAAM,CAAC,eAAe,CAAC,GAAGoD,gBAAgB;IAEhE,MAAMC,gBAAgB,GAAG7F,aAAa,CAAC0F,WAAW,EAAE,eAAe,CAAC;IACpE,IAAIG,gBAAgB,EAAErD,MAAM,CAAC,eAAe,CAAC,GAAGqD,gBAAgB;;IAEhE;IACA,MAAMC,YAAY,GAAGrD,MAAM,aAANA,MAAM,wBAAAH,mBAAA,GAANG,MAAM,CAAEgB,UAAU,cAAAnB,mBAAA,wBAAAC,qBAAA,GAAlBD,mBAAA,CAAoBU,MAAM,cAAAT,qBAAA,uBAA1BA,qBAAA,CAA6BhE,QAAQ,CAAC;IAC3D,MAAMwH,iBAAiB,GAAG/F,aAAa,CAAC8F,YAAY,EAAE,eAAe,CAAC;IACtE,IAAIC,iBAAiB,EAAEvD,MAAM,CAAC,eAAe,CAAC,GAAGuD,iBAAiB;IAElEtG,mBAAmB,CAAC+C,MAAM,CAAC;IAC3B7C,eAAe,CAAC,KAAK,CAAC;IACtB,OAAOqG,MAAM,CAACC,IAAI,CAACzD,MAAM,CAAC,CAAC/B,MAAM,KAAK,CAAC;EAC3C,CAAC;;EAED;EACAjD,SAAS,CAAC,MAAM;IACZ0I,MAAM,CAACC,2BAA2B,GAAGzF,iBAAiB;IACtD,OAAO,MAAM;MACT,OAAOwF,MAAM,CAACC,2BAA2B;IAC7C,CAAC;EACL,CAAC,EAAE,CAAC9H,cAAc,EAAEE,QAAQ,EAAEW,WAAW,CAAC,CAAC;EAE3C,MAAMkH,eAAe,GAAGA,CAAA,KAAM;IAC1B;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EAAA,CACH;EAED5I,SAAS,CAAC,MAAM;IAEZyB,QAAQ,CAACpB,iBAAiB,CAAC;MAAES,WAAW,EAAE,eAAe;MAAE+H,OAAO,EAAGtI,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEuI;IAAe,CAAC,CAAC,CAAC;EACpG,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,IAAI,CAAClI,QAAQ,EAAE;IACX,oBACIF,OAAA;MAAKqI,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC5BtI,OAAA;QAAIqI,SAAS,EAAC,4BAA4B;QAAAC,QAAA,EAAC;MAAqB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACrE1I,OAAA;QAAGqI,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAC;MAAwC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxE,CAAC;EAEd;EAEA,IAAI1H,WAAW,KAAK,CAAC,CAAC,EAAE;IACpB,oBACIhB,OAAA;MAAKqI,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC5BtI,OAAA;QAAIqI,SAAS,EAAC,4BAA4B;QAAAC,QAAA,EAAC;MAAgB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAChE1I,OAAA;QAAGqI,SAAS,EAAC,eAAe;QAAAC,QAAA,GAAC,uBAAoB,EAACpI,QAAQ,EAAC,mBAAgB;MAAA;QAAAqI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9E,CAAC;EAEd;EAEA,oBACI1I,OAAA;IAAAsI,QAAA,gBACItI,OAAA,CAACR,YAAY;MAAC6B,EAAE,EAAE,0BAA0B,GAAGnB,QAAS;MAACyI,KAAK,EAAE,gBAAiB;MAACJ,QAAQ,EAAE;IAAsB;MAAAA,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAErH1I,OAAA,CAACP,cAAc;MACXW,WAAW,EAAEA,WAAY;MACzBwI,OAAO,EAAE,QAAS;MAClBC,MAAM,EAAE,CACJ;QAAEC,KAAK,EAAE,OAAO;QAAEH,KAAK,EAAE,eAAe;QAAEI,UAAU,EAAE,OAAO;QAAEC,YAAY,EAAE1H,gBAAgB,CAAC,cAAc,CAAC;QAAEK,QAAQ,EAAE;MAAe,CAAC,EACzI;QAAEmH,KAAK,EAAE,OAAO;QAAEH,KAAK,EAAE,aAAa;QAAEI,UAAU,EAAE,UAAU;QAAEE,SAAS,EAAE,EAAE;QAAED,YAAY,EAAE1H,gBAAgB,CAAC,iBAAiB,CAAC;QAAEK,QAAQ,EAAE;MAAkB,CAAC,EAC/J;QAAEmH,KAAK,EAAE,OAAO;QAAEH,KAAK,EAAE,aAAa;QAAEI,UAAU,EAAE,QAAQ;QAAEC,YAAY,EAAE1H,gBAAgB,CAAC,eAAe,CAAC;QAAEK,QAAQ,EAAE;MAAgB;MACzI;MAAA,CACF;MACFuH,UAAU,EAAE,CAAC;QAAEP,KAAK,EAAE,iBAAiB;QAAEtH,EAAE,EAAE,eAAe,GAAInB,QAAS;QAAE8I,YAAY,EAAE1H,gBAAgB,CAAC,cAAc,CAAC;QAAEK,QAAQ,EAAE;MAAe,CAAC,CAAE;MACvJgE,OAAO,EAAE,QAAS;MAClBtF,QAAQ,EAAEA,QAAS;MACnBF,cAAc,EAAEA,cAAe;MAC/BgJ,SAAS,EAAEnI,WAAW,GAAG,CAAE;MAC3BU,oBAAoB,EAAEA;IAAqB;MAAA6G,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9C,CAAC,eAGF1I,OAAA;MAAKqI,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC1BtI,OAAA;QAAIqI,SAAS,EAAE,mCAAoC;QAAAC,QAAA,EAAC;MAAsB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,EAE3EvI,cAAc,aAAdA,cAAc,wBAAAI,qBAAA,GAAdJ,cAAc,CAAGa,WAAW,CAAC,cAAAT,qBAAA,wBAAAC,sBAAA,GAA7BD,qBAAA,CAA+BgF,UAAU,cAAA/E,sBAAA,wBAAAC,sBAAA,GAAzCD,sBAAA,CAA2CgF,SAAS,cAAA/E,sBAAA,wBAAAC,sBAAA,GAApDD,sBAAA,CAAsDgF,QAAQ,cAAA/E,sBAAA,uBAA9DA,sBAAA,CAAgE0I,GAAG,CAAC,CAACC,OAAO,EAAEzD,KAAK,EAAE0D,CAAC,KAAK;QACvF,MAAMC,MAAM,GAAG3D,KAAK,KAAK0D,CAAC,CAAC/G,MAAM,GAAG,CAAC;QACrC,oBACIvC,OAAA,CAACF,qBAAqB;UAClBM,WAAW,EAAEA,WAAY;UACzBoJ,UAAU,EAAE,UAAU,IAAI5D,KAAK,GAAG,CAAC,CAAE;UACrCiD,MAAM,EAAE,CACJ;YAAEC,KAAK,EAAE,OAAO;YAAEH,KAAK,EAAE,OAAO;YAAEI,UAAU,EAAE,OAAO;YAAEC,YAAY,EAAE1H,gBAAgB,CAAC,gBAAgBsE,KAAK,QAAQ,CAAC;YAAEjE,QAAQ,EAAE,gBAAgBiE,KAAK;UAAS,CAAC,EAC/J;YAAEkD,KAAK,EAAE,UAAU;YAAEH,KAAK,EAAE,aAAa;YAAEI,UAAU,EAAE,SAAS;YAAEC,YAAY,EAAE1H,gBAAgB,CAAC,gBAAgBsE,KAAK,UAAU,CAAC;YAAEjE,QAAQ,EAAE,gBAAgBiE,KAAK;UAAW,CAAC,CAChL;UACFD,OAAO,EAAE,YAAa;UACtB8D,UAAU,EAAE,WAAY;UACxBC,iBAAiB,EAAE,UAAW;UAC9B9D,KAAK,EAAEA,KAAM;UACbvF,QAAQ,EAAEA,QAAS;UACnBF,cAAc,EAAEA,cAAe;UAC/BgJ,SAAS,EAAEnI,WAAW,GAAG,CAAE;UAC3BA,WAAW,EAAEA,WAAY;UACzBd,QAAQ,EAAEA,QAAS;UACnByJ,YAAY,EAAE,IAAK;UACnBC,QAAQ,EAAE,KAAM;UAChBlI,oBAAoB,EAAEA;QAAqB,GAlBnBkE,KAAK;UAAA2C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAmBhC,CAAC;MAEV,CAAC,CAAC,eAEN1I,OAAA;QAAQqI,SAAS,EAAC,mCAAmC;QACjDwB,OAAO,EAAE3B,eAAgB;QAAAI,QAAA,EAC5B;MAAmB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5B,CAAC,eAGN1I,OAAA;MAAKqI,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC1BtI,OAAA;QAAIqI,SAAS,EAAE,mCAAoC;QAAAC,QAAA,EAAC;MAAuB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAChF1I,OAAA;QAAKqI,SAAS,EAAC,MAAM;QAAAC,QAAA,eACjBtI,OAAA,CAACP,cAAc;UACXW,WAAW,EAAEA,WAAY;UACzBoJ,UAAU,EAAE,aAAc;UAC1BX,MAAM,EAAE,CACJ;YAAEC,KAAK,EAAE,OAAO;YAAEH,KAAK,EAAE,eAAe;YAAEI,UAAU,EAAE,OAAO;YAAEE,SAAS,EAAE,EAAE;YAAED,YAAY,EAAE1H,gBAAgB,CAAC,mBAAmB,CAAC;YAAEK,QAAQ,EAAE;UAAoB,CAAC,EAClK;YAAEmH,KAAK,EAAE,OAAO;YAAEH,KAAK,EAAE,aAAa;YAAEI,UAAU,EAAE,QAAQ;YAAEE,SAAS,EAAE,EAAE;YAAED,YAAY,EAAE1H,gBAAgB,CAAC,oBAAoB,CAAC;YAAEK,QAAQ,EAAE;UAAqB;UAClK;UAAA,CACF;UACFgE,OAAO,EAAE,YAAa;UACtB8D,UAAU,EAAE,YAAa;UACzBpJ,QAAQ,EAAEA,QAAS;UACnBF,cAAc,EAAEA,cAAe;UAC/BgJ,SAAS,EAAEnI,WAAW,GAAG,CAAE;UAC3Bd,QAAQ,EAAEA,QAAS;UACnBwB,oBAAoB,EAAEA;QAAqB;UAAA6G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,EAEFvI,cAAc,aAAdA,cAAc,wBAAAQ,sBAAA,GAAdR,cAAc,CAAGa,WAAW,CAAC,cAAAL,sBAAA,wBAAAC,sBAAA,GAA7BD,sBAAA,CAA+B4E,UAAU,cAAA3E,sBAAA,wBAAAC,sBAAA,GAAzCD,sBAAA,CAA2CwF,UAAU,cAAAvF,sBAAA,wBAAAC,sBAAA,GAArDD,sBAAA,CAAuD2F,SAAS,cAAA1F,sBAAA,uBAAhEA,sBAAA,CAAkEsI,GAAG,CAAC,CAACC,OAAO,EAAEzD,KAAK,EAAE0D,CAAC,KAAK;QACzF,MAAMC,MAAM,GAAG3D,KAAK,KAAK0D,CAAC,CAAC/G,MAAM,GAAG,CAAC;QACrC,oBACIvC,OAAA,CAACF,qBAAqB;UAClBM,WAAW,EAAEA,WAAY;UACzBoJ,UAAU,EAAE,UAAU,IAAI5D,KAAK,GAAG,CAAC,CAAE;UACrCiD,MAAM,EAAE,CACJ;YAAEC,KAAK,EAAE,OAAO;YAAEH,KAAK,EAAE,OAAO;YAAEI,UAAU,EAAE,OAAO;YAAEE,SAAS,EAAE,EAAE;YAAED,YAAY,EAAE1H,gBAAgB,CAAC,YAAYsE,KAAK,QAAQ,CAAC;YAAEjE,QAAQ,EAAE,YAAYiE,KAAK;UAAS,CAAC,EACtK;YAAEkD,KAAK,EAAE,OAAO;YAAEH,KAAK,EAAE,aAAa;YAAEI,UAAU,EAAE,aAAa;YAAEE,SAAS,EAAE,EAAE;YAAED,YAAY,EAAE1H,gBAAgB,CAAC,YAAYsE,KAAK,cAAc,CAAC;YAAEjE,QAAQ,EAAE,YAAYiE,KAAK;UAAe,CAAC,CAChM;UACFsD,UAAU,EAAE,CAAC;YAAEP,KAAK,EAAE,MAAM;YAAEtH,EAAE,EAAE,oBAAoBnB,QAAQ,IAAI0F,KAAK,EAAE;YAAEoD,YAAY,EAAE1H,gBAAgB,CAAC,YAAYsE,KAAK,OAAO,CAAC;YAAEjE,QAAQ,EAAE,YAAYiE,KAAK;UAAQ,CAAC,CAAE;UAC3KD,OAAO,EAAE,YAAa;UACtB8D,UAAU,EAAE,YAAa;UACzBC,iBAAiB,EAAE,WAAY;UAC/B9D,KAAK,EAAEA,KAAM;UACbvF,QAAQ,EAAEA,QAAS;UACnBF,cAAc,EAAEA,cAAe;UAC/BgJ,SAAS,EAAEnI,WAAW,GAAG,CAAE;UAC3BA,WAAW,EAAEA,WAAY;UACzBd,QAAQ,EAAEA,QAAS;UACnB0J,QAAQ,EAAE,KAAM;UAChBlI,oBAAoB,EAAEA;QAAqB,GAlBnBkE,KAAK;UAAA2C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAmBhC,CAAC;MAEV,CAAC,CAAC,eAIN1I,OAAA;QAAAsI,QAAA,eACItI,OAAA,CAACP,cAAc;UACXW,WAAW,EAAEA,WAAY;UACzBoJ,UAAU,EAAE,yBAA0B;UACtCX,MAAM,EAAE,CACJ;YAAEC,KAAK,EAAE,OAAO;YAAEH,KAAK,EAAE,eAAe;YAAEI,UAAU,EAAE,MAAM;YAAEC,YAAY,EAAE1H,gBAAgB,CAAC,eAAe,CAAC;YAAEK,QAAQ,EAAE;UAAgB,CAAC,EAC1I;YAAEmH,KAAK,EAAE,OAAO;YAAEH,KAAK,EAAE,MAAM;YAAEI,UAAU,EAAE,MAAM;YAAEC,YAAY,EAAE1H,gBAAgB,CAAC,eAAe,CAAC;YAAEK,QAAQ,EAAE;UAAgB;UAChI;UAAA,CACF;UACFgE,OAAO,EAAE,YAAa;UACtB8D,UAAU,EAAE,YAAa;UACzBC,iBAAiB,EAAE,eAAgB;UACnCrJ,QAAQ,EAAEA,QAAS;UACnBF,cAAc,EAAEA,cAAe;UAC/BgJ,SAAS,EAAEnI,WAAW,GAAG,CAAE;UAC3Bd,QAAQ,EAAEA,QAAS;UACnBwB,oBAAoB,EAAEA;QAAqB;UAAA6G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAGN1I,OAAA,CAACP,cAAc;MACXW,WAAW,EAAEA,WAAY;MACzBoJ,UAAU,EAAE,eAAgB;MAC5BX,MAAM,EAAE,CACJ;QAAEC,KAAK,EAAE,OAAO;QAAEH,KAAK,EAAE,QAAQ;QAAEI,UAAU,EAAE,QAAQ;QAAEC,YAAY,EAAE1H,gBAAgB,CAAC,eAAe,CAAC;QAAEK,QAAQ,EAAE;MAAgB,CAAC,CACvI;MACFgE,OAAO,EAAE,YAAa;MACtBtF,QAAQ,EAAEA,QAAS;MACnBF,cAAc,EAAEA,cAAe;MAC/BgJ,SAAS,EAAEnI,WAAW,GAAG,CAAE;MAC3Bd,QAAQ,EAAEA,QAAS;MACnBwB,oBAAoB,EAAEA;IAAqB;MAAA6G,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9C,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAED,CAAC;AAEd,CAAC;AAAApI,EAAA,CApUKL,mBAAmB;EAAA,QACJP,WAAW;AAAA;AAAAoK,EAAA,GAD1B7J,mBAAmB;AAsUzB,eAAeA,mBAAmB;AAAA,IAAA6J,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}