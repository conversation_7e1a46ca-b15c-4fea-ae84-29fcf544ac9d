import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  GetObjectLegalHoldOutput,
  GetObjectLegalHoldRequest,
} from "../models/models_0";
import {
  S3ClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../S3Client";
export { __MetadataBearer };
export { $Command };
export interface GetObjectLegalHoldCommandInput
  extends GetObjectLegalHoldRequest {}
export interface GetObjectLegalHoldCommandOutput
  extends GetObjectLegalHoldOutput,
    __MetadataBearer {}
declare const GetObjectLegalHoldCommand_base: {
  new (
    input: GetObjectLegalHoldCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    GetObjectLegalHoldCommandInput,
    GetObjectLegalHoldCommandOutput,
    S3ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: GetObjectLegalHoldCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    GetObjectLegalHoldCommandInput,
    GetObjectLegalHoldCommandOutput,
    S3ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class GetObjectLegalHoldCommand extends GetObjectLegalHoldCommand_base {
  protected static __types: {
    api: {
      input: GetObjectLegalHoldRequest;
      output: GetObjectLegalHoldOutput;
    };
    sdk: {
      input: GetObjectLegalHoldCommandInput;
      output: GetObjectLegalHoldCommandOutput;
    };
  };
}
