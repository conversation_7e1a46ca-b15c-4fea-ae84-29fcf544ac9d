
Object.defineProperty(exports, "__esModule", { value: true });

const {
  Decimal,
  objectEnumValues,
  makeStrictEnum,
  Public,
  getRuntime,
  skip
} = require('@prisma/client/runtime/index-browser.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 6.2.1
 * Query Engine version: 4123509d24aa4dede1e864b46351bf2790323b69
 */
Prisma.prismaVersion = {
  client: "6.2.1",
  engine: "4123509d24aa4dede1e864b46351bf2790323b69"
}

Prisma.PrismaClientKnownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientKnownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)};
Prisma.PrismaClientUnknownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientUnknownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientRustPanicError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientRustPanicError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientInitializationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientInitializationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientValidationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientValidationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`sqltag is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.empty = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`empty is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.join = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`join is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.raw = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`raw is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.getExtensionContext is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.defineExtension = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.defineExtension is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}



/**
 * Enums
 */

exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  ReadUncommitted: 'ReadUncommitted',
  ReadCommitted: 'ReadCommitted',
  RepeatableRead: 'RepeatableRead',
  Serializable: 'Serializable'
});

exports.Prisma.ContactQueryScalarFieldEnum = {
  id: 'id',
  name: 'name',
  email: 'email',
  phone: 'phone',
  message: 'message',
  status: 'status',
  notes: 'notes',
  checked: 'checked',
  checkedBy: 'checkedBy',
  checkedAt: 'checkedAt',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.AuditLogScalarFieldEnum = {
  id: 'id',
  action_performed: 'action_performed',
  actionType: 'actionType',
  entity: 'entity',
  entityId: 'entityId',
  oldValue: 'oldValue',
  newValue: 'newValue',
  ipAddress: 'ipAddress',
  browserInfo: 'browserInfo',
  outcome: 'outcome',
  timestamp: 'timestamp',
  metadata: 'metadata'
};

exports.Prisma.MediaScalarFieldEnum = {
  id: 'id',
  url: 'url',
  publicId: 'publicId',
  type: 'type',
  width: 'width',
  height: 'height',
  altText: 'altText',
  createdAt: 'createdAt',
  resourceId: 'resourceId'
};

exports.Prisma.NotificationScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  role: 'role',
  message: 'message',
  isRead: 'isRead',
  createdAt: 'createdAt'
};

exports.Prisma.OtpScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  deviceId: 'deviceId',
  otpCode: 'otpCode',
  createdAt: 'createdAt',
  expiresAt: 'expiresAt',
  isUsed: 'isUsed',
  otpOrigin: 'otpOrigin',
  updatedAt: 'updatedAt'
};

exports.Prisma.SubPermissionScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description'
};

exports.Prisma.PermissionScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  roleTypeId: 'roleTypeId',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.RateLimitScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  attempts: 'attempts',
  failures: 'failures',
  lastAttempt: 'lastAttempt',
  blockUntil: 'blockUntil'
};

exports.Prisma.UserRoleScalarFieldEnum = {
  userId: 'userId',
  roleId: 'roleId',
  createdAt: 'createdAt'
};

exports.Prisma.UserAuditLogScalarFieldEnum = {
  auditLogId: 'auditLogId',
  userId: 'userId',
  createdAt: 'createdAt'
};

exports.Prisma.RolePermissionScalarFieldEnum = {
  roleId: 'roleId',
  permissionId: 'permissionId',
  createdAt: 'createdAt'
};

exports.Prisma.PermissionSubPermissionScalarFieldEnum = {
  permissionId: 'permissionId',
  subPermissionId: 'subPermissionId',
  createdAt: 'createdAt'
};

exports.Prisma.ResourceVersionSectionScalarFieldEnum = {
  id: 'id',
  order: 'order',
  resourceVersionId: 'resourceVersionId',
  sectionVersionId: 'sectionVersionId'
};

exports.Prisma.SectionVersionItemScalarFieldEnum = {
  id: 'id',
  order: 'order',
  sectionVersionId: 'sectionVersionId',
  resourceId: 'resourceId'
};

exports.Prisma.ResourceRoleScalarFieldEnum = {
  id: 'id',
  resourceId: 'resourceId',
  userId: 'userId',
  role: 'role',
  status: 'status',
  createdAt: 'createdAt'
};

exports.Prisma.ResourceVersionRoleScalarFieldEnum = {
  id: 'id',
  role: 'role',
  status: 'status',
  resourceVersionId: 'resourceVersionId',
  userId: 'userId',
  createdAt: 'createdAt'
};

exports.Prisma.ResourceVerifierScalarFieldEnum = {
  id: 'id',
  stage: 'stage',
  status: 'status',
  resourceId: 'resourceId',
  userId: 'userId',
  createdAt: 'createdAt'
};

exports.Prisma.ResourceVersionVerifierScalarFieldEnum = {
  id: 'id',
  stage: 'stage',
  status: 'status',
  resourceVersionId: 'resourceVersionId',
  userId: 'userId',
  createdAt: 'createdAt'
};

exports.Prisma.ResourceVersioningRequestScalarFieldEnum = {
  id: 'id',
  status: 'status',
  flowStatus: 'flowStatus',
  type: 'type',
  editorComments: 'editorComments',
  resourceVersionId: 'resourceVersionId',
  senderId: 'senderId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.RequestApprovalScalarFieldEnum = {
  id: 'id',
  stage: 'stage',
  status: 'status',
  comments: 'comments',
  requestId: 'requestId',
  approverId: 'approverId',
  approverStatus: 'approverStatus',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.WorkflowStateScalarFieldEnum = {
  id: 'id',
  fromState: 'fromState',
  toState: 'toState',
  allowedRole: 'allowedRole',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ReminderScalarFieldEnum = {
  id: 'id',
  senderId: 'senderId',
  receiverId: 'receiverId',
  subject: 'subject',
  message: 'message',
  replied: 'replied',
  response: 'response',
  sendOnEmail: 'sendOnEmail',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.FiltersScalarFieldEnum = {
  id: 'id',
  nameEn: 'nameEn',
  nameAr: 'nameAr',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ResourceScalarFieldEnum = {
  id: 'id',
  titleEn: 'titleEn',
  titleAr: 'titleAr',
  slug: 'slug',
  status: 'status',
  resourceType: 'resourceType',
  resourceTag: 'resourceTag',
  relationType: 'relationType',
  isAssigned: 'isAssigned',
  autoApproval: 'autoApproval',
  autoApprovalTime: 'autoApprovalTime',
  liveVersionId: 'liveVersionId',
  newVersionEditModeId: 'newVersionEditModeId',
  scheduledVersionId: 'scheduledVersionId',
  parentId: 'parentId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ResourceVersionScalarFieldEnum = {
  id: 'id',
  versionNumber: 'versionNumber',
  versionStatus: 'versionStatus',
  notes: 'notes',
  referenceDoc: 'referenceDoc',
  content: 'content',
  icon: 'icon',
  Image: 'Image',
  lockedById: 'lockedById',
  lockedAt: 'lockedAt',
  resourceId: 'resourceId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  scheduledAt: 'scheduledAt',
  publishedAt: 'publishedAt'
};

exports.Prisma.RoleTypeScalarFieldEnum = {
  id: 'id',
  name: 'name'
};

exports.Prisma.RoleScalarFieldEnum = {
  id: 'id',
  name: 'name',
  status: 'status',
  roleTypeId: 'roleTypeId',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.SectionTypeScalarFieldEnum = {
  id: 'id',
  name: 'name',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.SectionScalarFieldEnum = {
  id: 'id',
  title: 'title',
  sectionTypeId: 'sectionTypeId',
  isGlobal: 'isGlobal',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.SectionVersionScalarFieldEnum = {
  id: 'id',
  icon: 'icon',
  version: 'version',
  sectionVersionTitle: 'sectionVersionTitle',
  content: 'content',
  sectionId: 'sectionId',
  resourceId: 'resourceId',
  resourceVersionId: 'resourceVersionId',
  parentVersionId: 'parentVersionId'
};

exports.Prisma.SEOScalarFieldEnum = {
  id: 'id',
  metaTitle: 'metaTitle',
  metaDescription: 'metaDescription',
  keywords: 'keywords',
  createdAt: 'createdAt',
  resourceId: 'resourceId'
};

exports.Prisma.UserScalarFieldEnum = {
  id: 'id',
  name: 'name',
  image: 'image',
  email: 'email',
  password: 'password',
  isSuperUser: 'isSuperUser',
  status: 'status',
  phone: 'phone',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.NullableJsonNullValueInput = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull
};

exports.Prisma.JsonNullValueInput = {
  JsonNull: Prisma.JsonNull
};

exports.Prisma.QueryMode = {
  default: 'default',
  insensitive: 'insensitive'
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};

exports.Prisma.JsonNullValueFilter = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull,
  AnyNull: Prisma.AnyNull
};
exports.QueryStatus = exports.$Enums.QueryStatus = {
  PENDING: 'PENDING',
  IN_PROGRESS: 'IN_PROGRESS',
  RESOLVED: 'RESOLVED',
  CLOSED: 'CLOSED'
};

exports.logOutcome = exports.$Enums.logOutcome = {
  Success: 'Success',
  Failure: 'Failure',
  Unknown: 'Unknown'
};

exports.MediaType = exports.$Enums.MediaType = {
  IMAGE: 'IMAGE',
  VIDEO: 'VIDEO',
  DOCUMENT: 'DOCUMENT',
  AUDIO: 'AUDIO'
};

exports.otpOrigin = exports.$Enums.otpOrigin = {
  MFA_Login: 'MFA_Login',
  forgot_Pass: 'forgot_Pass',
  NULL: 'NULL'
};

exports.ResourceRoleType = exports.$Enums.ResourceRoleType = {
  MANAGER: 'MANAGER',
  EDITOR: 'EDITOR',
  PUBLISHER: 'PUBLISHER'
};

exports.Status = exports.$Enums.Status = {
  ACTIVE: 'ACTIVE',
  INACTIVE: 'INACTIVE',
  TRASHED: 'TRASHED'
};

exports.RequestStatus = exports.$Enums.RequestStatus = {
  VERIFICATION_PENDING: 'VERIFICATION_PENDING',
  PUBLISH_PENDING: 'PUBLISH_PENDING',
  PUBLISHED: 'PUBLISHED',
  SCHEDULED: 'SCHEDULED'
};

exports.FlowStatus = exports.$Enums.FlowStatus = {
  PENDING: 'PENDING',
  APPROVED: 'APPROVED',
  REJECTED: 'REJECTED',
  SCHEDULED: 'SCHEDULED',
  PUBLISHED: 'PUBLISHED'
};

exports.RequestType = exports.$Enums.RequestType = {
  VERIFICATION: 'VERIFICATION',
  PUBLICATION: 'PUBLICATION'
};

exports.ApprovalStatus = exports.$Enums.ApprovalStatus = {
  PENDING: 'PENDING',
  APPROVED: 'APPROVED',
  REJECTED: 'REJECTED'
};

exports.VersionStatus = exports.$Enums.VersionStatus = {
  EDITING: 'EDITING',
  DRAFT: 'DRAFT',
  VERIFICATION_PENDING: 'VERIFICATION_PENDING',
  REJECTED: 'REJECTED',
  PUBLISH_PENDING: 'PUBLISH_PENDING',
  SCHEDULED: 'SCHEDULED',
  PUBLISHED: 'PUBLISHED',
  LIVE: 'LIVE',
  NULL: 'NULL'
};

exports.ResourceType = exports.$Enums.ResourceType = {
  MAIN_PAGE: 'MAIN_PAGE',
  SUB_PAGE: 'SUB_PAGE',
  SUB_PAGE_ITEM: 'SUB_PAGE_ITEM',
  FORM: 'FORM',
  HEADER: 'HEADER',
  FOOTER: 'FOOTER',
  NULL: 'NULL'
};

exports.ResourceTag = exports.$Enums.ResourceTag = {
  HOME: 'HOME',
  ABOUT: 'ABOUT',
  SOLUTION: 'SOLUTION',
  SERVICE: 'SERVICE',
  MARKET: 'MARKET',
  PROJECT: 'PROJECT',
  CAREER: 'CAREER',
  NEWS: 'NEWS',
  TESTIMONIAL: 'TESTIMONIAL',
  CONTACT: 'CONTACT',
  HEADER: 'HEADER',
  FOOTER: 'FOOTER',
  HISTORY: 'HISTORY',
  SAFETY_RESPONSIBILITY: 'SAFETY_RESPONSIBILITY',
  VISION: 'VISION',
  HSE: 'HSE',
  AFFILIATES: 'AFFILIATES',
  ORGANIZATION_CHART: 'ORGANIZATION_CHART',
  TEMPLATE_ONE: 'TEMPLATE_ONE',
  TEMPLATE_TWO: 'TEMPLATE_TWO',
  TEMPLATE_THREE: 'TEMPLATE_THREE',
  TEMPLATE_FOUR: 'TEMPLATE_FOUR',
  BOXES: 'BOXES',
  NULL: 'NULL'
};

exports.RelationType = exports.$Enums.RelationType = {
  PARENT: 'PARENT',
  CHILD: 'CHILD',
  NULL: 'NULL'
};

exports.Prisma.ModelName = {
  ContactQuery: 'ContactQuery',
  AuditLog: 'AuditLog',
  Media: 'Media',
  Notification: 'Notification',
  Otp: 'Otp',
  SubPermission: 'SubPermission',
  Permission: 'Permission',
  RateLimit: 'RateLimit',
  UserRole: 'UserRole',
  UserAuditLog: 'UserAuditLog',
  RolePermission: 'RolePermission',
  PermissionSubPermission: 'PermissionSubPermission',
  ResourceVersionSection: 'ResourceVersionSection',
  SectionVersionItem: 'SectionVersionItem',
  ResourceRole: 'ResourceRole',
  ResourceVersionRole: 'ResourceVersionRole',
  ResourceVerifier: 'ResourceVerifier',
  ResourceVersionVerifier: 'ResourceVersionVerifier',
  ResourceVersioningRequest: 'ResourceVersioningRequest',
  RequestApproval: 'RequestApproval',
  WorkflowState: 'WorkflowState',
  Reminder: 'Reminder',
  Filters: 'Filters',
  Resource: 'Resource',
  ResourceVersion: 'ResourceVersion',
  RoleType: 'RoleType',
  Role: 'Role',
  SectionType: 'SectionType',
  Section: 'Section',
  SectionVersion: 'SectionVersion',
  SEO: 'SEO',
  User: 'User'
};

/**
 * This is a stub Prisma Client that will error at runtime if called.
 */
class PrismaClient {
  constructor() {
    return new Proxy(this, {
      get(target, prop) {
        let message
        const runtime = getRuntime()
        if (runtime.isEdge) {
          message = `PrismaClient is not configured to run in ${runtime.prettyName}. In order to run Prisma Client on edge runtime, either:
- Use Prisma Accelerate: https://pris.ly/d/accelerate
- Use Driver Adapters: https://pris.ly/d/driver-adapters
`;
        } else {
          message = 'PrismaClient is unable to run in this browser environment, or has been bundled for the browser (running in `' + runtime.prettyName + '`).'
        }
        
        message += `
If this is unexpected, please open an issue: https://pris.ly/prisma-prisma-bug-report`

        throw new Error(message)
      }
    })
  }
}

exports.PrismaClient = PrismaClient

Object.assign(exports, Prisma)
