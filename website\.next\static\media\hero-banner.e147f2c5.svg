<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="heroGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1e3a8a;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#3b82f6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#06b6d4;stop-opacity:1" />
    </linearGradient>
    <pattern id="gridPattern" width="40" height="40" patternUnits="userSpaceOnUse">
      <path d="M 40 0 L 0 0 0 40" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/>
    </pattern>
  </defs>
  
  <!-- Background -->
  <rect width="1920" height="1080" fill="url(#heroGradient)"/>
  
  <!-- Grid Pattern -->
  <rect width="1920" height="1080" fill="url(#gridPattern)"/>
  
  <!-- Geometric Shapes -->
  <circle cx="1600" cy="200" r="120" fill="rgba(255,255,255,0.1)" opacity="0.6"/>
  <circle cx="300" cy="800" r="80" fill="rgba(255,255,255,0.08)" opacity="0.8"/>
  <rect x="1400" y="600" width="200" height="200" fill="rgba(255,255,255,0.05)" transform="rotate(45 1500 700)" opacity="0.7"/>
  <polygon points="100,300 200,200 300,300 200,400" fill="rgba(255,255,255,0.06)" opacity="0.9"/>
  
  <!-- Central Focus Area -->
  <circle cx="960" cy="540" r="300" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="2" opacity="0.5"/>
  <circle cx="960" cy="540" r="200" fill="none" stroke="rgba(255,255,255,0.15)" stroke-width="1" opacity="0.7"/>
  <circle cx="960" cy="540" r="100" fill="none" stroke="rgba(255,255,255,0.2)" stroke-width="1" opacity="0.9"/>
  
  <!-- Subtle Lines -->
  <line x1="0" y1="540" x2="1920" y2="540" stroke="rgba(255,255,255,0.05)" stroke-width="1" opacity="0.6"/>
  <line x1="960" y1="0" x2="960" y2="1080" stroke="rgba(255,255,255,0.05)" stroke-width="1" opacity="0.6"/>
  
  <!-- Corner Accents -->
  <rect x="0" y="0" width="100" height="100" fill="rgba(255,255,255,0.08)" opacity="0.5"/>
  <rect x="1820" y="0" width="100" height="100" fill="rgba(255,255,255,0.08)" opacity="0.5"/>
  <rect x="0" y="980" width="100" height="100" fill="rgba(255,255,255,0.08)" opacity="0.5"/>
  <rect x="1820" y="980" width="100" height="100" fill="rgba(255,255,255,0.08)" opacity="0.5"/>
</svg>
