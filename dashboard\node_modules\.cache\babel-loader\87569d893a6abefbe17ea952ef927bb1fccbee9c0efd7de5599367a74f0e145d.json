{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Akshay\\\\LOOP_PROJECTS\\\\shade_cms\\\\dashboard\\\\src\\\\features\\\\Resources\\\\components\\\\contentmanager\\\\CMforDetails\\\\CareerDetailManager.jsx\",\n  _s = $RefreshSig$();\nimport { useEffect, useState } from \"react\";\nimport FileUploader from \"../../../../../components/Input/InputFileUploader\";\nimport ContentSection from \"../../breakUI/ContentSections\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { updateMainContent, updateTheProjectSummaryList } from \"../../../../common/homeContentSlice\";\nimport content from \"../../websiteComponent/content.json\";\nimport DynamicContentSection from \"../../breakUI/DynamicContentSection\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CareerDetailManager = ({\n  careerId,\n  currentContent,\n  currentPath,\n  language\n}) => {\n  _s();\n  var _currentContent$caree, _currentContent$caree2, _currentContent$caree3, _currentContent$caree4, _currentContent$caree5, _currentContent$caree6, _currentContent$caree7, _currentContent$caree8;\n  const dispatch = useDispatch();\n  // Get careerDetails array from Redux state instead of relying on currentContent prop\n  const careerDetailsFromRedux = useSelector(state => state.homeContent.present.careerDetails);\n  // Use careerDetails from Redux if available, otherwise fall back to currentContent (with safety check)\n  const careerDetailsArray = Array.isArray(careerDetailsFromRedux) ? careerDetailsFromRedux : Array.isArray(currentContent) ? currentContent : [];\n  const careerIndex = careerDetailsArray === null || careerDetailsArray === void 0 ? void 0 : careerDetailsArray.findIndex(e => e.id == careerId);\n\n  // Validation states\n  const [validationErrors, setValidationErrors] = useState({});\n  const [isValidating, setIsValidating] = useState(false);\n\n  // Function to clear specific validation error\n  const clearValidationError = errorKey => {\n    setValidationErrors(prev => {\n      const newErrors = {\n        ...prev\n      };\n      delete newErrors[errorKey];\n      return newErrors;\n    });\n  };\n\n  // Validation functions\n  const validateField = (value, fieldName) => {\n    if (!value || typeof value === 'string' && value.trim() === '') {\n      return \"Required\";\n    }\n    return null;\n  };\n  const validateImage = (imageUrl, fieldName) => {\n    if (!imageUrl || imageUrl.trim() === '') {\n      return \"Required\";\n    }\n    return null;\n  };\n  const validateArray = (array, fieldName, minLength = 1) => {\n    if (!array || array.length < minLength) {\n      return \"Required\";\n    }\n    return null;\n  };\n\n  // Comprehensive validation function\n  const validateAllFields = () => {\n    var _career$banner, _career$banner$title, _career$banner2, _career$banner2$subTi, _career$banner3, _career$banner3$butto, _career$banner4, _career$banner4$image, _career$banner4$image2, _career$jobDetails, _career$jobDetails$le, _career$jobDetails2, _career$jobDetails2$r, _career$jobDetails2$r2, _career$jobDetails3, _career$jobDetails3$r, _career$jobDetails3$r2, _career$jobDetails4, _career$jobDetails4$r, _career$jobDetails5, _career$jobDetails5$r, _career$jobDetails5$r2, _career$jobDetails5$r3, _career$jobDetails6, _career$jobDetails6$r, _career$jobDetails6$r2, _career$jobDetails6$r3, _career$jobDetails7, _career$jobDetails7$b;\n    setIsValidating(true);\n    const errors = {};\n    const career = currentContent === null || currentContent === void 0 ? void 0 : currentContent[careerIndex];\n\n    // Banner section validation\n    const bannerTitle = career === null || career === void 0 ? void 0 : (_career$banner = career.banner) === null || _career$banner === void 0 ? void 0 : (_career$banner$title = _career$banner.title) === null || _career$banner$title === void 0 ? void 0 : _career$banner$title[language];\n    const bannerSubTitle = career === null || career === void 0 ? void 0 : (_career$banner2 = career.banner) === null || _career$banner2 === void 0 ? void 0 : (_career$banner2$subTi = _career$banner2.subTitle) === null || _career$banner2$subTi === void 0 ? void 0 : _career$banner2$subTi[language];\n    const bannerButton = career === null || career === void 0 ? void 0 : (_career$banner3 = career.banner) === null || _career$banner3 === void 0 ? void 0 : (_career$banner3$butto = _career$banner3.button) === null || _career$banner3$butto === void 0 ? void 0 : _career$banner3$butto[language];\n    const bannerImage = career === null || career === void 0 ? void 0 : (_career$banner4 = career.banner) === null || _career$banner4 === void 0 ? void 0 : (_career$banner4$image = _career$banner4.images) === null || _career$banner4$image === void 0 ? void 0 : (_career$banner4$image2 = _career$banner4$image[0]) === null || _career$banner4$image2 === void 0 ? void 0 : _career$banner4$image2.url;\n    const bannerTitleError = validateField(bannerTitle, \"Banner Title\");\n    if (bannerTitleError) errors[\"banner_title\"] = bannerTitleError;\n    const bannerSubTitleError = validateField(bannerSubTitle, \"Banner Sub Title\");\n    if (bannerSubTitleError) errors[\"banner_subtitle\"] = bannerSubTitleError;\n    const bannerButtonError = validateField(bannerButton, \"Banner Button\");\n    if (bannerButtonError) errors[\"banner_button\"] = bannerButtonError;\n    const bannerImageError = validateImage(bannerImage, \"Banner Image\");\n    if (bannerImageError) errors[\"banner_image\"] = bannerImageError;\n\n    // Left panel sections validation\n    const leftPanelSections = career === null || career === void 0 ? void 0 : (_career$jobDetails = career.jobDetails) === null || _career$jobDetails === void 0 ? void 0 : (_career$jobDetails$le = _career$jobDetails.leftPanel) === null || _career$jobDetails$le === void 0 ? void 0 : _career$jobDetails$le.sections;\n    if (leftPanelSections && leftPanelSections.length > 0) {\n      leftPanelSections.forEach((section, index) => {\n        var _section$title, _section$content;\n        const sectionTitle = section === null || section === void 0 ? void 0 : (_section$title = section.title) === null || _section$title === void 0 ? void 0 : _section$title[language];\n        const sectionContent = section === null || section === void 0 ? void 0 : (_section$content = section.content) === null || _section$content === void 0 ? void 0 : _section$content[language];\n        const sectionTitleError = validateField(sectionTitle, `Left Panel Section ${index + 1} Title`);\n        if (sectionTitleError) errors[`left_section_${index}_title`] = sectionTitleError;\n        const sectionContentError = validateField(sectionContent, `Left Panel Section ${index + 1} Content`);\n        if (sectionContentError) errors[`left_section_${index}_content`] = sectionContentError;\n      });\n    }\n\n    // Right panel top section validation\n    const rightPanelTitle = career === null || career === void 0 ? void 0 : (_career$jobDetails2 = career.jobDetails) === null || _career$jobDetails2 === void 0 ? void 0 : (_career$jobDetails2$r = _career$jobDetails2.rightPanel) === null || _career$jobDetails2$r === void 0 ? void 0 : (_career$jobDetails2$r2 = _career$jobDetails2$r.title) === null || _career$jobDetails2$r2 === void 0 ? void 0 : _career$jobDetails2$r2[language];\n    const rightPanelButton = career === null || career === void 0 ? void 0 : (_career$jobDetails3 = career.jobDetails) === null || _career$jobDetails3 === void 0 ? void 0 : (_career$jobDetails3$r = _career$jobDetails3.rightPanel) === null || _career$jobDetails3$r === void 0 ? void 0 : (_career$jobDetails3$r2 = _career$jobDetails3$r.button) === null || _career$jobDetails3$r2 === void 0 ? void 0 : _career$jobDetails3$r2[language];\n    const rightPanelTitleError = validateField(rightPanelTitle, \"Right Panel Title\");\n    if (rightPanelTitleError) errors[\"right_panel_title\"] = rightPanelTitleError;\n    const rightPanelButtonError = validateField(rightPanelButton, \"Right Panel Button\");\n    if (rightPanelButtonError) errors[\"right_panel_button\"] = rightPanelButtonError;\n\n    // Right panel tailwraps validation\n    const tailwraps = career === null || career === void 0 ? void 0 : (_career$jobDetails4 = career.jobDetails) === null || _career$jobDetails4 === void 0 ? void 0 : (_career$jobDetails4$r = _career$jobDetails4.rightPanel) === null || _career$jobDetails4$r === void 0 ? void 0 : _career$jobDetails4$r.tailwraps;\n    if (tailwraps && tailwraps.length > 0) {\n      tailwraps.forEach((tailwrap, index) => {\n        var _tailwrap$title, _tailwrap$description, _tailwrap$images, _tailwrap$images$;\n        const tailwrapTitle = tailwrap === null || tailwrap === void 0 ? void 0 : (_tailwrap$title = tailwrap.title) === null || _tailwrap$title === void 0 ? void 0 : _tailwrap$title[language];\n        const tailwrapDescription = tailwrap === null || tailwrap === void 0 ? void 0 : (_tailwrap$description = tailwrap.description) === null || _tailwrap$description === void 0 ? void 0 : _tailwrap$description[language];\n        const tailwrapIcon = tailwrap === null || tailwrap === void 0 ? void 0 : (_tailwrap$images = tailwrap.images) === null || _tailwrap$images === void 0 ? void 0 : (_tailwrap$images$ = _tailwrap$images[0]) === null || _tailwrap$images$ === void 0 ? void 0 : _tailwrap$images$.url;\n        const tailwrapTitleError = validateField(tailwrapTitle, `Tailwrap ${index + 1} Title`);\n        if (tailwrapTitleError) errors[`tailwrap_${index}_title`] = tailwrapTitleError;\n        const tailwrapDescriptionError = validateField(tailwrapDescription, `Tailwrap ${index + 1} Description`);\n        if (tailwrapDescriptionError) errors[`tailwrap_${index}_description`] = tailwrapDescriptionError;\n        const tailwrapIconError = validateImage(tailwrapIcon, `Tailwrap ${index + 1} Icon`);\n        if (tailwrapIconError) errors[`tailwrap_${index}_icon`] = tailwrapIconError;\n      });\n    }\n\n    // Right panel view all button validation\n    const viewAllText = career === null || career === void 0 ? void 0 : (_career$jobDetails5 = career.jobDetails) === null || _career$jobDetails5 === void 0 ? void 0 : (_career$jobDetails5$r = _career$jobDetails5.rightPanel) === null || _career$jobDetails5$r === void 0 ? void 0 : (_career$jobDetails5$r2 = _career$jobDetails5$r.viewAllButton) === null || _career$jobDetails5$r2 === void 0 ? void 0 : (_career$jobDetails5$r3 = _career$jobDetails5$r2.text) === null || _career$jobDetails5$r3 === void 0 ? void 0 : _career$jobDetails5$r3[language];\n    const viewAllLink = career === null || career === void 0 ? void 0 : (_career$jobDetails6 = career.jobDetails) === null || _career$jobDetails6 === void 0 ? void 0 : (_career$jobDetails6$r = _career$jobDetails6.rightPanel) === null || _career$jobDetails6$r === void 0 ? void 0 : (_career$jobDetails6$r2 = _career$jobDetails6$r.viewAllButton) === null || _career$jobDetails6$r2 === void 0 ? void 0 : (_career$jobDetails6$r3 = _career$jobDetails6$r2.link) === null || _career$jobDetails6$r3 === void 0 ? void 0 : _career$jobDetails6$r3[language];\n    const viewAllTextError = validateField(viewAllText, \"View All Text\");\n    if (viewAllTextError) errors[\"view_all_text\"] = viewAllTextError;\n    const viewAllLinkError = validateField(viewAllLink, \"View All Link\");\n    if (viewAllLinkError) errors[\"view_all_link\"] = viewAllLinkError;\n\n    // Bottom button validation\n    const bottomButton = career === null || career === void 0 ? void 0 : (_career$jobDetails7 = career.jobDetails) === null || _career$jobDetails7 === void 0 ? void 0 : (_career$jobDetails7$b = _career$jobDetails7.button) === null || _career$jobDetails7$b === void 0 ? void 0 : _career$jobDetails7$b[language];\n    const bottomButtonError = validateField(bottomButton, \"Bottom Button\");\n    if (bottomButtonError) errors[\"bottom_button\"] = bottomButtonError;\n    setValidationErrors(errors);\n    setIsValidating(false);\n    return Object.keys(errors).length === 0;\n  };\n\n  // Expose validation function globally\n  useEffect(() => {\n    window.validateCareerDetailContent = validateAllFields;\n    return () => {\n      delete window.validateCareerDetailContent;\n    };\n  }, [currentContent, language, careerIndex]);\n  const addExtraSummary = () => {\n    // dispatch(updateCardAndItemsArray(\n    //     {\n    //         insert: {\n    //             title: {\n    //                 ar: \"\",\n    //                 en: \"\"\n    //             },\n    //             content: {\n    //                 ar: \"\",\n    //                 en: \"\"\n    //             }\n    //         },\n    //         careerIndex,\n    //         context: \"careerDetails\",\n    //         operation: 'add'\n    //     }\n    // ))\n  };\n  useEffect(() => {\n    dispatch(updateMainContent({\n      currentPath: \"careerDetails\",\n      payload: content === null || content === void 0 ? void 0 : content.careerDetails\n    }));\n  }, []);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(FileUploader, {\n      id: \"CareerDetailsIDReference\" + careerId,\n      label: \"Rerference doc\",\n      fileName: \"Upload your file...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 175,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(ContentSection, {\n      currentPath: currentPath,\n      Heading: \"Banner\",\n      inputs: [{\n        input: \"input\",\n        label: \"Heading/title\",\n        updateType: \"title\",\n        errorMessage: validationErrors[\"banner_title\"],\n        errorKey: \"banner_title\"\n      }, {\n        input: \"input\",\n        label: \"Description\",\n        updateType: \"subTitle\",\n        maxLength: 23,\n        errorMessage: validationErrors[\"banner_subtitle\"],\n        errorKey: \"banner_subtitle\"\n      }, {\n        input: \"input\",\n        label: \"Button Text\",\n        updateType: \"button\",\n        errorMessage: validationErrors[\"banner_button\"],\n        errorKey: \"banner_button\"\n      }\n      // { input: \"input\", label: \"Url\", updateType: \"url\" },\n      ],\n      inputFiles: [{\n        label: \"Backround Image\",\n        id: \"careerBanner/\" + careerId,\n        errorMessage: validationErrors[\"banner_image\"],\n        errorKey: \"banner_image\"\n      }],\n      section: \"banner\",\n      language: language,\n      currentContent: currentContent,\n      projectId: careerIndex + 1,\n      clearValidationError: clearValidationError\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 177,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-4 border-b\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: `font-semibold text-[1.25rem] mb-4`,\n        children: \"Job Details Left Panel\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 196,\n        columnNumber: 17\n      }, this), currentContent === null || currentContent === void 0 ? void 0 : (_currentContent$caree = currentContent[careerIndex]) === null || _currentContent$caree === void 0 ? void 0 : (_currentContent$caree2 = _currentContent$caree.jobDetails) === null || _currentContent$caree2 === void 0 ? void 0 : (_currentContent$caree3 = _currentContent$caree2.leftPanel) === null || _currentContent$caree3 === void 0 ? void 0 : (_currentContent$caree4 = _currentContent$caree3.sections) === null || _currentContent$caree4 === void 0 ? void 0 : _currentContent$caree4.map((element, index, a) => {\n        const isLast = index === a.length - 1;\n        return /*#__PURE__*/_jsxDEV(DynamicContentSection, {\n          currentPath: currentPath,\n          subHeading: \"Section \" + (index + 1),\n          inputs: [{\n            input: \"input\",\n            label: \"Title\",\n            updateType: \"title\",\n            errorMessage: validationErrors[`left_section_${index}_title`],\n            errorKey: `left_section_${index}_title`\n          }, {\n            input: \"textarea\",\n            label: \"Description\",\n            updateType: \"content\",\n            errorMessage: validationErrors[`left_section_${index}_content`],\n            errorKey: `left_section_${index}_content`\n          }],\n          section: \"jobDetails\",\n          subSection: \"leftPanel\",\n          subSectionsProMax: \"sections\",\n          index: index,\n          language: language,\n          currentContent: currentContent,\n          projectId: careerIndex + 1,\n          careerIndex: careerIndex,\n          careerId: careerId,\n          allowRemoval: true,\n          isBorder: false,\n          clearValidationError: clearValidationError\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 201,\n          columnNumber: 29\n        }, this);\n      }), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"text-blue-500 cursor-pointer mb-3\",\n        onClick: addExtraSummary,\n        children: \"Add More Section...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 224,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 195,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-4 border-b\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: `font-semibold text-[1.25rem] mb-4`,\n        children: \"Job Details Right Panel\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 231,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-6\",\n        children: /*#__PURE__*/_jsxDEV(ContentSection, {\n          currentPath: currentPath,\n          subHeading: \"Top Section\",\n          inputs: [{\n            input: \"input\",\n            label: \"Heading/title\",\n            updateType: \"title\",\n            maxLength: 15,\n            errorMessage: validationErrors[\"right_panel_title\"],\n            errorKey: \"right_panel_title\"\n          }, {\n            input: \"input\",\n            label: \"Button Text\",\n            updateType: \"button\",\n            maxLength: 18,\n            errorMessage: validationErrors[\"right_panel_button\"],\n            errorKey: \"right_panel_button\"\n          }\n          // { input: \"input\", label: \"Url\", updateType: \"url\" },\n          ],\n          section: \"jobDetails\",\n          subSection: \"rightPanel\",\n          language: language,\n          currentContent: currentContent,\n          projectId: careerIndex + 1,\n          careerId: careerId,\n          clearValidationError: clearValidationError\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 233,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 232,\n        columnNumber: 17\n      }, this), currentContent === null || currentContent === void 0 ? void 0 : (_currentContent$caree5 = currentContent[careerIndex]) === null || _currentContent$caree5 === void 0 ? void 0 : (_currentContent$caree6 = _currentContent$caree5.jobDetails) === null || _currentContent$caree6 === void 0 ? void 0 : (_currentContent$caree7 = _currentContent$caree6.rightPanel) === null || _currentContent$caree7 === void 0 ? void 0 : (_currentContent$caree8 = _currentContent$caree7.tailwraps) === null || _currentContent$caree8 === void 0 ? void 0 : _currentContent$caree8.map((element, index, a) => {\n        const isLast = index === a.length - 1;\n        return /*#__PURE__*/_jsxDEV(DynamicContentSection, {\n          currentPath: currentPath,\n          subHeading: \"Section \" + (index + 1),\n          inputs: [{\n            input: \"input\",\n            label: \"Title\",\n            updateType: \"title\",\n            maxLength: 15,\n            errorMessage: validationErrors[`tailwrap_${index}_title`],\n            errorKey: `tailwrap_${index}_title`\n          }, {\n            input: \"input\",\n            label: \"Description\",\n            updateType: \"description\",\n            maxLength: 19,\n            errorMessage: validationErrors[`tailwrap_${index}_description`],\n            errorKey: `tailwrap_${index}_description`\n          }],\n          inputFiles: [{\n            label: \"icon\",\n            id: `careerRightPanel/${careerId}/${index}`,\n            errorMessage: validationErrors[`tailwrap_${index}_icon`],\n            errorKey: `tailwrap_${index}_icon`\n          }],\n          section: \"jobDetails\",\n          subSection: \"rightPanel\",\n          subSectionsProMax: \"tailwraps\",\n          index: index,\n          language: language,\n          currentContent: currentContent,\n          projectId: careerIndex + 1,\n          careerIndex: careerIndex,\n          careerId: careerId,\n          isBorder: false,\n          clearValidationError: clearValidationError\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 254,\n          columnNumber: 29\n        }, this);\n      }), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: /*#__PURE__*/_jsxDEV(ContentSection, {\n          currentPath: currentPath,\n          subHeading: \"Right panel redirection\",\n          inputs: [{\n            input: \"input\",\n            label: \"Heading/title\",\n            updateType: \"text\",\n            errorMessage: validationErrors[\"view_all_text\"],\n            errorKey: \"view_all_text\"\n          }, {\n            input: \"input\",\n            label: \"Link\",\n            updateType: \"link\",\n            errorMessage: validationErrors[\"view_all_link\"],\n            errorKey: \"view_all_link\"\n          }\n          // { input: \"input\", label: \"Url\", updateType: \"url\" },\n          ],\n          section: \"jobDetails\",\n          subSection: \"rightPanel\",\n          subSectionsProMax: \"viewAllButton\",\n          language: language,\n          currentContent: currentContent,\n          projectId: careerIndex + 1,\n          careerId: careerId,\n          clearValidationError: clearValidationError\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 280,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 279,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 230,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(ContentSection, {\n      currentPath: currentPath,\n      subHeading: \"Bottom Button\",\n      inputs: [{\n        input: \"input\",\n        label: \"Button\",\n        updateType: \"button\",\n        errorMessage: validationErrors[\"bottom_button\"],\n        errorKey: \"bottom_button\"\n      }],\n      section: \"jobDetails\",\n      language: language,\n      currentContent: currentContent,\n      projectId: careerIndex + 1,\n      careerId: careerId,\n      clearValidationError: clearValidationError\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 301,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 174,\n    columnNumber: 9\n  }, this);\n};\n_s(CareerDetailManager, \"bbXD9TSXTNNjKXJSzh2AVWf+CUg=\", false, function () {\n  return [useDispatch, useSelector];\n});\n_c = CareerDetailManager;\nexport default CareerDetailManager;\nvar _c;\n$RefreshReg$(_c, \"CareerDetailManager\");", "map": {"version": 3, "names": ["useEffect", "useState", "FileUploader", "ContentSection", "useDispatch", "useSelector", "update<PERSON>ain<PERSON><PERSON>nt", "updateTheProjectSummaryList", "content", "DynamicContentSection", "jsxDEV", "_jsxDEV", "CareerDetailManager", "careerId", "currentC<PERSON>nt", "currentPath", "language", "_s", "_currentContent$caree", "_currentContent$caree2", "_currentContent$caree3", "_currentContent$caree4", "_currentContent$caree5", "_currentContent$caree6", "_currentContent$caree7", "_currentContent$caree8", "dispatch", "careerDetailsFromRedux", "state", "homeContent", "present", "careerDetails", "careerDetailsArray", "Array", "isArray", "careerIndex", "findIndex", "e", "id", "validationErrors", "setValidationErrors", "isValidating", "setIsValidating", "clearValidationError", "<PERSON><PERSON><PERSON>", "prev", "newErrors", "validateField", "value", "fieldName", "trim", "validateImage", "imageUrl", "validate<PERSON><PERSON>y", "array", "<PERSON><PERSON><PERSON><PERSON>", "length", "validate<PERSON>ll<PERSON>ields", "_career$banner", "_career$banner$title", "_career$banner2", "_career$banner2$subTi", "_career$banner3", "_career$banner3$butto", "_career$banner4", "_career$banner4$image", "_career$banner4$image2", "_career$jobDetails", "_career$jobDetails$le", "_career$jobDetails2", "_career$jobDetails2$r", "_career$jobDetails2$r2", "_career$jobDetails3", "_career$jobDetails3$r", "_career$jobDetails3$r2", "_career$jobDetails4", "_career$jobDetails4$r", "_career$jobDetails5", "_career$jobDetails5$r", "_career$jobDetails5$r2", "_career$jobDetails5$r3", "_career$jobDetails6", "_career$jobDetails6$r", "_career$jobDetails6$r2", "_career$jobDetails6$r3", "_career$jobDetails7", "_career$jobDetails7$b", "errors", "career", "bannerTitle", "banner", "title", "bannerSubTitle", "subTitle", "bannerButton", "button", "bannerImage", "images", "url", "bannerTitleError", "bannerSubTitleError", "bannerButtonError", "bannerImageError", "leftPanelSections", "jobDetails", "leftPanel", "sections", "for<PERSON>ach", "section", "index", "_section$title", "_section$content", "sectionTitle", "sectionContent", "sectionTitleError", "sectionContentError", "rightPanelTitle", "rightPanel", "rightPanelButton", "rightPanelTitleError", "rightPanelButtonError", "tailwraps", "tailwrap", "_tailwrap$title", "_tailwrap$description", "_tailwrap$images", "_tailwrap$images$", "tailwrapTitle", "tailwrapDescription", "description", "tailwrapIcon", "tailwrapTitleError", "tailwrapDescriptionError", "tailwrapIconError", "viewAllText", "viewAllButton", "text", "viewAllLink", "link", "viewAllTextError", "viewAllLinkError", "bottomButton", "bottomButtonError", "Object", "keys", "window", "validateCareerDetailContent", "addExtraSummary", "payload", "children", "label", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Heading", "inputs", "input", "updateType", "errorMessage", "max<PERSON><PERSON><PERSON>", "inputFiles", "projectId", "className", "map", "element", "a", "isLast", "subHeading", "subSection", "subSectionsProMax", "allowRemoval", "isBorder", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Akshay/LOOP_PROJECTS/shade_cms/dashboard/src/features/Resources/components/contentmanager/CMforDetails/CareerDetailManager.jsx"], "sourcesContent": ["import { useEffect, useState } from \"react\"\r\nimport FileUploader from \"../../../../../components/Input/InputFileUploader\"\r\nimport ContentSection from \"../../breakUI/ContentSections\"\r\nimport { useDispatch, useSelector } from \"react-redux\"\r\nimport { updateMainContent, updateTheProjectSummaryList } from \"../../../../common/homeContentSlice\"\r\nimport content from \"../../websiteComponent/content.json\"\r\nimport DynamicContentSection from \"../../breakUI/DynamicContentSection\"\r\n\r\nconst CareerDetailManager = ({ careerId, currentContent, currentPath, language }) => {\r\n    const dispatch = useDispatch();\r\n    // Get careerDetails array from Redux state instead of relying on currentContent prop\r\n    const careerDetailsFromRedux = useSelector(state => state.homeContent.present.careerDetails)\r\n    // Use careerDetails from Redux if available, otherwise fall back to currentContent (with safety check)\r\n    const careerDetailsArray = Array.isArray(careerDetailsFromRedux) ? careerDetailsFromRedux : (Array.isArray(currentContent) ? currentContent : [])\r\n    const careerIndex = careerDetailsArray?.findIndex(e => e.id == careerId)\r\n\r\n    // Validation states\r\n    const [validationErrors, setValidationErrors] = useState({})\r\n    const [isValidating, setIsValidating] = useState(false)\r\n\r\n    // Function to clear specific validation error\r\n    const clearValidationError = (errorKey) => {\r\n        setValidationErrors(prev => {\r\n            const newErrors = { ...prev }\r\n            delete newErrors[errorKey]\r\n            return newErrors\r\n        })\r\n    }\r\n\r\n    // Validation functions\r\n    const validateField = (value, fieldName) => {\r\n        if (!value || (typeof value === 'string' && value.trim() === '')) {\r\n            return \"Required\"\r\n        }\r\n        return null\r\n    }\r\n\r\n    const validateImage = (imageUrl, fieldName) => {\r\n        if (!imageUrl || imageUrl.trim() === '') {\r\n            return \"Required\"\r\n        }\r\n        return null\r\n    }\r\n\r\n    const validateArray = (array, fieldName, minLength = 1) => {\r\n        if (!array || array.length < minLength) {\r\n            return \"Required\"\r\n        }\r\n        return null\r\n    }\r\n\r\n    // Comprehensive validation function\r\n    const validateAllFields = () => {\r\n        setIsValidating(true)\r\n        const errors = {}\r\n\r\n        const career = currentContent?.[careerIndex]\r\n\r\n        // Banner section validation\r\n        const bannerTitle = career?.banner?.title?.[language]\r\n        const bannerSubTitle = career?.banner?.subTitle?.[language]\r\n        const bannerButton = career?.banner?.button?.[language]\r\n        const bannerImage = career?.banner?.images?.[0]?.url\r\n\r\n        const bannerTitleError = validateField(bannerTitle, \"Banner Title\")\r\n        if (bannerTitleError) errors[\"banner_title\"] = bannerTitleError\r\n\r\n        const bannerSubTitleError = validateField(bannerSubTitle, \"Banner Sub Title\")\r\n        if (bannerSubTitleError) errors[\"banner_subtitle\"] = bannerSubTitleError\r\n\r\n        const bannerButtonError = validateField(bannerButton, \"Banner Button\")\r\n        if (bannerButtonError) errors[\"banner_button\"] = bannerButtonError\r\n\r\n        const bannerImageError = validateImage(bannerImage, \"Banner Image\")\r\n        if (bannerImageError) errors[\"banner_image\"] = bannerImageError\r\n\r\n        // Left panel sections validation\r\n        const leftPanelSections = career?.jobDetails?.leftPanel?.sections\r\n        if (leftPanelSections && leftPanelSections.length > 0) {\r\n            leftPanelSections.forEach((section, index) => {\r\n                const sectionTitle = section?.title?.[language]\r\n                const sectionContent = section?.content?.[language]\r\n\r\n                const sectionTitleError = validateField(sectionTitle, `Left Panel Section ${index + 1} Title`)\r\n                if (sectionTitleError) errors[`left_section_${index}_title`] = sectionTitleError\r\n\r\n                const sectionContentError = validateField(sectionContent, `Left Panel Section ${index + 1} Content`)\r\n                if (sectionContentError) errors[`left_section_${index}_content`] = sectionContentError\r\n            })\r\n        }\r\n\r\n        // Right panel top section validation\r\n        const rightPanelTitle = career?.jobDetails?.rightPanel?.title?.[language]\r\n        const rightPanelButton = career?.jobDetails?.rightPanel?.button?.[language]\r\n\r\n        const rightPanelTitleError = validateField(rightPanelTitle, \"Right Panel Title\")\r\n        if (rightPanelTitleError) errors[\"right_panel_title\"] = rightPanelTitleError\r\n\r\n        const rightPanelButtonError = validateField(rightPanelButton, \"Right Panel Button\")\r\n        if (rightPanelButtonError) errors[\"right_panel_button\"] = rightPanelButtonError\r\n\r\n        // Right panel tailwraps validation\r\n        const tailwraps = career?.jobDetails?.rightPanel?.tailwraps\r\n        if (tailwraps && tailwraps.length > 0) {\r\n            tailwraps.forEach((tailwrap, index) => {\r\n                const tailwrapTitle = tailwrap?.title?.[language]\r\n                const tailwrapDescription = tailwrap?.description?.[language]\r\n                const tailwrapIcon = tailwrap?.images?.[0]?.url\r\n\r\n                const tailwrapTitleError = validateField(tailwrapTitle, `Tailwrap ${index + 1} Title`)\r\n                if (tailwrapTitleError) errors[`tailwrap_${index}_title`] = tailwrapTitleError\r\n\r\n                const tailwrapDescriptionError = validateField(tailwrapDescription, `Tailwrap ${index + 1} Description`)\r\n                if (tailwrapDescriptionError) errors[`tailwrap_${index}_description`] = tailwrapDescriptionError\r\n\r\n                const tailwrapIconError = validateImage(tailwrapIcon, `Tailwrap ${index + 1} Icon`)\r\n                if (tailwrapIconError) errors[`tailwrap_${index}_icon`] = tailwrapIconError\r\n            })\r\n        }\r\n\r\n        // Right panel view all button validation\r\n        const viewAllText = career?.jobDetails?.rightPanel?.viewAllButton?.text?.[language]\r\n        const viewAllLink = career?.jobDetails?.rightPanel?.viewAllButton?.link?.[language]\r\n\r\n        const viewAllTextError = validateField(viewAllText, \"View All Text\")\r\n        if (viewAllTextError) errors[\"view_all_text\"] = viewAllTextError\r\n\r\n        const viewAllLinkError = validateField(viewAllLink, \"View All Link\")\r\n        if (viewAllLinkError) errors[\"view_all_link\"] = viewAllLinkError\r\n\r\n        // Bottom button validation\r\n        const bottomButton = career?.jobDetails?.button?.[language]\r\n        const bottomButtonError = validateField(bottomButton, \"Bottom Button\")\r\n        if (bottomButtonError) errors[\"bottom_button\"] = bottomButtonError\r\n\r\n        setValidationErrors(errors)\r\n        setIsValidating(false)\r\n        return Object.keys(errors).length === 0\r\n    }\r\n\r\n    // Expose validation function globally\r\n    useEffect(() => {\r\n        window.validateCareerDetailContent = validateAllFields;\r\n        return () => {\r\n            delete window.validateCareerDetailContent;\r\n        };\r\n    }, [currentContent, language, careerIndex]);\r\n\r\n    const addExtraSummary = () => {\r\n        // dispatch(updateCardAndItemsArray(\r\n        //     {\r\n        //         insert: {\r\n        //             title: {\r\n        //                 ar: \"\",\r\n        //                 en: \"\"\r\n        //             },\r\n        //             content: {\r\n        //                 ar: \"\",\r\n        //                 en: \"\"\r\n        //             }\r\n        //         },\r\n        //         careerIndex,\r\n        //         context: \"careerDetails\",\r\n        //         operation: 'add'\r\n        //     }\r\n        // ))\r\n    }\r\n\r\n    useEffect(() => {\r\n\r\n        dispatch(updateMainContent({ currentPath: \"careerDetails\", payload: (content?.careerDetails) }))\r\n    }, [])\r\n    return (\r\n        <div>\r\n            <FileUploader id={\"CareerDetailsIDReference\" + careerId} label={\"Rerference doc\"} fileName={\"Upload your file...\"} />\r\n            {/** Hero Banner */}\r\n            <ContentSection\r\n                currentPath={currentPath}\r\n                Heading={\"Banner\"}\r\n                inputs={[\r\n                    { input: \"input\", label: \"Heading/title\", updateType: \"title\", errorMessage: validationErrors[\"banner_title\"], errorKey: \"banner_title\" },\r\n                    { input: \"input\", label: \"Description\", updateType: \"subTitle\", maxLength: 23, errorMessage: validationErrors[\"banner_subtitle\"], errorKey: \"banner_subtitle\" },\r\n                    { input: \"input\", label: \"Button Text\", updateType: \"button\", errorMessage: validationErrors[\"banner_button\"], errorKey: \"banner_button\" },\r\n                    // { input: \"input\", label: \"Url\", updateType: \"url\" },\r\n                ]}\r\n                inputFiles={[{ label: \"Backround Image\", id: \"careerBanner/\" + (careerId), errorMessage: validationErrors[\"banner_image\"], errorKey: \"banner_image\" }]}\r\n                section={\"banner\"}\r\n                language={language}\r\n                currentContent={currentContent}\r\n                projectId={careerIndex + 1}\r\n                clearValidationError={clearValidationError}\r\n            />\r\n\r\n            {/* left panel */}\r\n            <div className=\"mt-4 border-b\">\r\n                <h3 className={`font-semibold text-[1.25rem] mb-4`}>Job Details Left Panel</h3>\r\n                {\r\n                    currentContent?.[careerIndex]?.jobDetails?.leftPanel?.sections?.map((element, index, a) => {\r\n                        const isLast = index === a.length - 1\r\n                        return (\r\n                            <DynamicContentSection key={index}\r\n                                currentPath={currentPath}\r\n                                subHeading={\"Section \" + (index + 1)}\r\n                                inputs={[\r\n                                    { input: \"input\", label: \"Title\", updateType: \"title\", errorMessage: validationErrors[`left_section_${index}_title`], errorKey: `left_section_${index}_title` },\r\n                                    { input: \"textarea\", label: \"Description\", updateType: \"content\", errorMessage: validationErrors[`left_section_${index}_content`], errorKey: `left_section_${index}_content` },\r\n                                ]}\r\n                                section={\"jobDetails\"}\r\n                                subSection={\"leftPanel\"}\r\n                                subSectionsProMax={\"sections\"}\r\n                                index={index}\r\n                                language={language}\r\n                                currentContent={currentContent}\r\n                                projectId={careerIndex + 1}\r\n                                careerIndex={careerIndex}\r\n                                careerId={careerId}\r\n                                allowRemoval={true}\r\n                                isBorder={false}\r\n                                clearValidationError={clearValidationError}\r\n                            />\r\n                        )\r\n                    })\r\n                }\r\n                <button className=\"text-blue-500 cursor-pointer mb-3\"\r\n                    onClick={addExtraSummary}\r\n                >Add More Section...</button>\r\n            </div>\r\n\r\n            {/* right panel */}\r\n            <div className=\"mt-4 border-b\">\r\n                <h3 className={`font-semibold text-[1.25rem] mb-4`}>Job Details Right Panel</h3>\r\n                <div className=\"mb-6\">\r\n                    <ContentSection\r\n                        currentPath={currentPath}\r\n                        subHeading={\"Top Section\"}\r\n                        inputs={[\r\n                            { input: \"input\", label: \"Heading/title\", updateType: \"title\", maxLength: 15, errorMessage: validationErrors[\"right_panel_title\"], errorKey: \"right_panel_title\" },\r\n                            { input: \"input\", label: \"Button Text\", updateType: \"button\", maxLength: 18, errorMessage: validationErrors[\"right_panel_button\"], errorKey: \"right_panel_button\" },\r\n                            // { input: \"input\", label: \"Url\", updateType: \"url\" },\r\n                        ]}\r\n                        section={\"jobDetails\"}\r\n                        subSection={\"rightPanel\"}\r\n                        language={language}\r\n                        currentContent={currentContent}\r\n                        projectId={careerIndex + 1}\r\n                        careerId={careerId}\r\n                        clearValidationError={clearValidationError}\r\n                    />\r\n                </div>\r\n                {\r\n                    currentContent?.[careerIndex]?.jobDetails?.rightPanel?.tailwraps?.map((element, index, a) => {\r\n                        const isLast = index === a.length - 1\r\n                        return (\r\n                            <DynamicContentSection key={index}\r\n                                currentPath={currentPath}\r\n                                subHeading={\"Section \" + (index + 1)}\r\n                                inputs={[\r\n                                    { input: \"input\", label: \"Title\", updateType: \"title\", maxLength: 15, errorMessage: validationErrors[`tailwrap_${index}_title`], errorKey: `tailwrap_${index}_title` },\r\n                                    { input: \"input\", label: \"Description\", updateType: \"description\", maxLength: 19, errorMessage: validationErrors[`tailwrap_${index}_description`], errorKey: `tailwrap_${index}_description` },\r\n                                ]}\r\n                                inputFiles={[{ label: \"icon\", id: `careerRightPanel/${careerId}/${index}`, errorMessage: validationErrors[`tailwrap_${index}_icon`], errorKey: `tailwrap_${index}_icon` }]}\r\n                                section={\"jobDetails\"}\r\n                                subSection={\"rightPanel\"}\r\n                                subSectionsProMax={\"tailwraps\"}\r\n                                index={index}\r\n                                language={language}\r\n                                currentContent={currentContent}\r\n                                projectId={careerIndex + 1}\r\n                                careerIndex={careerIndex}\r\n                                careerId={careerId}\r\n                                isBorder={false}\r\n                                clearValidationError={clearValidationError}\r\n                            />\r\n                        )\r\n                    })\r\n                }\r\n\r\n                {/* right panel redirection link */}\r\n                <div>\r\n                    <ContentSection\r\n                        currentPath={currentPath}\r\n                        subHeading={\"Right panel redirection\"}\r\n                        inputs={[\r\n                            { input: \"input\", label: \"Heading/title\", updateType: \"text\", errorMessage: validationErrors[\"view_all_text\"], errorKey: \"view_all_text\" },\r\n                            { input: \"input\", label: \"Link\", updateType: \"link\", errorMessage: validationErrors[\"view_all_link\"], errorKey: \"view_all_link\" },\r\n                            // { input: \"input\", label: \"Url\", updateType: \"url\" },\r\n                        ]}\r\n                        section={\"jobDetails\"}\r\n                        subSection={\"rightPanel\"}\r\n                        subSectionsProMax={\"viewAllButton\"}\r\n                        language={language}\r\n                        currentContent={currentContent}\r\n                        projectId={careerIndex + 1}\r\n                        careerId={careerId}\r\n                        clearValidationError={clearValidationError}\r\n                    />\r\n                </div>\r\n            </div>\r\n\r\n            {/* the last button */}\r\n            <ContentSection\r\n                currentPath={currentPath}\r\n                subHeading={\"Bottom Button\"}\r\n                inputs={[\r\n                    { input: \"input\", label: \"Button\", updateType: \"button\", errorMessage: validationErrors[\"bottom_button\"], errorKey: \"bottom_button\" },\r\n                ]}\r\n                section={\"jobDetails\"}\r\n                language={language}\r\n                currentContent={currentContent}\r\n                projectId={careerIndex + 1}\r\n                careerId={careerId}\r\n                clearValidationError={clearValidationError}\r\n            />\r\n\r\n        </div>\r\n    )\r\n}\r\n\r\nexport default CareerDetailManager"], "mappings": ";;AAAA,SAASA,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC3C,OAAOC,YAAY,MAAM,mDAAmD;AAC5E,OAAOC,cAAc,MAAM,+BAA+B;AAC1D,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,iBAAiB,EAAEC,2BAA2B,QAAQ,qCAAqC;AACpG,OAAOC,OAAO,MAAM,qCAAqC;AACzD,OAAOC,qBAAqB,MAAM,qCAAqC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAEvE,MAAMC,mBAAmB,GAAGA,CAAC;EAAEC,QAAQ;EAAEC,cAAc;EAAEC,WAAW;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;EACjF,MAAMC,QAAQ,GAAGtB,WAAW,CAAC,CAAC;EAC9B;EACA,MAAMuB,sBAAsB,GAAGtB,WAAW,CAACuB,KAAK,IAAIA,KAAK,CAACC,WAAW,CAACC,OAAO,CAACC,aAAa,CAAC;EAC5F;EACA,MAAMC,kBAAkB,GAAGC,KAAK,CAACC,OAAO,CAACP,sBAAsB,CAAC,GAAGA,sBAAsB,GAAIM,KAAK,CAACC,OAAO,CAACpB,cAAc,CAAC,GAAGA,cAAc,GAAG,EAAG;EACjJ,MAAMqB,WAAW,GAAGH,kBAAkB,aAAlBA,kBAAkB,uBAAlBA,kBAAkB,CAAEI,SAAS,CAACC,CAAC,IAAIA,CAAC,CAACC,EAAE,IAAIzB,QAAQ,CAAC;;EAExE;EACA,MAAM,CAAC0B,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGvC,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC5D,MAAM,CAACwC,YAAY,EAAEC,eAAe,CAAC,GAAGzC,QAAQ,CAAC,KAAK,CAAC;;EAEvD;EACA,MAAM0C,oBAAoB,GAAIC,QAAQ,IAAK;IACvCJ,mBAAmB,CAACK,IAAI,IAAI;MACxB,MAAMC,SAAS,GAAG;QAAE,GAAGD;MAAK,CAAC;MAC7B,OAAOC,SAAS,CAACF,QAAQ,CAAC;MAC1B,OAAOE,SAAS;IACpB,CAAC,CAAC;EACN,CAAC;;EAED;EACA,MAAMC,aAAa,GAAGA,CAACC,KAAK,EAAEC,SAAS,KAAK;IACxC,IAAI,CAACD,KAAK,IAAK,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACE,IAAI,CAAC,CAAC,KAAK,EAAG,EAAE;MAC9D,OAAO,UAAU;IACrB;IACA,OAAO,IAAI;EACf,CAAC;EAED,MAAMC,aAAa,GAAGA,CAACC,QAAQ,EAAEH,SAAS,KAAK;IAC3C,IAAI,CAACG,QAAQ,IAAIA,QAAQ,CAACF,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MACrC,OAAO,UAAU;IACrB;IACA,OAAO,IAAI;EACf,CAAC;EAED,MAAMG,aAAa,GAAGA,CAACC,KAAK,EAAEL,SAAS,EAAEM,SAAS,GAAG,CAAC,KAAK;IACvD,IAAI,CAACD,KAAK,IAAIA,KAAK,CAACE,MAAM,GAAGD,SAAS,EAAE;MACpC,OAAO,UAAU;IACrB;IACA,OAAO,IAAI;EACf,CAAC;;EAED;EACA,MAAME,iBAAiB,GAAGA,CAAA,KAAM;IAAA,IAAAC,cAAA,EAAAC,oBAAA,EAAAC,eAAA,EAAAC,qBAAA,EAAAC,eAAA,EAAAC,qBAAA,EAAAC,eAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,kBAAA,EAAAC,qBAAA,EAAAC,mBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,mBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,mBAAA,EAAAC,qBAAA,EAAAC,mBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,mBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,mBAAA,EAAAC,qBAAA;IAC5B5C,eAAe,CAAC,IAAI,CAAC;IACrB,MAAM6C,MAAM,GAAG,CAAC,CAAC;IAEjB,MAAMC,MAAM,GAAG1E,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAGqB,WAAW,CAAC;;IAE5C;IACA,MAAMsD,WAAW,GAAGD,MAAM,aAANA,MAAM,wBAAA9B,cAAA,GAAN8B,MAAM,CAAEE,MAAM,cAAAhC,cAAA,wBAAAC,oBAAA,GAAdD,cAAA,CAAgBiC,KAAK,cAAAhC,oBAAA,uBAArBA,oBAAA,CAAwB3C,QAAQ,CAAC;IACrD,MAAM4E,cAAc,GAAGJ,MAAM,aAANA,MAAM,wBAAA5B,eAAA,GAAN4B,MAAM,CAAEE,MAAM,cAAA9B,eAAA,wBAAAC,qBAAA,GAAdD,eAAA,CAAgBiC,QAAQ,cAAAhC,qBAAA,uBAAxBA,qBAAA,CAA2B7C,QAAQ,CAAC;IAC3D,MAAM8E,YAAY,GAAGN,MAAM,aAANA,MAAM,wBAAA1B,eAAA,GAAN0B,MAAM,CAAEE,MAAM,cAAA5B,eAAA,wBAAAC,qBAAA,GAAdD,eAAA,CAAgBiC,MAAM,cAAAhC,qBAAA,uBAAtBA,qBAAA,CAAyB/C,QAAQ,CAAC;IACvD,MAAMgF,WAAW,GAAGR,MAAM,aAANA,MAAM,wBAAAxB,eAAA,GAANwB,MAAM,CAAEE,MAAM,cAAA1B,eAAA,wBAAAC,qBAAA,GAAdD,eAAA,CAAgBiC,MAAM,cAAAhC,qBAAA,wBAAAC,sBAAA,GAAtBD,qBAAA,CAAyB,CAAC,CAAC,cAAAC,sBAAA,uBAA3BA,sBAAA,CAA6BgC,GAAG;IAEpD,MAAMC,gBAAgB,GAAGpD,aAAa,CAAC0C,WAAW,EAAE,cAAc,CAAC;IACnE,IAAIU,gBAAgB,EAAEZ,MAAM,CAAC,cAAc,CAAC,GAAGY,gBAAgB;IAE/D,MAAMC,mBAAmB,GAAGrD,aAAa,CAAC6C,cAAc,EAAE,kBAAkB,CAAC;IAC7E,IAAIQ,mBAAmB,EAAEb,MAAM,CAAC,iBAAiB,CAAC,GAAGa,mBAAmB;IAExE,MAAMC,iBAAiB,GAAGtD,aAAa,CAAC+C,YAAY,EAAE,eAAe,CAAC;IACtE,IAAIO,iBAAiB,EAAEd,MAAM,CAAC,eAAe,CAAC,GAAGc,iBAAiB;IAElE,MAAMC,gBAAgB,GAAGnD,aAAa,CAAC6C,WAAW,EAAE,cAAc,CAAC;IACnE,IAAIM,gBAAgB,EAAEf,MAAM,CAAC,cAAc,CAAC,GAAGe,gBAAgB;;IAE/D;IACA,MAAMC,iBAAiB,GAAGf,MAAM,aAANA,MAAM,wBAAArB,kBAAA,GAANqB,MAAM,CAAEgB,UAAU,cAAArC,kBAAA,wBAAAC,qBAAA,GAAlBD,kBAAA,CAAoBsC,SAAS,cAAArC,qBAAA,uBAA7BA,qBAAA,CAA+BsC,QAAQ;IACjE,IAAIH,iBAAiB,IAAIA,iBAAiB,CAAC/C,MAAM,GAAG,CAAC,EAAE;MACnD+C,iBAAiB,CAACI,OAAO,CAAC,CAACC,OAAO,EAAEC,KAAK,KAAK;QAAA,IAAAC,cAAA,EAAAC,gBAAA;QAC1C,MAAMC,YAAY,GAAGJ,OAAO,aAAPA,OAAO,wBAAAE,cAAA,GAAPF,OAAO,CAAEjB,KAAK,cAAAmB,cAAA,uBAAdA,cAAA,CAAiB9F,QAAQ,CAAC;QAC/C,MAAMiG,cAAc,GAAGL,OAAO,aAAPA,OAAO,wBAAAG,gBAAA,GAAPH,OAAO,CAAEpG,OAAO,cAAAuG,gBAAA,uBAAhBA,gBAAA,CAAmB/F,QAAQ,CAAC;QAEnD,MAAMkG,iBAAiB,GAAGnE,aAAa,CAACiE,YAAY,EAAE,sBAAsBH,KAAK,GAAG,CAAC,QAAQ,CAAC;QAC9F,IAAIK,iBAAiB,EAAE3B,MAAM,CAAC,gBAAgBsB,KAAK,QAAQ,CAAC,GAAGK,iBAAiB;QAEhF,MAAMC,mBAAmB,GAAGpE,aAAa,CAACkE,cAAc,EAAE,sBAAsBJ,KAAK,GAAG,CAAC,UAAU,CAAC;QACpG,IAAIM,mBAAmB,EAAE5B,MAAM,CAAC,gBAAgBsB,KAAK,UAAU,CAAC,GAAGM,mBAAmB;MAC1F,CAAC,CAAC;IACN;;IAEA;IACA,MAAMC,eAAe,GAAG5B,MAAM,aAANA,MAAM,wBAAAnB,mBAAA,GAANmB,MAAM,CAAEgB,UAAU,cAAAnC,mBAAA,wBAAAC,qBAAA,GAAlBD,mBAAA,CAAoBgD,UAAU,cAAA/C,qBAAA,wBAAAC,sBAAA,GAA9BD,qBAAA,CAAgCqB,KAAK,cAAApB,sBAAA,uBAArCA,sBAAA,CAAwCvD,QAAQ,CAAC;IACzE,MAAMsG,gBAAgB,GAAG9B,MAAM,aAANA,MAAM,wBAAAhB,mBAAA,GAANgB,MAAM,CAAEgB,UAAU,cAAAhC,mBAAA,wBAAAC,qBAAA,GAAlBD,mBAAA,CAAoB6C,UAAU,cAAA5C,qBAAA,wBAAAC,sBAAA,GAA9BD,qBAAA,CAAgCsB,MAAM,cAAArB,sBAAA,uBAAtCA,sBAAA,CAAyC1D,QAAQ,CAAC;IAE3E,MAAMuG,oBAAoB,GAAGxE,aAAa,CAACqE,eAAe,EAAE,mBAAmB,CAAC;IAChF,IAAIG,oBAAoB,EAAEhC,MAAM,CAAC,mBAAmB,CAAC,GAAGgC,oBAAoB;IAE5E,MAAMC,qBAAqB,GAAGzE,aAAa,CAACuE,gBAAgB,EAAE,oBAAoB,CAAC;IACnF,IAAIE,qBAAqB,EAAEjC,MAAM,CAAC,oBAAoB,CAAC,GAAGiC,qBAAqB;;IAE/E;IACA,MAAMC,SAAS,GAAGjC,MAAM,aAANA,MAAM,wBAAAb,mBAAA,GAANa,MAAM,CAAEgB,UAAU,cAAA7B,mBAAA,wBAAAC,qBAAA,GAAlBD,mBAAA,CAAoB0C,UAAU,cAAAzC,qBAAA,uBAA9BA,qBAAA,CAAgC6C,SAAS;IAC3D,IAAIA,SAAS,IAAIA,SAAS,CAACjE,MAAM,GAAG,CAAC,EAAE;MACnCiE,SAAS,CAACd,OAAO,CAAC,CAACe,QAAQ,EAAEb,KAAK,KAAK;QAAA,IAAAc,eAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,iBAAA;QACnC,MAAMC,aAAa,GAAGL,QAAQ,aAARA,QAAQ,wBAAAC,eAAA,GAARD,QAAQ,CAAE/B,KAAK,cAAAgC,eAAA,uBAAfA,eAAA,CAAkB3G,QAAQ,CAAC;QACjD,MAAMgH,mBAAmB,GAAGN,QAAQ,aAARA,QAAQ,wBAAAE,qBAAA,GAARF,QAAQ,CAAEO,WAAW,cAAAL,qBAAA,uBAArBA,qBAAA,CAAwB5G,QAAQ,CAAC;QAC7D,MAAMkH,YAAY,GAAGR,QAAQ,aAARA,QAAQ,wBAAAG,gBAAA,GAARH,QAAQ,CAAEzB,MAAM,cAAA4B,gBAAA,wBAAAC,iBAAA,GAAhBD,gBAAA,CAAmB,CAAC,CAAC,cAAAC,iBAAA,uBAArBA,iBAAA,CAAuB5B,GAAG;QAE/C,MAAMiC,kBAAkB,GAAGpF,aAAa,CAACgF,aAAa,EAAE,YAAYlB,KAAK,GAAG,CAAC,QAAQ,CAAC;QACtF,IAAIsB,kBAAkB,EAAE5C,MAAM,CAAC,YAAYsB,KAAK,QAAQ,CAAC,GAAGsB,kBAAkB;QAE9E,MAAMC,wBAAwB,GAAGrF,aAAa,CAACiF,mBAAmB,EAAE,YAAYnB,KAAK,GAAG,CAAC,cAAc,CAAC;QACxG,IAAIuB,wBAAwB,EAAE7C,MAAM,CAAC,YAAYsB,KAAK,cAAc,CAAC,GAAGuB,wBAAwB;QAEhG,MAAMC,iBAAiB,GAAGlF,aAAa,CAAC+E,YAAY,EAAE,YAAYrB,KAAK,GAAG,CAAC,OAAO,CAAC;QACnF,IAAIwB,iBAAiB,EAAE9C,MAAM,CAAC,YAAYsB,KAAK,OAAO,CAAC,GAAGwB,iBAAiB;MAC/E,CAAC,CAAC;IACN;;IAEA;IACA,MAAMC,WAAW,GAAG9C,MAAM,aAANA,MAAM,wBAAAX,mBAAA,GAANW,MAAM,CAAEgB,UAAU,cAAA3B,mBAAA,wBAAAC,qBAAA,GAAlBD,mBAAA,CAAoBwC,UAAU,cAAAvC,qBAAA,wBAAAC,sBAAA,GAA9BD,qBAAA,CAAgCyD,aAAa,cAAAxD,sBAAA,wBAAAC,sBAAA,GAA7CD,sBAAA,CAA+CyD,IAAI,cAAAxD,sBAAA,uBAAnDA,sBAAA,CAAsDhE,QAAQ,CAAC;IACnF,MAAMyH,WAAW,GAAGjD,MAAM,aAANA,MAAM,wBAAAP,mBAAA,GAANO,MAAM,CAAEgB,UAAU,cAAAvB,mBAAA,wBAAAC,qBAAA,GAAlBD,mBAAA,CAAoBoC,UAAU,cAAAnC,qBAAA,wBAAAC,sBAAA,GAA9BD,qBAAA,CAAgCqD,aAAa,cAAApD,sBAAA,wBAAAC,sBAAA,GAA7CD,sBAAA,CAA+CuD,IAAI,cAAAtD,sBAAA,uBAAnDA,sBAAA,CAAsDpE,QAAQ,CAAC;IAEnF,MAAM2H,gBAAgB,GAAG5F,aAAa,CAACuF,WAAW,EAAE,eAAe,CAAC;IACpE,IAAIK,gBAAgB,EAAEpD,MAAM,CAAC,eAAe,CAAC,GAAGoD,gBAAgB;IAEhE,MAAMC,gBAAgB,GAAG7F,aAAa,CAAC0F,WAAW,EAAE,eAAe,CAAC;IACpE,IAAIG,gBAAgB,EAAErD,MAAM,CAAC,eAAe,CAAC,GAAGqD,gBAAgB;;IAEhE;IACA,MAAMC,YAAY,GAAGrD,MAAM,aAANA,MAAM,wBAAAH,mBAAA,GAANG,MAAM,CAAEgB,UAAU,cAAAnB,mBAAA,wBAAAC,qBAAA,GAAlBD,mBAAA,CAAoBU,MAAM,cAAAT,qBAAA,uBAA1BA,qBAAA,CAA6BtE,QAAQ,CAAC;IAC3D,MAAM8H,iBAAiB,GAAG/F,aAAa,CAAC8F,YAAY,EAAE,eAAe,CAAC;IACtE,IAAIC,iBAAiB,EAAEvD,MAAM,CAAC,eAAe,CAAC,GAAGuD,iBAAiB;IAElEtG,mBAAmB,CAAC+C,MAAM,CAAC;IAC3B7C,eAAe,CAAC,KAAK,CAAC;IACtB,OAAOqG,MAAM,CAACC,IAAI,CAACzD,MAAM,CAAC,CAAC/B,MAAM,KAAK,CAAC;EAC3C,CAAC;;EAED;EACAxD,SAAS,CAAC,MAAM;IACZiJ,MAAM,CAACC,2BAA2B,GAAGzF,iBAAiB;IACtD,OAAO,MAAM;MACT,OAAOwF,MAAM,CAACC,2BAA2B;IAC7C,CAAC;EACL,CAAC,EAAE,CAACpI,cAAc,EAAEE,QAAQ,EAAEmB,WAAW,CAAC,CAAC;EAE3C,MAAMgH,eAAe,GAAGA,CAAA,KAAM;IAC1B;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EAAA,CACH;EAEDnJ,SAAS,CAAC,MAAM;IAEZ0B,QAAQ,CAACpB,iBAAiB,CAAC;MAAES,WAAW,EAAE,eAAe;MAAEqI,OAAO,EAAG5I,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEuB;IAAe,CAAC,CAAC,CAAC;EACpG,CAAC,EAAE,EAAE,CAAC;EACN,oBACIpB,OAAA;IAAA0I,QAAA,gBACI1I,OAAA,CAACT,YAAY;MAACoC,EAAE,EAAE,0BAA0B,GAAGzB,QAAS;MAACyI,KAAK,EAAE,gBAAiB;MAACC,QAAQ,EAAE;IAAsB;MAAAA,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAErH/I,OAAA,CAACR,cAAc;MACXY,WAAW,EAAEA,WAAY;MACzB4I,OAAO,EAAE,QAAS;MAClBC,MAAM,EAAE,CACJ;QAAEC,KAAK,EAAE,OAAO;QAAEP,KAAK,EAAE,eAAe;QAAEQ,UAAU,EAAE,OAAO;QAAEC,YAAY,EAAExH,gBAAgB,CAAC,cAAc,CAAC;QAAEK,QAAQ,EAAE;MAAe,CAAC,EACzI;QAAEiH,KAAK,EAAE,OAAO;QAAEP,KAAK,EAAE,aAAa;QAAEQ,UAAU,EAAE,UAAU;QAAEE,SAAS,EAAE,EAAE;QAAED,YAAY,EAAExH,gBAAgB,CAAC,iBAAiB,CAAC;QAAEK,QAAQ,EAAE;MAAkB,CAAC,EAC/J;QAAEiH,KAAK,EAAE,OAAO;QAAEP,KAAK,EAAE,aAAa;QAAEQ,UAAU,EAAE,QAAQ;QAAEC,YAAY,EAAExH,gBAAgB,CAAC,eAAe,CAAC;QAAEK,QAAQ,EAAE;MAAgB;MACzI;MAAA,CACF;MACFqH,UAAU,EAAE,CAAC;QAAEX,KAAK,EAAE,iBAAiB;QAAEhH,EAAE,EAAE,eAAe,GAAIzB,QAAS;QAAEkJ,YAAY,EAAExH,gBAAgB,CAAC,cAAc,CAAC;QAAEK,QAAQ,EAAE;MAAe,CAAC,CAAE;MACvJgE,OAAO,EAAE,QAAS;MAClB5F,QAAQ,EAAEA,QAAS;MACnBF,cAAc,EAAEA,cAAe;MAC/BoJ,SAAS,EAAE/H,WAAW,GAAG,CAAE;MAC3BQ,oBAAoB,EAAEA;IAAqB;MAAA4G,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9C,CAAC,eAGF/I,OAAA;MAAKwJ,SAAS,EAAC,eAAe;MAAAd,QAAA,gBAC1B1I,OAAA;QAAIwJ,SAAS,EAAE,mCAAoC;QAAAd,QAAA,EAAC;MAAsB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,EAE3E5I,cAAc,aAAdA,cAAc,wBAAAI,qBAAA,GAAdJ,cAAc,CAAGqB,WAAW,CAAC,cAAAjB,qBAAA,wBAAAC,sBAAA,GAA7BD,qBAAA,CAA+BsF,UAAU,cAAArF,sBAAA,wBAAAC,sBAAA,GAAzCD,sBAAA,CAA2CsF,SAAS,cAAArF,sBAAA,wBAAAC,sBAAA,GAApDD,sBAAA,CAAsDsF,QAAQ,cAAArF,sBAAA,uBAA9DA,sBAAA,CAAgE+I,GAAG,CAAC,CAACC,OAAO,EAAExD,KAAK,EAAEyD,CAAC,KAAK;QACvF,MAAMC,MAAM,GAAG1D,KAAK,KAAKyD,CAAC,CAAC9G,MAAM,GAAG,CAAC;QACrC,oBACI7C,OAAA,CAACF,qBAAqB;UAClBM,WAAW,EAAEA,WAAY;UACzByJ,UAAU,EAAE,UAAU,IAAI3D,KAAK,GAAG,CAAC,CAAE;UACrC+C,MAAM,EAAE,CACJ;YAAEC,KAAK,EAAE,OAAO;YAAEP,KAAK,EAAE,OAAO;YAAEQ,UAAU,EAAE,OAAO;YAAEC,YAAY,EAAExH,gBAAgB,CAAC,gBAAgBsE,KAAK,QAAQ,CAAC;YAAEjE,QAAQ,EAAE,gBAAgBiE,KAAK;UAAS,CAAC,EAC/J;YAAEgD,KAAK,EAAE,UAAU;YAAEP,KAAK,EAAE,aAAa;YAAEQ,UAAU,EAAE,SAAS;YAAEC,YAAY,EAAExH,gBAAgB,CAAC,gBAAgBsE,KAAK,UAAU,CAAC;YAAEjE,QAAQ,EAAE,gBAAgBiE,KAAK;UAAW,CAAC,CAChL;UACFD,OAAO,EAAE,YAAa;UACtB6D,UAAU,EAAE,WAAY;UACxBC,iBAAiB,EAAE,UAAW;UAC9B7D,KAAK,EAAEA,KAAM;UACb7F,QAAQ,EAAEA,QAAS;UACnBF,cAAc,EAAEA,cAAe;UAC/BoJ,SAAS,EAAE/H,WAAW,GAAG,CAAE;UAC3BA,WAAW,EAAEA,WAAY;UACzBtB,QAAQ,EAAEA,QAAS;UACnB8J,YAAY,EAAE,IAAK;UACnBC,QAAQ,EAAE,KAAM;UAChBjI,oBAAoB,EAAEA;QAAqB,GAlBnBkE,KAAK;UAAA0C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAmBhC,CAAC;MAEV,CAAC,CAAC,eAEN/I,OAAA;QAAQwJ,SAAS,EAAC,mCAAmC;QACjDU,OAAO,EAAE1B,eAAgB;QAAAE,QAAA,EAC5B;MAAmB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5B,CAAC,eAGN/I,OAAA;MAAKwJ,SAAS,EAAC,eAAe;MAAAd,QAAA,gBAC1B1I,OAAA;QAAIwJ,SAAS,EAAE,mCAAoC;QAAAd,QAAA,EAAC;MAAuB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAChF/I,OAAA;QAAKwJ,SAAS,EAAC,MAAM;QAAAd,QAAA,eACjB1I,OAAA,CAACR,cAAc;UACXY,WAAW,EAAEA,WAAY;UACzByJ,UAAU,EAAE,aAAc;UAC1BZ,MAAM,EAAE,CACJ;YAAEC,KAAK,EAAE,OAAO;YAAEP,KAAK,EAAE,eAAe;YAAEQ,UAAU,EAAE,OAAO;YAAEE,SAAS,EAAE,EAAE;YAAED,YAAY,EAAExH,gBAAgB,CAAC,mBAAmB,CAAC;YAAEK,QAAQ,EAAE;UAAoB,CAAC,EAClK;YAAEiH,KAAK,EAAE,OAAO;YAAEP,KAAK,EAAE,aAAa;YAAEQ,UAAU,EAAE,QAAQ;YAAEE,SAAS,EAAE,EAAE;YAAED,YAAY,EAAExH,gBAAgB,CAAC,oBAAoB,CAAC;YAAEK,QAAQ,EAAE;UAAqB;UAClK;UAAA,CACF;UACFgE,OAAO,EAAE,YAAa;UACtB6D,UAAU,EAAE,YAAa;UACzBzJ,QAAQ,EAAEA,QAAS;UACnBF,cAAc,EAAEA,cAAe;UAC/BoJ,SAAS,EAAE/H,WAAW,GAAG,CAAE;UAC3BtB,QAAQ,EAAEA,QAAS;UACnB8B,oBAAoB,EAAEA;QAAqB;UAAA4G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,EAEF5I,cAAc,aAAdA,cAAc,wBAAAQ,sBAAA,GAAdR,cAAc,CAAGqB,WAAW,CAAC,cAAAb,sBAAA,wBAAAC,sBAAA,GAA7BD,sBAAA,CAA+BkF,UAAU,cAAAjF,sBAAA,wBAAAC,sBAAA,GAAzCD,sBAAA,CAA2C8F,UAAU,cAAA7F,sBAAA,wBAAAC,sBAAA,GAArDD,sBAAA,CAAuDiG,SAAS,cAAAhG,sBAAA,uBAAhEA,sBAAA,CAAkE2I,GAAG,CAAC,CAACC,OAAO,EAAExD,KAAK,EAAEyD,CAAC,KAAK;QACzF,MAAMC,MAAM,GAAG1D,KAAK,KAAKyD,CAAC,CAAC9G,MAAM,GAAG,CAAC;QACrC,oBACI7C,OAAA,CAACF,qBAAqB;UAClBM,WAAW,EAAEA,WAAY;UACzByJ,UAAU,EAAE,UAAU,IAAI3D,KAAK,GAAG,CAAC,CAAE;UACrC+C,MAAM,EAAE,CACJ;YAAEC,KAAK,EAAE,OAAO;YAAEP,KAAK,EAAE,OAAO;YAAEQ,UAAU,EAAE,OAAO;YAAEE,SAAS,EAAE,EAAE;YAAED,YAAY,EAAExH,gBAAgB,CAAC,YAAYsE,KAAK,QAAQ,CAAC;YAAEjE,QAAQ,EAAE,YAAYiE,KAAK;UAAS,CAAC,EACtK;YAAEgD,KAAK,EAAE,OAAO;YAAEP,KAAK,EAAE,aAAa;YAAEQ,UAAU,EAAE,aAAa;YAAEE,SAAS,EAAE,EAAE;YAAED,YAAY,EAAExH,gBAAgB,CAAC,YAAYsE,KAAK,cAAc,CAAC;YAAEjE,QAAQ,EAAE,YAAYiE,KAAK;UAAe,CAAC,CAChM;UACFoD,UAAU,EAAE,CAAC;YAAEX,KAAK,EAAE,MAAM;YAAEhH,EAAE,EAAE,oBAAoBzB,QAAQ,IAAIgG,KAAK,EAAE;YAAEkD,YAAY,EAAExH,gBAAgB,CAAC,YAAYsE,KAAK,OAAO,CAAC;YAAEjE,QAAQ,EAAE,YAAYiE,KAAK;UAAQ,CAAC,CAAE;UAC3KD,OAAO,EAAE,YAAa;UACtB6D,UAAU,EAAE,YAAa;UACzBC,iBAAiB,EAAE,WAAY;UAC/B7D,KAAK,EAAEA,KAAM;UACb7F,QAAQ,EAAEA,QAAS;UACnBF,cAAc,EAAEA,cAAe;UAC/BoJ,SAAS,EAAE/H,WAAW,GAAG,CAAE;UAC3BA,WAAW,EAAEA,WAAY;UACzBtB,QAAQ,EAAEA,QAAS;UACnB+J,QAAQ,EAAE,KAAM;UAChBjI,oBAAoB,EAAEA;QAAqB,GAlBnBkE,KAAK;UAAA0C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAmBhC,CAAC;MAEV,CAAC,CAAC,eAIN/I,OAAA;QAAA0I,QAAA,eACI1I,OAAA,CAACR,cAAc;UACXY,WAAW,EAAEA,WAAY;UACzByJ,UAAU,EAAE,yBAA0B;UACtCZ,MAAM,EAAE,CACJ;YAAEC,KAAK,EAAE,OAAO;YAAEP,KAAK,EAAE,eAAe;YAAEQ,UAAU,EAAE,MAAM;YAAEC,YAAY,EAAExH,gBAAgB,CAAC,eAAe,CAAC;YAAEK,QAAQ,EAAE;UAAgB,CAAC,EAC1I;YAAEiH,KAAK,EAAE,OAAO;YAAEP,KAAK,EAAE,MAAM;YAAEQ,UAAU,EAAE,MAAM;YAAEC,YAAY,EAAExH,gBAAgB,CAAC,eAAe,CAAC;YAAEK,QAAQ,EAAE;UAAgB;UAChI;UAAA,CACF;UACFgE,OAAO,EAAE,YAAa;UACtB6D,UAAU,EAAE,YAAa;UACzBC,iBAAiB,EAAE,eAAgB;UACnC1J,QAAQ,EAAEA,QAAS;UACnBF,cAAc,EAAEA,cAAe;UAC/BoJ,SAAS,EAAE/H,WAAW,GAAG,CAAE;UAC3BtB,QAAQ,EAAEA,QAAS;UACnB8B,oBAAoB,EAAEA;QAAqB;UAAA4G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAGN/I,OAAA,CAACR,cAAc;MACXY,WAAW,EAAEA,WAAY;MACzByJ,UAAU,EAAE,eAAgB;MAC5BZ,MAAM,EAAE,CACJ;QAAEC,KAAK,EAAE,OAAO;QAAEP,KAAK,EAAE,QAAQ;QAAEQ,UAAU,EAAE,QAAQ;QAAEC,YAAY,EAAExH,gBAAgB,CAAC,eAAe,CAAC;QAAEK,QAAQ,EAAE;MAAgB,CAAC,CACvI;MACFgE,OAAO,EAAE,YAAa;MACtB5F,QAAQ,EAAEA,QAAS;MACnBF,cAAc,EAAEA,cAAe;MAC/BoJ,SAAS,EAAE/H,WAAW,GAAG,CAAE;MAC3BtB,QAAQ,EAAEA,QAAS;MACnB8B,oBAAoB,EAAEA;IAAqB;MAAA4G,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9C,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAED,CAAC;AAEd,CAAC;AAAAzI,EAAA,CApTKL,mBAAmB;EAAA,QACJR,WAAW,EAEGC,WAAW;AAAA;AAAAyK,EAAA,GAHxClK,mBAAmB;AAsTzB,eAAeA,mBAAmB;AAAA,IAAAkK,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}