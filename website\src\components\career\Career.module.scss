// ============================================
// CAREER PAGE - WORLD CLASS RESPONSIVE DESIGN
// ============================================

// Variables
$primary-color: #2563eb;
$primary-hover: #1d4ed8;
$text-dark: #1e293b;
$text-light: #64748b;
$text-muted: #94a3b8;
$bg-gray: #f8fafc;
$border-color: #e2e8f0;
$shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
$shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
$shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.15);
$shadow-xl: 0 20px 40px rgba(0, 0, 0, 0.2);

// Breakpoints
$mobile: 480px;
$tablet: 768px;
$desktop: 1024px;
$large: 1280px;
$xlarge: 1536px;

// ============================================
// HERO SECTION
// ============================================

.heroSection {
  position: relative;
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 140px 20px 100px;
  overflow: hidden;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);

  @media (max-width: $tablet) {
    min-height: auto;
    padding: 120px 20px 80px;
  }

  @media (max-width: $mobile) {
    padding: 100px 16px 60px;
  }
}

.heroBackground {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url("https://loopwebsite.s3.ap-south-1.amazonaws.com/Hero+(2).jpg") no-repeat center center;
  background-size: cover;
  z-index: 0;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(37, 99, 235, 0.85) 0%, rgba(59, 130, 246, 0.75) 50%, rgba(147, 51, 234, 0.8) 100%);
  }

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at 20% 50%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
  }
}

.heroOverlay {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 30%;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.3) 0%, transparent 100%);
  z-index: 1;
}

.heroContent {
  position: relative;
  z-index: 2;
  max-width: 1200px;
  margin: 0 auto;
  text-align: center;
  width: 100%;
}

.heroTitle {
  font-size: clamp(48px, 8vw, 96px);
  font-weight: 600;
  color: #ffffff;
  margin: 0 0 60px;
  letter-spacing: -0.02em;
  line-height: 1;
  text-transform: uppercase;
  text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  animation: fadeInUp 0.8s ease-out;

  @media (max-width: $tablet) {
    margin-bottom: 40px;
  }

  @media (max-width: $mobile) {
    margin-bottom: 30px;
  }
}

.heroCard {
  // background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  padding: 0;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
  border: 1px solid rgba(255, 255, 255, 0.4);
  overflow: hidden;
  animation: fadeInUp 0.8s ease-out 0.2s both;

  @media (max-width: $tablet) {
    border-radius: 20px;
  }

  @media (max-width: $mobile) {
    border-radius: 16px;
  }
}

.heroCardContent {
  padding: 60px 80px;

  @media (max-width: $desktop) {
    padding: 50px 60px;
  }

  @media (max-width: $tablet) {
    padding: 40px 40px;
  }

  @media (max-width: $mobile) {
    padding: 32px 24px;
  }
}

.heroParagraph {
  font-size: clamp(16px, 1.8vw, 20px);
  line-height: 1.8;
  color: $text-dark;
  margin-bottom: 28px;
  text-align: left;
  font-weight: 400;

  &:last-child {
    margin-bottom: 0;
  }

  @media (max-width: $tablet) {
    font-size: 16px;
    line-height: 1.7;
    margin-bottom: 24px;
  }

  @media (max-width: $mobile) {
    font-size: 15px;
    line-height: 1.65;
    margin-bottom: 20px;
  }
}

// ============================================
// FILTER SECTION
// ============================================

.filterSection {
  background: #ffffff;
  padding: 60px 20px;
  border-bottom: 1px solid $border-color;

  @media (max-width: $tablet) {
    padding: 40px 20px;
  }

  @media (max-width: $mobile) {
    padding: 32px 16px;
  }
}

.filterWrapper {
  max-width: 1400px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.searchWrapper {
  width: 100%;
}

.searchInputGroup {
  display: flex;
  align-items: center;
  gap: 12px;
  background: #ffffff;
  border: 2px solid $border-color;
  border-radius: 16px;
  padding: 16px 24px;
  transition: all 0.3s ease;
  max-width: 600px;

  &:focus-within {
    border-color: $primary-color;
    box-shadow: 0 0 0 4px rgba(37, 99, 235, 0.1);
  }

  @media (max-width: $tablet) {
    max-width: 100%;
    padding: 14px 20px;
  }

  @media (max-width: $mobile) {
    padding: 12px 16px;
    border-radius: 12px;
  }
}

.searchIcon {
  flex-shrink: 0;
  color: $text-muted;
  width: 24px;
  height: 24px;

  @media (max-width: $mobile) {
    width: 20px;
    height: 20px;
  }
}

.searchInput {
  flex: 1;
  border: none;
  outline: none;
  font-size: 16px;
  color: $text-dark;
  background: transparent;
  width: 100%;

  &::placeholder {
    color: $text-muted;
  }

  @media (max-width: $mobile) {
    font-size: 15px;
  }
}

.filtersGroup {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;

  @media (max-width: $tablet) {
    gap: 12px;
  }
}

.filterItem {
  flex: 0 0 auto;
}

.filterSelect {
  appearance: none;
  background: #ffffff;
  border: 2px solid $border-color;
  border-radius: 12px;
  padding: 14px 44px 14px 20px;
  font-size: 15px;
  color: $text-dark;
  cursor: pointer;
  transition: all 0.3s ease;
  background-image: url("data:image/svg+xml,%3Csvg width='12' height='8' viewBox='0 0 12 8' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M1 1L6 6L11 1' stroke='%2364748b' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 16px center;
  min-width: 180px;

  &:hover {
    border-color: $primary-color;
  }

  &:focus {
    outline: none;
    border-color: $primary-color;
    box-shadow: 0 0 0 4px rgba(37, 99, 235, 0.1);
  }

  @media (max-width: $tablet) {
    min-width: 160px;
    padding: 12px 40px 12px 16px;
    font-size: 14px;
  }

  @media (max-width: $mobile) {
    min-width: auto;
    width: 100%;
    flex: 1;
  }
}

.clearButton {
  background: $text-muted;
  color: #ffffff;
  border: none;
  border-radius: 12px;
  padding: 14px 24px;
  font-size: 15px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background: $text-dark;
    transform: translateY(-1px);
  }

  @media (max-width: $tablet) {
    padding: 12px 20px;
    font-size: 14px;
  }

  @media (max-width: $mobile) {
    width: 100%;
  }
}

.resultsCounter {
  margin-top: 24px;
  
  p {
    font-size: 16px;
    color: $text-light;
    font-weight: 500;
  }

  @media (max-width: $mobile) {
    margin-top: 16px;
    
    p {
      font-size: 14px;
    }
  }
}

// ============================================
// JOBS SECTION
// ============================================

.jobsSection {
  background: $bg-gray;
  padding: 80px 20px 100px;
  min-height: 60vh;

  @media (max-width: $tablet) {
    padding: 60px 20px 80px;
  }

  @media (max-width: $mobile) {
    padding: 40px 16px 60px;
  }
}

.jobsList {
  max-width: 1400px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: 24px;

  @media (max-width: $mobile) {
    gap: 16px;
  }
}

.jobCard {
  background: #ffffff;
  border-radius: 20px;
  box-shadow: $shadow-sm;
  transition: all 0.3s ease;
  overflow: hidden;
  border: 1px solid transparent;

  &:hover {
    box-shadow: $shadow-lg;
    border-color: rgba(37, 99, 235, 0.1);
    transform: translateY(-2px);
  }

  @media (max-width: $tablet) {
    border-radius: 16px;
  }

  @media (max-width: $mobile) {
    border-radius: 12px;
  }
}

.jobCardHeader {
  padding: 32px;
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  gap: 24px;

  @media (max-width: $desktop) {
    flex-direction: column;
    gap: 24px;
  }

  @media (max-width: $tablet) {
    padding: 24px;
  }

  @media (max-width: $mobile) {
    padding: 20px;
    gap: 20px;
  }
}

.jobMainInfo {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 24px;

  @media (max-width: $mobile) {
    gap: 20px;
  }
}

.jobTitleSection {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.jobTitle {
  font-size: clamp(22px, 3vw, 28px);
  font-weight: 500;
  color: $text-dark;
  margin: 0;
  line-height: 1.3;
}

.jobCategory {
  font-size: 15px;
  color: $primary-color;
  font-weight: 500;
  margin: 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;

  @media (max-width: $mobile) {
    font-size: 14px;
  }
}

.jobMetaGroup {
  display: flex;
  flex-wrap: wrap;
  gap: 32px;

  @media (max-width: $tablet) {
    gap: 24px;
  }

  @media (max-width: $mobile) {
    flex-direction: column;
    gap: 16px;
  }
}

.jobMeta {
  display: flex;
  align-items: center;
  gap: 12px;

  svg {
    flex-shrink: 0;
    color: $text-muted;
    width: 20px;
    height: 20px;
  }

  div {
    display: flex;
    flex-direction: column;
    gap: 4px;
  }
}

.metaLabel {
  font-size: 13px;
  color: $text-muted;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;

  @media (max-width: $mobile) {
    font-size: 12px;
  }
}

.metaValue {
  font-size: 16px;
  color: $text-dark;
  font-weight: 500;

  @media (max-width: $mobile) {
    font-size: 15px;
  }
}

.jobActions {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-shrink: 0;

  @media (max-width: $desktop) {
    width: 100%;
    justify-content: flex-start;
  }

  @media (max-width: $mobile) {
    flex-wrap: wrap;
    gap: 10px;
  }
}

.applyButton {
  background: $primary-color;
  color: #ffffff;
  border: none;
  border-radius: 12px;
  padding: 14px 28px;
  font-size: 15px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
  box-shadow: $shadow-sm;

  &:hover {
    background: $primary-hover;
    transform: translateY(-2px);
    box-shadow: $shadow-md;
  }

  &:active {
    transform: translateY(0);
  }

  @media (max-width: $tablet) {
    padding: 12px 24px;
    font-size: 14px;
  }

  @media (max-width: $mobile) {
    flex: 1;
    min-width: calc(50% - 5px);
    padding: 12px 20px;
  }
}

.detailsButton {
  background: transparent;
  color: $text-dark;
  border: 2px solid $border-color;
  border-radius: 12px;
  padding: 14px 24px;
  font-size: 15px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;

  &:hover {
    border-color: $primary-color;
    color: $primary-color;
    background: rgba(37, 99, 235, 0.05);
  }

  @media (max-width: $tablet) {
    padding: 12px 20px;
    font-size: 14px;
  }

  @media (max-width: $mobile) {
    flex: 1;
    min-width: calc(50% - 5px);
    padding: 12px 20px;
  }
}

.expandButton {
  background: $bg-gray;
  color: $text-dark;
  border: 2px solid $border-color;
  border-radius: 12px;
  padding: 14px 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;

  svg {
    width: 20px;
    height: 20px;
    transition: transform 0.3s ease;
  }

  &:hover {
    border-color: $text-dark;
    background: #ffffff;
  }

  &.active {
    background: $text-dark;
    border-color: $text-dark;
    color: #ffffff;

    svg {
      transform: rotate(180deg);
    }
  }

  @media (max-width: $tablet) {
    padding: 12px 14px;
  }

  @media (max-width: $mobile) {
    width: 100%;
    padding: 12px;
  }
}

.jobCardDetails {
  overflow: hidden;
  border-top: 1px solid $border-color;
}

.jobDetailsContent {
  padding: 32px;
  background: $bg-gray;

  @media (max-width: $tablet) {
    padding: 24px;
  }

  @media (max-width: $mobile) {
    padding: 20px;
  }
}

.detailsTitle {
  font-size: 20px;
  font-weight: 600;
  color: $text-dark;
  margin: 0 0 20px;

  @media (max-width: $mobile) {
    font-size: 18px;
    margin-bottom: 16px;
  }
}

.jobDescriptionList {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 16px;

  li {
    position: relative;
    padding-left: 32px;
    font-size: 16px;
    line-height: 1.7;
    color: $text-dark;

    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 8px;
      width: 8px;
      height: 8px;
      background: $primary-color;
      border-radius: 50%;
    }

    @media (max-width: $mobile) {
      font-size: 15px;
      padding-left: 24px;
      gap: 12px;

      &::before {
        width: 6px;
        height: 6px;
        top: 7px;
      }
    }
  }
}

// ============================================
// NO RESULTS
// ============================================

.noResults {
  text-align: center;
  padding: 80px 20px;
  max-width: 600px;
  margin: 0 auto;

  @media (max-width: $mobile) {
    padding: 60px 20px;
  }
}

.noResultsIcon {
  width: 80px;
  height: 80px;
  margin: 0 auto 24px;
  color: $text-muted;

  @media (max-width: $mobile) {
    width: 60px;
    height: 60px;
  }
}

.noResults h3 {
  font-size: 24px;
  font-weight: 600;
  color: $text-dark;
  margin: 0 0 12px;

  @media (max-width: $mobile) {
    font-size: 20px;
  }
}

.noResults p {
  font-size: 16px;
  color: $text-light;
  line-height: 1.6;
  margin: 0 0 32px;

  @media (max-width: $mobile) {
    font-size: 15px;
    margin-bottom: 24px;
  }
}

.clearFiltersButton {
  background: $primary-color;
  color: #ffffff;
  border: none;
  border-radius: 12px;
  padding: 16px 32px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background: $primary-hover;
    transform: translateY(-2px);
    box-shadow: $shadow-md;
  }

  @media (max-width: $mobile) {
    width: 100%;
    padding: 14px 24px;
    font-size: 15px;
  }
}

// ============================================
// PAGINATION
// ============================================

.paginationWrapper {
  margin-top: 60px;
  display: flex;
  justify-content: center;

  @media (max-width: $mobile) {
    margin-top: 40px;
  }
}

// ============================================
// ANIMATIONS
// ============================================

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

// ============================================
// UTILITY CLASSES
// ============================================

.leftAlign {
  direction: rtl;
  
  .heroParagraph,
  .jobDescriptionList li {
    text-align: right;
  }

  .searchInputGroup,
  .filterSelect,
  .jobActions {
    direction: rtl;
  }
}
