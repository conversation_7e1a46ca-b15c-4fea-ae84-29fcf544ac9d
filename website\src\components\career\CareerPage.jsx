"use client";

import React, { useEffect, useState } from "react";
import styles from "./Career.module.scss";
import localFont from "next/font/local";
import Image from "next/image";
import Button from "@/common/Button";
import { motion, AnimatePresence } from "framer-motion";
import { useRouter } from "next/router";
import dynamic from "next/dynamic";
import ApplyModal from "./ApplyModal";

const Pagination = dynamic(() => import("../../common/Pagination"), {
  ssr: false,
});

const BankGothic = localFont({
  src: "../../../public/font/BankGothicLtBTLight.ttf",
  display: "swap",
});

import { useGlobalContext } from "../../contexts/GlobalContext";

const CareerPage = () => {
  const router = useRouter();
  const { language, content } = useGlobalContext();
  const currentContent = content?.career;

  const [activeIndex, setActiveIndex] = useState(null);
  const [isModal, setIsModal] = useState(false);
  const [selectedJob, setSelectedJob] = useState(null);
  const [selectedPage, setSelectedPage] = useState(1);
  const [totalDocuments, setTotalDocuments] = useState(0);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedJobType, setSelectedJobType] = useState("");
  const [selectedExperience, setSelectedExperience] = useState("");
  const [filteredJobs, setFilteredJobs] = useState([]);

  const itemsPerPage = 10;

  // Filter jobs based on search and filters
  useEffect(() => {
    let jobs = currentContent?.jobListSection?.jobs || [];

    // Apply search filter
    if (searchTerm) {
      jobs = jobs.filter((job) =>
        job.title.value[language].toLowerCase().includes(searchTerm.toLowerCase()) ||
        job.location.value[language].toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Apply job type filter
    if (selectedJobType) {
      jobs = jobs.filter((job) => job.jobType === selectedJobType);
    }

    // Apply experience filter
    if (selectedExperience) {
      jobs = jobs.filter((job) => job.experienceLevel === selectedExperience);
    }

    setFilteredJobs(jobs);
    setTotalDocuments(jobs.length);
    if (jobs.length <= itemsPerPage) {
      setSelectedPage(1);
    }
  }, [searchTerm, selectedJobType, selectedExperience, currentContent, language]);

  const toggleAccordion = (index) => {
    setActiveIndex(activeIndex === index ? null : index);
  };

  const handlePageChange = (page) => {
    setSelectedPage(page);
    setActiveIndex(null);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const paginatedJobs = filteredJobs.slice(
    (selectedPage - 1) * itemsPerPage,
    selectedPage * itemsPerPage
  );

  const clearFilters = () => {
    setSearchTerm("");
    setSelectedJobType("");
    setSelectedExperience("");
  };

  return (
    <>
      {/* Hero Section */}
      <section className={styles.heroSection}>
        <div className={styles.heroOverlay}></div>
        <div className={styles.heroBackground}></div>
        
        <div className="container">
          <motion.div 
            className={styles.heroContent}
            initial={{ opacity: 0, y: 40 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, ease: "easeOut" }}
          >
            <h1 className={styles.heroTitle}>
              {currentContent?.bannerSection?.title[language] || "CAREERS"}
            </h1>
            
            <div className={styles.heroCard}>
              <div className={styles.heroCardContent}>
                <p className={`${styles.heroParagraph} ${BankGothic.className}`}>
                  {currentContent?.bannerSection?.description[language] || 
                    "Every day we help our clients build capabilities, solve their toughest problems and create better outcomes. Our purpose is fueled by our people."}
                </p>
                
                <p className={`${styles.heroParagraph} ${BankGothic.className}`}>
                  {currentContent?.bannerSection?.description2?.[language] || 
                    "We believe in the power of human potential and our growth is driven by hiring exceptional talent. From entry-level to leadership positions, our people are encouraged to harness opportunity and add value in every direction for themselves, our clients and our communities. We build teams that trust one another, focus on common goals, and work towards achieving them."}
                </p>
                
                <p className={`${styles.heroParagraph} ${BankGothic.className}`}>
                  {currentContent?.bannerSection?.description3?.[language] || 
                    "Shade has a fair mix of experience, new ideas, and enthusiasm. We are an equal opportunity employer and have people from diverse cultures and backgrounds in our workforce. We look for passionate, curious, creative and solution-driven team players. We hope you find our team doing interesting work, come work with us."}
                </p>
              </div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Filter Section */}
      <section className={styles.filterSection}>
        <div className="container">
          <div className={styles.filterWrapper}>
            {/* Search Bar */}
            <div className={styles.searchWrapper}>
              <div className={styles.searchInputGroup}>
                <Image
                  src="https://loopwebsite.s3.ap-south-1.amazonaws.com/material-symbols_search+(1).svg"
                  alt="search"
                  width={24}
                  height={24}
                  className={styles.searchIcon}
                />
                <input
                  type="text"
                  placeholder={currentContent?.filterSection?.inputBox?.placeholder[language] || "Search by job title or location"}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className={`${styles.searchInput} ${BankGothic.className}`}
                  aria-label="Search jobs"
                />
              </div>
            </div>

            {/* Filters */}
            <div className={styles.filtersGroup}>
              {currentContent?.filterSection?.filtersSelections?.map((filter, index) => (
                <div key={index} className={styles.filterItem}>
                  <select
                    className={`${styles.filterSelect} ${BankGothic.className}`}
                    aria-label={filter?.title[language]}
                    value={
                      filter?.title[language]?.includes("Type") || filter?.title[language]?.includes("نوع")
                        ? selectedJobType
                        : selectedExperience
                    }
                    onChange={(e) => {
                      if (filter?.title[language]?.includes("Type") || filter?.title[language]?.includes("نوع")) {
                        setSelectedJobType(e.target.value);
                      } else {
                        setSelectedExperience(e.target.value);
                      }
                    }}
                  >
                    <option value="">{filter?.title[language]}</option>
                    {filter.options.map((option, optIndex) => (
                      <option key={optIndex} value={option?.value}>
                        {option?.title[language]}
                      </option>
                    ))}
                  </select>
                </div>
              ))}

              {(searchTerm || selectedJobType || selectedExperience) && (
                <button 
                  onClick={clearFilters}
                  className={styles.clearButton}
                  aria-label="Clear filters"
                >
                  {language === "en" ? "Clear All" : "مسح الكل"}
                </button>
              )}
            </div>
          </div>

          {/* Results Counter */}
          <div className={styles.resultsCounter}>
            <p className={BankGothic.className}>
              {language === "en" 
                ? `${filteredJobs.length} ${filteredJobs.length === 1 ? 'Position' : 'Positions'} Available`
                : `${filteredJobs.length} وظيفة متاحة`
              }
            </p>
          </div>
        </div>
      </section>

      {/* Jobs List Section */}
      <section className={styles.jobsSection}>
        <div className="container">
          {paginatedJobs.length === 0 ? (
            <motion.div 
              className={styles.noResults}
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
            >
              <div className={styles.noResultsIcon}>
                <svg width="80" height="80" viewBox="0 0 24 24" fill="none">
                  <path d="M21 21L15 15M17 10C17 13.866 13.866 17 10 17C6.13401 17 3 13.866 3 10C3 6.13401 6.13401 3 10 3C13.866 3 17 6.13401 17 10Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
              </div>
              <h3 className={BankGothic.className}>
                {language === "en" ? "No positions found" : "لم يتم العثور على وظائف"}
              </h3>
              <p className={BankGothic.className}>
                {language === "en" 
                  ? "Try adjusting your search or filters to find what you're looking for"
                  : "حاول تعديل البحث أو الفلاتر للعثور على ما تبحث عنه"
                }
              </p>
              <button onClick={clearFilters} className={styles.clearFiltersButton}>
                {language === "en" ? "Clear Filters" : "مسح الفلاتر"}
              </button>
            </motion.div>
          ) : (
            <div className={styles.jobsList}>
              <AnimatePresence mode="wait">
                {paginatedJobs.map((job, index) => (
                  <motion.div
                    key={job.id}
                    className={styles.jobCard}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ duration: 0.3, delay: index * 0.05 }}
                  >
                    {/* Job Card Header */}
                    <div className={styles.jobCardHeader}>
                      <div className={styles.jobMainInfo}>
                        <div className={styles.jobTitleSection}>
                          <h3 className={`${styles.jobTitle} ${BankGothic.className}`}>
                            {job?.title?.value[language]}
                          </h3>
                          <p className={`${styles.jobCategory} ${BankGothic.className}`}>
                            {job?.title?.key[language]}
                          </p>
                        </div>

                        <div className={styles.jobMetaGroup}>
                          <div className={styles.jobMeta}>
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                              <path d="M12 21C16.9706 21 21 16.9706 21 12C21 7.02944 16.9706 3 12 3C7.02944 3 3 7.02944 3 12C3 16.9706 7.02944 21 12 21Z" stroke="currentColor" strokeWidth="2"/>
                              <path d="M12 6V12L16 14" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
                            </svg>
                            <div>
                              <span className={`${styles.metaLabel} ${BankGothic.className}`}>
                                {job?.location?.key[language]}
                              </span>
                              <span className={`${styles.metaValue} ${BankGothic.className}`}>
                                {job?.location?.value[language]}
                              </span>
                            </div>
                          </div>

                          <div className={styles.jobMeta}>
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                              <path d="M8 7V3M16 7V3M7 11H17M5 21H19C20.1046 21 21 20.1046 21 19V7C21 5.89543 20.1046 5 19 5H5C3.89543 5 3 5.89543 3 7V19C3 20.1046 3.89543 21 5 21Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                            </svg>
                            <div>
                              <span className={`${styles.metaLabel} ${BankGothic.className}`}>
                                {job?.deadline?.key[language]}
                              </span>
                              <span className={`${styles.metaValue} ${BankGothic.className}`}>
                                {job?.deadline?.value[language]}
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div className={styles.jobActions}>
                        <Button
                          className={styles.applyButton}
                          onClick={() => {
                            setIsModal(true);
                            setSelectedJob(job?.title?.value[language]);
                          }}
                        >
                          {currentContent?.jobListSection?.buttons[0]?.text[language] || "Apply Now"}
                        </Button>
                        
                        <Button
                          className={styles.detailsButton}
                          onClick={() => router.push(`/career/${job.id}`)}
                        >
                          {currentContent?.jobListSection?.buttons[1]?.text[language] || "View Details"}
                        </Button>
                        
                        <button
                          className={`${styles.expandButton} ${activeIndex === index ? styles.active : ''}`}
                          onClick={() => toggleAccordion(index)}
                          aria-label={language === "en" ? "Show more" : "عرض المزيد"}
                        >
                          {activeIndex === index ? (
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                              <path d="M18 15L12 9L6 15" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                            </svg>
                          ) : (
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                              <path d="M6 9L12 15L18 9" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                            </svg>
                          )}
                        </button>
                      </div>
                    </div>

                    {/* Job Card Details (Expandable) */}
                    <AnimatePresence>
                      {activeIndex === index && (
                        <motion.div
                          className={styles.jobCardDetails}
                          initial={{ height: 0, opacity: 0 }}
                          animate={{ height: "auto", opacity: 1 }}
                          exit={{ height: 0, opacity: 0 }}
                          transition={{ duration: 0.3, ease: "easeInOut" }}
                        >
                          <div className={styles.jobDetailsContent}>
                            <h4 className={`${styles.detailsTitle} ${BankGothic.className}`}>
                              {language === "en" ? "Job Description" : "وصف الوظيفة"}
                            </h4>
                            <ul className={styles.jobDescriptionList}>
                              {job.descriptionList?.map((desc, i) => (
                                <li key={i} className={BankGothic.className}>
                                  {desc[language]}
                                </li>
                              ))}
                            </ul>
                          </div>
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </motion.div>
                ))}
              </AnimatePresence>
            </div>
          )}

          {/* Pagination */}
          {paginatedJobs.length > 0 && totalDocuments > itemsPerPage && (
            <div className={styles.paginationWrapper}>
              <Pagination
                totalDocuments={totalDocuments}
                handlePageChange={handlePageChange}
                selectedPage={selectedPage}
              />
            </div>
          )}
        </div>
      </section>

      {/* Apply Modal */}
      <ApplyModal
        isModal={isModal}
        jobTitle={selectedJob}
        onClose={() => setIsModal(false)}
      />
    </>
  );
};

export default CareerPage;
