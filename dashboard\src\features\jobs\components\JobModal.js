import { useState, useEffect } from 'react'
import { XMarkIcon, PlusIcon, TrashIcon } from '@heroicons/react/24/outline'

const JOB_CATEGORIES = [
    { value: 'ENGINEERING', label: 'Engineering' },
    { value: 'MANAGEMENT', label: 'Management' },
    { value: 'DESIGN', label: 'Design' },
    { value: 'MARKETING', label: 'Marketing' },
    { value: 'SALES', label: 'Sales' },
    { value: 'HR', label: 'Human Resources' },
    { value: 'FINANCE', label: 'Finance' },
    { value: 'OPERATIONS', label: 'Operations' },
    { value: 'IT', label: 'Information Technology' },
    { value: 'CONSTRUCTION', label: 'Construction' },
    { value: 'PROJECT_MANAGEMENT', label: 'Project Management' },
    { value: 'QUALITY_ASSURANCE', label: 'Quality Assurance' },
    { value: 'SAFETY', label: 'Safety' },
    { value: 'OTHER', label: 'Other' }
]

const JO<PERSON>_TYPES = [
    { value: 'FULL_TIME', label: 'Full Time' },
    { value: 'PART_TIME', label: 'Part Time' },
    { value: 'CONTRACT', label: 'Contract' },
    { value: 'INTERNSHIP', label: 'Internship' },
    { value: 'REMOTE', label: 'Remote' },
    { value: 'HYBRID', label: 'Hybrid' }
]

const EXPERIENCE_LEVELS = [
    { value: 'ENTRY_LEVEL', label: 'Entry Level' },
    { value: 'MID_LEVEL', label: 'Mid Level' },
    { value: 'SENIOR_LEVEL', label: 'Senior Level' },
    { value: 'EXECUTIVE', label: 'Executive' },
    { value: 'INTERNSHIP', label: 'Internship' }
]

const JOB_STATUSES = [
    { value: 'DRAFT', label: 'Draft' },
    { value: 'ACTIVE', label: 'Active' },
    { value: 'INACTIVE', label: 'Inactive' },
    { value: 'CLOSED', label: 'Closed' }
]

function JobModal({ isOpen, onClose, onSubmit, job, mode }) {
    const [formData, setFormData] = useState(job)
    const [activeTab, setActiveTab] = useState('basic')
    const [errors, setErrors] = useState({})

    useEffect(() => {
        setFormData(job)
        setErrors({})
        console.log('JobModal props:', { isOpen, mode, job }) // Debug log
    }, [job, isOpen, mode])

    const handleInputChange = (field, value) => {
        setFormData(prev => ({
            ...prev,
            [field]: value
        }))
        // Clear error when user starts typing
        if (errors[field]) {
            setErrors(prev => ({
                ...prev,
                [field]: null
            }))
        }
    }

    const handleArrayChange = (field, index, value) => {
        const newArray = [...(formData[field] || [])]
        if (value.trim() === '') {
            newArray.splice(index, 1)
        } else {
            newArray[index] = value
        }
        setFormData(prev => ({
            ...prev,
            [field]: newArray
        }))
    }

    const addArrayItem = (field) => {
        setFormData(prev => ({
            ...prev,
            [field]: [...(prev[field] || []), '']
        }))
    }

    const removeArrayItem = (field, index) => {
        const newArray = [...(formData[field] || [])]
        newArray.splice(index, 1)
        setFormData(prev => ({
            ...prev,
            [field]: newArray
        }))
    }

    const validateForm = () => {
        const newErrors = {}

        if (!formData.title?.trim()) {
            newErrors.title = 'Job title is required'
        }

        if (!formData.description?.trim()) {
            newErrors.description = 'Job description is required'
        }

        if (!formData.location?.trim()) {
            newErrors.location = 'Location is required'
        }

        if (formData.minExperience && formData.maxExperience && 
            parseInt(formData.maxExperience) < parseInt(formData.minExperience)) {
            newErrors.maxExperience = 'Max experience must be greater than min experience'
        }

        if (formData.minSalary && formData.maxSalary && 
            parseFloat(formData.maxSalary) < parseFloat(formData.minSalary)) {
            newErrors.maxSalary = 'Max salary must be greater than min salary'
        }

        setErrors(newErrors)
        return Object.keys(newErrors).length === 0
    }

    const handleSubmit = (e) => {
        e.preventDefault()
        if (validateForm()) {
            // Clean up empty array items
            const cleanedData = {
                ...formData,
                responsibilities: formData.responsibilities?.filter(item => item.trim()) || [],
                requirements: formData.requirements?.filter(item => item.trim()) || [],
                qualifications: formData.qualifications?.filter(item => item.trim()) || [],
                benefits: formData.benefits?.filter(item => item.trim()) || [],
                keywords: formData.keywords?.filter(item => item.trim()) || []
            }
            onSubmit(cleanedData)
        }
    }

    if (!isOpen) {
        console.log('Modal not open, isOpen:', isOpen) // Debug log
        return null
    }

    console.log('Modal rendering, isOpen:', isOpen) // Debug log
    const isReadOnly = mode === 'VIEW'

    return (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4" style={{zIndex: 9999}}>
            <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-hidden">
                {/* Header */}
                <div className="flex justify-between items-center p-6 border-b">
                    <h2 className="text-xl font-semibold">
                        {mode === 'CREATE' ? 'Create New Job' : 
                         mode === 'EDIT' ? 'Edit Job' : 'View Job'}
                    </h2>
                    <button 
                        onClick={onClose}
                        className="btn btn-ghost btn-sm"
                    >
                        <XMarkIcon className="w-5 h-5" />
                    </button>
                </div>

                {/* Tabs */}
                <div className="tabs tabs-bordered px-6">
                    <button 
                        className={`tab ${activeTab === 'basic' ? 'tab-active' : ''}`}
                        onClick={() => setActiveTab('basic')}
                    >
                        Basic Info
                    </button>
                    <button 
                        className={`tab ${activeTab === 'details' ? 'tab-active' : ''}`}
                        onClick={() => setActiveTab('details')}
                    >
                        Job Details
                    </button>
                    <button 
                        className={`tab ${activeTab === 'application' ? 'tab-active' : ''}`}
                        onClick={() => setActiveTab('application')}
                    >
                        Application
                    </button>
                    <button 
                        className={`tab ${activeTab === 'seo' ? 'tab-active' : ''}`}
                        onClick={() => setActiveTab('seo')}
                    >
                        SEO & Settings
                    </button>
                </div>

                {/* Content */}
                <div className="p-6 overflow-y-auto max-h-[60vh]">
                    <form onSubmit={handleSubmit}>
                        {activeTab === 'basic' && (
                            <div className="space-y-4">
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <label className="label">
                                            <span className="label-text">Job Title *</span>
                                        </label>
                                        <input
                                            type="text"
                                            className={`input input-bordered w-full ${errors.title ? 'input-error' : ''}`}
                                            value={formData.title || ''}
                                            onChange={(e) => handleInputChange('title', e.target.value)}
                                            disabled={isReadOnly}
                                            placeholder="e.g. Software Engineer"
                                        />
                                        {errors.title && <span className="text-error text-sm">{errors.title}</span>}
                                    </div>

                                    <div>
                                        <label className="label">
                                            <span className="label-text">Department</span>
                                        </label>
                                        <input
                                            type="text"
                                            className="input input-bordered w-full"
                                            value={formData.department || ''}
                                            onChange={(e) => handleInputChange('department', e.target.value)}
                                            disabled={isReadOnly}
                                            placeholder="e.g. Engineering"
                                        />
                                    </div>
                                </div>

                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <label className="label">
                                            <span className="label-text">Category *</span>
                                        </label>
                                        <select
                                            className="select select-bordered w-full"
                                            value={formData.category || 'OTHER'}
                                            onChange={(e) => handleInputChange('category', e.target.value)}
                                            disabled={isReadOnly}
                                        >
                                            {JOB_CATEGORIES.map(cat => (
                                                <option key={cat.value} value={cat.value}>
                                                    {cat.label}
                                                </option>
                                            ))}
                                        </select>
                                    </div>

                                    <div>
                                        <label className="label">
                                            <span className="label-text">Job Type *</span>
                                        </label>
                                        <select
                                            className="select select-bordered w-full"
                                            value={formData.jobType || 'FULL_TIME'}
                                            onChange={(e) => handleInputChange('jobType', e.target.value)}
                                            disabled={isReadOnly}
                                        >
                                            {JOB_TYPES.map(type => (
                                                <option key={type.value} value={type.value}>
                                                    {type.label}
                                                </option>
                                            ))}
                                        </select>
                                    </div>
                                </div>

                                <div>
                                    <label className="label">
                                        <span className="label-text">Location *</span>
                                    </label>
                                    <input
                                        type="text"
                                        className={`input input-bordered w-full ${errors.location ? 'input-error' : ''}`}
                                        value={formData.location || ''}
                                        onChange={(e) => handleInputChange('location', e.target.value)}
                                        disabled={isReadOnly}
                                        placeholder="e.g. Riyadh, Saudi Arabia"
                                    />
                                    {errors.location && <span className="text-error text-sm">{errors.location}</span>}
                                </div>

                                <div className="flex gap-4">
                                    <label className="label cursor-pointer">
                                        <input
                                            type="checkbox"
                                            className="checkbox"
                                            checked={formData.isRemote || false}
                                            onChange={(e) => handleInputChange('isRemote', e.target.checked)}
                                            disabled={isReadOnly}
                                        />
                                        <span className="label-text ml-2">Remote Work Available</span>
                                    </label>

                                    <label className="label cursor-pointer">
                                        <input
                                            type="checkbox"
                                            className="checkbox"
                                            checked={formData.isHybrid || false}
                                            onChange={(e) => handleInputChange('isHybrid', e.target.checked)}
                                            disabled={isReadOnly}
                                        />
                                        <span className="label-text ml-2">Hybrid Work Available</span>
                                    </label>
                                </div>

                                <div>
                                    <label className="label">
                                        <span className="label-text">Short Description</span>
                                    </label>
                                    <textarea
                                        className="textarea textarea-bordered w-full"
                                        rows="3"
                                        value={formData.shortDescription || ''}
                                        onChange={(e) => handleInputChange('shortDescription', e.target.value)}
                                        disabled={isReadOnly}
                                        placeholder="Brief summary for job listings..."
                                    />
                                </div>

                                <div>
                                    <label className="label">
                                        <span className="label-text">Full Description *</span>
                                    </label>
                                    <textarea
                                        className={`textarea textarea-bordered w-full ${errors.description ? 'textarea-error' : ''}`}
                                        rows="6"
                                        value={formData.description || ''}
                                        onChange={(e) => handleInputChange('description', e.target.value)}
                                        disabled={isReadOnly}
                                        placeholder="Detailed job description..."
                                    />
                                    {errors.description && <span className="text-error text-sm">{errors.description}</span>}
                                </div>
                            </div>
                        )}

                        {activeTab === 'details' && (
                            <div className="space-y-4">
                                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                    <div>
                                        <label className="label">
                                            <span className="label-text">Experience Level</span>
                                        </label>
                                        <select
                                            className="select select-bordered w-full"
                                            value={formData.experienceLevel || 'ENTRY_LEVEL'}
                                            onChange={(e) => handleInputChange('experienceLevel', e.target.value)}
                                            disabled={isReadOnly}
                                        >
                                            {EXPERIENCE_LEVELS.map(level => (
                                                <option key={level.value} value={level.value}>
                                                    {level.label}
                                                </option>
                                            ))}
                                        </select>
                                    </div>

                                    <div>
                                        <label className="label">
                                            <span className="label-text">Min Experience (years)</span>
                                        </label>
                                        <input
                                            type="number"
                                            className="input input-bordered w-full"
                                            value={formData.minExperience || ''}
                                            onChange={(e) => handleInputChange('minExperience', e.target.value)}
                                            disabled={isReadOnly}
                                            min="0"
                                            max="50"
                                        />
                                    </div>

                                    <div>
                                        <label className="label">
                                            <span className="label-text">Max Experience (years)</span>
                                        </label>
                                        <input
                                            type="number"
                                            className={`input input-bordered w-full ${errors.maxExperience ? 'input-error' : ''}`}
                                            value={formData.maxExperience || ''}
                                            onChange={(e) => handleInputChange('maxExperience', e.target.value)}
                                            disabled={isReadOnly}
                                            min="0"
                                            max="50"
                                        />
                                        {errors.maxExperience && <span className="text-error text-sm">{errors.maxExperience}</span>}
                                    </div>
                                </div>

                                {/* Salary Information */}
                                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                                    <div>
                                        <label className="label">
                                            <span className="label-text">Min Salary</span>
                                        </label>
                                        <input
                                            type="number"
                                            className="input input-bordered w-full"
                                            value={formData.minSalary || ''}
                                            onChange={(e) => handleInputChange('minSalary', e.target.value)}
                                            disabled={isReadOnly}
                                            min="0"
                                        />
                                    </div>

                                    <div>
                                        <label className="label">
                                            <span className="label-text">Max Salary</span>
                                        </label>
                                        <input
                                            type="number"
                                            className={`input input-bordered w-full ${errors.maxSalary ? 'input-error' : ''}`}
                                            value={formData.maxSalary || ''}
                                            onChange={(e) => handleInputChange('maxSalary', e.target.value)}
                                            disabled={isReadOnly}
                                            min="0"
                                        />
                                        {errors.maxSalary && <span className="text-error text-sm">{errors.maxSalary}</span>}
                                    </div>

                                    <div>
                                        <label className="label">
                                            <span className="label-text">Currency</span>
                                        </label>
                                        <select
                                            className="select select-bordered w-full"
                                            value={formData.currency || 'SAR'}
                                            onChange={(e) => handleInputChange('currency', e.target.value)}
                                            disabled={isReadOnly}
                                        >
                                            <option value="SAR">SAR</option>
                                            <option value="USD">USD</option>
                                            <option value="EUR">EUR</option>
                                        </select>
                                    </div>

                                    <div>
                                        <label className="label">
                                            <span className="label-text">Period</span>
                                        </label>
                                        <select
                                            className="select select-bordered w-full"
                                            value={formData.salaryPeriod || 'MONTHLY'}
                                            onChange={(e) => handleInputChange('salaryPeriod', e.target.value)}
                                            disabled={isReadOnly}
                                        >
                                            <option value="HOURLY">Hourly</option>
                                            <option value="DAILY">Daily</option>
                                            <option value="WEEKLY">Weekly</option>
                                            <option value="MONTHLY">Monthly</option>
                                            <option value="YEARLY">Yearly</option>
                                        </select>
                                    </div>
                                </div>

                                {/* Array fields */}
                                {['responsibilities', 'requirements', 'qualifications', 'benefits'].map(field => (
                                    <div key={field}>
                                        <label className="label">
                                            <span className="label-text capitalize">{field}</span>
                                            {!isReadOnly && (
                                                <button
                                                    type="button"
                                                    className="btn btn-ghost btn-xs"
                                                    onClick={() => addArrayItem(field)}
                                                >
                                                    <PlusIcon className="w-4 h-4" />
                                                </button>
                                            )}
                                        </label>
                                        <div className="space-y-2">
                                            {(formData[field] || []).map((item, index) => (
                                                <div key={index} className="flex gap-2">
                                                    <input
                                                        type="text"
                                                        className="input input-bordered flex-1"
                                                        value={item}
                                                        onChange={(e) => handleArrayChange(field, index, e.target.value)}
                                                        disabled={isReadOnly}
                                                        placeholder={`Enter ${field.slice(0, -1)}...`}
                                                    />
                                                    {!isReadOnly && (
                                                        <button
                                                            type="button"
                                                            className="btn btn-ghost btn-sm text-red-600"
                                                            onClick={() => removeArrayItem(field, index)}
                                                        >
                                                            <TrashIcon className="w-4 h-4" />
                                                        </button>
                                                    )}
                                                </div>
                                            ))}
                                            {(formData[field] || []).length === 0 && (
                                                <p className="text-gray-500 text-sm">No {field} added yet</p>
                                            )}
                                        </div>
                                    </div>
                                ))}
                            </div>
                        )}

                        {activeTab === 'application' && (
                            <div className="space-y-4">
                                <div>
                                    <label className="label">
                                        <span className="label-text">Application Deadline</span>
                                    </label>
                                    <input
                                        type="datetime-local"
                                        className="input input-bordered w-full"
                                        value={formData.applicationDeadline ? new Date(formData.applicationDeadline).toISOString().slice(0, 16) : ''}
                                        onChange={(e) => handleInputChange('applicationDeadline', e.target.value)}
                                        disabled={isReadOnly}
                                    />
                                </div>

                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <label className="label">
                                            <span className="label-text">Application Email</span>
                                        </label>
                                        <input
                                            type="email"
                                            className="input input-bordered w-full"
                                            value={formData.applicationEmail || ''}
                                            onChange={(e) => handleInputChange('applicationEmail', e.target.value)}
                                            disabled={isReadOnly}
                                            placeholder="<EMAIL>"
                                        />
                                    </div>

                                    <div>
                                        <label className="label">
                                            <span className="label-text">Application URL</span>
                                        </label>
                                        <input
                                            type="url"
                                            className="input input-bordered w-full"
                                            value={formData.applicationUrl || ''}
                                            onChange={(e) => handleInputChange('applicationUrl', e.target.value)}
                                            disabled={isReadOnly}
                                            placeholder="https://careers.company.com/apply"
                                        />
                                    </div>
                                </div>

                                <div>
                                    <label className="label">
                                        <span className="label-text">Application Instructions</span>
                                    </label>
                                    <textarea
                                        className="textarea textarea-bordered w-full"
                                        rows="4"
                                        value={formData.applicationInstructions || ''}
                                        onChange={(e) => handleInputChange('applicationInstructions', e.target.value)}
                                        disabled={isReadOnly}
                                        placeholder="Special instructions for applicants..."
                                    />
                                </div>
                            </div>
                        )}

                        {activeTab === 'seo' && (
                            <div className="space-y-4">
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <label className="label">
                                            <span className="label-text">Meta Title</span>
                                        </label>
                                        <input
                                            type="text"
                                            className="input input-bordered w-full"
                                            value={formData.metaTitle || ''}
                                            onChange={(e) => handleInputChange('metaTitle', e.target.value)}
                                            disabled={isReadOnly}
                                            placeholder="SEO title for search engines"
                                            maxLength="60"
                                        />
                                        <div className="text-xs text-gray-500 mt-1">
                                            {(formData.metaTitle || '').length}/60 characters
                                        </div>
                                    </div>

                                    <div>
                                        <label className="label">
                                            <span className="label-text">Status</span>
                                        </label>
                                        <select
                                            className="select select-bordered w-full"
                                            value={formData.status || 'DRAFT'}
                                            onChange={(e) => handleInputChange('status', e.target.value)}
                                            disabled={isReadOnly}
                                        >
                                            {JOB_STATUSES.map(status => (
                                                <option key={status.value} value={status.value}>
                                                    {status.label}
                                                </option>
                                            ))}
                                        </select>
                                    </div>
                                </div>

                                <div>
                                    <label className="label">
                                        <span className="label-text">Meta Description</span>
                                    </label>
                                    <textarea
                                        className="textarea textarea-bordered w-full"
                                        rows="3"
                                        value={formData.metaDescription || ''}
                                        onChange={(e) => handleInputChange('metaDescription', e.target.value)}
                                        disabled={isReadOnly}
                                        placeholder="SEO description for search engines"
                                        maxLength="160"
                                    />
                                    <div className="text-xs text-gray-500 mt-1">
                                        {(formData.metaDescription || '').length}/160 characters
                                    </div>
                                </div>

                                <div>
                                    <label className="label">
                                        <span className="label-text">Keywords</span>
                                        {!isReadOnly && (
                                            <button
                                                type="button"
                                                className="btn btn-ghost btn-xs"
                                                onClick={() => addArrayItem('keywords')}
                                            >
                                                <PlusIcon className="w-4 h-4" />
                                            </button>
                                        )}
                                    </label>
                                    <div className="space-y-2">
                                        {(formData.keywords || []).map((keyword, index) => (
                                            <div key={index} className="flex gap-2">
                                                <input
                                                    type="text"
                                                    className="input input-bordered flex-1"
                                                    value={keyword}
                                                    onChange={(e) => handleArrayChange('keywords', index, e.target.value)}
                                                    disabled={isReadOnly}
                                                    placeholder="Enter keyword..."
                                                />
                                                {!isReadOnly && (
                                                    <button
                                                        type="button"
                                                        className="btn btn-ghost btn-sm text-red-600"
                                                        onClick={() => removeArrayItem('keywords', index)}
                                                    >
                                                        <TrashIcon className="w-4 h-4" />
                                                    </button>
                                                )}
                                            </div>
                                        ))}
                                        {(formData.keywords || []).length === 0 && (
                                            <p className="text-gray-500 text-sm">No keywords added yet</p>
                                        )}
                                    </div>
                                </div>

                                <div className="flex gap-4">
                                    <label className="label cursor-pointer">
                                        <input
                                            type="checkbox"
                                            className="checkbox"
                                            checked={formData.isPublished || false}
                                            onChange={(e) => handleInputChange('isPublished', e.target.checked)}
                                            disabled={isReadOnly}
                                        />
                                        <span className="label-text ml-2">Published</span>
                                    </label>

                                    <label className="label cursor-pointer">
                                        <input
                                            type="checkbox"
                                            className="checkbox"
                                            checked={formData.isFeatured || false}
                                            onChange={(e) => handleInputChange('isFeatured', e.target.checked)}
                                            disabled={isReadOnly}
                                        />
                                        <span className="label-text ml-2">Featured Job</span>
                                    </label>
                                </div>
                            </div>
                        )}
                    </form>
                </div>

                {/* Footer */}
                {!isReadOnly && (
                    <div className="flex justify-end gap-2 p-6 border-t">
                        <button 
                            type="button"
                            className="btn btn-ghost"
                            onClick={onClose}
                        >
                            Cancel
                        </button>
                        <button 
                            type="submit"
                            className="btn btn-primary"
                            onClick={handleSubmit}
                        >
                            {mode === 'CREATE' ? 'Create Job' : 'Update Job'}
                        </button>
                    </div>
                )}
            </div>
        </div>
    )
}

export default JobModal
