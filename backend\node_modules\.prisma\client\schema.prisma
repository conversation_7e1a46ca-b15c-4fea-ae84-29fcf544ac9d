// Enum for defining the status of a page
enum PageStatus {
  PUBLISHED // ACTIVE
  ARCHIVED // INACTIVE
  TRASHED // REMOVED
}

// Enum for defining the general status of an entity
enum Status {
  ACTIVE
  INACTIVE
  TRASHED
}

// Enum for defining the origin of OTP
enum otpOrigin {
  <PERSON><PERSON>_Login
  forgot_Pass
  NULL
}

// Enum for defining the log outcome
enum logOutcome {
  Success
  Failure
  Unknown
}

// Enum for defining the action performed by user
enum logAction {
  CREATE
  UPDATE
  DELETE
  LOGIN
  LOGOUT
  NULL
}

// RESOURCE ENUM==================
// Enum for defining type of the resource
enum ResourceType {
  MAIN_PAGE
  SUB_PAGE
  SUB_PAGE_ITEM
  FORM
  HEADER
  FOOTER
  NULL
}

// Enum for defining page type
enum ResourceTag {
  HOME
  ABOUT
  SOLUTION
  SERVICE
  MARKET
  PROJECT
  CAREER
  NEWS
  TESTIMONIAL
  CONTACT
  HEADER
  FOOTER
  HISTORY
  SAFETY_RESPONSIBILITY
  VISION
  HSE
  AFFILIATES
  ORGANIZATION_CHART
  TEMPLATE_ONE
  TEMPLATE_TWO
  TEMPLATE_THREE
  TEMPLATE_FOUR
  BOXES
  NULL
}

// Enum for defining the relation type
enum RelationType {
  PARENT
  CHILD
  NULL
}

// RESOURCE VERSION ENUM==================
// Enum for defining the status of the version after assignment
enum VersionStatus {
  EDITING
  DRAFT
  VERIFICATION_PENDING
  REJECTED
  PUBLISH_PENDING
  SCHEDULED
  PUBLISHED
  LIVE
  NULL
}

// SECTION ENUM==================

enum ResourceRoleType {
  MANAGER
  EDITOR
  PUBLISHER
}

enum RequestStatus {
  VERIFICATION_PENDING
  PUBLISH_PENDING
  PUBLISHED
  SCHEDULED
}

enum FlowStatus {
  PENDING
  APPROVED
  REJECTED
  SCHEDULED
  PUBLISHED
}

enum RequestType {
  VERIFICATION
  PUBLICATION
}

enum ApprovalStatus {
  PENDING
  APPROVED
  REJECTED
}

enum MediaType {
  IMAGE
  VIDEO
  DOCUMENT
  AUDIO
}

// Store the action performed by user 
model AuditLog {
  id               String     @id @default(uuid())
  action_performed String
  actionType       String
  entity           String
  entityId         String
  oldValue         Json? // Stores old value as JSON (optional)
  newValue         Json? // Stores new value as JSON (optional)
  ipAddress        String?
  browserInfo      String?
  outcome          logOutcome @default(Unknown) // E.g. 'Success' or 'Failure'
  timestamp        DateTime   @default(now())
  metadata         Json? // Any additional metadata (optional)

  user UserAuditLog?

  @@index([id])
}

// Explanation of Fields
// id: Unique identifier for the log entry.
// userId: A foreign key that references the user who performed the action. This allows us to link the audit log to a user.
// actionType: A string describing what action was performed (e.g., CREATE, UPDATE, DELETE, LOGIN).
// entity: Describes the type of entity being audited (e.g., User, Product).
// entityId: The ID of the entity being affected. For example, if a User is updated, this field will store the User's ID.
// oldValue: Stores the old value of the entity before the update (optional, only for actions like UPDATE and DELETE).
// newValue: Stores the new value of the entity after the update (optional, only for actions like CREATE and UPDATE).
// ipAddress: Stores the IP address of the user who performed the action.
// browserInfo: Stores the user agent or browser/device info (optional).
// outcome: Stores whether the action was successful or not (e.g., Success, Failure).
// timestamp: The time when the action occurred.
// metadata: Any additional metadata related to the action (optional).

model Media {
  id         String    @id @default(uuid())
  url        String
  publicId   String
  type       MediaType
  width      Int?
  height     Int?
  altText    Json? // { en: "Alt text", ar: "نص بديل" }
  createdAt  DateTime  @default(now())
  // Relation
  resource   Resource  @relation(fields: [resourceId], references: [id], onDelete: Cascade)
  resourceId String

  @@index([resourceId])
}

model Notification {
  id        String   @id @default(uuid())
  userId    String? // Can be null for role-based notifications
  role      String // ADMIN, EDITOR, PUBLISHER, etc.
  message   String
  isRead    Boolean  @default(false)
  createdAt DateTime @default(now())

  @@index([userId]) // Add an index for better performance
}

model Otp {
  id        String    @id @default(uuid())
  userId    String // ID of the user to whom the OTP is associated
  deviceId  String
  otpCode   String
  createdAt DateTime  @default(now())
  expiresAt DateTime
  isUsed    Boolean   @default(false) // Whether the OTP has been used
  otpOrigin otpOrigin @default(NULL) // Origin of the OTP (mfa Login or Forgot Password)
  updatedAt DateTime  @updatedAt // Automatically update on modification

  @@unique([userId, deviceId, otpOrigin]) // Unique constraint for user-device-origin combination
  @@index([userId, deviceId, otpOrigin], name: "user_device_origin_idx") // Ensure unique OTP per user-device-origin combination
}

model SubPermission {
  id          String                    @id @default(uuid())
  name        String                    @unique
  description String                    @default("")
  permissions PermissionSubPermission[]

  @@index([id])
}

model Permission {
  id          String           @id @default(cuid())
  name        String           @unique
  description String           @default("")
  roles       RolePermission[]

  roleType   RoleType @relation(fields: [roleTypeId], references: [id]) // New field
  roleTypeId String

  subPermissions PermissionSubPermission[]

  created_at DateTime @default(now())
  updated_at DateTime @updatedAt

  @@index([name])
  @@index([roleTypeId])
}

model RateLimit {
  id          String    @id @default(cuid())
  userId      String    @unique
  attempts    Int       @default(0)
  failures    Int       @default(0)
  lastAttempt DateTime  @default(now())
  blockUntil  DateTime?

  @@index([userId])
}

model UserRole {
  userId    String
  roleId    String
  user      User     @relation(fields: [userId], references: [id])
  role      Role     @relation(fields: [roleId], references: [id])
  createdAt DateTime @default(now())

  @@id([userId, roleId]) // primary key as combination of both id
  @@index([userId])
  @@index([roleId])
}

model UserAuditLog {
  auditLogId String   @unique
  userId     String
  user       User     @relation(fields: [userId], references: [id])
  auditLog   AuditLog @relation(fields: [auditLogId], references: [id], onDelete: Cascade)
  createdAt  DateTime @default(now())

  @@id([auditLogId, userId]) // primary key as combination of both id
  @@index([auditLogId])
  @@index([userId])
}

model RolePermission {
  roleId       String
  permissionId String
  role         Role       @relation(fields: [roleId], references: [id])
  permission   Permission @relation(fields: [permissionId], references: [id])
  createdAt    DateTime   @default(now())

  @@id([roleId, permissionId]) // primary key as combination of both id
  @@index([roleId])
  @@index([permissionId])
}

model PermissionSubPermission {
  permissionId    String
  subPermissionId String
  permission      Permission    @relation(fields: [permissionId], references: [id])
  subPermission   SubPermission @relation(fields: [subPermissionId], references: [id])
  createdAt       DateTime      @default(now())

  @@id([permissionId, subPermissionId]) // primary key as combination of both id
  @@index([permissionId])
  @@index([subPermissionId])
}

// Junction table linking resource versions to section versions
model ResourceVersionSection {
  id    String @id @default(cuid())
  order Int // Display sequence

  // Relationships with cascading deletes
  resourceVersion   ResourceVersion @relation(fields: [resourceVersionId], references: [id], onDelete: Cascade)
  resourceVersionId String
  sectionVersion    SectionVersion  @relation(fields: [sectionVersionId], references: [id], onDelete: Cascade)
  sectionVersionId  String

  // Composite constraints
  @@unique([resourceVersionId, sectionVersionId]) // Prevent duplicate sections
  @@index([resourceVersionId, order]) // For ordered retrieval
  @@index([resourceVersionId])
  @@index([sectionVersionId])
}

// Content items within sections (e.g., cards in a grid)
model SectionVersionItem {
  id    String @id @default(cuid())
  order Int // Display sequence

  // Relationships with cascading deletes
  sectionVersion   SectionVersion @relation(fields: [sectionVersionId], references: [id], onDelete: Cascade)
  sectionVersionId String
  resource         Resource       @relation(fields: [resourceId], references: [id], onDelete: Cascade)
  resourceId       String

  // Composite constraints
  @@unique([sectionVersionId, resourceId]) // Prevent duplicates
  @@index([sectionVersionId, order]) // For ordered retrieval
  @@index([sectionVersionId])
  @@index([resourceId])
}

// TO TRACK USER ROLES(EDITOR, PUBLISHER, MANAGER) FOR THIS RESOURCE
// ROLES CAN BE CHANGED AT ANY TIME
model ResourceRole {
  id         String           @id @default(uuid())
  resource   Resource         @relation(fields: [resourceId], references: [id])
  resourceId String
  user       User             @relation(fields: [userId], references: [id])
  userId     String
  role       ResourceRoleType
  status     Status           @default(ACTIVE) // Add status field
  createdAt  DateTime         @default(now())

  @@unique([resourceId, role, userId, status]) // ✅ No duplicate user-role-status entry
  @@index([resourceId])
  @@index([userId])
}

// STORE HISTORY OF ROLES FOR THIS RESOURCE VERSION
// CAN NOT CHANGED AS THIS IS JUST TO STORE THE HISTORY
// Historical permission snapshot for versions
model ResourceVersionRole {
  id     String           @id @default(uuid())
  role   ResourceRoleType // Permission level at version creation
  status Status           @default(ACTIVE) // Add status field

  // Relationships
  resourceVersion   ResourceVersion @relation(fields: [resourceVersionId], references: [id], onDelete: Cascade)
  resourceVersionId String
  user              User            @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId            String

  // Timestamps
  createdAt DateTime @default(now())

  // Composite constraints
  @@unique([resourceVersionId, role, userId, status]) // ✅ No duplicate user-role-status in version 
  @@index([resourceVersionId]) // For version context
  @@index([userId])
}

// TO TRACK VERIFIERS(MANY, ONE USER PER STAGE) FOR THIS RESOURCE
// VERIFIERS CAN BE CHANGED AT ANY TIME
// Current approval workflow configuration
model ResourceVerifier {
  id     String @id @default(uuid())
  stage  Int
  status Status @default(ACTIVE)

  resource   Resource @relation(fields: [resourceId], references: [id], onDelete: Cascade)
  resourceId String
  user       User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId     String

  createdAt DateTime @default(now())

  // Composite constraints
  @@unique([resourceId, stage, userId, status]) // ✅ Prevent duplicate verifier assignments 
  // Indexes
  @@index([resourceId, stage])
  @@index([userId])
  @@index([resourceId])
}

// TO TRACK VERIFIERS(MANY, ONE USER PER STAGE) FOR THIS RESOURCE VERSION
// CAN NOT CHANGED AS THIS IS JUST TO STORE THE HISTORY
// Historical approval snapshot for versions
model ResourceVersionVerifier {
  id     String @id @default(uuid())
  stage  Int
  status Status @default(ACTIVE)

  resourceVersion   ResourceVersion @relation(fields: [resourceVersionId], references: [id], onDelete: Cascade)
  resourceVersionId String
  user              User            @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId            String

  createdAt DateTime @default(now())

  // Composite constraints
  @@unique([resourceVersionId, stage, userId, status]) // ✅ Prevent duplicate verifier entries 
  // Indexes
  @@index([resourceVersionId, stage])
  @@index([resourceVersionId])
  @@index([userId])
}

// Content workflow requests
model ResourceVersioningRequest {
  id                String            @id @default(uuid())
  status            RequestStatus     @default(VERIFICATION_PENDING)
  flowStatus        FlowStatus        @default(PENDING)
  type              RequestType // VERIFICATION or PUBLICATION
  editorComments    String? // Context for reviewers
  // Relationships
  resourceVersion   ResourceVersion   @relation(fields: [resourceVersionId], references: [id], onDelete: Cascade)
  resourceVersionId String
  sender            User              @relation("sender", fields: [senderId], references: [id], onDelete: Cascade)
  senderId          String
  approvals         RequestApproval[] // Approval records

  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([resourceVersionId])
  @@index([senderId])
  // Indexes
  @@index([resourceVersionId, status]) // For request tracking
  @@index([status, type]) // For workflow processing
}

// Individual approval actions
model RequestApproval {
  id             String                    @id @default(uuid())
  stage          Int? // Verification stage (null for publisher)
  status         ApprovalStatus            @default(PENDING)
  comments       String? // Feedback from approver
  // Relationships
  request        ResourceVersioningRequest @relation(fields: [requestId], references: [id], onDelete: Cascade)
  requestId      String
  approver       User                      @relation("approver", fields: [approverId], references: [id], onDelete: Cascade)
  approverId     String
  approverStatus Status                    @default(ACTIVE)

  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Composite constraints
  @@unique([requestId, approverId, stage]) // Prevent duplicate approvals for same stage/role
  @@unique([requestId, approverId, approverStatus, stage]) // Ensures only one active role per request per user
  // Indexes
  @@index([requestId, status]) // For request progress
  @@index([approverId]) // For user dashboard
  @@index([requestId])
}

// State transition rules for workflow validation
model WorkflowState {
  id          String           @id @default(cuid())
  fromState   VersionStatus
  toState     VersionStatus
  allowedRole ResourceRoleType // Changed from relation to direct enum reference

  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Corrected unique index - only scalar fields
  @@unique([fromState, toState, allowedRole])
  @@index([fromState])
}

model Reminder {
  id          String   @id @default(cuid())
  sender      User     @relation("SentReminders", fields: [senderId], references: [id])
  senderId    String
  receiver    User     @relation("ReceivedReminders", fields: [receiverId], references: [id])
  receiverId  String
  subject     String
  message     String
  replied     Boolean  @default(false)
  response    String?
  sendOnEmail Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@index([senderId])
  @@index([receiverId])
}

model Filters {
  id        String     @id @default(cuid())
  nameEn    String     @unique
  nameAr    String     @unique
  // Relationships
  resources Resource[]
  createdAt DateTime   @default(now())
  updatedAt DateTime   @updatedAt

  @@unique([nameEn, nameAr])
}

// RESOURCE IS TYPE OF CONTENT ON WEBSITE(PAGE, TESTIMONIAL, SERVICE, PROJECTS, HEADER etc)
// Core content entity representing any type of content on the website
model Resource {
  id           String       @id @default(cuid())
  titleEn      String
  titleAr      String
  slug         String       @unique // URL-friendly identifier
  status       Status       @default(ACTIVE)
  resourceType ResourceType @default(NULL) // Content classification
  resourceTag  ResourceTag  @default(NULL) // Content category
  relationType RelationType @default(NULL) // Hierarchy position
  isAssigned   Boolean      @default(false) // Whether content is assigned for work

  // Auto approval
  autoApproval     Boolean @default(false) // Whether content is auto-approved
  autoApprovalTime Int? // time for auto-approval

  // Versioning relationships
  versions             ResourceVersion[] // All historical versions
  liveVersionId        String?              @unique // Currently published version
  liveVersion          ResourceVersion?     @relation(name: "LiveVersion", fields: [liveVersionId], references: [id], onDelete: Restrict, onUpdate: Restrict) //  version can not be deleted
  newVersionEditModeId String?              @unique // Version being actively edited
  newVersionEditMode   ResourceVersion?     @relation(name: "EditModeVersion", fields: [newVersionEditModeId], references: [id], onDelete: Restrict, onUpdate: Restrict) //  version can not be deleted
  scheduledVersionId   String?              @unique // Future version to be published
  scheduledVersion     ResourceVersion?     @relation(name: "ScheduledVersion", fields: [scheduledVersionId], references: [id], onDelete: Restrict, onUpdate: Restrict) //  version can not be deleted
  filters              Filters[]
  // Access control
  roles                ResourceRole[] // Current user permissions
  verifiers            ResourceVerifier[] // Current approval workflow
  sectionVersions      SectionVersion[]
  sectionVersionItems  SectionVersionItem[]
  seo                  SEO?
  media                Media[]

  // Self-referencing hierarchy
  parentId String?
  parent   Resource?  @relation("ResourceToChildren", fields: [parentId], references: [id], onDelete: Restrict, onUpdate: Restrict)
  children Resource[] @relation("ResourceToChildren")

  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  // SEO metadata
  // seo       SEO?

  @@index([resourceType, status])
  @@index([slug])
  @@index([scheduledVersionId])
  @@index([parentId]) // For efficient hierarchy queries
}

// Versioned content with full history tracking
model ResourceVersion {
  id            String        @id @default(cuid())
  versionNumber Int           @default(1) // Sequential version identifier
  versionStatus VersionStatus @default(NULL) // Current lifecycle state
  notes         String? // Change description for auditors
  referenceDoc  String? // Reference to external documentation
  content       Json // Flexible content storage
  icon          String?
  Image         String?
  // Locking mechanism for concurrent edits
  lockedBy      User?         @relation(name: "UserToLockedResourceVersions", fields: [lockedById], references: [id])
  lockedById    String?
  lockedAt      DateTime?
  // Relationships
  resourceId    String
  resource      Resource      @relation(fields: [resourceId], references: [id])
  // Reverse relation mappings (used for multiple refs to ResourceVersion)
  liveFor       Resource[]    @relation(name: "LiveVersion")
  editModeFor   Resource[]    @relation(name: "EditModeVersion")
  scheduledFor  Resource[]    @relation(name: "ScheduledVersion")

  sections        ResourceVersionSection[] // Version-specific sections
  roles           ResourceVersionRole[] // Historical permissions snapshot
  verifiers       ResourceVersionVerifier[] // Historical approvers
  requests        ResourceVersioningRequest[] // Associated workflow requests
  sectionVersions SectionVersion[]

  //  Timestamps & Scheduling and publication dates
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  scheduledAt DateTime? // Future publish time
  publishedAt DateTime? // Actual publish time

  // Composite constraints and indexes
  @@unique([resourceId, versionNumber]) // Version sequence per resource
  @@index([resourceId, versionStatus]) // Common query pattern
  @@index([scheduledAt]) // For scheduling jobs
  @@index([publishedAt]) // For analytics
  @@index([lockedById]) // For analytics
}

model RoleType {
  id          String       @id @default(uuid())
  name        String       @unique // USER or MANAGER
  roles       Role[]
  permissions Permission[]
}

model Role {
  id     String @id @default(cuid())
  name   String @unique
  status Status @default(ACTIVE)

  // relationship references
  users       UserRole[]
  permissions RolePermission[]

  roleType   RoleType @relation(fields: [roleTypeId], references: [id])
  roleTypeId String

  created_at DateTime @default(now())
  updated_at DateTime @updatedAt

  @@index([name])
  @@index([roleTypeId])
}

// This is your Prisma schema file
datasource db {
  provider     = "postgresql"
  url          = env("DATABASE_URL")
  relationMode = "prisma"
}

generator client {
  provider        = "prisma-client-js"
  binaryTargets   = ["native", "debian-openssl-3.0.x"]
  previewFeatures = ["prismaSchemaFolder"]
}

// Reusable content components

model SectionType {
  id         String    @id @default(cuid())
  name       String    @unique
  sections   Section[] // One-to-many relationship
  created_at DateTime  @default(now())
  updated_at DateTime  @updatedAt

  @@index([name])
}

model Section {
  id            String           @id @default(cuid())
  title         String           @unique // Component name
  sectionTypeId String // Foreign key
  sectionType   SectionType      @relation(fields: [sectionTypeId], references: [id]) // Relation
  isGlobal      Boolean          @default(false) // Whether section can be reused across resources
  versions      SectionVersion[] // Version history
  createdAt     DateTime         @default(now())
  updatedAt     DateTime         @updatedAt

  @@index([sectionTypeId]) // For component filtering
  @@index([isGlobal]) // For finding reusable components
}

// Versioned section content
model SectionVersion {
  id                      String                   @id @default(cuid())
  icon                    String?
  version                 Int
  sectionVersionTitle     String?
  content                 Json
  // Relationships
  section                 Section                  @relation(fields: [sectionId], references: [id], onDelete: Cascade)
  sectionId               String
  resource                Resource                 @relation(fields: [resourceId], references: [id], onDelete: Cascade)
  resourceId              String
  resourceVersion         ResourceVersion          @relation(fields: [resourceVersionId], references: [id], onDelete: Cascade)
  resourceVersionId       String
  // Self-referencing relationship (parent-child)
  parentVersion           SectionVersion?          @relation("SectionVersionChildren", fields: [parentVersionId], references: [id], onDelete: Restrict, onUpdate: Restrict)
  parentVersionId         String?
  children                SectionVersion[]         @relation("SectionVersionChildren")
  items                   SectionVersionItem[]
  resourceVersionSections ResourceVersionSection[]

  // Constraints & indexes
  @@unique([sectionId, resourceId, version])
  @@index([sectionId, resourceId])
  @@index([resourceVersionId])
  @@index([sectionId])
  @@index([resourceId])
  @@index([parentVersionId])
}

model SEO {
  id              Int      @id @default(autoincrement())
  metaTitle       Json // Multi-language support for SEO titles
  metaDescription Json // Multi-language support for SEO descriptions
  keywords        Json // Multi-language support for keywords
  createdAt       DateTime @default(now())

  // Relation
  resource   Resource @relation(fields: [resourceId], references: [id], onDelete: Cascade)
  resourceId String   @unique

  @@index([resourceId])
}

// Represents a user in the system

model User {
  id          String  @id @default(cuid())
  name        String
  image       String  @default("")
  email       String  @unique
  password    String
  isSuperUser Boolean @default(false)
  status      Status  @default(ACTIVE)
  phone       String  @default("")

  // relationship references
  roles    UserRole[] // is a many-to-many relationship with Role, managed through UserRole
  auditLog UserAuditLog[] // is a many-to-many relationship with Logs, managed through AuditLog

  // Resource relations
  lockedResourceVersions   ResourceVersion[]           @relation("UserToLockedResourceVersions")
  resourceRoles            ResourceRole[]
  resourceVersionRoles     ResourceVersionRole[]
  resourceVerifiers        ResourceVerifier[]
  resourceVersionVerifiers ResourceVersionVerifier[]
  sentRequests             ResourceVersioningRequest[] @relation("sender")
  approvals                RequestApproval[]           @relation("approver")
  sentReminders            Reminder[]                  @relation("SentReminders")
  receivedReminders        Reminder[]                  @relation("ReceivedReminders")

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([email])
  @@index([id])
}
